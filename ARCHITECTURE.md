# MAS: Modular Multi-Agent System Architecture

This document provides an overview of the MAS architecture, explaining how the different components work together to create a flexible, scalable multi-agent system built on LangGraph.

## System Architecture

The MAS architecture is organized into several layers, each with specific responsibilities:

### 1. Core Layer

- **GraphManager** (`mas/core/graph_manager.py`): Central component that manages LangGraph workflows, including creation, execution, and state management.
- **AgentState**: Pydantic model that represents the state of a workflow, including messages, context, tasks, and next steps.

### 2. Agent Layer

- **BaseAgent** (from `cymulate.corelanggraph.agents.base_agent`): Abstract base class that all agents inherit from, providing common functionality.
- **BaseAgentLLM** (from `cymulate.corelanggraph.agents.base_agent_llm`): Base class for LLM-powered agents.
- **SupervisorAgent** (`mas/agents/global_agent/supervisor_agent.py`): Coordinates workflows and delegates tasks to specialized agents.
- **JiraAgent** (`mas/agents/jira/jira_agent.py`): Interacts with <PERSON><PERSON> for ticket management.
- **SlackAgent** (`mas/agents/messaging/slack_agent.py`): Handles Slack notifications and commands.
- **BitbucketAgent** (`mas/agents/bitbucket/bitbucket_agent.py`): Manages repositories and pull requests.
- **KubernetesAgent** (`mas/agents/k8s/kubernetes_agent.py`): Deploys and monitors Kubernetes resources.
- **ErrorHandlerAgent** (`mas/agents/global_agent/error_handler_agent.py`): Handles errors and provides recovery strategies.
- **ProcessingAgent** (`mas/agents/processing_agent.py`): Processes and transforms data.

### 3. Integration Layer

- **JiraClient** (`mas/integrations/jira/jira_client.py`): Client for Jira API.
- **SlackClient** (`mas/integrations/slack/slack_client.py`): Client for Slack API.
- **BitbucketClient** (`mas/integrations/bitbucket/bitbucket_client.py`): Client for Bitbucket API.
- **KubernetesClient** (`mas/integrations/k8s/kubernetes_client.py`): Client for Kubernetes API.

### 4. Orchestration Layer

- **WorkflowBuilder** (`mas/orchestration/workflow_builder.py`): High-level interface for building and managing agent workflows.

### 5. Model Layer

- **SupervisorOutput** (`mas/models/supervisor_models.py`): Structured output model for supervisor decisions.
- **Other domain-specific models**: Various Pydantic models for different agent outputs.

### 6. Workflow Layer

- **Domain-specific workflows** (`mas/workflow/`): Pre-configured workflows for common tasks.

### 7. Utilities Layer

- **Logger** (from `cymulate.logger`): Custom logging utilities.
- **LangfuseClient** (from `cymulate.corelanggraph.langfuse`): Observability for LLM calls.
- **ModelProvider** (from `cymulate.corellm`): LLM provider abstraction.

## Data Flow

1. **Request Handling**: CLI or API receives a request with a message and optional context.
2. **Workflow Initialization**: A workflow is created or loaded using the WorkflowBuilder.
3. **Supervisor Analysis**: The supervisor agent analyzes the request and delegates tasks to specialized agents.
4. **Agent Execution**: Specialized agents execute their tasks, interacting with external systems.
5. **Structured Output**: Agents use TrustCall for extracting structured data from LLM responses.
6. **State Management**: Workflow state is updated and managed by LangGraph.
7. **Response Generation**: Results are collected and returned to the client.

## Agent Capabilities

### Supervisor Agent
- Task delegation
- Workflow orchestration
- Error handling

### Jira Agent
- Create, update, and query Jira tickets
- Search for issues using JQL
- Manage issue fields and metadata

### Slack Agent
- Send notifications to channels
- Update messages
- Post ephemeral messages
- Retrieve channel history

### Bitbucket Agent
- Create and manage pull requests
- Update PR status
- Add comments to PRs
- List and search repositories

### Kubernetes Agent
- Deploy and manage Kubernetes resources
- Monitor resource status
- Apply YAML configurations
- List and search resources

### Error Handler Agent
- Analyze errors
- Provide recovery strategies (retry, fallback, abort, notify)
- Generate detailed error reports

### Processing Agent
- Extract data using patterns
- Transform data with various operations
- Analyze data for insights
- Format data in different formats (JSON, text, markdown)

## LangGraph Implementation

The system uses LangGraph for workflow management:

- **StateGraph**: Core LangGraph component for defining the agent workflow.
- **Conditional Routing**: Dynamic routing based on agent decisions.
- **Checkpointing**: Support for workflow persistence using memory or Redis backends.
- **START/END**: Flow control for workflow entry and exit points.

## TrustCall Integration

The system uses TrustCall for extracting structured output from LLM responses:

- **Extractors**: Convert free-form LLM responses into validated Pydantic models.
- **Tool Choice**: Direct the LLM to output in a specific format.
- **Validation**: Ensure outputs conform to expected schemas.

## Langfuse Observability

The system uses Langfuse for monitoring and tracking LLM calls:

- **Tracing**: Track LLM calls and agent executions.
- **Metrics**: Monitor performance and usage.
- **Debugging**: Identify issues and optimize prompts.

## Command Line Interface

The system provides a command-line interface for running workflows:

- **Environment Configuration**: Configure agents using environment variables.
- **Agent Selection**: Dynamically include agents based on available credentials.
- **Result Formatting**: Structured output of workflow results.

## Extensibility

The system is designed to be easily extended:

1. **Adding New Agents**: Create a new agent class inheriting from `BaseAgent` or `BaseAgentLLM`.
2. **Adding New Integrations**: Create new client classes for external systems.
3. **Custom Workflows**: Build domain-specific workflows using the `WorkflowBuilder`.
4. **Structured Output Models**: Define new Pydantic models for agent outputs.

## Performance Considerations

- **Asynchronous Execution**: All operations are asynchronous for better performance.
- **Checkpointing**: Support for persistent workflow state using memory or Redis.
- **Error Recovery**: Built-in error handling and recovery mechanisms.
- **LLM Provider Abstraction**: Flexibility to switch between different LLM providers.

## API Endpoints

- **POST /agent**: Send a request to the agent system
- **POST /process**: Process data using the processing agent
- **GET /workflows**: List all workflows
- **GET /workflows/{workflow_id}**: Get workflow state
- **DELETE /workflows/{workflow_id}**: Delete workflow state
- **POST /users**: Create a new user
- **GET /agents**: List all available agents
- **GET /health**: Health check endpoint

## Deployment Architecture

The system is designed to be deployed in a Kubernetes cluster:

- **API Server**: FastAPI application exposed via Ingress.
- **Redis**: Optional Redis instance for state persistence.
- **Horizontal Pod Autoscaler**: Scales the API server based on load.
- **Secrets**: Kubernetes secrets for API keys and credentials.
- **ConfigMap**: Configuration for the system.

## Security Model

- **API Key Authentication**: All API requests require a valid API key.
- **Role-Based Access Control**: Users have roles with associated permissions.
- **JWT Tokens**: Used for authentication in longer sessions.
- **Secure Storage**: Sensitive credentials are stored securely.

## Monitoring and Observability

- **Logging**: Comprehensive logging throughout the system.
- **Health Checks**: API endpoint for system health monitoring.
- **State Inspection**: API endpoints for inspecting workflow state.
- **Error Reporting**: Detailed error reports for troubleshooting.

## Future Enhancements

- **Additional Agents**: More specialized agents for different tasks.
- **Enhanced Security**: More sophisticated authentication and authorization.
- **Improved Observability**: Integration with monitoring systems.
- **Advanced Workflows**: More complex workflow patterns and templates.
- **Agent Learning**: Agents that learn from past interactions. 