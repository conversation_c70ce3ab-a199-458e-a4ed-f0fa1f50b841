import pytest

from storage.blob_service import StorageBlob


class DummyBlobClient:
    def upload_blob(self, *a, **k):
        return {"uploaded": True}
    def download_blob(self):
        class Dummy:
            def readall(self):
                return b"data"
        return Dummy()
    def delete_blob(self):
        return {"deleted": True}
    @property
    def url(self):
        return "https://url"

class DummyContainerClient:
    def get_blob_client(self, key):
        return DummyBlobClient()
    def list_blobs(self, name_starts_with=None):
        class Blob:
            name = "blob1"
        return [Blob()]

class DummyBlobServiceClient:
    def get_container_client(self, storage):
        return DummyContainerClient()
    @property
    def credential(self):
        class Cred:
            account_key = "key"
        return Cred()
    @staticmethod
    def from_connection_string(cs):
        return DummyBlobServiceClient()

def make_blob():
    b = StorageBlob()
    b.account = "acc"
    b.blob_service_client = DummyBlobServiceClient()
    return b

@pytest.mark.asyncio
async def test_blob_upload():
    b = make_blob()
    result = await b.upload({"storage": "bucket", "key": "k", "file": b"data"})
    assert result["uploaded"]

@pytest.mark.asyncio
async def test_blob_get_file_stream():
    b = make_blob()
    result = await b.get_file_stream({"storage": "bucket", "key": "k"})
    assert result == b"data"

def test_blob_get_file():
    b = make_blob()
    result = b.get_file({"storage": "bucket", "key": "k"})
    assert result == b"data"

@pytest.mark.asyncio
async def test_blob_delete():
    b = make_blob()
    result = await b.delete({"storage": "bucket", "key": "k"})
    assert result["deleted"]

@pytest.mark.asyncio
async def test_blob_empty_directory():
    b = make_blob()
    await b.empty_directory({"storage": "bucket", "dir": "d"})
    assert True

def test_blob_build_url():
    b = make_blob()
    url = b.build_url({"storage": "bucket", "key": "k"})
    assert url.startswith("https://acc.blob.core.windows.net/")

@pytest.mark.asyncio
async def test_blob_copy():
    b = make_blob()
    result = await b.copy({"storage": "bucket", "key": "k", "source": "src"})
    assert result is not None

@pytest.mark.asyncio
async def test_blob_get_url():
    b = make_blob()
    b.account = "acc"
    b.blob_service_client = DummyBlobServiceClient()
    result = await b.get_url({"storage": "bucket", "key": "k"})
    assert result.startswith("https://acc.blob.core.windows.net/") 