"""
Tests for the storage package.
"""

from unittest.mock import MagicMock, patch

import pytest

from storage import StorageBlob, StorageFactory, StorageType


def test_storage_factory_create_blob():
    """Test creating a Blob storage instance."""
    with patch("storage.blob_service.BlobServiceClient") as mock_blob_service_client:
        # Mock the Blob service client
        mock_blob = MagicMock()
        mock_blob_service_client.from_connection_string.return_value = mock_blob
        
        # Create a Blob storage instance
        storage = StorageFactory.create_blob_storage({
            "account": "test-account",
            "account_key": "test-account-key",
        })
        
        # Verify the storage instance
        assert isinstance(storage, StorageBlob)
        assert storage.type == "blob"
        assert storage.blob_service_client is not None
        
        # Verify BlobServiceClient.from_connection_string was called with the correct arguments
        mock_blob_service_client.from_connection_string.assert_called_once_with(
            "DefaultEndpointsProtocol=https;AccountName=test-account;AccountKey=test-account-key;EndpointSuffix=core.windows.net"
        )


def test_storage_factory_create_storage():
    """Test creating a storage instance based on the storage type."""
    with patch("storage.factory.StorageFactory.create_s3_storage") as mock_create_s3:
        with patch("storage.factory.StorageFactory.create_blob_storage") as mock_create_blob:
            # Mock the storage instances
            mock_s3 = MagicMock()
            mock_blob = MagicMock()
            mock_create_s3.return_value = mock_s3
            mock_create_blob.return_value = mock_blob
            
            # Create an S3 storage instance
            storage_s3 = StorageFactory.create_storage(StorageType.S3, {"config": "test"})
            
            # Verify create_s3_storage was called with the correct arguments
            mock_create_s3.assert_called_once_with({"config": "test"})
            assert storage_s3 is mock_s3
            
            # Create a Blob storage instance
            storage_blob = StorageFactory.create_storage(StorageType.BLOB, {"config": "test"})
            
            # Verify create_blob_storage was called with the correct arguments
            mock_create_blob.assert_called_once_with({"config": "test"})
            assert storage_blob is mock_blob
            
            # Test with string storage type
            storage_s3_str = StorageFactory.create_storage("s3", {"config": "test"})
            assert storage_s3_str is mock_s3
            
            # Test with invalid storage type
            with pytest.raises(ValueError):
                StorageFactory.create_storage("invalid", {"config": "test"})
