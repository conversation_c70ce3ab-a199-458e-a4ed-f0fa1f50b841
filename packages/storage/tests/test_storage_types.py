from storage.storage_types import (
    EmptyDirectory,
    GetBlobServiceClient,
    StorageType,
    Upload,
)


def test_storage_type_enum():
    assert StorageType.S3 == "s3"
    assert StorageType.BLOB == "blob"

def test_upload_typing():
    u: Upload = {"storage": "bucket", "key": "k", "file": b"data"}
    assert u["storage"] == "bucket"

def test_empty_directory_typing():
    e: EmptyDirectory = {"storage": "bucket", "dir": "d"}
    assert e["dir"] == "d"

def test_blob_service_client_typing():
    b: GetBlobServiceClient = {"account": "a", "account_key": "k"}
    assert b["account"] == "a" 