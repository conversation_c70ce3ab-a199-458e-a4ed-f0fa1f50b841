import pytest

from storage.s3_service import StorageS3


class DummyBoto3:
    def client(self, *a, **k):
        class Dummy:
            def put_object(self, **kwargs):
                return {"ok": True}
            def get_object(self, **kwargs):
                return {"Body": DummyBody(), "ResponseMetadata": {"HTTPStatusCode": 200}}
            def delete_object(self, **kwargs):
                return {"deleted": True}
            def list_objects_v2(self, **kwargs):
                return {"Contents": [{"Key": "k"}], "IsTruncated": False}
            def copy_object(self, **kwargs):
                return {"copied": True}
            def generate_presigned_url(self, *a, **k):
                return "url"
            def head_bucket(self, **kwargs):
                return True
            def upload_fileobj(self, *a, **k):
                return True
            def delete_objects(self, **kwargs):
                return {"Deleted": True}
        return Dummy()
class DummyBody:
    def iter_chunks(self):
        yield b"abc"
    def read(self):
        return b"abc"

def test_init_s3_client(monkeypatch):
    monkeypatch.setattr("boto3.client", DummyBoto3().client)
    s3 = StorageS3()
    s3.init_s3_client({"access_key_id": "id", "secret_access_key": "sec"})
    assert s3.s3

def test_build_url():
    s3 = StorageS3()
    url = s3.build_url({"storage": "bucket", "key": "k"})
    assert url.startswith("https://s3-")

@pytest.mark.asyncio
async def test_upload(monkeypatch):
    monkeypatch.setattr("boto3.client", DummyBoto3().client)
    s3 = StorageS3()
    s3.init_s3_client({"access_key_id": "id", "secret_access_key": "sec"})
    result = await s3.upload({"storage": "bucket", "key": "k", "file": b"data"})
    assert result["ok"]

@pytest.mark.asyncio
async def test_get_file_stream(monkeypatch):
    monkeypatch.setattr("boto3.client", DummyBoto3().client)
    s3 = StorageS3()
    s3.init_s3_client({"access_key_id": "id", "secret_access_key": "sec"})
    result = await s3.get_file_stream({"storage": "bucket", "key": "k"})
    assert hasattr(result, "iter_chunks")

@pytest.mark.asyncio
async def test_delete(monkeypatch):
    monkeypatch.setattr("boto3.client", DummyBoto3().client)
    s3 = StorageS3()
    s3.init_s3_client({"access_key_id": "id", "secret_access_key": "sec"})
    result = await s3.delete({"storage": "bucket", "key": "k"})
    assert result["deleted"]

@pytest.mark.asyncio
async def test_copy(monkeypatch):
    monkeypatch.setattr("boto3.client", DummyBoto3().client)
    s3 = StorageS3()
    s3.init_s3_client({"access_key_id": "id", "secret_access_key": "sec"})
    result = await s3.copy({"storage": "bucket", "key": "k", "source": "src"})
    assert result["copied"]

@pytest.mark.asyncio
async def test_get_url(monkeypatch):
    monkeypatch.setattr("boto3.client", DummyBoto3().client)
    s3 = StorageS3()
    s3.init_s3_client({"access_key_id": "id", "secret_access_key": "sec"})
    url = await s3.get_url({"storage": "bucket", "key": "k"})
    assert url == "url"

@pytest.mark.asyncio
async def test_empty_directory(monkeypatch):
    monkeypatch.setattr("boto3.client", DummyBoto3().client)
    s3 = StorageS3()
    s3.init_s3_client({"access_key_id": "id", "secret_access_key": "sec"})
    result = await s3.empty_directory({"storage": "bucket", "dir": "d"})
    assert result["Deleted"] 