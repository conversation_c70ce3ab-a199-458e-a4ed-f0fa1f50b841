import pytest

from storage.factory import StorageFactory
from storage.storage_types import StorageType


class DummyS3:
    def __init__(self):
        self.inited = False
    def init_s3_client(self, config):
        self.inited = True

class DummyBlob:
    def __init__(self):
        self.inited = False
    def get_blob_service_client(self, config):
        self.inited = True


def test_create_s3_storage_with_config(monkeypatch):
    monkeypatch.setenv("AWS_ACCESS_KEY_ID", "id")
    monkeypatch.setenv("AWS_SECRET_ACCESS_KEY", "secret")
    s3 = StorageFactory.create_s3_storage({"access_key_id": "id", "secret_access_key": "secret", "is_decrypted": True})
    assert s3.type == "s3"

def test_create_blob_storage_with_config(monkeypatch):
    monkeypatch.setenv("AZURE_STORAGE_ACCOUNT", "acc")
    monkeypatch.setenv("AZURE_STORAGE_KEY", "key")
    blob = StorageFactory.create_blob_storage({"account": "acc", "account_key": "key"})
    assert blob.type == "blob"

def test_create_storage_s3(monkeypatch):
    monkeypatch.setenv("AWS_ACCESS_KEY_ID", "id")
    monkeypatch.setenv("AWS_SECRET_ACCESS_KEY", "secret")
    s3 = StorageFactory.create_storage("s3")
    assert s3.type == "s3"

def test_create_storage_blob(monkeypatch):
    monkeypatch.setenv("AZURE_STORAGE_ACCOUNT", "acc")
    monkeypatch.setenv("AZURE_STORAGE_KEY", "key")
    blob = StorageFactory.create_storage("blob")
    assert blob.type == "blob"

def test_create_storage_invalid():
    with pytest.raises(ValueError):
        StorageFactory.create_storage("invalid")

def test_create_s3_storage_missing_config():
    with pytest.raises(ValueError):
        StorageFactory.create_s3_storage({"access_key_id": "id"})

def test_create_storage_enum():
    s3 = StorageFactory.create_storage(StorageType.S3)
    assert s3.type == "s3"
    blob = StorageFactory.create_storage(StorageType.BLOB)
    assert blob.type == "blob"

def test_create_s3_storage_env(monkeypatch):
    monkeypatch.setenv("AWS_ACCESS_KEY_ID", "id")
    monkeypatch.setenv("AWS_SECRET_ACCESS_KEY", "secret")
    s3 = StorageFactory.create_s3_storage()
    assert s3.type == "s3"

def test_create_blob_storage_env(monkeypatch):
    monkeypatch.setenv("AZURE_STORAGE_ACCOUNT", "acc")
    monkeypatch.setenv("AZURE_STORAGE_KEY", "key")
    blob = StorageFactory.create_blob_storage()
    assert blob.type == "blob"

def test_create_s3_storage_no_config(monkeypatch):
    monkeypatch.delenv("AWS_ACCESS_KEY_ID", raising=False)
    monkeypatch.delenv("AWS_SECRET_ACCESS_KEY", raising=False)
    s3 = StorageFactory.create_s3_storage()
    assert s3.type == "s3"

def test_create_blob_storage_no_config(monkeypatch):
    monkeypatch.delenv("AZURE_STORAGE_ACCOUNT", raising=False)
    monkeypatch.delenv("AZURE_STORAGE_KEY", raising=False)
    blob = StorageFactory.create_blob_storage()
    assert blob.type == "blob" 