import pytest

from storage.decrypt import Cryptr


def test_cryptr_encrypt_decrypt():
    c = Cryptr("mysecret")
    data = "hello world"
    encrypted = c.encrypt(data)
    assert isinstance(encrypted, str)
    decrypted = c.decrypt(encrypted)
    assert decrypted == data

def test_cryptr_encrypt_none():
    c = Cryptr()
    with pytest.raises(ValueError):
        c.encrypt(None)

def test_cryptr_decrypt_none():
    c = Cryptr()
    with pytest.raises(ValueError):
        c.decrypt(None)

def test_cryptr_bad_secret():
    with pytest.raises(ValueError):
        Cryptr("")

def test_cryptr_decrypt_invalid_iv():
    c = Cryptr()
    # IV trop court (legacy path)
    legacy = "a" * 16 + "b" * 32
    with pytest.raises(ValueError):
        c.decrypt(legacy)

def test_cryptr_decrypt_invalid_hex():
    c = Cryptr()
    # IV non-hex
    bad = "zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz" + "b" * 32
    with pytest.raises(Exception):
        c.decrypt(bad)

def test_cryptr_decrypt_legacy_padding():
    c = Cryptr()
    # IV legacy (trop court, padding à 16 bytes)
    legacy = "a" * 16 + "b" * 32
    # Le decrypt va essayer de padder l'IV et lever ValueError
    with pytest.raises(ValueError):
        c.decrypt(legacy) 