from storage.interfaces import Storage


class DummyStorage(Storage):
    @property
    def type(self):
        return "dummy"
    async def upload(self, params):
        return True
    async def get_file_stream(self, params):
        return b"stream"
    def get_file(self, params):
        return b"file"
    async def delete(self, params):
        return True
    async def empty_directory(self, params):
        return True
    def build_url(self, params):
        return "url"
    async def copy(self, params):
        return True
    async def get_url(self, params):
        return "url"

def test_stream_to_bytes():
    s = DummyStorage()
    assert s.stream_to_bytes(None) == b""

def test_dummy_storage_methods():
    s = DummyStorage()
    assert s.type == "dummy"

# Test retiré : il n'est pas possible d'instancier Storage directement (classe abstraite Python). 