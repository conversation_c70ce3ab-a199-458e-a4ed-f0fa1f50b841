# cymulate.storage

A unified storage library that provides an abstraction over multiple cloud storage providers, including AWS S3 and Azure Blob Storage. This library offers a consistent interface for common file operations such as upload, download, deletion, copying, and generating signed URLs.

## Features

- Unified interface for file operations (upload, download, delete, copy, etc.)
- Supports AWS S3 and Azure Blob Storage
- Asynchronous API for efficient I/O operations
- Strong typing with comprehensive type definitions and interfaces
- Factory pattern for easy creation of storage instances

## Installation

Install the package using pip:

```bash
pip install cymulate.storage
```

## Configuration

The library can be configured in several ways:

### AWS S3 Configuration

```python
from storage import StorageFactory

# Using explicit credentials
s3_storage = StorageFactory.create_s3_storage({
    "access_key_id": "your-access-key",
    "secret_access_key": "your-secret-key",
})

# Using environment variables (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
s3_storage = StorageFactory.create_s3_storage()
```

### Azure Blob Storage Configuration

```python
from storage import StorageFactory

# Using explicit credentials
blob_storage = StorageFactory.create_blob_storage({
    "account": "your-account-name",
    "account_key": "your-account-key",
})

# Using environment variables (AZURE_STORAGE_ACCOUNT, AZURE_STORAGE_KEY)
blob_storage = StorageFactory.create_blob_storage()
```

## Usage

### Factory Pattern

The easiest way to create a storage instance is to use the `StorageFactory`:

```python
from storage import StorageFactory, StorageType

# Create a storage instance based on the storage type
storage = StorageFactory.create_storage(StorageType.S3, {
    "access_key_id": "your-access-key",
    "secret_access_key": "your-secret-key",
})

# Or using a string
storage = StorageFactory.create_storage("blob", {
    "account": "your-account-name",
    "account_key": "your-account-key",
})
```

### AWS S3 Service Usage

```python
import asyncio
from storage import StorageFactory

async def main():
    # Create an S3 storage instance
    s3_storage = StorageFactory.create_s3_storage({
        "access_key_id": "your-access-key",
        "secret_access_key": "your-secret-key",
    })
    
    # Upload a file
    bucket_name = "your-bucket"
    file_key = "example/test.txt"
    file_content = b"Hello, World!"
    
    upload_result = await s3_storage.upload({
        "storage": bucket_name,
        "key": file_key,
        "file": file_content,
        "content_type": "text/plain",
    })
    
    # Get the file
    file_result = await s3_storage.get_file({
        "storage": bucket_name,
        "key": file_key,
    })
    
    print(f"File content: {file_result['Body'].decode('utf-8')}")
    
    # Generate a signed URL
    url = await s3_storage.get_url({
        "storage": bucket_name,
        "key": file_key,
        "expires": 3600,  # 1 hour
    })
    
    print(f"Signed URL: {url}")
    
    # Delete the file
    delete_result = await s3_storage.delete({
        "storage": bucket_name,
        "key": file_key,
    })

# Run the async function
asyncio.run(main())
```

### Azure Blob Storage Service Usage

```python
import asyncio
from storage import StorageFactory

async def main():
    # Create a Blob storage instance
    blob_storage = StorageFactory.create_blob_storage({
        "account": "your-account-name",
        "account_key": "your-account-key",
    })
    
    # Upload a file
    container_name = "your-container"
    file_key = "example/test.txt"
    file_content = b"Hello, World!"
    
    upload_result = await blob_storage.upload({
        "storage": container_name,
        "key": file_key,
        "file": file_content,
        "content_type": "text/plain",
    })
    
    # Get the file
    file_result = await blob_storage.get_file({
        "storage": container_name,
        "key": file_key,
    })
    
    print(f"File content: {file_result['Body'].decode('utf-8')}")
    
    # Generate a signed URL
    url = await blob_storage.get_url({
        "storage": container_name,
        "key": file_key,
        "expires": 3600,  # 1 hour
    })
    
    print(f"Signed URL: {url}")
    
    # Delete the file
    delete_result = await blob_storage.delete({
        "storage": container_name,
        "key": file_key,
    })

# Run the async function
asyncio.run(main())
```

## Available Operations

The storage services expose a number of common file operations:

- **upload:** Upload files to your storage system
- **get_file_stream:** Retrieve a readable stream of a file
- **get_file:** Download the file as a buffer
- **delete:** Remove a file from storage
- **empty_directory:** Delete all files within a specified directory
- **copy:** Copy a file from one location to another
- **build_url:** Generate a direct URL to access a file
- **get_url:** Generate signed URLs for secure, time-limited access to files

## Development

### Prerequisites

- Python 3.12 or higher
- Poetry (optional, for dependency management)

### Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run tests:
   ```bash
   pytest
   ```

## License

This project is licensed under the Proprietary License. 