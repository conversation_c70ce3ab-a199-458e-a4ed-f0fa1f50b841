"""
S3 storage service implementation.
"""

import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import boto3
from boto3.s3.transfer import TransferConfig
from botocore.exceptions import ClientError
from botocore.response import StreamingBody

from .interfaces import Storage
from .storage_types import (
    Common,
    Copy,
    EmptyDirectory,
    InitS3Client,
    ReadDirectory,
    StorageParams,
    Upload,
    GetUrl,
    UploadFromStreamOptionsByName,
    UploadFromStreamOptionsByPath,
)


class StorageS3(Storage):
    """AWS S3 storage service implementation."""

    def __init__(self):
        """Initialize the S3 storage service."""
        self.logger = logging.getLogger(self.__class__.__name__)
        self._type = "s3"
        self.s3 = None
        self.bucket_prefix = ""
        self.region = "eu-west-1"

    @property
    def type(self) -> str:
        """Get the storage type."""
        return self._type

    def init_s3_client(self, params: InitS3Client) -> Any:
        """Initialize the S3 client."""
        self.s3 = boto3.client(
            "s3",
            region_name=self.region,
            aws_access_key_id=params["access_key_id"],
            aws_secret_access_key=params["secret_access_key"],
        )
        return self.s3

    async def upload(self, params: Upload) -> Any:
        """Upload a file to S3."""
        storage = self.bucket_prefix + params["storage"]
        key = params["key"]
        file = params["file"]
        
        # Extract optional parameters
        acl = params.get("acl")
        content_encoding = params.get("content_encoding")
        content_type = params.get("content_type")
        
        upload_params = {
            "Body": file,
            "Bucket": storage,
            "Key": key,
        }
        
        if acl:
            upload_params["ACL"] = acl
        if content_encoding:
            upload_params["ContentEncoding"] = content_encoding
        if content_type:
            upload_params["ContentType"] = content_type
            
        return self.s3.put_object(**upload_params)

    def stream_to_bytes(self, stream: StreamingBody) -> bytes:
        """Convert a stream to bytes."""
        chunks = []
        for chunk in stream.iter_chunks():
            chunks.append(chunk)
        return b"".join(chunks)

    async def get_file_stream(self, params: Common) -> Any:
        """Get a file stream from S3."""
        storage = self.bucket_prefix + params["storage"]
        key = params["key"]
        
        response = self.s3.get_object(
            Bucket=storage,
            Key=key,
        )
        
        if "Body" not in response:
            self.logger.error(f"getFileStream - without body! {key}")
            
        if response.get("ResponseMetadata", {}).get("HTTPStatusCode") != 200:
            self.logger.error(
                f"getFileStream - return status code: {response.get('ResponseMetadata', {}).get('HTTPStatusCode')}! {key}"
            )
            
        return response["Body"]

    def get_file(self, params: Common) -> bytes:
        """Get a file from S3."""
        storage = self.bucket_prefix + params["storage"]
        key = params["key"]
        
        response = self.s3.get_object(
            Bucket=storage,
            Key=key,
        )
        
        return self.stream_to_bytes(response["Body"])

    async def delete(self, params: Common) -> Any:
        """Delete a file from S3."""
        storage = self.bucket_prefix + params["storage"]
        key = params["key"]
        
        return self.s3.delete_object(
            Bucket=storage,
            Key=key,
        )

    async def read_directory(self, params: ReadDirectory) -> Optional[Dict[str, Any]]:
        """Read a directory from S3."""
        storage = params["storage"]
        if not storage.startswith(self.bucket_prefix):
            storage = self.bucket_prefix + storage
            
        dir_path = params["dir"]
        
        list_params: StorageParams = {
            "bucket": storage,
            "prefix": dir_path,
        }
        
        response = self.s3.list_objects_v2(
            Bucket=list_params["bucket"],
            Prefix=list_params["prefix"],
        )
        
        if not response.get("Contents"):
            return None
            
        return response

    async def empty_directory(self, params: EmptyDirectory) -> Any:
        """Empty a directory in S3."""
        storage = self.bucket_prefix + params["storage"]
        dir_path = params["dir"]
        
        listed_objects = await self.read_directory({"storage": storage, "dir": dir_path})
        if not listed_objects or not listed_objects.get("Contents"):
            return
            
        delete_params = {
            "Bucket": storage,
            "Delete": {"Objects": []},
        }
        
        for obj in listed_objects["Contents"]:
            delete_params["Delete"]["Objects"].append({"Key": obj["Key"]})
            
        delete_response = self.s3.delete_objects(**delete_params)
        
        if listed_objects.get("IsTruncated"):
            return await self.empty_directory(params)
        else:
            return delete_response

    def build_url(self, params: Common) -> str:
        """Build a URL for a file in S3."""
        s3_area = self.region
        storage = self.bucket_prefix + params["storage"]
        key = params["key"]
        
        return f"https://s3-{s3_area}.amazonaws.com/{storage}/{key}"

    async def copy(self, params: Copy) -> Any:
        """Copy a file in S3."""
        storage = self.bucket_prefix + params["storage"]
        key = params["key"]
        source = params["source"]
        
        return self.s3.copy_object(
            CopySource=f"{storage}/{source}",
            Bucket=storage,
            Key=key,
        )

    async def get_url(self, params: GetUrl) -> str:
        """Get a signed URL for a file in S3."""
        storage = params["storage"]
        key = params["key"]
        expires = params.get("expires", 90)
        get_object_command_options = params.get("get_object_command_options", {})
        
        url_params = {
            "Bucket": storage,
            "Key": key,
            **get_object_command_options,
        }
        
        return self.s3.generate_presigned_url(
            "get_object",
            Params=url_params,
            ExpiresIn=expires,
        )

    def upload_from_stream(
        self,
        bucket_name: str,
        message_body: Any,
        options: Union[UploadFromStreamOptionsByName, UploadFromStreamOptionsByPath] = None,
    ) -> Dict[str, Any]:
        """Upload a stream to S3."""
        if options is None:
            options = {"file_name": self._generate_unique_name()}
            
        content_type = options.get("content_type", "text/plain")
        file_type = options.get("file_type", "txt")
        
        file_name = self._generate_unique_name()
        if "file_name" in options:
            file_name = options["file_name"]
            
        key = self._create_file_path(
            file_name,
            file_type,
            options.get("file_path") if "file_path" in options else None,
        )
        
        # Check if bucket exists
        bucket_exists = self._check_bucket_exists(bucket_name)
        if not bucket_exists:
            raise ValueError(f"Bucket {bucket_name} does not exist")
            
        # Upload the file
        self.s3.upload_fileobj(
            message_body,
            bucket_name,
            key,
            ExtraArgs={"ContentType": content_type},
        )
        
        result = {**options}
        result["file_path"] = key
        result["file_name"] = file_name
        result["file_type"] = file_type
        
        return result

    def _create_file_path(self, file_name: str, file_type: str, file_path: Optional[str] = None) -> str:
        """Create a file path for S3."""
        if file_path:
            file_name = os.path.join(file_path, file_name)
        return f"{file_name}.{file_type}"

    def _check_bucket_exists(self, bucket: str) -> bool:
        """Check if a bucket exists."""
        try:
            self.s3.head_bucket(Bucket=bucket)
            return True
        except ClientError as e:
            if e.response["ResponseMetadata"]["HTTPStatusCode"] == 404:
                return False
            raise

    def _generate_unique_name(self) -> str:
        """Generate a unique name for a file."""
        timestamp = int(time.time() * 1000)
        random_suffix = os.urandom(4).hex()
        return f"{timestamp}-{random_suffix}"

    def close(self) -> None:
        """Close the S3 client."""
        if self.s3:
            # No explicit close method in boto3, but we can remove references
            self.s3 = None 