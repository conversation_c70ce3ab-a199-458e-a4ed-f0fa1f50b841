"""
A unified storage library that provides an abstraction over multiple cloud storage providers
"""

__version__ = "0.1.0"

# Export interfaces
from .interfaces import Storage

# Export types
from .storage_types import (
    StorageType,
    Common,
    Copy,
    EmptyDirectory,
    GetUrl,
    InitS3Client,
    ReadDirectory,
    Upload,
    GetBlobServiceClient,
)

# Export services
from .s3_service import StorageS3
from .blob_service import StorageBlob

# Export factory
from .factory import StorageFactory