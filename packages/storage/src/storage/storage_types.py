"""
Type definitions for the storage package.
"""

from enum import Enum
from typing import Any, Dict, List, Optional, TypedDict, Union

from typing_extensions import NotRequired


class StorageType(str, Enum):
    """Storage type enum."""
    S3 = "s3"
    BLOB = "blob"


class Common(TypedDict):
    """Common parameters for storage operations."""
    storage: str
    key: str


class Upload(Common):
    """Parameters for upload operations."""
    file: Any
    acl: NotRequired[str]
    content_encoding: NotRequired[str]
    content_type: NotRequired[str]


class Copy(Common):
    """Parameters for copy operations."""
    source: str


class EmptyDirectory(TypedDict):
    """Parameters for empty directory operations."""
    storage: str
    dir: str


class ReadDirectory(TypedDict):
    """Parameters for read directory operations."""
    storage: str
    dir: str


class GetUrl(Common):
    """Parameters for get URL operations."""
    expires: int
    response_content_disposition: NotRequired[str]
    ignore_cloud_front: NotRequired[bool]
    get_object_command_options: NotRequired[Dict[str, Any]]


class InitS3Client(TypedDict):
    """Parameters for initializing S3 client."""
    access_key_id: str
    secret_access_key: str


class GetBlobServiceClient(TypedDict):
    """Parameters for getting blob service client."""
    account: str
    account_key: str


class StorageParams(TypedDict):
    """Parameters for storage operations."""
    bucket: str
    prefix: str


class UploadFromStreamOptions(TypedDict):
    """Options for uploading from stream."""
    content_type: NotRequired[str]
    file_type: NotRequired[str]


class UploadFromStreamOptionsByName(UploadFromStreamOptions):
    """Options for uploading from stream by name."""
    file_name: str
    folder_path: NotRequired[str]


class UploadFromStreamOptionsByPath(UploadFromStreamOptions):
    """Options for uploading from stream by path."""
    file_path: str 