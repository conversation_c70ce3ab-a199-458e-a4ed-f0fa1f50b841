"""
Example usage of the storage library.
"""

import asyncio
import os
from typing import Dict, Any

from .factory import StorageFactory
from .storage_types import StorageType


async def s3_example() -> None:
    """Example of using the S3 storage service."""
    # Create an S3 storage instance
    s3_storage = StorageFactory.create_s3_storage({
        "access_key_id": os.environ.get("AWS_ACCESS_KEY_ID", "340b8633af4bae9294212f637128b62da4868f8d20a4a256f09418dc722f99fd1af48778"),
        "secret_access_key": os.environ.get("AWS_SECRET_ACCESS_KEY", "****************************************************************************************2ad3c53ffa9f2bd33c57d29b"),
    })
    
  

    
    # Generate a signed URL s3://cym-kafka-msg/0199f552-5a0a-4900-b539-5bf5c53ce2dd.json
    url = await s3_storage.get_url({
        "storage": "cym-kafka-msg",
        "key": "0199f552-5a0a-4900-b539-5bf5c53ce2dd.json",
        "expires": 3600,  # 1 hour
    })
    
    print(f"Signed URL: {url}")



    """Example of using the Azure Blob Storage service."""
    # Create a Blob storage instance
    blob_storage = StorageFactory.create_blob_storage({
        "account": os.environ.get("AZURE_STORAGE_ACCOUNT", "your-account"),
        "account_key": os.environ.get("AZURE_STORAGE_KEY", "your-account-key"),
    })
    
    # Upload a file
    container_name = "your-container"
    file_key = "example/test.txt"
    file_content = b"Hello, World!"
    
    upload_result = await blob_storage.upload({
        "storage": container_name,
        "key": file_key,
        "file": file_content,
        "content_type": "text/plain",
    })
    
    print(f"Upload result: {upload_result}")
    
    # Get the file
    file_result = await blob_storage.get_file({
        "storage": container_name,
        "key": file_key,
    })
    
    print(f"File content: {file_result['Body'].decode('utf-8')}")
    
    # Generate a signed URL
    url = await blob_storage.get_url({
        "storage": container_name,
        "key": file_key,
        "expires": 3600,  # 1 hour
    })
    
    print(f"Signed URL: {url}")
    
    # Delete the file
    delete_result = await blob_storage.delete({
        "storage": container_name,
        "key": file_key,
    })
    
    print(f"Delete result: {delete_result}")


# async def factory_example() -> None:
#     """Example of using the storage factory."""
#     # Create a storage instance based on the storage type
#     storage_type = os.environ.get("STORAGE_TYPE", "s3")
    
#     if storage_type == "s3":
#         config = {
#             "access_key_id": os.environ.get("AWS_ACCESS_KEY_ID", "your-access-key"),
#             "secret_access_key": os.environ.get("AWS_SECRET_ACCESS_KEY", "your-secret-key"),
#         }
   
    
#     storage = StorageFactory.create_storage(storage_type, config)
    
#     # Use the storage instance
#     container_name = "your-container" if storage_type == "blob" else "your-bucket"
#     file_key = "example/test.txt"
#     file_content = b"Hello, World!"
    
#     upload_result = await storage.upload({
#         "storage": container_name,
#         "key": file_key,
#         "file": file_content,
#         "content_type": "text/plain",
#     })
    
#     print(f"Upload result: {upload_result}")


def main() -> None:
    """Run the examples."""
    # Choose which example to run
    example = os.environ.get("EXAMPLE", "factory")
    
    if example == "s3":
        asyncio.run(s3_example())
    # elif example == "blob":
    #     asyncio.run(blob_example())
    # else:  # factory
    #     asyncio.run(factory_example())


if __name__ == "__main__":
    main()
