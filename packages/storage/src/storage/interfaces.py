"""
Interfaces for the storage package.
"""

from abc import ABC, abstractmethod
from typing import Any, BinaryIO, Dict, List, Optional, Union

from .storage_types import Common, Copy, EmptyDirectory, GetUrl, Upload


class Storage(ABC):
    """Abstract base class for storage providers."""

    @property
    @abstractmethod
    def type(self) -> str:
        """Get the storage type."""
        pass

    @abstractmethod
    async def upload(self, params: Upload) -> Any:
        """Upload a file to storage."""
        pass

    def stream_to_bytes(self, stream: Any) -> bytes:
        """Convert a stream to bytes."""
        return b""

    @abstractmethod
    async def get_file_stream(self, params: Common) -> Any:
        """Get a file stream from storage."""
        pass

    @abstractmethod
    def get_file(self, params: Common) -> bytes:
        """Get a file from storage."""
        pass

    @abstractmethod
    async def delete(self, params: Common) -> Any:
        """Delete a file from storage."""
        pass

    @abstractmethod
    async def empty_directory(self, params: EmptyDirectory) -> Any:
        """Empty a directory in storage."""
        pass

    @abstractmethod
    def build_url(self, params: Common) -> str:
        """Build a URL for a file in storage."""
        pass

    @abstractmethod
    async def copy(self, params: Copy) -> Any:
        """Copy a file in storage."""
        pass

    @abstractmethod
    async def get_url(self, params: GetUrl) -> str:
        """Get a signed URL for a file in storage."""
        pass 