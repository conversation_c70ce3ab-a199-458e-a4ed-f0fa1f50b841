"""
Factory for creating storage instances.
"""

import os
from typing import Dict, Optional, Type, Union

from .interfaces import Storage
from .s3_service import StorageS3
from .blob_service import StorageBlob
from .storage_types import StorageType, InitS3Client, GetBlobServiceClient
from .decrypt import Cryptr


class StorageFactory:
    """Factory for creating storage instances."""

    @staticmethod
    def create_storage(
        storage_type: Union[str, StorageType],
        config: Optional[Dict] = None,
    ) -> Storage:
        """
        Create a storage instance based on the storage type.

        Args:
            storage_type: The type of storage to create.
            config: Configuration for the storage.

        Returns:
            A storage instance.

        Raises:
            ValueError: If the storage type is not supported.
        """
        if isinstance(storage_type, str):
            storage_type = StorageType(storage_type)

        if storage_type == StorageType.S3:
            return StorageFactory.create_s3_storage(config)
        elif storage_type == StorageType.BLOB:
            return StorageFactory.create_blob_storage(config)
        else:
            raise ValueError(f"Unsupported storage type: {storage_type}")

    @staticmethod
    def create_s3_storage(config: Optional[Dict] = None) -> StorageS3:
        """
        Create an S3 storage instance.

        Args:
            config: Configuration for the S3 storage.

        Returns:
            An S3 storage instance.
        """
        storage = StorageS3()

        if config:
            access_key_id = config.get("access_key_id")
            secret_access_key = config.get("secret_access_key")
            # Initialize the S3 client with the provided config
            if access_key_id and secret_access_key:
                if not config.get("is_decrypted"):
                    cryptr = Cryptr()
                    # Decrypt the access key and secret access key
                    access_key_id = cryptr.decrypt(access_key_id)
                    secret_access_key = cryptr.decrypt(secret_access_key)

                storage.init_s3_client(
                    {
                        "access_key_id": access_key_id,
                        "secret_access_key": secret_access_key,
                    }
                )
            else:
                raise ValueError("Access key ID and secret access key are required")
        elif os.environ.get("AWS_ACCESS_KEY_ID") and os.environ.get(
            "AWS_SECRET_ACCESS_KEY"
        ):
            # Initialize the S3 client with environment variables
            cryptr = Cryptr()
            # Decrypt the access key and secret access key
            access_key_id = cryptr.decrypt(os.environ["CUST_AWS_ACCESS_KEY_ID"])
            secret_access_key = cryptr.decrypt(os.environ["CUST_AWS_SECRET_ACCESS_KEY"])

            storage.init_s3_client(
                {
                    "access_key_id": access_key_id,
                    "secret_access_key": secret_access_key,
                }
            )

        return storage

    @staticmethod
    def create_blob_storage(config: Optional[Dict] = None) -> StorageBlob:
        """
        Create a Blob storage instance.

        Args:
            config: Configuration for the Blob storage.

        Returns:
            A Blob storage instance.
        """
        storage = StorageBlob()

        if config:
            # Initialize the Blob service client with the provided config
            storage.get_blob_service_client(config)
        elif os.environ.get("AZURE_STORAGE_ACCOUNT") and os.environ.get(
            "AZURE_STORAGE_KEY"
        ):
            # Initialize the Blob service client with environment variables
            storage.get_blob_service_client(
                {
                    "account": os.environ["AZURE_STORAGE_ACCOUNT"],
                    "account_key": os.environ["AZURE_STORAGE_KEY"],
                }
            )

        return storage
