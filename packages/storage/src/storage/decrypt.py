import hashlib
import os
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

class Cryptr:
    def __init__(self, secret: str ='a1976ce0927b185314d85b2fc48cf94b'):
        if not secret or not isinstance(secret, str):
            raise ValueError('Cryptr: secret must be a non-0-length string')

        # In Python, hashlib.sha256().update() expects bytes.
        # The digest() method returns bytes.
        self._key = hashlib.sha256(secret.encode('utf-8')).digest()
        self._algorithm = algorithms.AES
        self._mode = modes.CTR # AES-CTR

    def encrypt(self, value: str) -> str:
        if value is None:
            raise ValueError('value must not be null or undefined')

        value_bytes = str(value).encode('utf-8')
        iv = os.urandom(16) # AES block size is 128 bits (16 bytes)

        cipher = Cipher(self._algorithm(self._key), self._mode(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        encrypted_bytes = encryptor.update(value_bytes) + encryptor.finalize()

        # Return IV (hex) + encrypted_data (hex)
        return iv.hex() + encrypted_bytes.hex()

    def decrypt(self, value: str) -> str:
        if value is None:
            raise ValueError('value must not be null or undefined')

        string_value = str(value)
        
        # Standard IV is 32 hex characters (16 bytes)
        iv_hex = string_value[:32]
        encrypted_hex = string_value[32:]
        legacy_value = False
        
        try:
            iv = bytes.fromhex(iv_hex)
            if len(iv) != 16: # Check IV length for AES-CTR (must be 16 bytes)
                 raise ValueError("Invalid IV length")
            cipher = Cipher(self._algorithm(self._key), self._mode(iv), backend=default_backend())
        except ValueError as e:
            # This mimics the JavaScript logic where "Invalid IV length" specifically means a legacy value.
            # Other ValueErrors (e.g. non-hex string for iv_hex) would also fall here.
            # The JS code checked the *message* of the exception.
            # In Python, it's generally better to catch specific exception types,
            # but to match the JS logic closely, we'll check if the IV length is the issue.
            # A more robust way might be to try decrypting with 16-byte IV first, then 8-byte if that specific error occurs.
            # For now, we'll assume any ValueError during IV processing for a 32-char hex IV implies a legacy value.
            # This is a simplification; the original JS specifically checks for "Invalid IV length".
            # A more direct Python equivalent for the JS "Invalid IV length" check from crypto.createDecipheriv
            # is tricky as `cryptography` library might raise different errors or messages.
            # We'll try to parse the 16-byte IV first and if that fails due to length (e.g. if it was shorter),
            # then try the legacy.
            
            # Attempt to parse as legacy (first 16 hex chars for IV = 8 bytes)
            # The JS legacy IV was `stringValue.slice(0, 16)` which is 16 hex characters = 8 bytes.
            # AES CTR IVs should typically be 16 bytes. The original JS `crypto.randomBytes(16)` for IV implies a 16-byte IV.
            # The legacy handling in JS `const legacyIv = stringValue.slice(0, 16);` (16 hex chars)
            # and then `decipher = crypto.createDecipheriv(algorithm, key, legacyIv);`
            # This suggests the legacy IV might have been 8 bytes if `legacyIv` was passed directly.
            # However, `Buffer.from(stringValue.slice(0, 32), 'hex')` in the primary path implies the modern IV is 16 bytes.
            # Let's stick to the provided JS logic:
            # Modern IV: 32 hex chars (16 bytes)
            # Legacy IV: 16 hex chars (8 bytes)
            # CTR mode in `cryptography` requires a 16-byte IV for AES.
            # The original JS code `crypto.createDecipheriv(algorithm, key, legacyIv)` where `legacyIv` is `stringValue.slice(0,16)`
            # suggests that Node.js's crypto module might have been more lenient with IV sizes or handled padding for CTR mode differently for older versions.
            # The `cryptography` library is stricter. An 8-byte IV is not standard for AES-CTR (which uses a 16-byte block size).
            #
            # Given the constraint, the most direct "conversion" is difficult if the legacy path truly used an 8-byte IV with aes-256-ctr.
            # For now, I will assume the legacy path *also* expects a 16-byte IV that was somehow truncated or misinterpreted,
            # or the original JS `crypto.createDecipheriv` had a different behavior for `aes-256-ctr` with shorter IVs.
            #
            # Re-evaluating the JS:
            # `const iv = crypto.randomBytes(16);` -> This is always 16 bytes.
            # `return iv.toString('hex') + encrypted;` -> So `stringValue.slice(0, 32)` is correct for 16-byte IV.
            # The `catch (exception)` for `Invalid IV length` in JS for `crypto.createDecipheriv(algorithm, key, iv)`
            # where `iv` is `Buffer.from(stringValue.slice(0, 32), 'hex')` is confusing.
            # If `stringValue.slice(0, 32)` is not 32 hex chars, `Buffer.from` might complain.
            # If it *is* 32 hex chars (16 bytes), `createDecipheriv` shouldn't say "Invalid IV length" for AES-256-CTR.
            #
            # Let's assume "legacyValue" means the *entire* input `value` was shorter than expected (e.g. less than 32 hex chars for IV).
            # Or that the "legacy" part refers to a *different algorithm or mode* that used a shorter IV.
            # But the code uses `algorithm` (aes-256-ctr) for both.
            #
            # The most plausible interpretation of the JS legacy path:
            # `const legacyIv = stringValue.slice(0, 16);` // This is 16 hex characters -> 8 bytes.
            # `decipher = crypto.createDecipheriv(algorithm, key, legacyIv);`
            # This implies Node's `crypto` allowed an 8-byte IV for `aes-256-ctr` in some "legacy" scenario.
            # Python's `cryptography` library's `modes.CTR` strictly requires a 16-byte (128-bit) nonce/IV for AES.
            #
            # To make this runnable in Python and somewhat analogous,
            # we can't directly use an 8-byte IV with `modes.CTR` for AES.
            # The JS behavior is specific to Node's crypto module's leniency or internal handling.
            #
            # For now, I will simplify and assume the "legacy" path is not hit or involves a misunderstanding
            # of how `aes-256-ctr` would work with a non-standard IV size in a way that's portable.
            # The primary path with a 16-byte IV is standard.
            #
            # If the intention of "legacyValue" was that the input `value` itself was encrypted with an older scheme
            # that *produced* a 16-char hex IV (8 bytes), then true decryption with `aes-256-ctr` and that IV in Python's
            # `cryptography` is not directly possible if it demands a 16-byte IV.
            #
            # Given the direct conversion request, I'll keep the structure but note this incompatibility.
            # The Python code will likely fail if it tries to use an 8-byte IV with `modes.CTR`.
            # I will raise a NotImplementedError for the legacy path, as direct translation isn't feasible with `cryptography`'s strictness.

            # Simplification: If the primary IV (16 bytes) parsing/usage fails, assume it's an unsupported legacy format.
            # The JS code's error message "Invalid IV length" is key.
            # If `bytes.fromhex(iv_hex)` fails because `iv_hex` is not 32 chars, it's one type of error.
            # If `len(iv)` is not 16 after successful `fromhex`, that's another.
            
            # The JS error occurs in `crypto.createDecipheriv(algorithm, key, iv)`.
            # The `iv` passed there is `Buffer.from(stringValue.slice(0, 32), 'hex')`.
            # So the error "Invalid IV length" happens if this buffer is not 16 bytes.
            # This would happen if `stringValue.slice(0, 32)` is not actually 32 hex characters.
            # e.g., if `value` is too short.

            if len(iv_hex) != 32 : # Check if the hex string for IV is not 32 characters
                legacy_value = True
            else: # It was 32 hex chars, but `bytes.fromhex` or `Cipher` creation failed for other reasons
                  # or len(iv) was not 16 (e.g. bad hex)
                raise e # Re-throw original error if not an IV length issue for the *expected* 32-char hex IV

        if not legacy_value:
            try:
                encrypted_bytes = bytes.fromhex(encrypted_hex)
                decryptor = cipher.decryptor()
                decrypted_bytes = decryptor.update(encrypted_bytes) + decryptor.finalize()
                return decrypted_bytes.decode('utf-8')
            except Exception as e_decrypt:
                # It's possible the key is wrong, or data corrupted, or it's a legacy value that passed the initial IV check by chance.
                # The original JS code doesn't have a try-catch around the actual decryption update/final for the non-legacy path.
                # To be more robust or align with the idea that "legacyValue" is a fallback:
                # Try standard decryption. If it fails (for any reason), then try legacy.
                # This is speculative. The JS code sets `legacyValue` only on `createDecipheriv` failure.

                # For now, let's assume if `createCipheriv` succeeded, `update/final` should ideally work.
                # If it fails here, it's a decryption error, not necessarily a cue for legacy.
                raise e_decrypt


        # Legacy path:
        # In JS: const legacyIv = stringValue.slice(0, 16); // 16 hex chars -> 8 bytes
        #         const legacyEncrypted = stringValue.slice(16);
        #         decipher = crypto.createDecipheriv(algorithm, key, legacyIv);
        # This is problematic for Python's `cryptography` with AES-CTR, which expects a 16-byte IV.
        # An 8-byte IV cannot be directly used.
        # We will raise an error here indicating this part is not directly portable.
        
        # Try to read legacy IV (8 bytes = 16 hex chars) and the rest as encrypted data
        legacy_iv_hex = string_value[:16]
        legacy_encrypted_hex = string_value[16:]

        if len(legacy_iv_hex) != 16:
            raise ValueError("Legacy value is too short to contain a valid 8-byte IV (16 hex chars).")

        try:
            legacy_iv_bytes = bytes.fromhex(legacy_iv_hex) # Should be 8 bytes
        except ValueError:
            raise ValueError("Legacy IV is not a valid hex string.")

        if len(legacy_iv_bytes) != 8:
             # This case should ideally be caught by len(legacy_iv_hex) != 16,
             # unless fromhex behaves unexpectedly with odd-length strings (it raises error).
            raise ValueError(f"Legacy IV has unexpected length: {len(legacy_iv_bytes)} bytes. Expected 8 bytes.")

        # As stated, aes-256-ctr with an 8-byte IV is non-standard for `cryptography` package.
        # Node.js's `crypto` might have handled this by padding or other internal mechanisms.
        # To proceed, one would need to know exactly how Node.js's `crypto.createDecipheriv`
        # used an 8-byte IV with AES-CTR.
        # If it's a different algorithm/mode used in legacy, that's not specified.
        # If it zero-padded the IV to 16 bytes, we could try that:
        # padded_legacy_iv = legacy_iv_bytes + b'\x00' * (16 - len(legacy_iv_bytes))
        # However, this is an assumption.

        # For the purpose of this conversion, I will assume the legacy path means
        # the original value was encrypted using some method that *is* compatible
        # with an 8-byte IV, or that the JS was tolerant.
        # Since Python's `cryptography` is not, this path is problematic.

        # Let's try to simulate what Node might have done if it padded the IV:
        # Option 1: Zero-padding the 8-byte legacy IV to 16 bytes for CTR mode.
        padded_legacy_iv = legacy_iv_bytes.ljust(16, b'\x00')

        try:
            legacy_cipher = Cipher(self._algorithm(self._key), self._mode(padded_legacy_iv), backend=default_backend())
            legacy_encrypted_bytes = bytes.fromhex(legacy_encrypted_hex)
            decryptor = legacy_cipher.decryptor()
            decrypted_bytes = decryptor.update(legacy_encrypted_bytes) + decryptor.finalize()
            return decrypted_bytes.decode('utf-8')
        except Exception as e:
            # If padded IV decryption fails, the legacy format is truly incompatible or misunderstood.
            raise ValueError(
                "Failed to decrypt legacy value. "
                "The legacy IV handling (8-byte IV for AES-CTR) in the original JS "
                "is not directly compatible with Python's cryptography library's strict 16-byte IV requirement for AES-CTR. "
                "Tried padding the 8-byte IV to 16 bytes with zeros."
            ) from e
