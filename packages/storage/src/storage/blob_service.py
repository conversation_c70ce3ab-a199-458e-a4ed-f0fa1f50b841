"""
Azure Blob Storage service implementation.
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, BinaryIO

from azure.storage.blob import (
    BlobServiceClient,
    BlobSasPermissions,
    generate_blob_sas,
    ContentSettings,
)

from .interfaces import Storage
from .storage_types import Common, Copy, EmptyDirectory, GetBlobServiceClient, GetUrl, Upload


class StorageBlob(Storage):
    """Azure Blob Storage service implementation."""

    def __init__(self):
        """Initialize the Azure Blob Storage service."""
        self.logger = logging.getLogger(self.__class__.__name__)
        self._type = "blob"
        self.account = None
        self.blob_service_client = None
        self.shared_key_credential = None

    @property
    def type(self) -> str:
        """Get the storage type."""
        return self._type

    async def upload(self, params: Upload) -> Any:
        """Upload a file to Azure Blob Storage."""
        storage = params["storage"]
        key = params["key"]
        file = params["file"]
        
        container_client = self.blob_service_client.get_container_client(storage)
        blob_client = container_client.get_blob_client(key)
        
        # Set content settings if provided
        content_settings = None
        if params.get("content_type") or params.get("content_encoding"):
            content_settings = ContentSettings(
                content_type=params.get("content_type"),
                content_encoding=params.get("content_encoding"),
            )
        
        # Upload the file
        return blob_client.upload_blob(
            file,
            overwrite=True,
            content_settings=content_settings,
        )

    async def get_file_stream(self, params: Common) -> Any:
        """Get a file stream from Azure Blob Storage."""
        storage = params["storage"]
        key = params["key"]
        
        container_client = self.blob_service_client.get_container_client(storage)
        blob_client = container_client.get_blob_client(key)
        
        download_stream = blob_client.download_blob()
        return download_stream.readall()

    def get_file(self, params: Common) -> bytes:
        """Get a file from Azure Blob Storage."""
        storage = params["storage"]
        key = params["key"]
        
        container_client = self.blob_service_client.get_container_client(storage)
        blob_client = container_client.get_blob_client(key)
        
        return blob_client.download_blob().readall()

    async def delete(self, params: Common) -> Any:
        """Delete a file from Azure Blob Storage."""
        storage = params["storage"]
        key = params["key"]
        
        container_client = self.blob_service_client.get_container_client(storage)
        blob_client = container_client.get_blob_client(key)
        
        return blob_client.delete_blob()

    async def empty_directory(self, params: EmptyDirectory) -> None:
        """Empty a directory in Azure Blob Storage."""
        storage = params["storage"]
        dir_path = params["dir"]
        
        container_client = self.blob_service_client.get_container_client(storage)
        
        # List all blobs with the specified prefix
        blob_list = container_client.list_blobs(name_starts_with=dir_path)
        
        # Delete each blob
        for blob in blob_list:
            blob_client = container_client.get_blob_client(blob.name)
            await blob_client.delete_blob()

    def get_blob_service_client(self, params: GetBlobServiceClient) -> BlobServiceClient:
        """Get a BlobServiceClient."""
        account = params["account"]
        account_key = params["account_key"]
        
        # Store the account name for later use
        self.account = account
        
        # Create the blob service client with connection string
        connection_string = f"DefaultEndpointsProtocol=https;AccountName={account};AccountKey={account_key};EndpointSuffix=core.windows.net"
        self.blob_service_client = BlobServiceClient.from_connection_string(connection_string)
        
        return self.blob_service_client

    def build_url(self, params: Common) -> str:
        """Build a URL for a file in Azure Blob Storage."""
        account = self.account
        storage = params["storage"]
        key = params["key"]
        
        return f"https://{account}.blob.core.windows.net/{storage}/{key}"

    async def copy(self, params: Copy) -> Any:
        """Copy a file in Azure Blob Storage."""
        storage = params["storage"]
        key = params["key"]
        source = params["source"]
        
        container_client = self.blob_service_client.get_container_client(storage)
        source_blob_client = container_client.get_blob_client(source)
        destination_blob_client = container_client.get_blob_client(key)
        
        # Start the copy operation
        copy_operation = destination_blob_client.start_copy_from_url(source_blob_client.url)
        
        return copy_operation

    async def get_url(self, params: GetUrl) -> str:
        """Get a signed URL for a file in Azure Blob Storage."""
        storage = params["storage"]
        key = params["key"]
        expires = params.get("expires", 90)
        
        # Calculate expiry time
        expiry = datetime.utcnow() + timedelta(seconds=expires)
        
        # Get the account key from the connection string
        account_key = self.blob_service_client.credential.account_key
        
        # Generate SAS token
        sas_token = generate_blob_sas(
            account_name=self.account,
            container_name=storage,
            blob_name=key,
            account_key=account_key,
            permission=BlobSasPermissions(read=True),
            expiry=expiry,
        )
        
        # Build the full URL
        url = f"https://{self.account}.blob.core.windows.net/{storage}/{key}?{sas_token}"
        
        return url

    def close(self) -> None:
        """Close the Azure Blob Storage client."""
        # No explicit close method needed for Azure Blob Storage client
        pass 