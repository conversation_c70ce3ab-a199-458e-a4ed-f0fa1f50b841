"""
Kafka Consumer singleton for Cymulate
"""

import json
import os
import threading
import time
import asyncio
from typing import Any, Callable, Dict, List, Optional, Union, Awaitable
import elasticapm
from elasticapm import get_client, capture_span
from elasticapm.utils.disttracing import TraceParent
from elasticapm.conf import constants
from elasticapm import Client as ElasticAPMClient
from confluent_kafka import KafkaError, Message
from confluent_kafka import Consumer as ConfluentConsumer

from logger import logger

from secretmanager.models import KafkaConfig


class KafkaConsumer:
    """
    Singleton Kafka Consumer for Cymulate.
    
    This class provides a thread-safe singleton implementation of a Kafka consumer.
    It handles connection management, message deserialization, and error handling.
    """
    
    _instance = None
    _lock = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            if cls._lock is None:
                import threading
                cls._lock = threading.Lock()
            
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(KafkaConsumer, cls).__new__(cls)
                    cls._instance._initialized = False
        
        return cls._instance
    
    def __init__(self, config: Optional[KafkaConfig] = None, group_id: Optional[str] = None):
        """
        Initialize the Kafka consumer with the provided configuration.
        
        Args:
            config: KafkaConfig object containing broker and authentication details
            group_id: Optional consumer group ID
        """
        if self._initialized:
            return
            
        self._initialized = True
        self._consumer = None
        self._config = config
        self._connected = False
        self._running = False
        self._consumer_thread = None
        self._message_handlers = {}
        self._group_id = group_id 
        
        if config:
            self.connect(config)
    
    def connect(self, config: KafkaConfig, group_id: Optional[str] = None) -> None:
        """
        Connect to Kafka using the provided configuration.
        
        Args:
            config: KafkaConfig object containing broker and authentication details
            group_id: Optional consumer group ID
        """
        if self._is_already_connected():
            return
            
        self._validate_config(config)
        self._update_config(config, group_id)
        
        consumer_config = self._build_consumer_config(config)
        self._create_consumer(consumer_config)
    
    def _is_already_connected(self) -> bool:
        """Check if consumer is already connected."""
        if self._connected and self._consumer:
            logger.info("Already connected to Kafka")
            return True
        return False
    
    def _validate_config(self, config: KafkaConfig) -> None:
        """Validate the provided configuration."""
        if not config.broker:
            raise ValueError("Kafka broker addresses are required")
    
    def _update_config(self, config: KafkaConfig, group_id: Optional[str]) -> None:
        """Update internal configuration."""
        self._config = config
        if group_id:
            self._group_id = group_id
    
    def _build_consumer_config(self, config: KafkaConfig) -> Dict[str, Any]:
        """Build the consumer configuration dictionary."""
        consumer_config = self._get_base_consumer_config(config)
        
        if config.sasl:
            consumer_config.update(self._get_sasl_config(config))
        
        return consumer_config
    
    def _get_base_consumer_config(self, config: KafkaConfig) -> Dict[str, Any]:
        """Get base consumer configuration."""
        return {
            'bootstrap.servers': ','.join(config.broker),
            'group.id': self._group_id,
            'enable.auto.commit': True,
            'retry.backoff.ms': 100,
            'max.in.flight.requests.per.connection': 1,
            'allow.auto.create.topics': True,
            'auto.offset.reset': 'latest',
            'session.timeout.ms': 120000,
            'heartbeat.interval.ms': 30000,
            'group.instance.id': os.environ.get('HOSTNAME', 'localhost'),
        }
    
    def _get_sasl_config(self, config: KafkaConfig) -> Dict[str, str]:
        """Get SASL configuration."""
        return {
            'security.protocol': 'SASL_SSL',
            'sasl.mechanism': 'SCRAM-SHA-512',
            'sasl.username': config.sasl.get('username', ''),
            'sasl.password': config.sasl.get('password', ''),
        }
    
    def _create_consumer(self, consumer_config: Dict[str, Any]) -> None:
        """Create the Kafka consumer instance."""
        try:
            self._consumer = ConfluentConsumer(consumer_config)
            self._connected = True
            logger.info(f"Connected to Kafka brokers: {self._config.broker}")
        except Exception as e:
            logger.error(f"Failed to connect to Kafka: {str(e)}")
            raise
    
    def subscribe(self, topics: Union[str, List[str]], 
                 handler: Union[Callable[[Dict[str, Any], Message], None], 
                              Callable[[Dict[str, Any], Message], Awaitable[None]]]) -> None:
        """
        Subscribe to one or more topics and register a message handler.
        
        Args:
            topics: Topic or list of topics to subscribe to
            handler: Callback function to handle messages. Can be either synchronous or asynchronous.
        """
        if not self._consumer or not self._connected:
            logger.error("Consumer not connected to Kafka")
            return
            
        topics_list = self._normalize_topics(topics)
        self._register_handlers(topics_list, handler)
        
        logger.info(f"Subscribed to topics: {topics_list}")
    
    def _normalize_topics(self, topics: Union[str, List[str]]) -> List[str]:
        """Normalize topics to a list format."""
        return [topics] if isinstance(topics, str) else topics
    
    def _register_handlers(self, topics: List[str], handler: Callable) -> None:
        """Register message handlers for topics."""
        try:            
            for topic in topics:
                self._message_handlers[topic] = handler
        except Exception as e:
            logger.error(f"Failed to subscribe to topics: {str(e)}")
            raise

    def on_assign(self, consumer, partitions):
        """Handle partition assignment."""
        assignment = self._build_assignment_dict(partitions)
        logger.info(f'on_assign {json.dumps({
            "groupId": str(self._group_id),
            "memberAssignment": assignment,
        }, separators=(",", ":"))}')

    def on_revoke(self, consumer, partitions):
        """Handle partition revocation."""
        assignment = self._build_assignment_dict(partitions)
        logger.info(f'on_revoke {json.dumps({
            "groupId": str(self._group_id), 
            "memberAssignment": assignment,
        }, separators=(",", ":"))}')

    def on_lost(self, consumer, partitions):
        """Handle partition loss."""
        assignment = self._build_assignment_dict(partitions)
        logger.info(f'on_lost {json.dumps({
            "groupId": str(self._group_id),
            "memberAssignment": assignment,
        }, separators=(",", ":"))}')
    
    def _build_assignment_dict(self, partitions) -> Dict[str, List[int]]:
        """Build assignment dictionary from partitions."""
        assignment = {}
        for tp in partitions:
            assignment.setdefault(tp.topic, []).append(tp.partition)
        return assignment
    
    def start(self) -> None:
        """Start consuming messages in a background thread."""
        if not self._can_start():
            return
        
        self._setup_consumer_subscription()
        self._start_consumer_thread()
        
        logger.info("Kafka consumer started")
    
    def _can_start(self) -> bool:
        """Check if consumer can be started."""
        if not self._consumer or not self._connected:
            logger.error("Consumer not connected to Kafka")
            return False
            
        if self._running:
            logger.info("Consumer is already running")
            return False
        
        return True
    
    def _setup_consumer_subscription(self) -> None:
        """Set up consumer subscription with callbacks."""
        self._consumer.subscribe(
            list(self._message_handlers.keys()), 
            on_assign=self.on_assign, 
            on_revoke=self.on_revoke, 
            on_lost=self.on_lost
        )
    
    def _start_consumer_thread(self) -> None:
        """Start the consumer thread."""
        self._running = True
        self._consumer_thread = threading.Thread(target=self._consume_messages)
        self._consumer_thread.daemon = True
        self._consumer_thread.start()
    
    def stop(self) -> None:
        """Stop consuming messages and close the consumer."""
        if not self._running:
            logger.info("Consumer is not running")
            return
            
        self._stop_consumer_thread()
        self._close_consumer()
        
        logger.info("Kafka consumer stopped")
    
    def _stop_consumer_thread(self) -> None:
        """Stop the consumer thread."""
        self._running = False
        
        if self._consumer_thread:
            self._consumer_thread.join(timeout=5.0)
            self._consumer_thread = None
    
    def _close_consumer(self) -> None:
        """Close the consumer connection."""
        if self._consumer:
            self._consumer.close()
            self._consumer = None
            self._connected = False
    
    def _consume_messages(self) -> None:
        """Internal method to consume messages in a loop."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            while self._running:
                self._process_single_message(loop)
        finally:
            loop.close()
    
    def _process_single_message(self, loop) -> None:
        """Process a single message from Kafka."""
        try:
            msg = self._consumer.poll(1.0)
            
            if msg is None:
                return
                
            if msg.error():
                self._handle_message_error(msg)
                return
                
            self._handle_valid_message(msg, loop)
                    
        except Exception as e:
            logger.error(f"Error in consumer loop: {str(e)}")
            time.sleep(1)  # Avoid tight loop in case of persistent errors
    
    def _handle_message_error(self, msg: Message) -> None:
        """Handle message errors."""
        if msg.error().code() == KafkaError._PARTITION_EOF:
            logger.debug(f"Reached end of partition {msg.partition()} for topic {msg.topic()} at offset {msg.offset()}")
        else:
            logger.error(f"Error consuming message: {msg.error()}")
    
    def _handle_valid_message(self, msg: Message, loop) -> None:
        """Handle a valid message."""
        handler = self._message_handlers.get(msg.topic())
        
        if not handler:
            logger.warning(f"No handler registered for topic {msg.topic()}")
            return
            
        try:
            data = self._deserialize_message(msg)
            self._process_message_with_apm(handler, data, msg, loop)
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
    
    def _deserialize_message(self, msg: Message) -> Dict[str, Any]:
        """Deserialize message value."""
        value = msg.value()
        if not value:
            return {}
            
        try:
            return json.loads(value.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            return {"raw": value.decode('utf-8', errors='replace')}
    
    def _process_message_with_apm(self, handler, data, msg, loop):
        """Process a message with proper APM instrumentation."""
        trace_parent = self._extract_trace_parent(msg)
        client = get_client()
        
        if client:
            self._process_with_apm_transaction(handler, data, msg, loop, client, trace_parent)
        else:
            self._call_handler(handler, data, msg, loop)
    
    def _extract_trace_parent(self, msg: Message) -> Optional[TraceParent]:
        """Extract trace parent from message headers."""
        if not msg.headers():
            return None
            
        for header_key, header_value in msg.headers():
            if header_key == 'traceparent':
                try:
                    return TraceParent.from_string(header_value.decode('utf-8'))
                except Exception:
                    pass  # Invalid trace parent, ignore
        
        return None
    
    def _process_with_apm_transaction(self, handler, data, msg, loop, client, trace_parent):
        """Process message within an APM transaction."""
        transaction = client.begin_transaction(
            transaction_type='messaging',
            trace_parent=trace_parent
        )
        
        try:
            self._setup_apm_transaction(msg, transaction)
            self._call_handler(handler, data, msg, loop)
            elasticapm.set_transaction_result(constants.OUTCOME.SUCCESS)
        except Exception as e:
            elasticapm.set_transaction_result(constants.OUTCOME.FAILURE)
            raise
        finally:
            client.end_transaction()
    
    def _setup_apm_transaction(self, msg: Message, transaction) -> None:
        """Set up APM transaction context."""
        elasticapm.set_transaction_name(f"Kafka RECEIVE from {msg.topic()}")
        transaction.context = self._build_transaction_context(msg)
    
    def _build_transaction_context(self, msg: Message) -> Dict[str, Any]:
        """Build transaction context for APM."""
        base_context = {
            "message": {"queue": {"name": msg.topic()}},
            "service": {"framework": {"name": "Kafka"}},
        }
        
        if hasattr(msg, 'timestamp') and msg.timestamp()[0] == 0:  # CreateTime
            current_time_millis = int(round(time.time() * 1000))
            age = current_time_millis - msg.timestamp()[1]
            base_context["message"]["age"] = {"ms": age}
        
        return base_context
    
    def _call_handler(self, handler, data, msg, loop) -> None:
        """Call the message handler (sync or async)."""
        if asyncio.iscoroutinefunction(handler):
            loop.run_until_complete(handler(data, msg))
        else:
            handler(data, msg)
    
    def close(self) -> None:
        """Close the Kafka consumer connection."""
        self.stop()
        logger.info("Kafka consumer connection closed") 