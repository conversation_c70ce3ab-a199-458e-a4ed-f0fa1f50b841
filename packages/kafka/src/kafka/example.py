"""
Example usage of the Cymulate Kafka wrapper
"""

import time
from typing import Dict, Any

from confluent_kafka.cimpl import Message

from .manager import KafkaManager
from secretmanager.models import KafkaConfig

from logger import logger

def message_handler(data: Dict[str, Any], msg: Message) -> None:
    """
    Example message handler for Kafka consumer.
    
    Args:
        data: Deserialized message data
        msg: Raw Kafka message
    """
    logger.info(f"Received message from topic {msg.topic()}, partition {msg.partition()}, offset {msg.offset()}")
    logger.info(f"Message data: {data}")


def producer_example() -> None:
    """
    Example of using the Kafka producer.
    """
    # Create Kafka configuration
    config = KafkaConfig(
        broker=["localhost:9092"],
        sasl={
            "username": "your-username",
            "password": "your-password"
        }
    )
    
    # Initialize Kafka manager
    kafka_manager = KafkaManager(config)
    
    # Send a message
    topic = "example-topic"
    message = {
        "id": 123,
        "name": "Example Message",
        "timestamp": time.time()
    }
    
    success = kafka_manager.send_message(topic, message)
    logger.info(f"Message sent: {success}")
    
    # Close connections
    kafka_manager.close()


def consumer_example() -> None:
    """
    Example of using the Kafka consumer.
    """
    # Create Kafka configuration
    config = KafkaConfig(
        broker=["localhost:9092"],
        sasl={
            "username": "your-username",
            "password": "your-password"
        }
    )
    
    # Initialize Kafka manager
    kafka_manager = KafkaManager(config)
    
    # Subscribe to topics
    topics = ["example-topic"]
    kafka_manager.subscribe(topics, message_handler)
    
    # Start consuming
    kafka_manager.start_consuming()
    
    try:
        # Keep the consumer running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        # Stop consuming on keyboard interrupt
        kafka_manager.stop_consuming()
        kafka_manager.close()


if __name__ == "__main__":
    # Uncomment to run examples
    # producer_example()
    # consumer_example()
    pass 