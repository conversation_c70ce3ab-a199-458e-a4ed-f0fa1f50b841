"""
Kafka Manager for Cymulate
"""

from typing import Any, Callable, Dict, List, Optional, Union
from functools import wraps

from confluent_kafka.cimpl import Message

from .producer import KafkaProducer
from .consumer import KafkaConsumer
from secretmanager.models import KafkaConfig

from logger import logger



class KafkaManager:
    """
    Kafka Manager for Cymulate.
    
    This class provides a unified interface for managing both Kafka producers and consumers.
    It handles configuration, connection management, and provides convenience methods for
    common Kafka operations.
    
    This class implements the Singleton pattern to ensure only one instance exists.
    """
    
    _instance = None
    _handlers = []
    
    def __new__(cls, *args, **kwargs):
        """
        Create a new instance or return the existing one.
        
        Args:
            config: KafkaConfig object containing broker and authentication details
            
        Returns:
            KafkaManager: The singleton instance
        """
        if cls._instance is None:
            cls._instance = super(KafkaManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, config: Optional[KafkaConfig] = None, group_id: Optional[str] = None):
        """
        Initialize the Kafka manager with the provided configuration.
        This method will only initialize the instance once.
        
        Args:
            config: KafkaConfig object containing broker and authentication details
        """
        if self._initialized:
            return
            
        self._config = config
        self._producer = None
        self._consumer = None
        self._initialized = True
        
        if not config:
            raise ValueError("Kafka configuration not provided")
        
        self.initialize(config, group_id)
    

    
    def initialize(self, config: KafkaConfig, group_id: Optional[str] = None) -> None:
        """
        Initialize the Kafka manager with the provided configuration.
        
        Args:
            config: KafkaConfig object containing broker and authentication details
        """
        self._config = config
        
        # Initialize producer
        self._producer = KafkaProducer(config)
        
        # Initialize consumer
        self._consumer = KafkaConsumer(config, group_id)
        
        logger.info("Kafka manager initialized")
    
    def get_producer(self) -> KafkaProducer:
        """
        Get the Kafka producer instance.
        
        Returns:
            KafkaProducer: The Kafka producer instance
        """
        if not self._producer:
            if not self._config:
                raise ValueError("Kafka configuration not provided")
            self._producer = KafkaProducer(self._config)
        
        return self._producer
    
    def get_consumer(self) -> KafkaConsumer:
        """
        Get the Kafka consumer instance.
        
        Returns:
            KafkaConsumer: The Kafka consumer instance
        """
        if not self._consumer:
            if not self._config:
                raise ValueError("Kafka configuration not provided")
            self._consumer = KafkaConsumer(self._config)
        
        return self._consumer
    
    def send_message(self, topic: str, message: Union[str, Dict[str, Any], bytes], 
                    key: Optional[Union[str, bytes]] = None, 
                    partition: Optional[int] = None,
                    ensure_topic: bool = True) -> bool:
        """
        Send a message to a Kafka topic.
        
        Args:
            topic: Name of the topic to send the message to
            message: Message to send (string, dict, or bytes)
            key: Optional message key
            partition: Optional partition to send to
            ensure_topic: Whether to ensure the topic exists before sending
            
        Returns:
            bool: True if the message was sent successfully
        """
        producer = self.get_producer()
        return producer.send(topic, message, key, partition, ensure_topic)
    
    def subscribe(self, topics: Union[str, List[str]], 
                 handler: Callable[[Dict[str, Any], Message], None],
                 group_id: Optional[str] = None) -> None:
        """
        Subscribe to one or more topics and register a message handler.
        
        Args:
            topics: Topic or list of topics to subscribe to
            handler: Callback function to handle messages
            group_id: Optional consumer group ID
        """
        consumer = self.get_consumer()
        
        # If group_id is provided, reconnect with the new group_id
        if group_id:
            consumer.connect(self._config, group_id)
            
        if isinstance(topics, str):
            topics = [topics]
            
        for topic in topics:
            self.ensure_topic_exists(topic)
        consumer.subscribe(topics, handler)
    
    def start_consuming(self) -> None:
        """
        Start consuming messages from subscribed topics.
        """
        consumer = self.get_consumer()
        consumer.start()
    
    def stop_consuming(self) -> None:
        """
        Stop consuming messages.
        """
        if self._consumer:
            self._consumer.stop()
    
    def ensure_topic_exists(self, topic: str, num_partitions: int = 1, replication_factor: int = 1) -> bool:
        """
        Ensure that a topic exists, creating it if necessary.
        
        Args:
            topic: Name of the topic to ensure exists
            num_partitions: Number of partitions for the topic
            replication_factor: Replication factor for the topic
            
        Returns:
            bool: True if the topic exists or was created successfully
        """
        producer = self.get_producer()
        return producer.ensure_topic_exists(topic, num_partitions, replication_factor)
    
    def close(self) -> None:
        """
        Close all Kafka connections.
        """
        if self._producer:
            self._producer.close()
            self._producer = None
            
        if self._consumer:
            self._consumer.close()
            self._consumer = None
            
        logger.info("Kafka manager closed") 


    def kafka_event(self, topics: Union[str, List[str]], group_id: Optional[str] = None):
        """
        Decorator for handling Kafka events.
        
        Args:
            topics: Topic or list of topics to subscribe to
            group_id: Optional consumer group ID
            
        Example:
            @kafka_event(topics="my-topic")
            def handle_message(data: Dict[str, Any], message: Message):
                print(f"Received message: {data}")
        """
        
        def decorator(func: Callable):
            self.subscribe(topics, func, group_id)  
            @wraps(func)
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)
                
            # Store the Kafka configuration for later use
            wrapper._kafka_topics = topics
            wrapper._kafka_group_id = group_id
            wrapper._is_kafka_handler = True
            
            return wrapper
        return decorator