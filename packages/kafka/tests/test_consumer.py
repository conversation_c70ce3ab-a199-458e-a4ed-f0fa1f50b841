"""
Tests for KafkaConsumer class.
"""

import json
from unittest.mock import Mock, patch

import pytest
from secretmanager.models import KafkaConfig

from kafka.consumer import KafkaConsumer


class TestKafkaConsumer:
    """Test cases for KafkaConsumer."""

    def setup_method(self):
        """Reset singleton instance before each test."""
        KafkaConsumer._instance = None
        KafkaConsumer._lock = None

    def test_singleton_pattern(self):
        """Test that KafkaConsumer follows singleton pattern."""
        config = KafkaConfig(broker=["localhost:9092"])
        
        with patch('kafka.consumer.ConfluentConsumer'):
            consumer1 = KafkaConsumer(config, group_id="test-group")
            consumer2 = KafkaConsumer(config, group_id="test-group")
            
            assert consumer1 is consumer2

    def test_init_without_config(self):
        """Test initialization without config."""
        consumer = KafkaConsumer()
        assert consumer._config is None
        assert not consumer._connected
        assert not consumer._running

    @patch('kafka.consumer.ConfluentConsumer')
    def test_init_with_config(self, mock_consumer):
        """Test initialization with config."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        assert consumer._config == config
        assert consumer._connected
        assert consumer._group_id == "test-group"
        mock_consumer.assert_called_once()

    @patch('kafka.consumer.ConfluentConsumer')
    def test_connect_basic(self, mock_consumer):
        """Test basic connection."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer()
        consumer.connect(config, group_id="test-group")
        
        assert consumer._connected
        assert consumer._group_id == "test-group"
        mock_consumer.assert_called_once()

    @patch('kafka.consumer.ConfluentConsumer')
    def test_connect_with_sasl(self, mock_consumer):
        """Test connection with SASL authentication."""
        config = KafkaConfig(
            broker=["localhost:9092"],
            sasl={"username": "user", "password": "pass"}
        )
        consumer = KafkaConsumer()
        consumer.connect(config, group_id="test-group")
        
        # Check that SASL config was passed
        call_args = mock_consumer.call_args[0][0]
        assert call_args['security.protocol'] == 'SASL_SSL'
        assert call_args['sasl.mechanism'] == 'SCRAM-SHA-512'
        assert call_args['sasl.username'] == 'user'
        assert call_args['sasl.password'] == 'pass'

    def test_connect_no_broker(self):
        """Test connection without broker raises error."""
        config = KafkaConfig(broker=[])
        consumer = KafkaConsumer()
        
        with pytest.raises(ValueError, match="Kafka broker addresses are required"):
            consumer.connect(config)

    @patch('kafka.consumer.ConfluentConsumer')
    def test_connect_already_connected(self, mock_consumer):
        """Test connecting when already connected."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        # Reset mock and try to connect again
        mock_consumer.reset_mock()
        
        consumer.connect(config)
        
        # Should not create new connection
        mock_consumer.assert_not_called()

    @patch('kafka.consumer.ConfluentConsumer')
    def test_subscribe_single_topic(self, mock_consumer):
        """Test subscribing to a single topic."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        def test_handler(data, message):
            pass
            
        consumer.subscribe("test-topic", test_handler)
        
        assert "test-topic" in consumer._message_handlers
        assert consumer._message_handlers["test-topic"] == test_handler

    @patch('kafka.consumer.ConfluentConsumer')
    def test_subscribe_multiple_topics(self, mock_consumer):
        """Test subscribing to multiple topics."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        def test_handler(data, message):
            pass
            
        topics = ["topic1", "topic2", "topic3"]
        consumer.subscribe(topics, test_handler)
        
        for topic in topics:
            assert topic in consumer._message_handlers
            assert consumer._message_handlers[topic] == test_handler

    def test_subscribe_not_connected(self):
        """Test subscribing when not connected."""
        consumer = KafkaConsumer()
        
        def test_handler(data, message):
            pass
            
        # Should not raise error but log warning
        consumer.subscribe("test-topic", test_handler)

    @patch('kafka.consumer.ConfluentConsumer')
    def test_start_consuming(self, mock_consumer):
        """Test starting message consumption."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer.return_value
        
        def test_handler(data, message):
            pass
            
        consumer.subscribe("test-topic", test_handler)
        
        with patch('threading.Thread') as mock_thread:
            consumer.start()
            
            assert consumer._running
            mock_consumer_instance.subscribe.assert_called_once_with(["test-topic"])
            mock_thread.assert_called_once()

    @patch('kafka.consumer.ConfluentConsumer')
    def test_start_already_running(self, mock_consumer):
        """Test starting when already running."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        consumer._running = True
        
        with patch('threading.Thread') as mock_thread:
            consumer.start()
            
            # Should not create new thread
            mock_thread.assert_not_called()

    def test_start_not_connected(self):
        """Test starting when not connected."""
        consumer = KafkaConsumer()
        
        with patch('threading.Thread') as mock_thread:
            consumer.start()
            
            # Should not start
            mock_thread.assert_not_called()

    @patch('kafka.consumer.ConfluentConsumer')
    def test_stop_consuming(self, mock_consumer):
        """Test stopping message consumption."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer.return_value
        
        # Simulate running state
        consumer._running = True
        mock_thread = Mock()
        consumer._consumer_thread = mock_thread
        
        consumer.stop()
        
        assert not consumer._running
        mock_thread.join.assert_called_once_with(timeout=5.0)
        mock_consumer_instance.close.assert_called_once()

    def test_stop_not_running(self):
        """Test stopping when not running."""
        consumer = KafkaConsumer()
        
        # Should not raise error
        consumer.stop()

    @patch('kafka.consumer.ConfluentConsumer')
    def test_consume_messages_json_message(self, mock_consumer):
        """Test consuming JSON messages."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer.return_value
        
        # Create mock message
        mock_message = Mock()
        mock_message.error.return_value = None
        mock_message.topic.return_value = "test-topic"
        mock_message.value.return_value = json.dumps({"key": "value"}).encode()
        
        # Mock poll to return message once then None
        mock_consumer_instance.poll.side_effect = [mock_message, None]
        
        handler_called = []
        def test_handler(data, message):
            handler_called.append((data, message))
            consumer._running = False  # Stop after first message
            
        consumer._message_handlers["test-topic"] = test_handler
        consumer._running = True
        
        with patch('asyncio.new_event_loop') as mock_loop_create, \
             patch('asyncio.set_event_loop') as mock_set_loop:
            mock_loop = Mock()
            mock_loop_create.return_value = mock_loop
            
            consumer._consume_messages()
            
            assert len(handler_called) == 1
            data, message = handler_called[0]
            assert data == {"key": "value"}
            assert message == mock_message

    @patch('kafka.consumer.ConfluentConsumer')
    def test_consume_messages_raw_message(self, mock_consumer):
        """Test consuming non-JSON messages."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer.return_value
        
        # Create mock message with non-JSON content
        mock_message = Mock()
        mock_message.error.return_value = None
        mock_message.topic.return_value = "test-topic"
        mock_message.value.return_value = b"raw message"
        
        mock_consumer_instance.poll.side_effect = [mock_message, None]
        
        handler_called = []
        def test_handler(data, message):
            handler_called.append((data, message))
            consumer._running = False
            
        consumer._message_handlers["test-topic"] = test_handler
        consumer._running = True
        
        with patch('asyncio.new_event_loop') as mock_loop_create, \
             patch('asyncio.set_event_loop') as mock_set_loop:
            mock_loop = Mock()
            mock_loop_create.return_value = mock_loop
            
            consumer._consume_messages()
            
            assert len(handler_called) == 1
            data, message = handler_called[0]
            assert data == {"raw": "raw message"}

    # @patch('kafka.consumer.ConfluentConsumer')
    # def test_consume_messages_async_handler(self, mock_consumer):
        """Test consuming messages with async handler."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer.return_value
        
        # Create mock message
        mock_message = Mock()
        mock_message.error.return_value = None
        mock_message.topic.return_value = "test-topic"
        mock_message.value.return_value = json.dumps({"key": "value"}).encode()
        
        mock_consumer_instance.poll.side_effect = [mock_message, None]
        
        handler_called = []
        async def async_handler(data, message):
            handler_called.append((data, message))
            consumer._running = False
            
        consumer._message_handlers["test-topic"] = async_handler
        consumer._running = True
        
        with patch('asyncio.new_event_loop') as mock_loop_create, \
             patch('asyncio.set_event_loop') as mock_set_loop, \
             patch('asyncio.iscoroutinefunction', return_value=True):
            mock_loop = Mock()
            mock_loop_create.return_value = mock_loop
            
            consumer._consume_messages()
            
            # Async handler should be called via run_until_complete
            mock_loop.run_until_complete.assert_called()

    # @patch('kafka.consumer.ConfluentConsumer')
    # def test_consume_messages_error(self, mock_consumer):
    #     """Test consuming messages with Kafka error."""
    #     config = KafkaConfig(broker=["localhost:9092"])
    #     consumer = KafkaConsumer(config, group_id="test-group")
        
    #     mock_consumer_instance = mock_consumer.return_value
        
    #     # Create mock message with error
    #     mock_message = Mock()
    #     mock_error = Mock()
    #     mock_error.code.return_value = KafkaError._PARTITION_EOF
    #     mock_message.error.return_value = mock_error
    #     mock_message.partition.return_value = 0
    #     mock_message.topic.return_value = "test-topic"
    #     mock_message.offset.return_value = 100
        
    #     mock_consumer_instance.poll.side_effect = [mock_message, None]
        
    #     consumer._running = True
        
    #     with patch('asyncio.new_event_loop') as mock_loop_create, \
    #          patch('asyncio.set_event_loop') as mock_set_loop:
    #         mock_loop = Mock()
    #         mock_loop_create.return_value = mock_loop
            
    #         # Should handle error gracefully
    #         consumer._consume_messages()

    # @patch('kafka.consumer.ConfluentConsumer')
    # def test_consume_messages_no_handler(self, mock_consumer):
    #     """Test consuming messages with no registered handler."""
    #     config = KafkaConfig(broker=["localhost:9092"])
    #     consumer = KafkaConsumer(config, group_id="test-group")
        
    #     mock_consumer_instance = mock_consumer.return_value
        
    #     # Create mock message
    #     mock_message = Mock()
    #     mock_message.error.return_value = None
    #     mock_message.topic.return_value = "unknown-topic"
    #     mock_message.value.return_value = b"message"
        
    #     mock_consumer_instance.poll.side_effect = [mock_message, None]
        
    #     consumer._running = True
        
    #     with patch('asyncio.new_event_loop') as mock_loop_create, \
    #          patch('asyncio.set_event_loop') as mock_set_loop:
    #         mock_loop = Mock()
    #         mock_loop_create.return_value = mock_loop
            
    #         # Should handle missing handler gracefully
    #         consumer._consume_messages()

    # @patch('kafka.consumer.ConfluentConsumer')
    # def test_close(self, mock_consumer):
        """Test closing consumer."""
        config = KafkaConfig(broker=["localhost:9092"])
        consumer = KafkaConsumer(config, group_id="test-group")
        
        consumer.close()
        
        assert not consumer._running 