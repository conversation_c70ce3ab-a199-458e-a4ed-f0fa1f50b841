"""
Tests for KafkaManager class.
"""

from unittest.mock import Mock, patch

import pytest
from secretmanager.models import KafkaConfig

from kafka.manager import KafkaManager


class TestKafkaManager:
    """Test cases for KafkaManager."""

    def setup_method(self):
        """Reset singleton instance before each test."""
        KafkaManager._instance = None
        KafkaManager._handlers = []

    def test_singleton_pattern(self):
        """Test that KafkaManager follows singleton pattern."""
        config = KafkaConfig(broker=["localhost:9092"])
        
        with patch('kafka.manager.KafkaProducer'), \
             patch('kafka.manager.KafkaConsumer'):
            manager1 = KafkaManager(config, group_id="test-group")
            manager2 = KafkaManager(config, group_id="test-group")
            
            assert manager1 is manager2

    def test_init_without_config_raises_error(self):
        """Test initialization without config raises ValueError."""
        with pytest.raises(ValueError, match="Kafka configuration not provided"):
            KafkaManager()

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_init_with_config(self, mock_producer, mock_consumer):
        """Test initialization with config."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        assert manager._config == config
        assert manager._initialized
        mock_producer.assert_called_once_with(config)
        mock_consumer.assert_called_once_with(config, "test-group")

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_initialize(self, mock_producer, mock_consumer):
        """Test initialize method."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager.__new__(KafkaManager)
        manager._initialized = False
        
        manager.initialize(config, group_id="test-group")
        
        assert manager._config == config
        mock_producer.assert_called_once_with(config)
        mock_consumer.assert_called_once_with(config, "test-group")

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_get_producer_existing(self, mock_producer_class, mock_consumer):
        """Test getting existing producer."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        mock_producer_instance = mock_producer_class.return_value
        
        producer = manager.get_producer()
        
        assert producer == mock_producer_instance

    def test_get_producer_no_config_raises_error(self):
        """Test getting producer without config raises error."""
        manager = KafkaManager.__new__(KafkaManager)
        manager._initialized = True
        manager._config = None
        manager._producer = None
        
        with pytest.raises(ValueError, match="Kafka configuration not provided"):
            manager.get_producer()

    @patch('kafka.manager.KafkaProducer')
    def test_get_producer_create_new(self, mock_producer):
        """Test creating new producer when none exists."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager.__new__(KafkaManager)
        manager._initialized = True
        manager._config = config
        manager._producer = None
        manager._consumer = None
        
        producer = manager.get_producer()
        
        mock_producer.assert_called_once_with(config)
        assert manager._producer == mock_producer.return_value

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_get_consumer_existing(self, mock_producer, mock_consumer_class):
        """Test getting existing consumer."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer_class.return_value
        
        consumer = manager.get_consumer()
        
        assert consumer == mock_consumer_instance

    def test_get_consumer_no_config_raises_error(self):
        """Test getting consumer without config raises error."""
        manager = KafkaManager.__new__(KafkaManager)
        manager._initialized = True
        manager._config = None
        manager._consumer = None
        
        with pytest.raises(ValueError, match="Kafka configuration not provided"):
            manager.get_consumer()

    @patch('kafka.manager.KafkaConsumer')
    def test_get_consumer_create_new(self, mock_consumer):
        """Test creating new consumer when none exists."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager.__new__(KafkaManager)
        manager._initialized = True
        manager._config = config
        manager._producer = None
        manager._consumer = None
        
        consumer = manager.get_consumer()
        
        mock_consumer.assert_called_once_with(config)
        assert manager._consumer == mock_consumer.return_value

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_send_message(self, mock_producer_class, mock_consumer):
        """Test sending message."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        mock_producer_instance = mock_producer_class.return_value
        mock_producer_instance.send.return_value = True
        
        result = manager.send_message("test-topic", "test message", key="test-key")
        
        assert result is True
        mock_producer_instance.send.assert_called_once_with(
            "test-topic", "test message", "test-key", None, True
        )

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_subscribe_single_topic(self, mock_producer, mock_consumer_class):
        """Test subscribing to single topic."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer_class.return_value
        
        def test_handler(data, message):
            pass
            
        with patch.object(manager, 'ensure_topic_exists', return_value=True):
            manager.subscribe("test-topic", test_handler)
            
        mock_consumer_instance.subscribe.assert_called_once_with(["test-topic"], test_handler)

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_subscribe_multiple_topics(self, mock_producer, mock_consumer_class):
        """Test subscribing to multiple topics."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer_class.return_value
        
        def test_handler(data, message):
            pass
            
        topics = ["topic1", "topic2", "topic3"]
        
        with patch.object(manager, 'ensure_topic_exists', return_value=True) as mock_ensure:
            manager.subscribe(topics, test_handler)
            
        # Should ensure all topics exist
        assert mock_ensure.call_count == 3
        mock_consumer_instance.subscribe.assert_called_once_with(topics, test_handler)

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_subscribe_with_group_id(self, mock_producer, mock_consumer_class):
        """Test subscribing with specific group ID."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer_class.return_value
        
        def test_handler(data, message):
            pass
            
        with patch.object(manager, 'ensure_topic_exists', return_value=True):
            manager.subscribe("test-topic", test_handler, group_id="new-group")
            
        # Should reconnect with new group ID
        mock_consumer_instance.connect.assert_called_with(config, "new-group")

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_start_consuming(self, mock_producer, mock_consumer_class):
        """Test starting consumption."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer_class.return_value
        
        manager.start_consuming()
        
        mock_consumer_instance.start.assert_called_once()

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_stop_consuming(self, mock_producer, mock_consumer_class):
        """Test stopping consumption."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        mock_consumer_instance = mock_consumer_class.return_value
        
        manager.stop_consuming()
        
        mock_consumer_instance.stop.assert_called_once()

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_stop_consuming_no_consumer(self, mock_producer, mock_consumer):
        """Test stopping consumption when no consumer exists."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        manager._consumer = None
        
        # Should not raise error
        manager.stop_consuming()

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_ensure_topic_exists(self, mock_producer_class, mock_consumer):
        """Test ensuring topic exists."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        mock_producer_instance = mock_producer_class.return_value
        mock_producer_instance.ensure_topic_exists.return_value = True
        
        result = manager.ensure_topic_exists("test-topic", num_partitions=3, replication_factor=2)
        
        assert result is True
        mock_producer_instance.ensure_topic_exists.assert_called_once_with(
            "test-topic", 3, 2
        )

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_close(self, mock_producer_class, mock_consumer_class):
        """Test closing manager."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        mock_producer_instance = mock_producer_class.return_value
        mock_consumer_instance = mock_consumer_class.return_value
        
        manager.close()
        
        mock_producer_instance.close.assert_called_once()
        mock_consumer_instance.close.assert_called_once()
        assert manager._producer is None
        assert manager._consumer is None

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_close_no_instances(self, mock_producer, mock_consumer):
        """Test closing manager when no instances exist."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        manager._producer = None
        manager._consumer = None
        
        # Should not raise error
        manager.close()

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_kafka_event_decorator(self, mock_producer, mock_consumer):
        """Test kafka_event decorator."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        with patch.object(manager, 'subscribe') as mock_subscribe:
            @manager.kafka_event(topics="test-topic", group_id="test-group")
            def test_handler(data, message):
                return "handled"
                
            # Check that subscribe was called with correct arguments
            assert mock_subscribe.call_count == 1
            call_args = mock_subscribe.call_args[0]
            assert call_args[0] == "test-topic"
            assert callable(call_args[1])  # Function should be callable
            assert call_args[2] == "test-group"  # group_id is third positional argument
            
            # Check that wrapper attributes are set
            assert test_handler._kafka_topics == "test-topic"
            assert test_handler._kafka_group_id == "test-group"
            assert test_handler._is_kafka_handler is True
            
            # Check that function still works
            result = test_handler({"test": "data"}, Mock())
            assert result == "handled"

    @patch('kafka.manager.KafkaConsumer')
    @patch('kafka.manager.KafkaProducer')
    def test_kafka_event_decorator_multiple_topics(self, mock_producer, mock_consumer):
        """Test kafka_event decorator with multiple topics."""
        config = KafkaConfig(broker=["localhost:9092"])
        manager = KafkaManager(config, group_id="test-group")
        
        topics = ["topic1", "topic2"]
        
        with patch.object(manager, 'subscribe') as mock_subscribe:
            @manager.kafka_event(topics=topics)
            def test_handler(data, message):
                pass
                
            # Check that subscribe was called with correct arguments
            assert mock_subscribe.call_count == 1
            call_args = mock_subscribe.call_args[0]
            assert call_args[0] == topics
            assert callable(call_args[1])  # Function should be callable
            assert call_args[2] is None  # group_id is third positional argument
            
            # Check that wrapper attributes are set
            assert test_handler._kafka_topics == topics
            assert test_handler._kafka_group_id == None
            assert test_handler._is_kafka_handler is True
            
            # Check that function still works
            result = test_handler({"test": "data"}, Mock())
            assert result == None 