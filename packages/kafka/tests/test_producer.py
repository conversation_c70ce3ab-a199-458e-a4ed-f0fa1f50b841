"""
Tests for KafkaProducer class.
"""

import json
from unittest.mock import Mock, patch

import pytest
from secretmanager.models import KafkaConfig

from kafka.producer import KafkaProducer


class TestKafkaProducer:
    """Test cases for KafkaProducer."""

    def setup_method(self):
        """Reset singleton instance before each test."""
        KafkaProducer._instance = None
        KafkaProducer._lock = None

    def test_singleton_pattern(self):
        """Test that KafkaProducer follows singleton pattern."""
        config = KafkaConfig(broker=["localhost:9092"])
        
        with patch('kafka.producer.ConfluentProducer'), \
             patch('kafka.producer.AdminClient'):
            producer1 = KafkaProducer(config)
            producer2 = KafkaProducer(config)
            
            assert producer1 is producer2

    def test_init_without_config(self):
        """Test initialization without config."""
        producer = KafkaProducer()
        assert producer._config is None
        assert not producer._connected

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_init_with_config(self, mock_producer, mock_admin):
        """Test initialization with config."""
        config = KafkaConfig(broker=["localhost:9092"])
        producer = KafkaProducer(config)
        
        assert producer._config == config
        assert producer._connected
        mock_producer.assert_called_once()
        mock_admin.assert_called_once()

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_connect_basic(self, mock_producer, mock_admin):
        """Test basic connection."""
        config = KafkaConfig(broker=["localhost:9092"])
        producer = KafkaProducer()
        producer.connect(config)
        
        assert producer._connected
        mock_producer.assert_called_once()
        mock_admin.assert_called_once()

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_connect_with_sasl(self, mock_producer, mock_admin):
        """Test connection with SASL authentication."""
        config = KafkaConfig(
            broker=["localhost:9092"],
            sasl={"username": "user", "password": "pass"}
        )
        producer = KafkaProducer()
        producer.connect(config)
        
        # Check that SASL config was passed
        call_args = mock_producer.call_args[0][0]
        assert call_args['security.protocol'] == 'SASL_SSL'
        assert call_args['sasl.mechanism'] == 'SCRAM-SHA-512'
        assert call_args['sasl.username'] == 'user'
        assert call_args['sasl.password'] == 'pass'

    def test_connect_no_broker(self):
        """Test connection without broker raises error."""
        config = KafkaConfig(broker=[])
        producer = KafkaProducer()
        
        with pytest.raises(ValueError, match="Kafka broker addresses are required"):
            producer.connect(config)

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_connect_already_connected(self, mock_producer, mock_admin):
        """Test connecting when already connected."""
        config = KafkaConfig(broker=["localhost:9092"])
        producer = KafkaProducer(config)
        
        # Reset mocks and try to connect again
        mock_producer.reset_mock()
        mock_admin.reset_mock()
        
        producer.connect(config)
        
        # Should not create new connections
        mock_producer.assert_not_called()
        mock_admin.assert_not_called()

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_ensure_topic_exists_already_exists(self, mock_producer, mock_admin):
        """Test ensuring topic exists when it already exists."""
        config = KafkaConfig(broker=["localhost:9092"])
        producer = KafkaProducer(config)
        
        # Mock admin client to return existing topic
        mock_topics = Mock()
        mock_topics.topics = {"test-topic": Mock()}
        mock_admin.return_value.list_topics.return_value = mock_topics
        
        result = producer.ensure_topic_exists("test-topic")
        assert result is True

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_ensure_topic_exists_create_new(self, mock_producer, mock_admin):
        """Test creating a new topic."""
        config = KafkaConfig(broker=["localhost:9092"])
        producer = KafkaProducer(config)
        
        # Mock admin client to return no existing topics
        mock_topics = Mock()
        mock_topics.topics = {}
        mock_admin.return_value.list_topics.return_value = mock_topics
        
        # Mock successful topic creation
        mock_future = Mock()
        mock_future.result.return_value = None
        mock_admin.return_value.create_topics.return_value = {"test-topic": mock_future}
        
        result = producer.ensure_topic_exists("test-topic")
        assert result is True

    def test_ensure_topic_exists_no_admin(self):
        """Test ensuring topic exists without admin client."""
        producer = KafkaProducer()
        result = producer.ensure_topic_exists("test-topic")
        assert result is False

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_send_success(self, mock_producer_class, mock_admin):
        """Test successful message sending."""
        config = KafkaConfig(broker=["localhost:9092"])
        producer = KafkaProducer(config)
        
        # Mock the producer instance
        mock_producer_instance = mock_producer_class.return_value
        mock_producer_instance.produce = Mock()
        mock_producer_instance.flush = Mock()
        
        # Mock ensure_topic_exists
        with patch.object(producer, 'ensure_topic_exists', return_value=True):
            result = producer.send("test-topic", "test message")
            
        assert result is True
        mock_producer_instance.produce.assert_called_once()
        mock_producer_instance.flush.assert_called_once()

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_send_dict_message(self, mock_producer_class, mock_admin):
        """Test sending dictionary message."""
        config = KafkaConfig(broker=["localhost:9092"])
        producer = KafkaProducer(config)
        
        mock_producer_instance = mock_producer_class.return_value
        
        message_dict = {"key": "value", "number": 42}
        
        with patch.object(producer, 'ensure_topic_exists', return_value=True):
            result = producer.send("test-topic", message_dict)
            
        assert result is True
        # Check that message was JSON serialized
        call_args = mock_producer_instance.produce.call_args
        assert json.loads(call_args[1]['value'].decode()) == message_dict

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_send_with_key_and_partition(self, mock_producer_class, mock_admin):
        """Test sending message with key and partition."""
        config = KafkaConfig(broker=["localhost:9092"])
        producer = KafkaProducer(config)
        
        mock_producer_instance = mock_producer_class.return_value
        
        with patch.object(producer, 'ensure_topic_exists', return_value=True):
            result = producer.send("test-topic", "message", key="test-key", partition=1)
            
        assert result is True
        call_args = mock_producer_instance.produce.call_args
        assert call_args[1]['value'] == b"message"

    def test_send_not_connected(self):
        """Test sending message when not connected."""
        producer = KafkaProducer()
        result = producer.send("test-topic", "message")
        assert result is False

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_send_exception(self, mock_producer_class, mock_admin):
        """Test sending message with exception."""
        config = KafkaConfig(broker=["localhost:9092"])
        producer = KafkaProducer(config)
        
        mock_producer_instance = mock_producer_class.return_value
        mock_producer_instance.produce.side_effect = Exception("Send failed")
        
        with patch.object(producer, 'ensure_topic_exists', return_value=True):
            result = producer.send("test-topic", "message")
            
        assert result is False

    @patch('kafka.producer.AdminClient')
    @patch('kafka.producer.ConfluentProducer')
    def test_close(self, mock_producer_class, mock_admin):
        """Test closing producer."""
        config = KafkaConfig(broker=["localhost:9092"])
        producer = KafkaProducer(config)
        
        mock_producer_instance = mock_producer_class.return_value
        
        producer.close()
        
        mock_producer_instance.flush.assert_called_once()
        mock_producer_instance.close.assert_called_once()
        assert not producer._connected
        assert producer._producer is None 