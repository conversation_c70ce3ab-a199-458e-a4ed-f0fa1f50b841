"""
Tests for kafka example module.
"""

from unittest.mock import Mock, patch

from secretmanager.models import KafkaConfig

from kafka.example import consumer_example, message_handler, producer_example


class TestExample:
    """Test cases for example functions."""

    def test_message_handler(self):
        """Test the message handler function."""
        # Create mock message
        mock_message = Mock()
        mock_message.topic.return_value = "test-topic"
        mock_message.partition.return_value = 0
        mock_message.offset.return_value = 123
        
        data = {"key": "value", "number": 42}
        
        with patch('kafka.example.logger') as mock_logger:
            message_handler(data, mock_message)
            
            # Check that logger was called with expected messages
            assert mock_logger.info.call_count == 2
            calls = mock_logger.info.call_args_list
            assert "test-topic" in str(calls[0])
            assert "partition 0" in str(calls[0])
            assert "offset 123" in str(calls[0])
            assert str(data) in str(calls[1])

    @patch('kafka.example.KafkaManager')
    @patch('kafka.example.time')
    def test_producer_example(self, mock_time, mock_kafka_manager_class):
        """Test the producer example function."""
        mock_time.time.return_value = 1234567890.0
        
        # Mock KafkaManager instance
        mock_manager = Mock()
        mock_kafka_manager_class.return_value = mock_manager
        mock_manager.send_message.return_value = True
        
        with patch('kafka.example.logger') as mock_logger:
            producer_example()
            
            # Check that KafkaManager was initialized with correct config
            mock_kafka_manager_class.assert_called_once()
            config_arg = mock_kafka_manager_class.call_args[0][0]
            assert isinstance(config_arg, KafkaConfig)
            assert config_arg.broker == ["localhost:9092"]
            assert config_arg.sasl["username"] == "your-username"
            assert config_arg.sasl["password"] == "your-password"
            
            # Check that send_message was called
            mock_manager.send_message.assert_called_once()
            call_args = mock_manager.send_message.call_args[0]
            assert call_args[0] == "example-topic"
            message = call_args[1]
            assert message["id"] == 123
            assert message["name"] == "Example Message"
            assert message["timestamp"] == 1234567890.0
            
            # Check that close was called
            mock_manager.close.assert_called_once()
            
            # Check that logger was called
            mock_logger.info.assert_called_with("Message sent: True")

    @patch('kafka.example.KafkaManager')
    @patch('kafka.example.time')
    def test_consumer_example(self, mock_time, mock_kafka_manager_class):
        """Test the consumer example function."""
        # Mock KafkaManager instance
        mock_manager = Mock()
        mock_kafka_manager_class.return_value = mock_manager
        
        # Mock time.sleep to raise KeyboardInterrupt after first call
        mock_time.sleep.side_effect = [None, KeyboardInterrupt()]
        
        consumer_example()
        
        # Check that KafkaManager was initialized with correct config
        mock_kafka_manager_class.assert_called_once()
        config_arg = mock_kafka_manager_class.call_args[0][0]
        assert isinstance(config_arg, KafkaConfig)
        assert config_arg.broker == ["localhost:9092"]
        assert config_arg.sasl["username"] == "your-username"
        assert config_arg.sasl["password"] == "your-password"
        
        # Check that subscribe was called
        mock_manager.subscribe.assert_called_once()
        call_args = mock_manager.subscribe.call_args[0]
        assert call_args[0] == ["example-topic"]
        assert callable(call_args[1])  # Should be message_handler function
        
        # Check that start_consuming was called
        mock_manager.start_consuming.assert_called_once()
        
        # Check that stop_consuming and close were called on KeyboardInterrupt
        mock_manager.stop_consuming.assert_called_once()
        mock_manager.close.assert_called_once()

    @patch('kafka.example.KafkaManager')
    @patch('kafka.example.time')
    def test_consumer_example_no_interrupt(self, mock_time, mock_kafka_manager_class):
        """Test the consumer example function without KeyboardInterrupt."""
        # Mock KafkaManager instance
        mock_manager = Mock()
        mock_kafka_manager_class.return_value = mock_manager
        
        # Mock time.sleep to simulate running for a few iterations
        call_count = 0
        def sleep_side_effect(duration):
            nonlocal call_count
            call_count += 1
            if call_count >= 3:  # Stop after 3 iterations
                raise KeyboardInterrupt()
        
        mock_time.sleep.side_effect = sleep_side_effect
        
        consumer_example()
        
        # Check that sleep was called multiple times
        assert mock_time.sleep.call_count == 3
        
        # Check that all expected methods were called
        mock_manager.subscribe.assert_called_once()
        mock_manager.start_consuming.assert_called_once()
        mock_manager.stop_consuming.assert_called_once()
        mock_manager.close.assert_called_once()

    def test_main_block(self):
        """Test the main block (should do nothing)."""
        # Import the module to execute the main block
        
        # The main block should not raise any errors and should do nothing
        # This test just ensures the main block can be executed without issues 