"""
Tests for kafka module imports and version.
"""

from kafka import <PERSON><PERSON><PERSON><PERSON>ons<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, KafkaProducer, __version__


def test_imports():
    """Test that all main classes can be imported."""
    assert KafkaManager is not None
    assert KafkaProducer is not None
    assert KafkaConsumer is not None


def test_version():
    """Test that version is defined."""
    assert isinstance(__version__, str)
    assert len(__version__) > 0


def test_all_exports():
    """Test that __all__ contains expected exports."""
    from kafka import __all__
    expected = ["KafkaManager", "KafkaProducer", "KafkaConsumer"]
    assert set(__all__) == set(expected) 