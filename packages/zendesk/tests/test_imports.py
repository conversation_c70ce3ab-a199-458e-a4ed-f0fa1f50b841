"""
Tests for module imports and basic functionality.
"""

import os
import sys

# Add the src directory to the path to import our local zendesk module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


def test_zendesk_client_import():
    """Test that ZendeskClient can be imported."""
    from zendesk.zendesk_client import ZendeskClient
    assert ZendeskClient is not None


def test_zendesk_module_import():
    """Test that the zendesk module can be imported."""
    import zendesk
    assert zendesk is not None


def test_zendesk_client_class_exists():
    """Test that ZendeskClient class has expected methods."""
    from zendesk.zendesk_client import ZendeskClient
    
    # Check that the class has the expected methods
    assert hasattr(ZendeskClient, '__init__')
    assert hasattr(ZendeskClient, 'search_new_tasks')
    assert hasattr(ZendeskClient, 'get_ticket_comments')
    assert hasattr(ZendeskClient, 'add_private_note_to_ticket')
    
    # Check that methods are callable
    assert callable(getattr(ZendeskClient, '__init__'))
    assert callable(getattr(ZendeskClient, 'search_new_tasks'))
    assert callable(getattr(ZendeskClient, 'get_ticket_comments'))
    assert callable(getattr(ZendeskClient, 'add_private_note_to_ticket')) 