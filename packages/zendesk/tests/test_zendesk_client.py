"""
Tests for ZendeskClient.
"""

import os
import sys
from datetime import datetime, timedelta, timezone
from unittest.mock import Mock, patch

from requests.exceptions import RequestException

# Add the src directory to the path to import our local zendesk module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from zendesk.zendesk_client import ZendeskClient


class TestZendeskClient:
    """Test cases for ZendeskClient."""

    def setup_method(self):
        """Set up test fixtures."""
        self.email = "<EMAIL>"
        self.token = "test-token"
        self.subdomain = "test-subdomain"
        
    @patch('zendesk.zendesk_client.Zenpy')
    def test_init(self, mock_zenpy):
        """Test ZendeskClient initialization."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Check that credentials are stored correctly
        assert client.credentials['email'] == self.email
        assert client.credentials['token'] == self.token
        assert client.credentials['subdomain'] == self.subdomain
        
        # Check that custom fields are set
        assert client.custom_fields['ticket_form_id'] == '11319272564749'
        
        # Check that AI tag is set
        assert client.ai_tag == "[Cymulate AI]"
        
        # Check that Zenpy client is initialized
        mock_zenpy.assert_called_once_with(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        assert client.zenpy_client == mock_zenpy.return_value

    @patch('zendesk.zendesk_client.Zenpy')
    def test_search_new_tasks_success(self, mock_zenpy):
        """Test successful search for new tasks."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Mock the search results
        mock_search_results = [Mock(), Mock()]
        mock_zenpy.return_value.search.return_value = mock_search_results
        
        # Test search with a specific datetime
        since = datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        result = client.search_new_tasks(since)
        
        # Verify the search was called with correct parameters
        expected_time = since.strftime('%Y-%m-%dT%H:%M:%S%z')
        expected_query = f"type:ticket status:new created>{expected_time} ticket_form_id:11319272564749"
        
        mock_zenpy.return_value.search.assert_called_once_with(
            query=expected_query,
            sort_by='created_at',
            sort_order='desc'
        )
        
        # Verify the result
        assert result == mock_search_results

    @patch('zendesk.zendesk_client.logger')
    @patch('zendesk.zendesk_client.Zenpy')
    def test_search_new_tasks_request_exception(self, mock_zenpy, mock_logger):
        """Test search for new tasks with RequestException."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Mock RequestException
        mock_zenpy.return_value.search.side_effect = RequestException("Network error")
        
        
        since = datetime.now(timezone.utc)
        result = client.search_new_tasks(since)
        
        # Should return empty list on exception
        assert result == []

    @patch('zendesk.zendesk_client.Zenpy')
    def test_search_new_tasks_query_formatting(self, mock_zenpy):
        """Test that the search query is formatted correctly."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Add another custom field to test multiple fields
        client.custom_fields['another_field'] = 'another_value'
        
        mock_zenpy.return_value.search.return_value = []
        
        since = datetime(2024, 6, 15, 14, 30, 45, tzinfo=timezone.utc)
        client.search_new_tasks(since)
        
        # Check that the query includes all custom fields
        expected_time = "2024-06-15T14:30:45+0000"
        call_args = mock_zenpy.return_value.search.call_args
        query = call_args[1]['query']
        
        assert "type:ticket status:new" in query
        assert f"created>{expected_time}" in query
        assert "ticket_form_id:11319272564749" in query
        assert "another_field:another_value" in query

    @patch('zendesk.zendesk_client.Zenpy')
    def test_get_ticket_comments_success(self, mock_zenpy):
        """Test successful retrieval of ticket comments."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Mock comments
        mock_comments = [Mock(), Mock(), Mock()]
        mock_zenpy.return_value.tickets.comments.return_value = mock_comments
        
        ticket_id = 12345
        result = client.get_ticket_comments(ticket_id)
        
        # Verify the comments method was called correctly
        mock_zenpy.return_value.tickets.comments.assert_called_once_with(ticket=ticket_id)
        
        # Verify the result
        assert result == mock_comments

    @patch('zendesk.zendesk_client.logger')
    @patch('zendesk.zendesk_client.Zenpy')
    def test_get_ticket_comments_exception(self, mock_zenpy, mock_logger):
        """Test get_ticket_comments with exception."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Mock exception
        mock_zenpy.return_value.tickets.comments.side_effect = Exception("API error")
        
        # Mock logger opt method
        
        ticket_id = 12345
        result = client.get_ticket_comments(ticket_id)
        
        # Should return empty list on exception
        assert result == []

    @patch('zendesk.zendesk_client.Comment')
    @patch('zendesk.zendesk_client.Zenpy')
    def test_add_private_note_to_ticket_success(self, mock_zenpy, mock_comment):
        """Test successful addition of private note to ticket."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Mock ticket and comment
        mock_ticket = Mock()
        mock_zenpy.return_value.tickets.return_value = mock_ticket
        mock_comment_instance = Mock()
        mock_comment.return_value = mock_comment_instance
        
        ticket_id = 12345
        note = "This is a private note"
        
        client.add_private_note_to_ticket(ticket_id, note)
        
        # Verify ticket was retrieved
        mock_zenpy.return_value.tickets.assert_called_once_with(id=ticket_id)
        
        # Verify comment was created correctly
        mock_comment.assert_called_once_with(body=note, public=False)
        
        # Verify comment was attached to ticket
        assert mock_ticket.comment == mock_comment_instance
        
        # Verify ticket was updated
        mock_zenpy.return_value.tickets.update.assert_called_once_with(mock_ticket)

    @patch('zendesk.zendesk_client.logger')
    @patch('zendesk.zendesk_client.Comment')
    @patch('zendesk.zendesk_client.Zenpy')
    def test_add_private_note_to_ticket_exception(self, mock_zenpy, mock_comment, mock_logger):
        """Test add_private_note_to_ticket with exception."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Mock exception during ticket retrieval
        mock_zenpy.return_value.tickets.side_effect = Exception("API error")
        
        # Mock logger opt method
        
        ticket_id = 12345
        note = "This is a private note"
        
        # Should not raise exception
        client.add_private_note_to_ticket(ticket_id, note)

    @patch('zendesk.zendesk_client.logger')
    @patch('zendesk.zendesk_client.Comment')
    @patch('zendesk.zendesk_client.Zenpy')
    def test_add_private_note_to_ticket_update_exception(self, mock_zenpy, mock_comment, mock_logger):
        """Test add_private_note_to_ticket with exception during update."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Mock ticket retrieval success but update failure
        mock_ticket = Mock()
        mock_zenpy.return_value.tickets.return_value = mock_ticket
        mock_zenpy.return_value.tickets.update.side_effect = Exception("Update failed")
        
        mock_comment_instance = Mock()
        mock_comment.return_value = mock_comment_instance
        
        # Mock logger opt method
        
        ticket_id = 12345
        note = "This is a private note"
        
        # Should not raise exception
        client.add_private_note_to_ticket(ticket_id, note)
        
        # Verify that ticket retrieval and comment creation still happened
        mock_zenpy.return_value.tickets.assert_called_once_with(id=ticket_id)
        mock_comment.assert_called_once_with(body=note, public=False)

    @patch('zendesk.zendesk_client.Zenpy')
    def test_credentials_immutability(self, mock_zenpy):
        """Test that credentials are properly stored and immutable."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Verify credentials are stored correctly
        original_credentials = client.credentials.copy()
        
        # Try to modify credentials (shouldn't affect the original)
        modified_credentials = client.credentials
        modified_credentials['email'] = '<EMAIL>'
        
        # Original credentials should still be intact for new instances
        client2 = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # The class should have been called with original credentials
        assert mock_zenpy.call_count == 2

    @patch('zendesk.zendesk_client.Zenpy')
    def test_custom_fields_configuration(self, mock_zenpy):
        """Test that custom fields are properly configured."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Test that custom fields can be modified
        client.custom_fields['new_field'] = 'new_value'
        assert client.custom_fields['new_field'] == 'new_value'
        assert client.custom_fields['ticket_form_id'] == '11319272564749'
        
        # Test that modifications affect search query
        mock_zenpy.return_value.search.return_value = []
        since = datetime.now(timezone.utc)
        client.search_new_tasks(since)
        
        call_args = mock_zenpy.return_value.search.call_args
        query = call_args[1]['query']
        assert 'new_field:new_value' in query

    @patch('zendesk.zendesk_client.Zenpy')
    def test_ai_tag_configuration(self, mock_zenpy):
        """Test that AI tag is properly configured."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        # Test default AI tag
        assert client.ai_tag == "[Cymulate AI]"
        
        # Test that AI tag can be modified
        client.ai_tag = "[Custom AI Tag]"
        assert client.ai_tag == "[Custom AI Tag]"

    @patch('zendesk.zendesk_client.Zenpy')
    def test_timezone_handling_in_search(self, mock_zenpy):
        """Test that timezone information is properly handled in search."""
        client = ZendeskClient(
            email=self.email,
            token=self.token,
            subdomain=self.subdomain
        )
        
        mock_zenpy.return_value.search.return_value = []
        
        # Test with different timezone
        since_utc = datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        since_offset = datetime(2024, 1, 1, 12, 0, 0, tzinfo=timezone(timedelta(hours=5)))
        
        client.search_new_tasks(since_utc)
        utc_call = mock_zenpy.return_value.search.call_args
        
        mock_zenpy.return_value.search.reset_mock()
        
        client.search_new_tasks(since_offset)
        offset_call = mock_zenpy.return_value.search.call_args
        
        # Both calls should have properly formatted timezone information
        utc_query = utc_call[1]['query']
        offset_query = offset_call[1]['query']
        
        assert '+0000' in utc_query
        assert '+0500' in offset_query
        assert utc_query != offset_query 