
from datetime import datetime
from zenpy import Zenpy
from zenpy.lib.api_objects import Comment
from requests.exceptions import RequestException

from logger import logger


class ZendeskClient:
    def __init__(self,email:str,token:str,subdomain:str):
        self.credentials = {
            'email': email,
            'token': token,
            'subdomain': subdomain,
        }

        self.custom_fields = {
            'ticket_form_id': '11319272564749',  # customer support form
        }

        self.ai_tag = "[Cymulate AI]"
        self.zenpy_client = Zenpy(**self.credentials)

    def search_new_tasks(self, since: datetime):
        one_hour_ago = since.strftime('%Y-%m-%dT%H:%M:%S%z')
        query = f"type:ticket status:new created>{one_hour_ago}"
        for field_id, field_value in self.custom_fields.items():
            query += f" {field_id}:{field_value}"
        try:
            return self.zenpy_client.search(query=query, sort_by='created_at', sort_order='desc')
        except RequestException as e:
            logger.error(f"Network error during API request: {e}")
            return []

    def get_ticket_comments(self, ticket_id: int):
        try:
            return self.zenpy_client.tickets.comments(ticket=ticket_id)
        except Exception as e:
            logger.error(f"Failed to fetch comments for ticket {ticket_id}: {e}")
            return []

    def add_private_note_to_ticket(self, ticket_id: int, note: str):
        try:
            ticket = self.zenpy_client.tickets(id=ticket_id)
            comment = Comment(body=note, public=False)
            ticket.comment = comment
            self.zenpy_client.tickets.update(ticket)
            logger.info(f"Private note successfully posted on ticket {ticket_id}")
        except Exception as e:
            logger.error(f"Failed to post private note on ticket {ticket_id}: {e}")
