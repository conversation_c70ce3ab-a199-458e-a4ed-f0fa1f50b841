"""
Example module for cymulate.rag.
"""


from .vector_store.base_store import VectorStoreBase
from .vector_store.mongo_store import MongoDBVectorStore
from .vector_store.milvus_store import MilvusVectorStore
from typing import Any
from langchain_core.embeddings import Embeddings

class VectorStoreFactory:
    """Factory for creating vector stores."""
    
    @staticmethod
    def get_vector_store(store_type: str = "mongodb", client: Any = None, db_name: str = None, embedding_model: Embeddings = None) -> VectorStoreBase:
        """
        Get a vector store instance based on the store type.
        
        Args:
            store_type: Type of vector store ("mongodb" or "milvus")
            **kwargs: Additional arguments for the vector store
            
        Returns:
            VectorStoreBase instance
        """
        if store_type.lower() == "mongodb":
            return MongoDBVectorStore(client, db_name,embedding_model)
        elif store_type.lower() == "milvus":
            return MilvusVectorStore(client, db_name,embedding_model)
        else:
            raise ValueError(f"Unsupported vector store type: {store_type}")
