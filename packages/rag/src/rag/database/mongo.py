from pymongo import MongoClient


def get_mongo_client(connection_string: str):
    """
    Initialize and return a MongoDB client connection.
    
    Returns:
        MongoClient or None: Configured MongoDB client or None if connection fails
    """
    if not connection_string:
        return None
        
    try:
        client = MongoClient(connection_string)
        # Test connection
        client.admin.command('ping')
        return client
    except Exception as e:
        print(f"Failed to initialize MongoDB client: {str(e)}")
        return None



def init_db(client: MongoClient, db_name: str, collection_name: str):
    """
    Initialize a MongoDB database.
    """

    # Get database
    db = client[db_name]
    
    # Drop existing collection if exists
    if collection_name in db.list_collection_names():
        db[collection_name].drop()
    else:
        db.create_collection(collection_name)
