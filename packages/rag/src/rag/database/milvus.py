from pydantic import BaseModel, <PERSON>
from typing import Optional

from pymilvus import MilvusClient, MilvusException, Collection, utility

class MilvusClientArgs(BaseModel):
    uri: str = Field(default="http://localhost:19530",description="The URI of the Milvus server")
    user: str = Field(default="",description="The username for the Milvus server")
    password: str = Field(default="",description="The password for the Milvus server")
    db_name: str = Field(default="",description="The name of the Milvus database")
    token: str = Field(default="",description="The token for the Milvus server")
    timeout: Optional[float] = Field(default=None,description="The timeout for the Milvus server")




def get_milvus_client(args: MilvusClientArgs):
    return MilvusClient(**args.model_dump())


def init_db(client: MilvusClient, db_name: str, collection_name: str):

    """
    Initialize a Milvus database.
    """

    try:
        existing_databases = client.list_databases()
        if db_name in existing_databases:
            print(f"Database '{db_name}' already exists.")

            # Use the database context
            client.using_database(db_name)

            # Drop all collections in the database
            collections = client.list_collections()
            if collection_name in collections:
                client.drop_collection(collection_name)
                print(f"Collection '{collection_name}' has been dropped.")
            else:
                print(f"Collection '{collection_name}' does not exist.")
                client.create_collection(collection_name)
                print(f"Collection '{collection_name}' created successfully.")

            client.drop_database(db_name)
            print(f"Database '{db_name}' has been deleted.")
        else:
            print(f"Database '{db_name}' does not exist.")
            client.create_database(db_name)
            print(f"Database '{db_name}' created successfully.")
    except MilvusException as e:
        print(f"An error occurred: {e}")