{"name": "langraph-redis-checkpointer", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "packages/langraph-redis-checkpointer/src", "targets": {"lock": {"executor": "@nxlv/python:lock", "options": {"update": false}}, "sync": {"executor": "@nxlv/python:sync", "options": {}}, "add": {"executor": "@nxlv/python:add", "options": {}}, "update": {"executor": "@nxlv/python:update", "options": {}}, "remove": {"executor": "@nxlv/python:remove", "options": {}}, "build": {"executor": "@nxlv/python:build", "outputs": ["{projectRoot}/dist"], "options": {"outputPath": "{projectRoot}/dist", "publish": false, "lockedVersions": true, "bundleLocalDependencies": true}, "cache": true}, "lint": {"executor": "@nxlv/python:ruff-check", "outputs": [], "options": {"lintFilePatterns": ["langraph-redis-checkpointer", "tests"]}, "cache": true}, "format": {"executor": "@nxlv/python:ruff-format", "outputs": [], "options": {"filePatterns": ["langraph-redis-checkpointer", "tests"]}, "cache": true}, "test": {"executor": "@nxlv/python:run-commands", "outputs": ["{workspaceRoot}/reports/{projectRoot}/unittests", "{workspaceRoot}/coverage/{projectRoot}"], "options": {"command": "uv run pytest tests/", "cwd": "{projectRoot}"}, "cache": true}, "install": {"executor": "@nxlv/python:install", "options": {"silent": false, "args": "", "verbose": false, "debug": false}}}, "tags": [], "release": {"version": {"versionActions": "@nxlv/python/src/release/version-actions"}}}