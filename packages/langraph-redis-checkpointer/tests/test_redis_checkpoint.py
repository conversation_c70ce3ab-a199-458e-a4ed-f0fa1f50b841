import asyncio
from unittest.mock import AsyncMock, patch

import pytest

from langraph_redis_checkpointer.redis_checkpoint import (
    AsyncRedisSaver,
    RedisSaver,
    _make_redis_checkpoint_key,
    _make_redis_checkpoint_writes_key,
    _parse_redis_checkpoint_key,
    _parse_redis_checkpoint_writes_key,
    _sanitize_redis_key_part,
    _unsanitize_redis_key_part,
)


def test_sanitize_and_unsanitize():
    s = "a:b__c"
    sanitized = _sanitize_redis_key_part(s)
    unsanitized = _unsanitize_redis_key_part(sanitized)
    assert unsanitized == s

def test_make_and_parse_checkpoint_key():
    key = _make_redis_checkpoint_key("tid", "ns", "cid")
    parsed = _parse_redis_checkpoint_key(key)
    assert parsed["thread_id"] == "tid"
    assert parsed["checkpoint_ns"] == "ns"
    assert parsed["checkpoint_id"] == "cid"

def test_make_and_parse_writes_key():
    key = _make_redis_checkpoint_writes_key("tid", "ns", "cid", "task", 1)
    parsed = _parse_redis_checkpoint_writes_key(key)
    assert parsed["thread_id"] == "tid"
    assert parsed["checkpoint_ns"] == "ns"
    assert parsed["checkpoint_id"] == "cid"
    assert parsed["task_id"] == "task"
    assert parsed["idx"] == "1"
    # No idx
    key2 = _make_redis_checkpoint_writes_key("tid", "ns", "cid", "task", None)
    parsed2 = _parse_redis_checkpoint_writes_key(key2)
    assert parsed2["idx"] is None

def test_parse_checkpoint_key_invalid():
    with pytest.raises(ValueError):
        _parse_redis_checkpoint_key("bad:key")
    with pytest.raises(ValueError):
        _parse_redis_checkpoint_key("notcheckpoint:tid:ns:cid")

def test_parse_writes_key_invalid():
    with pytest.raises(ValueError):
        _parse_redis_checkpoint_writes_key("bad:key")
    with pytest.raises(ValueError):
        _parse_redis_checkpoint_writes_key("notwrites:tid:ns:cid:task")

class DummySerde:
    def dumps_typed(self, obj):
        return ("type", b"serialized")
    def dumps(self, obj):
        return b"metadata"
    def loads_typed(self, tup):
        return {"loaded": True}
    def loads(self, data):
        if isinstance(data, bytes):
            data = data.decode()
        return {"loaded_metadata": True}

class DummyConn:
    def __init__(self):
        self._data = {}
        self._expire = {}
    def hset(self, key, mapping):
        if isinstance(key, str):
            key = key.encode()
        # Store mapping keys as bytes, values as bytes if they're strings
        self._data[key] = {
            (k.encode() if isinstance(k, str) else k): (v.encode() if isinstance(v, str) else v) 
            for k, v in mapping.items()
        }
    def hsetnx(self, key, field, value):
        if isinstance(key, str):
            key = key.encode()
        if isinstance(field, str):
            field = field.encode()
        if isinstance(value, str):
            value = value.encode()
        if key not in self._data:
            self._data[key] = {}
        if field not in self._data[key]:
            self._data[key][field] = value
    def expire(self, key, ttl):
        if isinstance(key, str):
            key = key.encode()
        self._expire[key] = ttl
    def hgetall(self, key):
        if isinstance(key, str):
            key = key.encode()
        return self._data.get(key, {})
    def keys(self, pattern):
        # Simple pattern matching - if pattern ends with *, match prefix
        if isinstance(pattern, str):
            pattern = pattern.encode()
        if pattern.endswith(b'*'):
            prefix = pattern[:-1]
            return [k for k in self._data.keys() if k.startswith(prefix)]
        else:
            # Exact match
            return [k for k in self._data.keys() if k == pattern]
    def close(self):
        pass

# Minimal config for testing
config = {"configurable": {"thread_id": "tid", "checkpoint_ns": "ns", "checkpoint_id": "cid"}}

def test_redis_saver_put_and_get_tuple():
    conn = DummyConn()
    saver = RedisSaver(conn)
    saver.serde = DummySerde()
    checkpoint = {"id": "cid"}
    metadata = {"meta": 1}
    new_versions = None
    # Test put
    updated_config = saver.put(config, checkpoint, metadata, new_versions)
    assert updated_config["configurable"]["checkpoint_id"] == "cid"
    # Test get_tuple
    result = saver.get_tuple(config)
    assert result is not None

def test_redis_saver_list():
    conn = DummyConn()
    saver = RedisSaver(conn)
    saver.serde = DummySerde()
    checkpoint = {"id": "cid"}
    metadata = {"meta": 1}
    new_versions = None
    saver.put(config, checkpoint, metadata, new_versions)
    # Should yield at least one tuple
    results = list(saver.list(config))
    assert results 

def test_redis_saver_put_writes():
    conn = DummyConn()
    saver = RedisSaver(conn)
    saver.serde = DummySerde()
    config_with_checkpoint = {"configurable": {"thread_id": "tid", "checkpoint_ns": "ns", "checkpoint_id": "cid"}}
    writes = [("channel1", "value1"), ("channel2", "value2")]
    task_id = "task123"
    
    # Test put_writes
    saver.put_writes(config_with_checkpoint, writes, task_id)
    # Should not raise an error
    assert True


def test_redis_saver_get_tuple_no_checkpoint():
    conn = DummyConn()
    saver = RedisSaver(conn)
    saver.serde = DummySerde()
    config_empty = {"configurable": {"thread_id": "nonexistent", "checkpoint_ns": "ns"}}
    
    # Test get_tuple with no checkpoint
    result = saver.get_tuple(config_empty)
    assert result is None


def test_redis_saver_list_empty():
    conn = DummyConn()
    saver = RedisSaver(conn)
    saver.serde = DummySerde()
    config_empty = {"configurable": {"thread_id": "nonexistent", "checkpoint_ns": "ns"}}
    
    # Test list with no checkpoints
    results = list(saver.list(config_empty))
    assert results == []


def test_redis_saver_with_ttl():
    conn = DummyConn()
    saver = RedisSaver(conn, ttl=3600)
    saver.serde = DummySerde()
    checkpoint = {"id": "cid"}
    metadata = {"meta": 1}
    new_versions = None
    
    # Test put with TTL
    updated_config = saver.put(config, checkpoint, metadata, new_versions)
    assert updated_config["configurable"]["checkpoint_id"] == "cid"
    # Check that expire was called
    assert len(conn._expire) > 0


def test_redis_saver_from_conn_info():
    # Test the context manager (will fail to connect but we test the structure)
    try:
        with RedisSaver.from_conn_info(host="localhost", port=6379, db=0) as saver:
            assert isinstance(saver, RedisSaver)
    except Exception:
        # Expected to fail since no real Redis, but we tested the structure
        pass


def test_filter_keys():
    from langraph_redis_checkpointer.redis_checkpoint import _filter_keys
    keys = [b"checkpoint:tid:ns:cid1", b"checkpoint:tid:ns:cid2", b"checkpoint:tid:ns:cid3"]
    
    # Test without filters
    result = _filter_keys(keys, None, None)
    assert len(result) == 3
    
    # Test with limit
    result = _filter_keys(keys, None, 2)
    assert len(result) == 2


def test_load_writes():
    from langraph_redis_checkpointer.redis_checkpoint import _load_writes
    serde = DummySerde()
    task_data = {
        ("task1", "0"): {b"channel": b"ch1", b"type": b"str", b"value": b"val1"},
        ("task2", "1"): {b"channel": b"ch2", b"type": b"str", b"value": b"val2"}
    }
    
    result = _load_writes(serde, task_data)
    assert len(result) == 2


def test_parse_redis_checkpoint_data():
    from langraph_redis_checkpointer.redis_checkpoint import (
        _parse_redis_checkpoint_data,
    )
    serde = DummySerde()
    key = "checkpoint:tid:ns:cid"
    data = {b"checkpoint": b"data", b"metadata": b"meta", b"type": b"dict"}
    
    result = _parse_redis_checkpoint_data(serde, key, data)
    assert result is not None
    
    # Test with empty data
    result = _parse_redis_checkpoint_data(serde, key, {})
    assert result is None 

class DummyAsyncConn:
    def __init__(self):
        self._data = {}
        self._expire = {}
    
    async def hset(self, key, mapping):
        if isinstance(key, str):
            key = key.encode()
        self._data[key] = {
            (k.encode() if isinstance(k, str) else k): (v.encode() if isinstance(v, str) else v) 
            for k, v in mapping.items()
        }
    
    async def hsetnx(self, key, field, value):
        if isinstance(key, str):
            key = key.encode()
        if isinstance(field, str):
            field = field.encode()
        if isinstance(value, str):
            value = value.encode()
        if key not in self._data:
            self._data[key] = {}
        if field not in self._data[key]:
            self._data[key][field] = value
    
    async def expire(self, key, ttl):
        if isinstance(key, str):
            key = key.encode()
        self._expire[key] = ttl
    
    async def hgetall(self, key):
        if isinstance(key, str):
            key = key.encode()
        return self._data.get(key, {})
    
    async def keys(self, pattern):
        if isinstance(pattern, str):
            pattern = pattern.encode()
        if pattern.endswith(b'*'):
            prefix = pattern[:-1]
            return [k for k in self._data.keys() if k.startswith(prefix)]
        else:
            return [k for k in self._data.keys() if k == pattern]
    
    async def aclose(self):
        pass
    
    async def ping(self):
        return True


def test_async_redis_saver_init():
    conn = DummyAsyncConn()
    saver = AsyncRedisSaver(conn, ttl=3600)
    assert saver.conn == conn
    assert saver.ttl == 3600


@patch('langraph_redis_checkpointer.redis_checkpoint.AsyncRedis')
def test_get_redis_checkpointer(mock_redis):
    # Mock the AsyncRedis class
    mock_conn = AsyncMock()
    mock_redis.return_value = mock_conn
    
    async def test_func():
        try:
            async with AsyncRedisSaver.from_conn_info(host="localhost", port=6379, db=0) as saver:
                assert isinstance(saver, AsyncRedisSaver)
        except Exception:
            # Expected since we're mocking
            pass
    
    # Run the async test
    asyncio.run(test_func())


def test_async_redis_saver_methods():
    conn = DummyAsyncConn()
    saver = AsyncRedisSaver(conn)
    saver.serde = DummySerde()
    
    async def test_async_methods():
        # Test aput
        checkpoint = {"id": "cid"}
        metadata = {"meta": 1}
        new_versions = None
        config_test = {"configurable": {"thread_id": "tid", "checkpoint_ns": "ns", "checkpoint_id": "cid"}}
        
        result = await saver.aput(config_test, checkpoint, metadata, new_versions)
        assert result["configurable"]["checkpoint_id"] == "cid"
        
        # Test aput_writes
        writes = [("channel1", "value1"), ("channel2", "value2")]
        task_id = "task123"
        await saver.aput_writes(config_test, writes, task_id)
        
        # Test aget_tuple
        tuple_result = await saver.aget_tuple(config_test)
        assert tuple_result is not None
        
        # Test alist
        async for item in saver.alist(config_test):
            assert item is not None
            break  # Just test that it yields something
    
    # Run the async test
    asyncio.run(test_async_methods())


def test_edge_cases():
    # Test with parent checkpoint ID
    conn = DummyConn()
    saver = RedisSaver(conn)
    saver.serde = DummySerde()
    config_with_parent = {
        "configurable": {
            "thread_id": "tid", 
            "checkpoint_ns": "ns", 
            "checkpoint_id": "parent_cid"
        }
    }
    checkpoint = {"id": "cid"}
    metadata = {"meta": 1}
    new_versions = None
    
    # Test put with parent checkpoint
    result = saver.put(config_with_parent, checkpoint, metadata, new_versions)
    assert result["configurable"]["checkpoint_id"] == "cid"


def test_list_with_filters():
    conn = DummyConn()
    saver = RedisSaver(conn)
    saver.serde = DummySerde()
    
    # Add multiple checkpoints
    for i in range(3):
        config_i = {"configurable": {"thread_id": "tid", "checkpoint_ns": "ns", "checkpoint_id": f"cid{i}"}}
        checkpoint = {"id": f"cid{i}"}
        metadata = {"meta": i}
        saver.put(config_i, checkpoint, metadata, None)
    
    base_config = {"configurable": {"thread_id": "tid", "checkpoint_ns": "ns"}}
    
    # Test list with limit
    results = list(saver.list(base_config, limit=2))
    assert len(results) <= 2
    
    # Test list with before filter
    before_config = {"configurable": {"checkpoint_id": "cid2"}}
    results = list(saver.list(base_config, before=before_config))
    # Should return checkpoints before cid2 