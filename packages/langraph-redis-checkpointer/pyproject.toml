[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "langraph-redis-checkpointer"
version = "0.1.7"
description = "Redis-based checkpointer for LangGraph workflows"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
]
license = { text = "Proprietary" }
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.13",
    "License :: Other/Proprietary License",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = [
    "redis>=5.0.1",
    "langgraph==0.6.2",
    "langchain-core==0.3.72",
    "typing-extensions>=4.7.0",
]

[tool.ruff]
select = ["I", "F401"]


[project.optional-dependencies]
# Add optional dependencies here if needed
# example = [
#     "package>=1.0.0",
# ]

[project.urls]
"Homepage" = "https://bitbucket.org/cymulate/pythonlibs"
"Bug Tracker" = "https://bitbucket.org/cymulate/pythonlibs/jira"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]
namespaces = true

# Use the same formatting and linting configuration as the root project
[tool.black]
line-length = 88
target-version = ["py38", "py39", "py310", "py311", "py312", "py313"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true


[tool.pytest.ini_options]
addopts = "--cov=langraph_redis_checkpointer --cov-report=term-missing"
