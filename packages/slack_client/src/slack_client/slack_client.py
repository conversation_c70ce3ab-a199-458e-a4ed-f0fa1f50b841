from slack_bolt.adapter.socket_mode.aiohttp import Async<PERSON>ocketModeHandler
from slack_bolt.app.async_app import Async<PERSON>pp
from slack_sdk.web.async_client import Async<PERSON>eb<PERSON>lient


from logger import logger



class SlackClient:
    def __init__(self,bot_token:str,app_token:str):
        self.app = AsyncApp(token=bot_token)
        self.client: AsyncWebClient = self.app.client
        self.app_token = app_token

    async def start(self):
        await AsyncSocketModeHandler(
            app=self.app,
            app_token=self.app_token,
            ping_interval=60,
            web_client=self.client
        ).start_async()

    async def post_message(self, channel: str, text: str, thread_ts: str = None) -> dict:
        try:
            response = await self.client.chat_postMessage(
                channel=channel,
                text=text,
                thread_ts=thread_ts
            )
            logger.info(f"Posted message to channel {channel} with text: {text}")
            return response.data
        except Exception as e:
            logger.error(f"Failed to post message to channel {channel}. Error: {e}")
            raise

    async def update_message(self, channel: str, ts: str, text: str) -> dict:
        try:
            response = await self.client.chat_update(
                channel=channel,
                ts=ts,
                text=text
            )
            logger.info(f"Updated message in channel {channel} at {ts} with new text: {text}")
            return response.data
        except Exception as e:
            logger.error(f"Failed to update message in channel {channel} at {ts}. Error: {e}")
            raise

    async def delete_message(self, channel: str, ts: str) -> dict:
        try:
            response = await self.client.chat_delete(
                channel=channel,
                ts=ts
            )
            logger.info(f"Deleted message in channel {channel} at {ts}")
            return response.data
        except Exception as e:
            logger.error(f"Failed to delete message in channel {channel} at {ts}. Error: {e}")
            raise

    async def get_user_info(self, user: str) -> dict:
        try:
            response = await self.client.users_info(user=user)
            logger.info(f"Retrieved user info for user {user}")
            return response.data
        except Exception as e:
            logger.error(f"Failed to retrieve user info for user {user}. Error: {e}")
            raise

    async def get_channel_info(self, channel: str) -> dict:
        try:
            response = await self.client.conversations_info(channel=channel)
            logger.info(f"Retrieved channel info for channel {channel}")
            return response.data
        except Exception as e:
            logger.error(f"Failed to retrieve channel info for channel {channel}. Error: {e}")
            raise
