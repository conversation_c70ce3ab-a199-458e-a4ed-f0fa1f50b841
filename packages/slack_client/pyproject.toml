[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "slack_client"
version = "0.1.7"
description = "SLACK package for MAS"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "reouven<PERSON>@cymulate.com" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
]
license = { text = "Proprietary" }
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "License :: Other/Proprietary License",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = [
    # Add package dependencies here
    "slack-bolt==1.23.0",
    "slack-sdk==3.35.0",
    "logger",

]

[tool.ruff]
select = ["I", "F401"]

[dependency-groups]
dev = ["pytest-asyncio>=0.23.0"]

[project.optional-dependencies]
# Add optional dependencies here if needed
# example = [
#     "package>=1.0.0",
# ]

[project.urls]
"Homepage" = "https://bitbucket.org/cymulate/pythonlibs"
"Bug Tracker" = "https://bitbucket.org/cymulate/pythonlibs/jira"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]
namespaces = true

# Use the same formatting and linting configuration as the root project
[tool.black]
line-length = 88
target-version = ["py312"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.uv.sources]
logger = { workspace = true }

[tool.pytest.ini_options]
addopts = "--cov=slack_client --cov-report=term-missing --cov-fail-under=80"
asyncio_mode = "auto"
