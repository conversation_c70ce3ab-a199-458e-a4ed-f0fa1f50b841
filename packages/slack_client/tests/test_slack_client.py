"""
Tests for SlackClient class.
"""

import os
import sys
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

# Add the src directory to the path to import our local slack module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from slack_client import SlackClient


class TestSlackClient:
    """Test cases for SlackClient."""

    @patch('slack_client.slack_client.AsyncApp')
    def test_init(self, mock_app):
        """Test SlackClient initialization."""
        mock_app_instance = Mock()
        mock_client = Mock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        bot_token = "xoxb-test-token"
        app_token = "xapp-test-token"
        
        client = SlackClient(bot_token, app_token)
        
        # Check that Async<PERSON>pp was initialized with correct token
        mock_app.assert_called_once_with(token=bot_token)
        
        # Check that attributes are set correctly
        assert client.app == mock_app_instance
        assert client.client == mock_client
        assert client.app_token == app_token

    @patch('slack_client.slack_client.AsyncSocketModeHandler')
    @patch('slack_client.slack_client.AsyncApp')
    async def test_start(self, mock_app, mock_handler):
        """Test starting the Slack client."""
        mock_app_instance = Mock()
        mock_client = Mock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        mock_handler_instance = AsyncMock()
        mock_handler.return_value = mock_handler_instance
        
        client = SlackClient("bot_token", "app_token")
        
        await client.start()
        
        # Check that AsyncSocketModeHandler was created with correct parameters
        mock_handler.assert_called_once_with(
            app=mock_app_instance,
            app_token="app_token",
            ping_interval=60,
            web_client=mock_client
        )
        
        # Check that start_async was called
        mock_handler_instance.start_async.assert_called_once()

    @patch('slack_client.slack_client.AsyncApp')
    async def test_post_message_success(self, mock_app):
        """Test successful message posting."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock successful response
        mock_response = Mock()
        mock_response.data = {"ok": True, "ts": "1234567890.123456"}
        mock_client.chat_postMessage.return_value = mock_response
        
        client = SlackClient("bot_token", "app_token")
        
        result = await client.post_message("C1234567890", "Hello, World!")
        
        # Check that chat_postMessage was called with correct parameters
        mock_client.chat_postMessage.assert_called_once_with(
            channel="C1234567890",
            text="Hello, World!",
            thread_ts=None
        )
        
        # Check return value
        assert result == {"ok": True, "ts": "1234567890.123456"}

    @patch('slack_client.slack_client.AsyncApp')
    async def test_post_message_with_thread(self, mock_app):
        """Test posting message in thread."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock successful response
        mock_response = Mock()
        mock_response.data = {"ok": True, "ts": "1234567890.123456"}
        mock_client.chat_postMessage.return_value = mock_response
        
        client = SlackClient("bot_token", "app_token")
        
        result = await client.post_message("C1234567890", "Reply", thread_ts="1234567890.000000")
        
        # Check that chat_postMessage was called with thread_ts
        mock_client.chat_postMessage.assert_called_once_with(
            channel="C1234567890",
            text="Reply",
            thread_ts="1234567890.000000"
        )
        
        assert result == {"ok": True, "ts": "1234567890.123456"}

    @patch('slack_client.slack_client.logger')
    @patch('slack_client.slack_client.AsyncApp')
    async def test_post_message_failure(self, mock_app, mock_logger):
        """Test message posting failure."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock exception
        mock_client.chat_postMessage.side_effect = Exception("API Error")
        
        client = SlackClient("bot_token", "app_token")
        
        with pytest.raises(Exception, match="API Error"):
            await client.post_message("C1234567890", "Hello, World!")

    @patch('slack_client.slack_client.AsyncApp')
    async def test_update_message_success(self, mock_app):
        """Test successful message update."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock successful response
        mock_response = Mock()
        mock_response.data = {"ok": True, "ts": "1234567890.123456"}
        mock_client.chat_update.return_value = mock_response
        
        client = SlackClient("bot_token", "app_token")
        
        result = await client.update_message("C1234567890", "1234567890.123456", "Updated text")
        
        # Check that chat_update was called with correct parameters
        mock_client.chat_update.assert_called_once_with(
            channel="C1234567890",
            ts="1234567890.123456",
            text="Updated text"
        )
        
        assert result == {"ok": True, "ts": "1234567890.123456"}

    @patch('slack_client.slack_client.logger')
    @patch('slack_client.slack_client.AsyncApp')
    async def test_update_message_failure(self, mock_app, mock_logger):
        """Test message update failure."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock exception
        mock_client.chat_update.side_effect = Exception("Update failed")
        
        client = SlackClient("bot_token", "app_token")
        
        with pytest.raises(Exception, match="Update failed"):
            await client.update_message("C1234567890", "1234567890.123456", "Updated text")

    @patch('slack_client.slack_client.AsyncApp')
    async def test_delete_message_success(self, mock_app):
        """Test successful message deletion."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock successful response
        mock_response = Mock()
        mock_response.data = {"ok": True}
        mock_client.chat_delete.return_value = mock_response
        
        client = SlackClient("bot_token", "app_token")
        
        result = await client.delete_message("C1234567890", "1234567890.123456")
        
        # Check that chat_delete was called with correct parameters
        mock_client.chat_delete.assert_called_once_with(
            channel="C1234567890",
            ts="1234567890.123456"
        )
        
        assert result == {"ok": True}

    @patch('slack_client.slack_client.logger')
    @patch('slack_client.slack_client.AsyncApp')
    async def test_delete_message_failure(self, mock_app, mock_logger):
        """Test message deletion failure."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock exception
        mock_client.chat_delete.side_effect = Exception("Delete failed")
        
        client = SlackClient("bot_token", "app_token")
        
        with pytest.raises(Exception, match="Delete failed"):
            await client.delete_message("C1234567890", "1234567890.123456")

    @patch('slack_client.slack_client.AsyncApp')
    async def test_get_user_info_success(self, mock_app):
        """Test successful user info retrieval."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock successful response
        mock_response = Mock()
        mock_response.data = {
            "ok": True,
            "user": {
                "id": "U1234567890",
                "name": "testuser",
                "real_name": "Test User"
            }
        }
        mock_client.users_info.return_value = mock_response
        
        client = SlackClient("bot_token", "app_token")
        
        result = await client.get_user_info("U1234567890")
        
        # Check that users_info was called with correct parameters
        mock_client.users_info.assert_called_once_with(user="U1234567890")
        
        assert result["user"]["id"] == "U1234567890"
        assert result["user"]["name"] == "testuser"

    @patch('slack_client.slack_client.logger')
    @patch('slack_client.slack_client.AsyncApp')
    async def test_get_user_info_failure(self, mock_app, mock_logger):
        """Test user info retrieval failure."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock exception
        mock_client.users_info.side_effect = Exception("User not found")
        
        client = SlackClient("bot_token", "app_token")
        
        with pytest.raises(Exception, match="User not found"):
            await client.get_user_info("U1234567890")

    @patch('slack_client.slack_client.AsyncApp')
    async def test_get_channel_info_success(self, mock_app):
        """Test successful channel info retrieval."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock successful response
        mock_response = Mock()
        mock_response.data = {
            "ok": True,
            "channel": {
                "id": "C1234567890",
                "name": "general",
                "is_channel": True
            }
        }
        mock_client.conversations_info.return_value = mock_response
        
        client = SlackClient("bot_token", "app_token")
        
        result = await client.get_channel_info("C1234567890")
        
        # Check that conversations_info was called with correct parameters
        mock_client.conversations_info.assert_called_once_with(channel="C1234567890")
        
        assert result["channel"]["id"] == "C1234567890"
        assert result["channel"]["name"] == "general"

    @patch('slack_client.slack_client.logger')
    @patch('slack_client.slack_client.AsyncApp')
    async def test_get_channel_info_failure(self, mock_app, mock_logger):
        """Test channel info retrieval failure."""
        mock_app_instance = Mock()
        mock_client = AsyncMock()
        mock_app_instance.client = mock_client
        mock_app.return_value = mock_app_instance
        
        # Mock exception
        mock_client.conversations_info.side_effect = Exception("Channel not found")
        
        client = SlackClient("bot_token", "app_token")
        
        with pytest.raises(Exception, match="Channel not found"):
            await client.get_channel_info("C1234567890") 