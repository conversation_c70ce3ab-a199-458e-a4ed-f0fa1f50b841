
from fastapi import Request
import uuid
from .logger import logger as log  # Import our custom logger functions
import contextvars

# Create a request-scoped context variable for correlation ID
request_correlation_id = contextvars.ContextVar('request_correlation_id', default=None)

async def correlation_id_middleware(request: Request, call_next):
    """Middleware to handle correlation ID and request context for HTTP requests."""

    if request.url.path == "/health":
        return await call_next(request)
    
    # Try to get correlation ID from header, or generate a new one
    correlation_id = request.headers.get('X-Correlation-ID')
    
    # Always ensure we have a valid correlation ID
    if not correlation_id or correlation_id.lower() == 'none' or correlation_id.lower() == 'null':
        correlation_id = str(uuid.uuid4())
        log.info(f"🆔 Generated new correlation ID: {correlation_id}")
    
    # Store the correlation ID in the request state for other middleware/handlers to access
    request.state.correlation_id = correlation_id
    
    # Set the correlation ID in the context variable for this request
    token = request_correlation_id.set(correlation_id)
    
    # Create context with correlation ID and request info
    context = {
        'method': request.method,
        'path': request.url.path,
        'request_id': str(uuid.uuid4()),
        'client_ip': request.client.host if request.client else None
    }
    
    # Filter out None values and format context
    context = {k: v for k, v in context.items() if v is not None}
    ctx_str = " | ".join(f"{k}={v}" for k, v in context.items()) if context else ""
    
    # Use the context manager to ensure thread safety
    with log.contextualize(correlation_id=correlation_id, context=ctx_str):
        log.info(f"🌐 Incoming request to {request.method} {request.url.path}")
        
        try:
            # Process the request
            response = await call_next(request)
            
            # Add correlation ID to response headers
            response.headers['X-Correlation-ID'] = correlation_id
            
            log.info(f"🏁 Request completed with status {response.status_code}")
            
            # Reset the context variable
            request_correlation_id.reset(token)
            
            return response
            
        except Exception as e:
            log.exception(f"💥 Request failed: {str(e)}")
            # Reset the context variable even if there's an error
            request_correlation_id.reset(token)
            raise 