import traceback
import uuid
from typing import Optional, Dict, Any
from loguru import logger as _logger
import sys
import os
from contextlib import contextmanager
from functools import wraps
import asyncio
from pathlib import Path
import contextvars
import re

# Create a context variable for correlation ID
correlation_id_var = contextvars.ContextVar('correlation_id', default='')

# Remove default handler
_logger.remove()

# Define a patcher function to format correlation_id and context
def format_extra_fields(record):
    """Format correlation_id and context to be displayed only if they exist."""
    try:
        # Robustesse : si extra n'est pas un dict, on le remplace
        if not isinstance(record.get("extra"), dict):
            record["extra"] = {}
        # Format correlation_id
        if record["extra"].get("correlation_id"):
            record["extra"]["correlation_id_display"] = f"correlation_id={record['extra']['correlation_id']} | "
        else:
            record["extra"]["correlation_id_display"] = ""
        
        # Format context as a JSON-like object
        if record["extra"].get("context"):
            context_str = record["extra"]["context"]
            
            # Special case: if context itself contains "context=" (nested context)
            if context_str.startswith("context="):
                # Extract the inner context value
                inner_context = context_str[8:]  # Remove "context=" prefix
                
                # If it looks like a JSON object, process it
                if inner_context.startswith("{") and inner_context.endswith("}"):
                    # Just use the inner context directly
                    record["extra"]["context_display"] = f"{context_str} | "
                    return record
            
            # If context already contains key=value pairs, format them nicely
            if "=" in context_str:
                # Split by pipe and trim whitespace
                parts = [part.strip() for part in context_str.split("|")]
                
                # Process each part to truncate base64 values
                processed_parts = []
                for part in parts:
                    if "=" in part:
                        key, value = part.split("=", 1)
                        
                        # Check for data URLs
                        if value.startswith("data:") and ";base64," in value:
                            prefix_end = value.index(";base64,") + 8
                            prefix = value[:prefix_end]
                            base64_content = value[prefix_end:]
                            if len(base64_content) > 30:
                                value = prefix + base64_content[:20] + "..." + base64_content[-10:] + " (truncated base64)"
                        # Check if value looks like base64 (long string with base64 characters)
                        elif len(value) > 100 and re.match(r'^[A-Za-z0-9+/=]+$', value):
                            # Truncate base64 value
                            value = value[:20] + "..." + value[-10:] + " (truncated base64)"
                        
                        processed_parts.append(f"{key}={value}")
                    else:
                        processed_parts.append(part)
                
                # Format as JSON-like object
                formatted_context = "{" + ", ".join(processed_parts) + "}"
                record["extra"]["context_display"] = f"context={formatted_context} | "
            else:
                # Just wrap the context in quotes
                record["extra"]["context_display"] = f"context=\"{context_str}\" | "
        else:
            record["extra"]["context_display"] = ""
        return record
    except Exception:
        # If any error occurs, set empty display values and return the record
        record["extra"]["correlation_id_display"] = ""
        record["extra"]["context_display"] = ""
        return record

def truncate_base64(message):
    """Truncate base64 encoded strings in log messages to save space."""
    if not isinstance(message, str):
        return message
        
    try:
        # Handle data URLs (e.g., data:image/png;base64,...)
        data_url_pattern = r'(data:[^;]+;base64,)([A-Za-z0-9+/=]{100,})'
        message = re.sub(data_url_pattern, lambda m: f"{m.group(1)}{m.group(2)[:20]}...{m.group(2)[-10:]} (truncated base64)", message)
        
        # Handle JSON fields with base64 content
        json_base64_pattern = r'("[^"]+"\s*:\s*")((?:[A-Za-z0-9+/=]{4})*(?:[A-Za-z0-9+/=]{2}==|[A-Za-z0-9+/=]{3}=)?)"'
        
        def process_json_match(match):
            key = match.group(1)
            value = match.group(2)
            if len(value) > 100:  # Only truncate long strings
                return f"{key}{value[:20]}...{value[-10:]} (truncated base64)\""
            return f"{key}{value}\""
        
        message = re.sub(json_base64_pattern, process_json_match, message)
        
        # Handle CampaignInfoData and other objects with base64 content
        # This pattern looks for base64 content in various object representations
        object_base64_pattern = r'((?:CampaignInfoData|[A-Za-z]+Data)\([^=]+=)([A-Za-z0-9+/=]{100,})([,\)])'
        
        def process_object_match(match):
            prefix = match.group(1)
            value = match.group(2)
            suffix = match.group(3)
            return f"{prefix}{value[:20]}...{value[-10:]} (truncated base64){suffix}"
        
        message = re.sub(object_base64_pattern, process_object_match, message)
        
        # Handle standalone base64 strings (not in quotes or other structures)
        # This is more aggressive, so we need to be careful
        standalone_base64_pattern = r'(?<![a-zA-Z0-9+/="])([A-Za-z0-9+/=]{100,})(?![a-zA-Z0-9+/="])'
        
        def process_standalone_match(match):
            value = match.group(1)
            # Additional check to ensure it's likely base64 (multiple of 4 length or proper padding)
            if (len(value) % 4 == 0 or value.endswith('=') or value.endswith('==')) and re.match(r'^[A-Za-z0-9+/=]+$', value):
                return f"{value[:20]}...{value[-10:]} (truncated base64)"
            return value
        
        message = re.sub(standalone_base64_pattern, process_standalone_match, message)
        
        return message
    except Exception as e:
        # If any error occurs during processing, return the original message
        # This ensures logging still works even if our processing fails
        return message

def process_log_message(message):
    """Process log message before sending to output."""
    try:
        # Truncate base64 content in the message
        record = message.record
        record["message"] = truncate_base64(record["message"])
        
        # Format extra fields
        record = format_extra_fields(record)
        
        # Return the processed message
        return message
    except Exception:
        # If any error occurs, return the original message unmodified
        # This ensures logging still works even if our processing fails
        return message

# Define a custom formatter function
def formatter(record):
    # Process the record
    try:
        # Truncate base64 in message
        record["message"] = truncate_base64(record["message"])
        
        # Add file and line information
        file_info = f"{record['file']}:{record['line']}"
        record["extra"]["file_line"] = file_info
        
        # Format correlation_id
        if record["extra"].get("correlation_id"):
            record["extra"]["correlation_id_display"] = f"correlation_id={record['extra']['correlation_id']} | "
        else:
            record["extra"]["correlation_id_display"] = ""
        
        # Format context
        if record["extra"].get("context"):
            context_str = record["extra"]["context"]
            record["extra"]["context_display"] = f"context=\"{context_str}\" | "
        else:
            record["extra"]["context_display"] = ""
        
        # Return the formatted string with file and line info
        return "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{extra[file_line]}</cyan> | {extra[correlation_id_display]}{extra[context_display]}<level>{message}</level>\n"
    except Exception as e:
        # If any error occurs, return a simple format
        return "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <level>{message}</level>\n"
    except Exception as e:
        # If any error occurs, return a simple format
        return "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <level>{message}</level>\n"

# Initialize logger with default configuration
logger = _logger

# Configure logger with default extras
logger.configure(
    extra={"correlation_id": "", "context": "", "correlation_id_display": "", "context_display": ""}
)

# Remove default handlers
logger.remove()

# Add stderr handler
logger.add(
    sys.stderr,
    format=formatter,
    level=os.getenv("LOG_LEVEL", "INFO"),
    colorize=True,
    backtrace=True,
    diagnose=True,
    enqueue=True,
    catch=True
)

# Add file logging if configured
LOG_FILE = os.getenv('LOG_FILE')
if LOG_FILE:
    # Ensure the log directory exists
    log_path = Path(LOG_FILE)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Add file handler with rotation - using JSON format
    logger.add(
        LOG_FILE,
        rotation="500 MB",
        retention="10 days",
        compression="gz",
        level=os.getenv("LOG_LEVEL", "INFO"),
        enqueue=True,
        backtrace=True,
        diagnose=True,
        serialize=True  # Use Loguru's built-in JSON serialization
    )
    logger.info(f"📝 Configured JSON file logging to: {LOG_FILE}")

# Add Loki-specific log file that captures ALL logs regardless of log level
LOKI_LOG_FILE = os.getenv('LOKI_LOG_FILE')
if LOKI_LOG_FILE:
    # Ensure the log directory exists
    loki_log_path = Path(LOKI_LOG_FILE)
    loki_log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Add file handler with rotation - capturing ALL log levels with JSON format
    logger.add(
        LOKI_LOG_FILE,
        rotation="500 MB",
        retention="10 days",
        compression="gz",
        level="TRACE",  # Capture all logs regardless of level
        enqueue=True,
        backtrace=True,
        diagnose=True,
        serialize=True  # Use Loguru's built-in JSON serialization
    )
    logger.info(f"🔍 Configured Loki JSON log sink to: {LOKI_LOG_FILE} (all log levels)")

# Logging functions with emojis that automatically include correlation ID
def debug(message, *args, **kwargs):
    """Log a debug message with emoji."""
    # Get correlation ID from context and add it to extra
    corr_id = correlation_id_var.get()
    extra = kwargs.pop('extra', {})
    # Only add correlation_id if it's not empty
    if corr_id:
        extra['correlation_id'] = corr_id
    
    # Truncate base64 values in the message
    message = truncate_base64(message)
    
    logger.opt(depth=1).bind(**extra).debug(f"🐛 {message}", *args, **kwargs)

def info(message, *args, **kwargs):
    """Log an info message with emoji."""
    # Get correlation ID from context and add it to extra
    corr_id = correlation_id_var.get()
    extra = kwargs.pop('extra', {})
    # Only add correlation_id if it's not empty
    if corr_id:
        extra['correlation_id'] = corr_id
    
    # Truncate base64 values in the message
    message = truncate_base64(message)
    
    logger.opt(depth=1).bind(**extra).info(f"ℹ️ {message}", *args, **kwargs)

def warning(message, *args, **kwargs):
    """Log a warning message with emoji."""
    # Get correlation ID from context and add it to extra
    corr_id = correlation_id_var.get()
    extra = kwargs.pop('extra', {})
    # Only add correlation_id if it's not empty
    if corr_id:
        extra['correlation_id'] = corr_id
    
    # Truncate base64 values in the message
    message = truncate_base64(message)
    
    logger.opt(depth=1).bind(**extra).warning(f"⚠️ {message}", *args, **kwargs)

def error(message, *args, **kwargs):
    """Log an error message with emoji."""
    # Get correlation ID from context and add it to extra
    corr_id = correlation_id_var.get()
    extra = kwargs.pop('extra', {})
    # Only add correlation_id if it's not empty
    if corr_id:
        extra['correlation_id'] = corr_id
    
    # Truncate base64 values in the message
    message = truncate_base64(message)

    error_trace = "".join(traceback.format_exc())
    
    logger.opt(depth=1).bind(**extra).error(f"❌ {message} {error_trace}", *args, **kwargs)

def critical(message, *args, **kwargs):
    """Log a critical message with emoji."""
    # Get correlation ID from context and add it to extra
    corr_id = correlation_id_var.get()
    extra = kwargs.pop('extra', {})
    # Only add correlation_id if it's not empty
    if corr_id:
        extra['correlation_id'] = corr_id
    
    # Truncate base64 values in the message
    message = truncate_base64(message)
    error_trace = "".join(traceback.format_exc())
    
    
    logger.opt(depth=1).bind(**extra).critical(f"🔥 {message} {error_trace}", *args, **kwargs)

def success(message, *args, **kwargs):
    """Log a success message with emoji."""
    # Get correlation ID from context and add it to extra
    corr_id = correlation_id_var.get()
    extra = kwargs.pop('extra', {})
    # Only add correlation_id if it's not empty
    if corr_id:
        extra['correlation_id'] = corr_id
    
    # Truncate base64 values in the message
    message = truncate_base64(message)
    
    logger.opt(depth=1).bind(**extra).success(f"✅ {message}", *args, **kwargs)

def exception(message, *args, **kwargs):
    """Log an exception message with emoji and traceback."""
    # Get correlation ID from context and add it to extra
    corr_id = correlation_id_var.get()
    extra = kwargs.pop('extra', {})
    # Only add correlation_id if it's not empty
    if corr_id:
        extra['correlation_id'] = corr_id
    
    # Truncate base64 values in the message
    message = truncate_base64(message)
    
    logger.opt(depth=1).bind(**extra).exception(f"💥 {message}", *args, **kwargs)

@contextmanager
def correlation_context(corr_id=None):
    """Context manager for setting correlation ID."""
    # Try to get correlation ID from middleware if not provided
    if corr_id is None:
        try:
            # Import here to avoid circular imports
            from .middleware import request_correlation_id
            middleware_corr_id = request_correlation_id.get()
            if middleware_corr_id:
                corr_id = middleware_corr_id
        except (ImportError, AttributeError):
            # Middleware might not be available, just continue with provided correlation_id
            pass
    
    # If still no correlation ID, generate a new one
    if corr_id is None:
        corr_id = str(uuid.uuid4())
    
    # Use Loguru's contextualize to set the correlation ID for this scope
    with logger.contextualize(correlation_id=corr_id):
        yield

def contextualize(correlation_id=None, **kwargs):
    """Contextualize the logger with correlation ID and other context values."""
    # Try to get correlation ID from middleware if not provided
    if correlation_id is None or correlation_id == "":
        try:
            # Import here to avoid circular imports
            from .middleware import request_correlation_id
            middleware_corr_id = request_correlation_id.get()
            if middleware_corr_id:
                correlation_id = middleware_corr_id
        except (ImportError, AttributeError):
            # Middleware might not be available, just continue with provided correlation_id
            pass
        
    # If still no correlation ID, generate a new one
    if correlation_id is None or correlation_id == "":
        correlation_id = str(uuid.uuid4())
    
    # Format context string if kwargs are provided
    context = ""
    if kwargs:
        # Filter out None values
        kwargs = {k: v for k, v in kwargs.items() if v is not None}
        context = " | ".join(f"{k}={v}" for k, v in kwargs.items()) if kwargs else ""
    
    # Use Loguru's contextualize to set the context for this scope
    return logger.contextualize(correlation_id=correlation_id, context=context)

def with_context(**ctx_kwargs):
    """Decorator to add context to a function."""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Get correlation ID from kwargs
            correlation_id = kwargs.pop('correlation_id', None)
            
            # Try to get correlation ID from middleware if not provided
            if correlation_id is None:
                try:
                    # Import here to avoid circular imports
                    from .middleware import request_correlation_id
                    middleware_corr_id = request_correlation_id.get()
                    if middleware_corr_id:
                        correlation_id = middleware_corr_id
                except (ImportError, AttributeError):
                    # Middleware might not be available, just continue with provided correlation_id
                    pass
            
            # If still no correlation ID, generate a new one
            if correlation_id is None:
                correlation_id = str(uuid.uuid4())
            
            # Merge context from decorator and function call
            context = {**ctx_kwargs}
            
            # Use the context manager to ensure thread safety
            with contextualize(correlation_id=correlation_id, **context):
                return await func(*args, **kwargs)
                
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Get correlation ID from kwargs
            correlation_id = kwargs.pop('correlation_id', None)
            
            # Try to get correlation ID from middleware if not provided
            if correlation_id is None:
                try:
                    # Import here to avoid circular imports
                    from .middleware import request_correlation_id
                    middleware_corr_id = request_correlation_id.get()
                    if middleware_corr_id:
                        correlation_id = middleware_corr_id
                except (ImportError, AttributeError):
                    # Middleware might not be available, just continue with provided correlation_id
                    pass
            
            # If still no correlation ID, generate a new one
            if correlation_id is None:
                correlation_id = str(uuid.uuid4())
            
            # Merge context from decorator and function call
            context = {**ctx_kwargs}
            
            # Use the context manager to ensure thread safety
            with contextualize(correlation_id=correlation_id, **context):
                return func(*args, **kwargs)
        
        # Return the appropriate wrapper based on whether the function is async or not
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
            
    return decorator

# Keep the CymulateLogger class for backward compatibility
class CymulateLogger:
    def __init__(self):
        self._logger = logger
    
    def bind(self, **kwargs):
        """Bind values to the logger."""
        return self._logger.bind(**kwargs)
    
    debug = staticmethod(debug)
    info = staticmethod(info)
    warning = staticmethod(warning)
    error = staticmethod(error)
    critical = staticmethod(critical)
    success = staticmethod(success)
    exception = staticmethod(exception)
    contextualize = staticmethod(contextualize)
    with_context = staticmethod(with_context)
    correlation_context = staticmethod(correlation_context) 