import types

import pytest

from logger.middleware import correlation_id_middleware


class DummyRequest:
    def __init__(self, path="/", headers=None, method="GET", client_host=None):
        self.url = types.SimpleNamespace(path=path)
        self.headers = headers or {}
        self.method = method
        self.state = types.SimpleNamespace()
        self.client = types.SimpleNamespace(host=client_host) if client_host else None

class DummyResponse:
    def __init__(self, status_code=200):
        self.status_code = status_code
        self.headers = {}

@pytest.mark.asyncio
async def test_correlation_id_middleware_health():
    req = DummyRequest(path="/health")
    async def call_next(request):
        return DummyResponse(200)
    resp = await correlation_id_middleware(req, call_next)
    assert isinstance(resp, DummyResponse)

@pytest.mark.asyncio
async def test_correlation_id_middleware_with_header():
    req = DummyRequest(headers={"X-Correlation-ID": "abc"}, client_host="*******")
    async def call_next(request):
        return DummyResponse(201)
    resp = await correlation_id_middleware(req, call_next)
    assert req.state.correlation_id == "abc"
    assert resp.headers["X-Correlation-ID"] == "abc"
    assert resp.status_code == 201

@pytest.mark.asyncio
async def test_correlation_id_middleware_no_header():
    req = DummyRequest(client_host="*******")
    async def call_next(request):
        return DummyResponse(202)
    resp = await correlation_id_middleware(req, call_next)
    assert req.state.correlation_id
    assert resp.headers["X-Correlation-ID"] == req.state.correlation_id
    assert resp.status_code == 202

@pytest.mark.asyncio
async def test_correlation_id_middleware_exception():
    req = DummyRequest()
    async def call_next(request):
        raise RuntimeError("fail")
    with pytest.raises(RuntimeError):
        await correlation_id_middleware(req, call_next) 