import os
import tempfile

import pytest
from loguru import logger as loguru_logger

from logger.logger import (
    CymulateLogger,
    contextualize,
    correlation_context,
    critical,
    debug,
    error,
    exception,
    format_extra_fields,
    formatter,
    info,
    process_log_message,
    success,
    truncate_base64,
    warning,
    with_context,
)


def test_debug_info_warning_error_critical_success(caplog):
    # Redirige loguru vers caplog
    loguru_logger.remove()
    loguru_logger.add(caplog.handler, format="{message}")
    debug("debug message")
    info("info message")
    warning("warning message")
    error("error message")
    critical("critical message")
    success("success message")
    messages = [r.getMessage() for r in caplog.records]
    assert any("debug message" in m for m in messages)
    assert any("info message" in m for m in messages)
    assert any("warning message" in m for m in messages)
    assert any("error message" in m for m in messages)
    assert any("critical message" in m for m in messages)
    assert any("success message" in m for m in messages)


def test_exception(caplog):
    loguru_logger.remove()
    loguru_logger.add(caplog.handler, format="{message}")
    try:
        1 / 0
    except Exception as e:
        exception("exception message")
    messages = [r.getMessage() for r in caplog.records]
    assert any("exception message" in m for m in messages)


def test_contextualize_sets_context():
    with contextualize(correlation_id="test-corr", user="bob"):
        loguru_logger.info("contextualized message")
    # No assertion: just ensure no error


def test_correlation_context_sets_correlation_id():
    with correlation_context("my-corr-id"):
        loguru_logger.info("correlation context message")
    # No assertion: just ensure no error


def test_with_context_decorator_sync():
    @with_context(user="alice")
    def foo():
        loguru_logger.info("decorated sync function")
        return True
    assert foo() is True

@pytest.mark.asyncio
def test_with_context_decorator_async():
    @with_context(user="bob")
    async def foo():
        loguru_logger.info("decorated async function")
        return True
    import asyncio
    assert asyncio.run(foo()) is True

def test_cymulate_logger_methods():
    c = CymulateLogger()
    c.debug("debug")
    c.info("info")
    c.warning("warning")
    c.error("error")
    c.critical("critical")
    c.success("success")
    c.exception("exception")
    c.contextualize(correlation_id="cid")
    c.with_context(user="bob")
    c.correlation_context("cid")
    # No assertion: just ensure no error 

def test_truncate_base64():
    # Pas base64
    assert truncate_base64("hello") == "hello"
    # Chaîne base64 longue
    b64 = "aGVsbG8=" * 30
    out = truncate_base64(b64)
    assert out.startswith(b64[:20]) and "truncated base64" in out
    # Data URL
    data_url = "data:image/png;base64," + ("A" * 120)
    out = truncate_base64(data_url)
    assert out.startswith("data:image/png;base64,AAAA") and "truncated base64" in out


def test_format_extra_fields_basic():
    record = {"extra": {"correlation_id": "abc", "context": "user=1|role=admin"}}
    result = format_extra_fields(record)
    assert "correlation_id_display" in result["extra"]
    assert "context_display" in result["extra"]
    assert "correlation_id=abc" in result["extra"]["correlation_id_display"]
    assert "context={user=1, role=admin}" in result["extra"]["context_display"]


def test_process_log_message_ok():
    class DummyMsg:
        def __init__(self):
            self.record = {"message": "foo", "extra": {}}
    msg = DummyMsg()
    out = process_log_message(msg)
    assert hasattr(out, "record")


def test_formatter_basic():
    record = {"message": "foo", "extra": {"correlation_id": "cid", "context": "user=1"}}
    # Doit retourner un string formaté
    result = formatter(record)
    assert isinstance(result, str)


def test_cymulate_logger_bind():
    c = CymulateLogger()
    bound = c.bind(test=123)
    assert hasattr(bound, "info") 

def test_truncate_base64_short_and_non_str():
    # Non-str
    assert truncate_base64(123) == 123
    # Short base64
    b64 = "aGVsbG8="
    assert truncate_base64(b64) == b64

def test_truncate_base64_object_pattern():
    obj = "CampaignInfoData(id=" + ("A" * 120) + ")"
    out = truncate_base64(obj)
    assert "truncated base64" in out

def test_truncate_base64_json_pattern():
    json_b64 = '"data": "' + ("A" * 120) + '"'
    out = truncate_base64(json_b64)
    assert "truncated base64" in out

def test_truncate_base64_standalone():
    s = "A" * 120
    out = truncate_base64(s)
    assert "truncated base64" in out

def test_format_extra_fields_empty():
    record = {"extra": {}}
    result = format_extra_fields(record)
    assert result["extra"]["correlation_id_display"] == ""
    assert result["extra"]["context_display"] == ""

def test_format_extra_fields_nested_context():
    record = {"extra": {"context": "context={user=1}"}}
    result = format_extra_fields(record)
    assert "context={user=1}" in result["extra"]["context_display"]

def test_format_extra_fields_context_str():
    record = {"extra": {"context": "simplecontext"}}
    result = format_extra_fields(record)
    assert 'context="simplecontext"' in result["extra"]["context_display"]

def test_format_extra_fields_error():
    # Simule une erreur dans le traitement (extra=None)
    record = {"extra": None}
    result = format_extra_fields(record)
    assert result["extra"].get("correlation_id_display", "") == ""

def test_formatter_error():
    # Simule une erreur dans le formatter
    record = None
    out = formatter(record)
    assert isinstance(out, str)

def test_contextualize_no_kwargs():
    with contextualize():
        loguru_logger.info("no context")

def test_correlation_context_no_id():
    with correlation_context():
        loguru_logger.info("no corr id")

def test_with_context_decorator_empty():
    @with_context()
    def foo():
        return True
    assert foo() is True

def test_cymulate_logger_static_methods():
    c = CymulateLogger()
    for method in [c.debug, c.info, c.warning, c.error, c.critical, c.success, c.exception]:
        method("msg")
    c.contextualize()
    c.with_context()
    c.correlation_context()


def test_logger_file_handlers(monkeypatch):
    # Test LOG_FILE and LOKI_LOG_FILE branches
    with tempfile.NamedTemporaryFile(delete=False) as f:
        log_path = f.name
    monkeypatch.setenv("LOG_FILE", log_path)
    monkeypatch.setenv("LOKI_LOG_FILE", log_path + "_loki")
    import importlib
    import sys
    if "logger.logger" in sys.modules:
        importlib.reload(sys.modules["logger.logger"])
    else:
        importlib.import_module("logger.logger")
    os.remove(log_path)
    os.remove(log_path + "_loki") 

def test_formatter_nested_context():
    record = {"message": "foo", "extra": {"correlation_id": "cid", "context": "context={user=1}"}}
    formatter(record)
    assert "context={user=1}" in record["extra"]["context_display"]

def test_formatter_context_key_value():
    record = {"message": "foo", "extra": {"correlation_id": "cid", "context": "user=1|role=admin"}}
    formatter(record)
    assert "user=1" in record["extra"]["context_display"] and "role=admin" in record["extra"]["context_display"]

def test_formatter_context_str():
    record = {"message": "foo", "extra": {"correlation_id": "cid", "context": "simplecontext"}}
    formatter(record)
    assert 'context="simplecontext"' in record["extra"]["context_display"]

def test_formatter_context_data_url_short():
    # Data url court, ne doit pas tronquer
    record = {"message": "foo", "extra": {"correlation_id": "cid", "context": "img=data:image/png;base64,AAAA"}}
    formatter(record)
    assert "img=data:image/png;base64,AAAA" in record["extra"]["context_display"]

def test_formatter_context_data_url_long():
    # Data url long, doit tronquer
    long_b64 = "A" * 100
    record = {"message": "foo", "extra": {"correlation_id": "cid", "context": f"img=data:image/png;base64,{long_b64}"}}
    formatter(record)
    assert "img=data:image/png;base64," in record["extra"]["context_display"]

def test_formatter_context_no_context():
    record = {"message": "foo", "extra": {"correlation_id": "cid"}}
    formatter(record)
    assert record["extra"]["context_display"] == ""

def test_log_functions_various_levels(caplog):
    loguru_logger.remove()
    loguru_logger.add(caplog.handler, format="{message}")
    debug("debug", extra={"correlation_id": "cid", "context": "user=1"})
    info("info", extra={"correlation_id": "cid"})
    warning("warning")
    error("error")
    critical("critical")
    success("success")
    exception("exception")
    messages = [r.getMessage() for r in caplog.records]
    assert any("debug" in m for m in messages)
    assert any("info" in m for m in messages)
    assert any("warning" in m for m in messages)
    assert any("error" in m for m in messages)
    assert any("critical" in m for m in messages)
    assert any("success" in m for m in messages)
    assert any("exception" in m for m in messages) 

def test_truncate_base64_exception(monkeypatch):
    # Force une exception dans re.sub
    def bad_sub(*a, **k):
        raise Exception("fail")
    monkeypatch.setattr("re.sub", bad_sub)
    assert truncate_base64("foo") == "foo"

def test_formatter_exception():
    # Force une exception dans formatter
    record = None
    out = formatter(record)
    assert isinstance(out, str)

def test_process_log_message_exception():
    class Dummy:
        @property
        def record(self):
            raise Exception("fail")
    msg = Dummy()
    out = process_log_message(msg)
    assert out is msg

def test_with_context_decorator_exception():
    @with_context(user="fail")
    def foo():
        raise ValueError("fail")
    try:
        foo()
    except ValueError:
        assert True
    else:
        assert False

def test_contextualize_with_empty_kwargs():
    with contextualize():
        assert True

def test_correlation_context_with_none():
    with correlation_context():
        assert True 