# Cymulate Logger

A powerful and flexible logging package for Python applications, built on top of loguru.

## Features

- 🚀 Simple and intuitive logging interface
- 📝 Rich log formatting with colors and structured output
- 🔄 Automatic log rotation and file management
- 🎯 Multiple output destinations (console, file, etc.)
- 🔍 Detailed context and traceback information
- ⚡ High performance with minimal overhead
- 🔒 Thread-safe logging operations
- 🌐 Optional FastAPI integration for web applications

## Installation

### Basic Installation

```bash
pip install cymulate.logger
```

### With FastAPI Support

```bash
pip install "cymulate.logger[fastapi]"
```

## Quick Start

```python
from logger import Logger

# Initialize logger
logger = Logger()

# Basic logging
logger.info("This is an info message")
logger.error("This is an error message")
logger.debug("This is a debug message")

# Logging with context
logger.info("User action", user_id="123", action="login")

# Exception logging
try:
    1/0
except Exception as e:
    logger.exception("An error occurred", exc_info=True)
```

## Configuration

### Basic Configuration

```python
from logger import Logger

logger = Logger(
    level="INFO",
    format="{time} | {level} | {message}",
    rotation="1 day",
    retention="30 days"
)
```

### Advanced Configuration

```python
from logger import Logger

logger = Logger(
    # Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    level="INFO",
    
    # Log format with custom fields
    format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
    
    # Log rotation settings
    rotation="1 day",  # Rotate daily
    retention="30 days",  # Keep logs for 30 days
    compression="zip",  # Compress rotated logs
    
    # Output settings
    enqueue=True,  # Thread-safe logging
    backtrace=True,  # Show full traceback
    diagnose=True,  # Show variable values in traceback
)
```

## FastAPI Integration

When installed with FastAPI support, you can easily integrate the logger with your FastAPI application:

```python
from fastapi import FastAPI
from logger import Logger

app = FastAPI()
logger = Logger()

@app.get("/")
async def root():
    logger.info("Root endpoint accessed")
    return {"message": "Hello World"}

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.exception("Unhandled exception occurred", exc_info=exc)
    return {"error": "Internal server error"}
```

## Log Levels

- `DEBUG`: Detailed information for debugging
- `INFO`: General information about program execution
- `WARNING`: Indicate a potential problem
- `ERROR`: A more serious problem
- `CRITICAL`: A critical problem that may prevent the program from running

## Best Practices

1. Use appropriate log levels for different types of messages
2. Include relevant context in log messages
3. Use structured logging for better log analysis
4. Configure log rotation to manage disk space
5. Enable thread-safe logging in multi-threaded applications

## Contributing

For internal Cymulate team members, please refer to the contribution guidelines in the internal documentation.

## License

Proprietary - All rights reserved

## Support

For support, please contact:
- Reouven Mimoun (<EMAIL>)
- Dudi Lugaci (<EMAIL>) 