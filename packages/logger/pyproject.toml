[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "logger"
version = "0.1.1"
description = "Logger"
readme = "README.md"
authors = [
    { name = "<PERSON>ou<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
]
license = { text = "Proprietary" }
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "License :: Other/Proprietary License",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = ["loguru>=0.7.3"]

[tool.ruff]
select = ["I", "F401"]


[project.optional-dependencies]
fastapi = ["fastapi"]

[project.urls]
"Homepage" = "https://bitbucket.org/cymulate/pythonlibs"
"Bug Tracker" = "https://bitbucket.org/cymulate/pythonlibs/jira"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]
namespaces = true

# Use the same formatting and linting configuration as the root project
[tool.black]
line-length = 88
target-version = ["py312"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
addopts = "--cov=logger --cov-report=term-missing"
