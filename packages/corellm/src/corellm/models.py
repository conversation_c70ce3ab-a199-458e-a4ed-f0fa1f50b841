"""
Data models for LLM requests and responses.
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional


@dataclass
class LLMRequest:
    """
    Request to an LLM provider.
    """

    prompt: str
    max_tokens: int = 1000
    temperature: float = 0.7
    model: str = "gpt-4"
    stop_sequences: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class LLMResponse:
    """
    Response from an LLM provider.
    """

    text: str
    usage: Dict[str, int] = field(default_factory=dict)
    model: Optional[str] = None
    finish_reason: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
