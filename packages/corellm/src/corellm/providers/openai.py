"""
OpenAI Model Provider

This module implements the OpenAI model provider.
"""

import os
from typing import Any, Dict, Optional, cast

from .base import BaseModel, EmbeddingsModel
from langchain_core.language_models import BaseChatModel

# These will be optional dependencies
try:
    from langchain_openai import Chat<PERSON><PERSON>A<PERSON>, OpenAIEmbeddings

    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False


class OpenAIModel(BaseModel):
    """
    OpenAI model provider implementation.

    This class implements the BaseModel interface for OpenAI models.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the OpenAI model provider.

        Args:
            config: Configuration dictionary for the OpenAI model provider
        """
        super().__init__(config)
        self.api_key = config.get("api_key", os.getenv("OPENAI_API_KEY"))
        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        if not LANGCHAIN_AVAILABLE:
            raise ImportError(
                "LangChain packages are required for OpenAI model. "
                "Install with `pip install langchain-openai`."
            )

    def initialize_chat_model(
        self, temperature: float = 0.7, max_tokens: int = 4096, top_p: float = 0.5
    ) -> BaseChatModel:
        """
        Initialize and return an OpenAI chat model.

        Args:
            temperature: Controls randomness. Higher values mean more random completions.
            max_tokens: Maximum number of tokens to generate.
            top_p: Controls diversity via nucleus sampling.

        Returns:
            An initialized OpenAI chat model
        """
        model = ChatOpenAI(
            model=self.config.get("model_name", "gpt-4o"),
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
            api_key=self.api_key,
        )
        return model

    def initialize_embeddings_model(self) -> EmbeddingsModel:
        """
        Initialize and return an OpenAI embeddings model.

        Returns:
            An initialized OpenAI embeddings model
        """
        embeddings_model = self.config.get("embeddings_model", "text-embedding-3-small")
        model = OpenAIEmbeddings(model=embeddings_model, api_key=self.api_key)
        return cast(EmbeddingsModel, model)
