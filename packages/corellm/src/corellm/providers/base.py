"""
Base Model Provider

This module defines the abstract base class for all model providers.
"""

import abc
from typing import Any, Dict, Optional, Protocol, TypeVar, Union


# Define type variables for the chat and embeddings models
# Using Protocol to allow for flexibility with different model implementations
class ChatModelProtocol(Protocol):
    """Protocol for chat models."""

    def __call__(self, *args: Any, **kwargs: Any) -> Any: ...


class EmbeddingsModelProtocol(Protocol):
    """Protocol for embeddings models."""

    def embed_documents(self, texts: list[str]) -> list[list[float]]: ...
    def embed_query(self, text: str) -> list[float]: ...


# Type variables for better type hinting
ChatModel = TypeVar("ChatModel", bound=ChatModelProtocol)
EmbeddingsModel = TypeVar("EmbeddingsModel", bound=EmbeddingsModelProtocol)


class BaseModel(abc.ABC):
    """
    Abstract base class for all model providers.

    This class defines the interface that all model providers must implement.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the model provider with configuration.

        Args:
            config: Configuration dictionary for the model provider
        """
        self.config = config
        self.model_type = config.get("type", "unknown")

    @abc.abstractmethod
    def initialize_chat_model(
        self, temperature: float = 0.7, max_tokens: int = 4096, top_p: float = 0.5
    ) -> ChatModel:
        """
        Initialize and return a chat model.

        Args:
            temperature: Controls randomness. Higher values mean more random completions.
            max_tokens: Maximum number of tokens to generate.
            top_p: Controls diversity via nucleus sampling.

        Returns:
            An initialized chat model
        """
        pass

    @abc.abstractmethod
    def initialize_embeddings_model(self) -> EmbeddingsModel:
        """
        Initialize and return an embeddings model.

        Returns:
            An initialized embeddings model
        """
        pass

    @property
    def model_name(self) -> str:
        """
        Get the name of the model.

        Returns:
            The name of the model
        """
        return self.config.get("model_name", "unknown")
