"""
Azure OpenAI Model Provider

This module implements the Azure OpenAI model provider.
"""

import os
from typing import Any, Dict, Optional, Set, cast

from .base import BaseModel, EmbeddingsModel
from langchain_core.language_models import BaseChatModel

# These will be optional dependencies
try:
    from langchain_openai.chat_models import AzureChatOpenAI
    from langchain_openai.embeddings import AzureOpenAIEmbeddings

    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False


class AzureModel(BaseModel):
    """
    Azure OpenAI model provider implementation.

    This class implements the BaseModel interface for Azure OpenAI models.
    """

    # Required parameters for Azure OpenAI
    required_params: list[str] = [
        "api_key",
        "azure_endpoint",
        "deployment_name",
        "api_version",
    ]

    # Optional parameters with default values
    optional_params: Dict[str, Any] = {
        "model_name": "gpt-4o",
        "engine_embeddings": None,
        "reasoning_effort": None,
        "unsupported_fields": [],
    }

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Azure OpenAI model provider.

        Args:
            config: Configuration dictionary for the Azure OpenAI model provider
        """
        super().__init__(config)
        self.params = config

        # Validate required parameters
        for param in self.required_params:
            if param not in config and not os.getenv(f"AZURE_OPENAI_{param.upper()}"):
                raise ValueError(f"Azure OpenAI parameter '{param}' is required")

        if not LANGCHAIN_AVAILABLE:
            raise ImportError(
                "LangChain packages are required for Azure OpenAI model. "
                "Install with `pip install langchain-openai`."
            )

    def initialize_chat_model(
        self, temperature: float = 0.7, max_tokens: int = 4096, top_p: float = 0.5
    ) -> BaseChatModel:
        """
        Initialize and return an Azure OpenAI chat model.

        Args:
            temperature: Controls randomness. Higher values mean more random completions.
            max_tokens: Maximum number of tokens to generate.
            top_p: Controls diversity via nucleus sampling.

        Returns:
            An initialized Azure OpenAI chat model
        """
        unsupported_fields: Set[str] = set(self.params.get("unsupported_fields") or [])

        params: Dict[str, Any] = {
            "openai_api_key": self.params.get(
                "api_key", os.getenv("AZURE_OPENAI_API_KEY")
            ),
            "azure_endpoint": self.params.get(
                "azure_endpoint", os.getenv("AZURE_OPENAI_ENDPOINT")
            ),
            "openai_api_version": self.params.get(
                "api_version", os.getenv("AZURE_OPENAI_API_VERSION", "2023-05-15")
            ),
            "deployment_name": self.params.get(
                "deployment_name", os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
            ),
            "cache": False,
            "model_name": self.params.get("model_name"),
            "max_retries": 5,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "top_p": top_p,
        }

        # Add any additional parameters that aren't in the unsupported fields list
        if unsupported_fields:
            for key, value in self.params.items():
                if (
                    key not in unsupported_fields
                    and value is not None
                    and key not in params
                ):
                    params[key] = value

            # Remove unsupported_fields from params if it was added
            params.pop("unsupported_fields", None)

        model = AzureChatOpenAI(**params)
        return model

    def initialize_embeddings_model(self) -> EmbeddingsModel:
        """
        Initialize and return an Azure OpenAI embeddings model.

        Returns:
            An initialized Azure OpenAI embeddings model
        """
        engine_embeddings = self.params.get("engine_embeddings") or self.params.get(
            "deployment_name"
        )
        model = AzureOpenAIEmbeddings(
            openai_api_key=self.params.get(
                "api_key", os.getenv("AZURE_OPENAI_API_KEY")
            ),
            azure_endpoint=self.params.get(
                "azure_endpoint", os.getenv("AZURE_OPENAI_ENDPOINT")
            ),
            openai_api_version=self.params.get(
                "api_version", os.getenv("AZURE_OPENAI_API_VERSION", "2023-05-15")
            ),
            max_retries=200,
            deployment=engine_embeddings,
        )
        return cast(EmbeddingsModel, model)
