"""
SageMaker Model Provider
https://docs.aws.amazon.com/sagemaker/latest/dg/inference-profiles-support.html
This module implements the AWS SageMaker Endpoint model provider.
"""

import os
from typing import Any, Dict, List, Optional, cast
import json
from .base import BaseModel, ChatModel, EmbeddingsModel

# These will be optional dependencies
try:
    from langchain_aws.llms import SagemakerEndpoint
    from langchain_community.embeddings import SagemakerEndpointEmbeddings
    from langchain_community.embeddings.sagemaker_endpoint import (
        EmbeddingsContentHandler,
    )
    from langchain_aws.llms.sagemaker_endpoint import LLMContentHandler

    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False


class ContentHandler(LLMContentHandler):
    content_type = "application/json"
    accepts = "application/json"

    def transform_input(self, prompt: str, model_kwargs: Dict) -> bytes:
        input_str = json.dumps({"inputs": prompt, "parameters": model_kwargs})
        return input_str.encode("utf-8")

    def transform_output(self, output: bytes) -> str:
        response_json = json.loads(output.read().decode("utf-8"))
        return response_json[0]["generated_text"]


class EContentHandler(EmbeddingsContentHandler):
    content_type = "application/json"
    accepts = "application/json"

    def transform_input(self, inputs: list[str], model_kwargs: Dict) -> bytes:
        """
        Transforms the input into bytes that can be consumed by SageMaker endpoint.
        Args:
            inputs: List of input strings.
            model_kwargs: Additional keyword arguments to be passed to the endpoint.
        Returns:
            The transformed bytes input.
        """
        # Example: inference.py expects a JSON string with a "inputs" key:
        input_str = json.dumps({"inputs": inputs, **model_kwargs})
        return input_str.encode("utf-8")

    def transform_output(self, output: bytes) -> List[List[float]]:
        """
        Transforms the bytes output from the endpoint into a list of embeddings.
        Args:
            output: The bytes output from SageMaker endpoint.
        Returns:
            The transformed output - list of embeddings
        Note:
            The length of the outer list is the number of input strings.
            The length of the inner lists is the embedding dimension.
        """
        # Example: inference.py returns a JSON string with the list of
        # embeddings in a "vectors" key:
        response_json = json.loads(output.read().decode("utf-8"))
        return response_json["vectors"]


class SageMakerModel(BaseModel):
    """
    AWS SageMaker model provider implementation.

    This class implements the BaseModel interface for AWS SageMaker models.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the AWS SageMaker model provider.

        Args:
            config: Configuration dictionary for the AWS SageMaker model provider
        """
        super().__init__(config)

        if not LANGCHAIN_AVAILABLE:
            raise ImportError(
                "LangChain AWS packages are required for SageMaker model. "
                "Install with `pip install langchain-aws`."
            )

    def initialize_chat_model(
        self, temperature: float = 0.7, max_tokens: int = 4096, top_p: float = 0.5
    ) -> ChatModel:
        """
        Initialize and return an AWS SageMaker chat model.

        Args:
            temperature: Controls randomness. Higher values mean more random completions.
            max_tokens: Maximum number of tokens to generate.
            top_p: Controls diversity via nucleus sampling.

        Returns:
            An initialized AWS SageMaker chat model
        """
        content_handler = ContentHandler()

        model = SagemakerEndpoint(
            endpoint_name="endpoint-name",
            credentials_profile_name="credentials-profile-name",
            region_name="us-west-2",
            model_kwargs={"temperature": 1e-10},
            content_handler=content_handler,
        )
        return cast(ChatModel, model)

    def initialize_embeddings_model(self) -> EmbeddingsModel:
        """
        Initialize and return an AWS SageMaker embeddings model.

        Returns:
            An initialized AWS SageMaker embeddings model
        """
        embeddings_model = self.config.get(
            "embeddings_model", "amazon.titan-embed-text-v1"
        )
        content_handler = EContentHandler()

        model = SagemakerEndpointEmbeddings(
            endpoint_name=embeddings_model,
            region_name="us-east-1",
            content_handler=content_handler,
        )
        return cast(EmbeddingsModel, model)
