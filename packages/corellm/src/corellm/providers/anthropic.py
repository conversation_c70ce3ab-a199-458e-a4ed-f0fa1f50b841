"""
Anthropic Model Provider

This module implements the Anthropic model provider.
"""

import os
from typing import Any, Dict, Optional, cast

from .base import BaseModel, ChatModel, EmbeddingsModel
from langchain_core.language_models import BaseChatModel

# These will be optional dependencies
try:
    from langchain_anthropic import ChatAnthropic

    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

try:
    from langchain_openai import OpenAIEmbeddings

    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False


class AnthropicModel(BaseModel):
    """
    Anthropic model provider implementation.

    This class implements the BaseModel interface for Anthropic models.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the Anthropic model provider.

        Args:
            config: Configuration dictionary for the Anthropic model provider
        """
        super().__init__(config)
        self.api_key = config.get("api_key", os.getenv("ANTHROPIC_API_KEY"))
        self.openai_api_key = config.get("openai_api_key", os.getenv("OPENAI_API_KEY"))

        if not self.api_key:
            raise ValueError("Anthropic API key is required")

        if not ANTHROPIC_AVAILABLE:
            raise ImportError(
                "LangChain packages are required for Anthropic model. "
                "Install with `pip install langchain-anthropic`."
            )

    def initialize_chat_model(
        self, temperature: float = 0.7, max_tokens: int = 4096, top_p: float = 0.5
    ) -> BaseChatModel:
        """
        Initialize and return an Anthropic chat model.

        Args:
            temperature: Controls randomness. Higher values mean more random completions.
            max_tokens: Maximum number of tokens to generate.
            top_p: Controls diversity via nucleus sampling.

        Returns:
            An initialized Anthropic chat model
        """
        model = ChatAnthropic(
            model=self.config.get("model_name", "claude-3-opus-20240229"),
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
            anthropic_api_key=self.api_key,
        )
        return model

    def initialize_embeddings_model(self) -> EmbeddingsModel:
        """
        Initialize and return an embeddings model.

        Note: Anthropic doesn't provide embeddings, so we use OpenAI as a fallback.

        Returns:
            An initialized embeddings model (OpenAI)

        Raises:
            ImportError: If OpenAI package is not available
            ValueError: If OpenAI API key is not provided
        """
        if not OPENAI_AVAILABLE:
            raise ImportError(
                "OpenAI package is required for embeddings with Anthropic models. "
                "Install with `pip install langchain-openai`."
            )

        if not self.openai_api_key:
            raise ValueError(
                "OpenAI API key is required for embeddings with Anthropic models"
            )

        embeddings_model = self.config.get("embeddings_model", "text-embedding-3-small")
        model = OpenAIEmbeddings(model=embeddings_model, api_key=self.openai_api_key)
        return cast(EmbeddingsModel, model)
