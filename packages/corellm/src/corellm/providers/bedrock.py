"""
Bedrock Model Provider
https://docs.aws.amazon.com/bedrock/latest/userguide/inference-profiles-support.html
This module implements the AWS Bedrock model provider.
"""

import os
from typing import Any, Dict, Optional, cast

from .base import BaseModel, EmbeddingsModel
from langchain_core.language_models import BaseChatModel


# These will be optional dependencies
try:
    from langchain_aws import ChatBedrockConverse, BedrockEmbeddings

    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False


class BedrockModel(BaseModel):
    """
    AWS Bedrock model provider implementation.

    This class implements the BaseModel interface for AWS Bedrock models.
    """

    def __init__(self, config: Dict[str, Any]):
        """
        Initialize the AWS Bedrock model provider.

        Args:
            config: Configuration dictionary for the AWS Bedrock model provider
        """
        super().__init__(config)

        if not LANGCHAIN_AVAILABLE:
            raise ImportError(
                "LangChain AWS packages are required for Bedrock model. "
                "Install with `pip install langchain-aws`."
            )

    def initialize_chat_model(
        self, temperature: float = 0.7, max_tokens: int = 4096, top_p: float = 0.5
    ) -> BaseChatModel:
        """
        Initialize and return an AWS Bedrock chat model.

        Args:
            temperature: Controls randomness. Higher values mean more random completions.
            max_tokens: Maximum number of tokens to generate.
            top_p: Controls diversity via nucleus sampling.

        Returns:
            An initialized AWS Bedrock chat model
        """
        model = ChatBedrockConverse(
            model=self.config.get(
                "model_name", "anthropic.claude-3-sonnet-20240229-v1:0"
            ),
            region_name=self.config.get("region", "us-east-1"),
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p,
        )
        return model

    def initialize_embeddings_model(self) -> EmbeddingsModel:
        """
        Initialize and return an AWS Bedrock embeddings model.

        Returns:
            An initialized AWS Bedrock embeddings model
        """
        embeddings_model = self.config.get(
            "embeddings_model", "amazon.titan-embed-text-v1"
        )
        model = BedrockEmbeddings(
            model_id=embeddings_model,
            region_name=self.config.get("region", "us-east-1"),
        )
        return cast(EmbeddingsModel, model)
