"""
Model Provider

This module provides a singleton class for managing model providers.
"""

import threading
from typing import Any, ClassVar, Dict, List, Optional, Type, Union

from .anthropic import AnthropicModel
from .azure import AzureModel
from .base import BaseModel, ChatModel, EmbeddingsModel
from .openai import OpenAIModel
from .bedrock import BedrockModel
from .sage_maker import SageMakerModel
from logger import logger


class SingletonMeta(type):
    """
    Singleton metaclass for ensuring only one instance of a class exists.
    """

    _instances: Dict[Type, "ModelProvider"] = {}
    _lock: threading.Lock = threading.Lock()

    def __call__(cls, *args, **kwargs):
        with cls._lock:
            if cls not in cls._instances:
                cls._instances[cls] = super(SingletonMeta, cls).__call__(
                    *args, **kwargs
                )
        return cls._instances[cls]


class ModelProvider(metaclass=SingletonMeta):
    """
    Singleton class for managing model providers.

    This class provides a centralized way to access different model providers.
    """

    MAX_TOKENS_LIMITS: ClassVar[Dict[str, int]] = {
        "azure": 400000,
        "openai": 128000,
        "anthropic": 200000,
    }

    MODEL_CLASSES: ClassVar[Dict[str, Type[BaseModel]]] = {
        "openai": OpenAIModel,
        "azure": AzureModel,
        "anthropic": AnthropicModel,
        "bedrock": BedrockModel,
        "sagemaker": SageMakerModel,
    }

    def __init__(self, configs: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        Initialize the model provider.

        Args:
            configs: List of configuration dictionaries for model providers
        """
        self._configs: List[Dict[str, Any]] = configs or []
        self._models: Dict[str, BaseModel] = {}
        self._default_model_name: Optional[str] = None
        self._load_models()

    @property
    def configs(self) -> List[Dict[str, Any]]:
        """
        Get the configurations for all model providers.

        Returns:
            List of configuration dictionaries
        """
        return self._configs

    @property
    def models(self) -> Dict[str, BaseModel]:
        """
        Get all initialized model providers.

        Returns:
            Dictionary of model providers
        """
        return self._models

    @property
    def default_model_name(self) -> Optional[str]:
        """
        Get the name of the default model provider.

        Returns:
            Name of the default model provider
        """
        return self._default_model_name

    @property
    def default_model(self) -> BaseModel:
        """
        Get the default model provider.

        Returns:
            Default model provider

        Raises:
            ValueError: If no default model is set
        """
        if self._default_model_name and self._default_model_name in self._models:
            return self._models[self._default_model_name]
        else:
            logger.error("No default model is set.")
            raise ValueError("No default model is set.")

    def _load_models(self) -> None:
        """
        Load all model providers from configurations.
        """
        for config in self._configs:
            model_type: str = config.get("type", "")
            if model_type not in self.MODEL_CLASSES:
                logger.error(f"Model type '{model_type}' not supported.")
                continue

            model_class: Type[BaseModel] = self.MODEL_CLASSES[model_type]
            try:
                model_instance: BaseModel = model_class(config)
                model_id: str = config.get("name", f"{model_type}_model")
                model_name = model_instance.model_name
                model_version = model_instance.config.get("api_version", "N/A")
                self._models[model_id] = model_instance
                logger.info(
                    f"Loaded model '{model_name}' with version '{model_version}'."
                )

                if config.get("default", False):
                    self._default_model_name = model_id
                    logger.info(f"Set default model to '{model_name}'.")
            except Exception as e:
                logger.error(f"Error loading model '{model_type}': {e}")
                continue

    def get_model(self, model_name: str) -> Optional[BaseModel]:
        """
        Get a model provider by name.

        Args:
            model_name: Name of the model provider

        Returns:
            Model provider or None if not found
        """
        return self._models.get(model_name, None)

    def get_llm(
        self,
        model_name: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 4096,
        top_p: float = 0.5,
    ) -> ChatModel:
        """
        Get a language model from a model provider.

        Args:
            model_name: Name of the model provider
            temperature: Controls randomness. Higher values mean more random completions.
            max_tokens: Maximum number of tokens to generate.
            top_p: Controls diversity via nucleus sampling.

        Returns:
            Language model

        Raises:
            ValueError: If the model provider is not found
        """
        model_instance: BaseModel
        if model_name:
            model_instance = self.get_model(model_name)
            if not model_instance:
                logger.error(f"Model '{model_name}' not found.")
                raise ValueError(f"Model '{model_name}' not found.")
        else:
            model_instance = self.default_model
        logger.info(
            f"Using model '{model_instance.model_name}' with type '{model_instance.model_type}'"
        )
        model_type: str = model_instance.model_type
        max_tokens = min(max_tokens, self.MAX_TOKENS_LIMITS.get(model_type, max_tokens))
        chat_model = model_instance.initialize_chat_model(
            temperature, max_tokens, top_p
        )
        return chat_model

    def get_embeddings(self, model_name: Optional[str] = None) -> EmbeddingsModel:
        """
        Get an embeddings model from a model provider.

        Args:
            model_name: Name of the model provider

        Returns:
            Embeddings model

        Raises:
            ValueError: If the model provider is not found
        """
        model_instance: BaseModel
        if model_name:
            model_instance = self.get_model(model_name)
            if not model_instance:
                logger.error(f"Model '{model_name}' not found.")
                raise ValueError(f"Model '{model_name}' not found.")
        else:
            model_instance = self.default_model

        embeddings_model = model_instance.initialize_embeddings_model()
        return embeddings_model
