from unittest import mock

import pytest

from corellm.providers.provider import <PERSON><PERSON>rovider, SingletonMeta


class DummyModel:
    def __init__(self, config):
        self.config = config
        self.model_name = config.get("model_name", "dummy")
        self.model_type = config.get("type", "dummy")
    def initialize_chat_model(self, *a, **k):
        return "chat"
    def initialize_embeddings_model(self):
        return "embed"

@mock.patch("corellm.providers.provider.logger")
def test_modelprovider_load_and_get(mock_logger):
    SingletonMeta._instances.clear()
    with mock.patch.dict("corellm.providers.provider.ModelProvider.MODEL_CLASSES", {"openai": DummyModel, "azure": DummyModel, "anthropic": DummyModel}):
        configs = [
            {"type": "openai", "name": "open", "model_name": "gpt", "default": True},
            {"type": "azure", "name": "az", "model_name": "gpt"},
        ]
        provider = ModelProvider(configs)
        assert provider.default_model_name == "open"
        az_model = provider.get_model("az")
        assert az_model is not None
        assert az_model.model_type == "azure"
        assert provider.get_llm() == "chat"
        assert provider.get_llm("az") == "chat"
        assert provider.get_embeddings("open") == "embed"
        with pytest.raises(ValueError):
            provider.get_llm("notfound")
        with pytest.raises(ValueError):
            provider.get_embeddings("notfound") 