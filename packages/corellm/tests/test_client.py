from corellm.client import LLMClient
from corellm.models import LLMResponse


class TestLLMClient:
    def test_init_defaults(self):
        client = LLMClient(api_key="test-key")
        assert client.api_key == "test-key"
        assert client.provider == "openai"
        assert client.model == "gpt-4"

    def test_init_custom(self):
        client = LLMClient(api_key="k", provider="anthropic", model="claude", foo=1)
        assert client.provider == "anthropic"
        assert client.model == "claude"
        assert client.config["foo"] == 1

    def test_generate_non_stream(self):
        client = LLMClient(api_key="k")
        resp = client.generate("prompt")
        assert isinstance(resp, LLMResponse)
        assert resp.text.startswith("This is a placeholder response")

    def test_generate_stream(self):
        client = LLMClient(api_key="k")
        resps = client.generate("prompt", stream=True)
        assert isinstance(resps, list)
        assert all(isinstance(r, LLMResponse) for r in resps)
        assert len(resps) == 3 