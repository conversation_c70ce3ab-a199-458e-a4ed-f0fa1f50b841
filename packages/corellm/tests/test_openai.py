import os
from unittest import mock

import pytest

from corellm.providers.openai import OpenAIModel


@mock.patch.dict(os.environ, {}, clear=True)
def test_init_no_api_key(monkeypatch):
    monkeypatch.setattr("corellm.providers.openai.LANGCHAIN_AVAILABLE", True)
    with pytest.raises(ValueError):
        OpenAIModel({})

@mock.patch.dict(os.environ, {"OPENAI_API_KEY": "o"})
def test_init_success(monkeypatch):
    monkeypatch.setattr("corellm.providers.openai.LANGCHAIN_AVAILABLE", True)
    model = OpenAIModel({"api_key": "o"})
    assert model.api_key == "o"

def test_import_error(monkeypatch):
    monkeypatch.setattr("corellm.providers.openai.LANGCHAIN_AVAILABLE", False)
    with pytest.raises(ImportError):
        OpenAIModel({"api_key": "o"}) 