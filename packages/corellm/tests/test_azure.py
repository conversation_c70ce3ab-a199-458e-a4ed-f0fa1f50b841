import os
from unittest import mock

import pytest

from corellm.providers.azure import AzureModel

REQUIRED = [
    ("api_key", "AZURE_OPENAI_API_KEY"),
    ("azure_endpoint", "AZURE_OPENAI_AZURE_ENDPOINT"),
    ("deployment_name", "AZURE_OPENAI_DEPLOYMENT_NAME"),
    ("api_version", "AZURE_OPENAI_API_VERSION"),
]

@mock.patch.dict(os.environ, {
    "AZURE_OPENAI_API_KEY": "k",
    "AZURE_OPENAI_AZURE_ENDPOINT": "e",
    "AZURE_OPENAI_DEPLOYMENT_NAME": "d",
    "AZURE_OPENAI_API_VERSION": "v"
})
def test_init_success(monkeypatch):
    monkeypatch.setattr("corellm.providers.azure.LANGCHAIN_AVAILABLE", True)
    model = AzureModel({})
    for param, _ in REQUIRED:
        assert hasattr(model, "params")

@mock.patch.dict(os.environ, {}, clear=True)
def test_init_missing_param(monkeypatch):
    monkeypatch.setattr("corellm.providers.azure.LANGCHAIN_AVAILABLE", True)
    for param, _ in REQUIRED:
        config = {k: "v" for k, _ in REQUIRED if k != param}
        with pytest.raises(ValueError, match=param):
            AzureModel(config)

def test_import_error(monkeypatch):
    monkeypatch.setattr("corellm.providers.azure.LANGCHAIN_AVAILABLE", False)
    with pytest.raises(ImportError):
        AzureModel({
            "api_key": "k",
            "azure_endpoint": "e",
            "deployment_name": "d",
            "api_version": "v"
        }) 