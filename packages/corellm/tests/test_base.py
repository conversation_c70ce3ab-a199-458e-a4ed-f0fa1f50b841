import pytest

from corellm.providers.base import BaseModel


class DummyModel(BaseModel):
    def initialize_chat_model(self, *a, **kw):
        return "chat"
    def initialize_embeddings_model(self):
        return "embed"

def test_model_name_property():
    m = DummyModel({"model_name": "foo"})
    assert m.model_name == "foo"
    m2 = DummyModel({})
    assert m2.model_name == "unknown"

def test_abstract_enforcement():
    with pytest.raises(TypeError):
        BaseModel({}) 