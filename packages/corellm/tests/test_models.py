from corellm.models import LLMRequest, LLMResponse


def test_llmrequest_defaults():
    req = LLMRequest(prompt="hi")
    assert req.prompt == "hi"
    assert req.max_tokens == 1000
    assert req.temperature == 0.7
    assert req.model == "gpt-4"
    assert req.stop_sequences == []
    assert isinstance(req.metadata, dict)

def test_llmresponse_defaults():
    resp = LLMResponse(text="out")
    assert resp.text == "out"
    assert isinstance(resp.usage, dict)
    assert resp.model is None
    assert resp.finish_reason is None
    assert isinstance(resp.metadata, dict) 