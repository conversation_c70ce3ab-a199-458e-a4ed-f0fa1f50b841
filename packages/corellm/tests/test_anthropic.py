import os
from unittest import mock

import pytest

from corellm.providers.anthropic import Anthropic<PERSON>odel


@mock.patch.dict(os.environ, {"ANTHROPIC_API_KEY": "a", "OPENAI_API_KEY": "o"})
def test_init_success(monkeypatch):
    monkeypatch.setattr("corellm.providers.anthropic.ANTHROPIC_AVAILABLE", True)
    monkeypatch.setattr("corellm.providers.anthropic.OPENAI_AVAILABLE", True)
    model = AnthropicModel({"api_key": "a", "openai_api_key": "o"})
    assert model.api_key == "a"
    assert model.openai_api_key == "o"

@mock.patch.dict(os.environ, {})
def test_init_no_api_key(monkeypatch):
    monkeypatch.setattr("corellm.providers.anthropic.ANTHROPIC_AVAILABLE", True)
    with pytest.raises(ValueError):
        AnthropicModel({})

def test_import_error(monkeypatch):
    monkeypatch.setattr("corellm.providers.anthropic.ANTHROPIC_AVAILABLE", False)
    with pytest.raises(ImportError):
        AnthropicModel({"api_key": "a"})

def test_embeddings_import(monkeypatch):
    monkeypatch.setattr("corellm.providers.anthropic.ANTHROPIC_AVAILABLE", True)
    monkeypatch.setattr("corellm.providers.anthropic.OPENAI_AVAILABLE", False)
    model = AnthropicModel({"api_key": "a", "openai_api_key": "o"})
    with pytest.raises(ImportError):
        model.initialize_embeddings_model()

def test_embeddings_key(monkeypatch):
    monkeypatch.setattr("corellm.providers.anthropic.ANTHROPIC_AVAILABLE", True)
    monkeypatch.setattr("corellm.providers.anthropic.OPENAI_AVAILABLE", True)
    model = AnthropicModel({"api_key": "a"})
    model.openai_api_key = None
    with pytest.raises(ValueError):
        model.initialize_embeddings_model() 