[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "corellm"
version = "0.1.3"
description = "Core LLM utilities and abstractions for Cymulate"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
]
license = { text = "Proprietary" }
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "License :: Other/Proprietary License",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = [
    "langchain-aws==0.2.30",
    "logger",
    "requests>=2.28.0",
    "typing-extensions>=4.7.0",
    "langchain-openai==0.3.27",
    "langchain-anthropic==0.3.17",
]


[project.urls]
"Homepage" = "https://bitbucket.org/cymulate/pythonlibs"
"Bug Tracker" = "https://bitbucket.org/cymulate/pythonlibs/jira"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]
namespaces = true

[tool.black]
line-length = 88
target-version = ["py38", "py39", "py310", "py311", "py312"]

[tool.isort]
profile = "black"
line_length = 88

[tool.uv.sources]
logger = { workspace = true }

[tool.pytest.ini_options]
addopts = "--cov=corellm --cov-report=term-missing"


[tool.ruff]
select = ["I", "F401"]
