import asyncio
import json
import random
import re
import jwt
from datetime import datetime, timedelta, timezone
from typing import Optional, Set, List, Callable, Awaitable, Self, Tuple

from socketio import AsyncClient
from logger import logger

from pydantic import BaseModel


class WebsocketConfig(BaseModel):
    server_url: str
    jwt_secret: str
    namespace: Optional[str] = None
    token_refresh_period: int = 50
    max_queue_size: int = 1000


EventHandler = Callable[[], Awaitable[None]]
Message = dict


JWT_ALGORITHM: str = "RS256"
ROOM_NAME_PATTERN: re.Pattern = re.compile(r"^[a-f\d]{24}$", re.IGNORECASE)


class WebsocketClient:
    _instance: Optional[Self] = None

    def __init__(self, config: WebsocketConfig):
        self.config = config
        if WebsocketClient._instance is not None:
            raise Exception("This class is a singleton!")

        logger.info("[WebSocket Client] Initializing Websocket Client")
        WebsocketClient._instance = self
        self.server_url: str = self.config.server_url
        self.namespace: str = "/"
        # self.namespace: str = namespace or "/"
        self.sio: AsyncClient = AsyncClient(
            reconnection=True,
            reconnection_attempts=float("inf"),
            reconnection_delay=5,
            reconnection_delay_max=20,
            request_timeout=120,
        )
        self.ping_period: int = 50
        self.token_refresh_period: int = self.config.token_refresh_period
        self.max_queue_size: int = self.config.max_queue_size
        self.operation_queue: asyncio.Queue = asyncio.Queue(maxsize=self.max_queue_size)
        self.token_refresh_task: Optional[asyncio.Task] = None
        self.ping_task: Optional[asyncio.Task] = None
        self.rooms: Set[str] = set()
        self.event_handlers: List[EventHandler] = []
        self.register_internal_event_handlers()

    @staticmethod
    def get_instance(config: WebsocketConfig):
        if WebsocketClient._instance is None:
            if not config.server_url:
                raise ValueError(
                    "A server URL must be provided for the first initialization."
                )
            WebsocketClient(config)
        return WebsocketClient._instance

    @staticmethod
    def generate_jwt(jwt_secret: str) -> Tuple[str, datetime]:
        expiration: datetime = datetime.now(timezone.utc) + timedelta(days=1)
        payload: dict = {
            "serviceName": "chatbot",
            "isInternalService": True,
            "exp": int(expiration.timestamp()),
        }
        token: str = jwt.encode(
            payload, jwt_secret.replace("\\n", "\n"), algorithm=JWT_ALGORITHM
        )
        logger.info(
            f"[WebSocket Client] Generated new JWT expiring at {expiration.isoformat()}"
        )
        return token, expiration

    def register_internal_event_handlers(self) -> None:
        namespace = self.namespace
        logger.info("[WebSocket Client] Registering internal event handlers")

        @self.sio.event(namespace=namespace)
        async def connect() -> None:
            await self.on_connect()

        @self.sio.event(namespace=namespace)
        async def disconnect() -> None:
            await self.on_disconnect()

        @self.sio.event(namespace=namespace)
        async def reconnect() -> None:
            await self.on_reconnect()

        @self.sio.event(namespace=namespace)
        async def reconnect_failed(data: dict) -> None:
            logger.error(
                f"[WebSocket Client] Reconnection failed to namespace {self.namespace}: {data}"
            )

        @self.sio.event(namespace=namespace)
        async def connect_error(data: dict) -> None:
            logger.error(
                f"[WebSocket Client] Connection error to namespace {self.namespace}: {data}"
            )

        @self.sio.on(event="error", namespace=namespace)
        async def error(data: dict) -> None:
            logger.error(
                f"[WebSocket Client] Error in namespace {self.namespace}: {data}"
            )

        @self.sio.on(event="connected", namespace=namespace)
        async def connected(data: dict = None) -> None:
            logger.debug(f"[WebSocket Client] Connected: {data}")

        @self.sio.on(event="authenticated", namespace=namespace)
        async def authenticated(data: dict) -> None:
            logger.debug(f"[WebSocket Client] Authenticated: {data}")

        @self.sio.on(event="pong", namespace=namespace)
        async def pong(data: dict) -> None:
            logger.debug(f"[WebSocket Client] Received pong: {data}")

    async def on_connect(self) -> None:
        logger.info(f"[WebSocket Client] Connected to namespace: {self.namespace}")
        await self.authenticate()
        await self.flush_operation_queue()
        self.start_token_rotation()
        self.start_ping()
        await self.rejoin_rooms()
        await self.register_external_event_handlers()

    async def on_disconnect(self) -> None:
        logger.error(
            f"[WebSocket Client] Disconnected from namespace: {self.namespace}"
        )
        self.stop_ping()
        self.stop_token_rotation()

    async def on_reconnect(self) -> None:
        logger.info(
            f"[WebSocket Client] Successfully reconnected to namespace: {self.namespace}"
        )
        await self.authenticate()
        await self.flush_operation_queue()
        await self.rejoin_rooms()
        await self.register_external_event_handlers()

    def register_external_event_handler(self, handler_func: EventHandler) -> None:
        if not callable(handler_func):
            raise ValueError("Handler must be callable and asynchronous.")
        self.event_handlers.append(handler_func)

    async def register_external_event_handlers(self) -> None:
        logger.info("[WebSocket Client] Registering external event handlers")
        for handler in self.event_handlers:
            if asyncio.iscoroutinefunction(handler):
                await handler()
            else:
                handler()

    async def connect(self, retries: int = 999, delay: int = 2) -> None:
        for attempt in range(retries):
            try:
                await self.sio.connect(self.server_url, namespaces=[self.namespace])
                logger.info(
                    f"[WebSocket Client] Connection initiated to namespace: {self.namespace}"
                )
                return
            except Exception as e:
                jitter = random.uniform(0, 1)
                wait_time = delay * jitter
                logger.error(
                    f"[WebSocket Client] Connection attempt {attempt + 1} failed: {e}. Retrying in {wait_time:.2f} seconds."
                )
                if attempt < retries - 1:
                    await asyncio.sleep(wait_time)
        raise ConnectionError("[WebSocket Client] Max connection retries reached.")

    async def flush_operation_queue(self) -> None:
        while not self.operation_queue.empty():
            event, message = await self.operation_queue.get()
            await self.send_message(event, message)
        logger.info("[WebSocket Client] Operation queue flushed")

    @staticmethod
    def validate_jwt(token: str, jwt_secret: str) -> bool:
        try:
            token_decoded = jwt.decode(
                token,
                jwt_secret,
                algorithms=[JWT_ALGORITHM],
                options={"verify_exp": True, "verify_signature": False},
            )

            exp_timestamp = token_decoded.get("exp")
            if exp_timestamp:
                exp_datetime = datetime.fromtimestamp(exp_timestamp, timezone.utc)
                current_datetime = datetime.now(timezone.utc)
                if current_datetime > exp_datetime:
                    logger.error("[WebSocket Client] JWT has expired")
                    return False
            else:
                logger.error("[WebSocket Client] JWT 'exp' field is missing")
                return False

            return True
        except jwt.ExpiredSignatureError:
            logger.error("[WebSocket Client] JWT has expired")
            return False
        except jwt.InvalidTokenError as e:
            logger.error(f"[WebSocket Client] Invalid JWT: {e}")
            return False

    async def authenticate(self, retries: int = 5, delay: int = 10) -> None:
        for attempt in range(retries):
            try:
                token, token_expiration = self.generate_jwt(self.config.jwt_secret)
                logger.debug(f"Generated token: {token}")
                if not self.validate_jwt(token, self.config.jwt_secret):
                    logger.error("[WebSocket Client] Generated token is invalid.")
                    continue

                logger.info(
                    f"[WebSocket Client] Authenticating with token expiring at {token_expiration.isoformat()}"
                )
                await self.sio.emit(
                    "authenticate", {"token": token}, namespace=self.namespace
                )
                return
            except jwt.InvalidTokenError as jwt_err:
                logger.error(f"[WebSocket Client] JWT Error: {jwt_err}")
            except Exception as e:
                jitter = random.uniform(0, 1)
                wait_time = delay * (2**attempt) + jitter
                logger.error(
                    f"[WebSocket Client] Authentication attempt {attempt + 1} failed: {e}. Retrying in {wait_time:.2f} seconds."
                )
                if attempt < retries - 1:
                    await asyncio.sleep(wait_time)
        raise RuntimeError("[WebSocket Client] Authentication failed after retries")

    def start_token_rotation(self) -> None:
        if self.token_refresh_task:
            self.token_refresh_task.cancel()
        self.token_refresh_task = self.ensure_task_running(
            self.token_refresh_task, self.token_rotation
        )

    @staticmethod
    def should_refresh_token(token: str, refresh_threshold: float = 0.1) -> bool:
        try:
            # Decode the token without verification to extract the expiration time
            payload = jwt.decode(token, options={"verify_signature": False})
            exp = payload.get("exp")
            if not exp:
                raise ValueError("Token does not contain an expiration time")

            # Calculate remaining time
            exp_time = datetime.fromtimestamp(exp, timezone.utc)
            now = datetime.now(timezone.utc)
            time_left = (exp_time - now).total_seconds()

            # Determine if the token should be refreshed
            total_validity = exp - payload.get(
                "iat", exp - 120
            )  # Use issued-at or default to 120 seconds validity
            refresh_threshold_seconds = total_validity * refresh_threshold
            return time_left <= refresh_threshold_seconds
        except Exception as e:
            logger.error(f"[WebSocket Client] Error checking token refresh status: {e}")
            return True  # Default to refreshing in case of errors

    def stop_token_rotation(self) -> None:
        if self.token_refresh_task:
            self.token_refresh_task.cancel()
            self.token_refresh_task = None  # Clear the task reference
            logger.info("[WebSocket Client] Token rotation stopped")

    async def token_rotation(self) -> None:
        retries = 20
        token, token_expiration = self.generate_jwt()
        while True:
            try:
                now = datetime.now(timezone.utc)
                if token_expiration - now < timedelta(
                    seconds=self.token_refresh_period * 0.1
                ):
                    logger.info("[WebSocket Client] Token refresh triggered")
                    for attempt in range(retries):
                        try:
                            token, token_expiration = (
                                self.generate_jwt()
                            )  # Refresh token
                            await self.authenticate()  # Send the new token
                            break  # Exit retry loop on success
                        except Exception as e:
                            logger.error(
                                f"[WebSocket Client] Token refresh attempt {attempt + 1} failed: {e}"
                            )
                            if attempt < retries - 1:
                                await asyncio.sleep(5)
                    else:
                        logger.error(
                            "[WebSocket Client] Token refresh failed after retries"
                        )
                        break  # Stop token rotation on failure
                await asyncio.sleep(self.token_refresh_period)
            except asyncio.CancelledError:
                logger.info("[WebSocket Client] Token rotation task was cancelled.")
                break
            except Exception as e:
                logger.error(f"[WebSocket Client] Token rotation failed: {e}")
                await asyncio.sleep(5)

    async def send_message(self, event: str, message: Message) -> None:
        try:
            assert message.get("clientID") and ROOM_NAME_PATTERN.match(
                message.get("clientID")
            ), "Invalid clientID"
            if self.sio.connected:
                await self.sio.emit(
                    event.value,
                    message,
                    namespace=self.namespace,
                )
            else:
                if self.operation_queue.full():
                    logger.warning(
                        "[WebSocket Client] Operation queue full. Dropping oldest message."
                    )
                    await self.operation_queue.get()  # Drop oldest message
                await self.operation_queue.put((event, message))
        except Exception as e:
            logger.error(f"[WebSocket Client] Sending message failed: {e}")

    def join_room(self, room: str) -> None:
        assert ROOM_NAME_PATTERN.match(room), "Invalid room name"
        self.rooms.add(room)
        asyncio.ensure_future(self.send_message("join", {"room": room}))

    def leave_room(self, room: str) -> None:
        assert ROOM_NAME_PATTERN.match(room), "Invalid room name"
        self.rooms.discard(room)
        asyncio.ensure_future(self.send_message("leave", {"room": room}))

    async def rejoin_rooms(self) -> None:
        if self.rooms:
            logger.info(f"[WebSocket Client] Rejoining rooms: {self.rooms}")
            await asyncio.gather(
                *(self.send_message("join", {"room": room}) for room in self.rooms)
            )

    @staticmethod
    def ensure_task_running(
        task: Optional[asyncio.Task], method: Callable
    ) -> asyncio.Task:
        if not task or task.done():
            return asyncio.ensure_future(method())
        return task

    async def disconnect(self) -> None:
        try:
            if self.token_refresh_task:
                self.token_refresh_task.cancel()
                try:
                    await self.token_refresh_task
                except asyncio.CancelledError:
                    pass
            if self.ping_task:
                self.ping_task.cancel()
                try:
                    await self.ping_task
                except asyncio.CancelledError:
                    pass
            if self.sio.connected:
                await self.sio.disconnect()
            logger.info("[WebSocket Client] Disconnected successfully.")
        except Exception as e:
            logger.error(f"[WebSocket Client] Error during disconnection: {e}")

    def start_ping(self) -> None:
        if self.ping_task:
            self.ping_task.cancel()
        self.ping_task = self.ensure_task_running(self.ping_task, self.ping_loop)

    async def ping_loop(self) -> None:
        while True:
            try:
                await asyncio.sleep(self.ping_period)
                if self.sio.connected:
                    await self.ping()
                else:
                    logger.warning("[WebSocket Client] Not connected, skipping ping")
            except asyncio.CancelledError:
                logger.info("[WebSocket Client] Ping loop task was cancelled.")
                break
            except Exception as e:
                logger.error(f"[WebSocket Client] Ping loop failed: {e}")

    async def ping(self, retries: int = 3) -> None:
        attempt = 0
        while attempt < retries:
            try:
                if self.sio.connected:
                    await self.sio.emit("ping", namespace=self.namespace)
                    logger.debug("[WebSocket Client] Ping successful")
                    return
                else:
                    logger.warning(
                        "[WebSocket Client] Socket not connected. Reconnecting..."
                    )
                    await self.connect()
                    return
            except Exception as e:
                attempt += 1
                logger.error(f"[WebSocket Client] Ping attempt {attempt} failed: {e}")
        logger.warning("[WebSocket Client] All ping attempts failed. Reconnecting...")
        await self.connect()

    def stop_ping(self) -> None:
        if self.ping_task:
            self.ping_task.cancel()
