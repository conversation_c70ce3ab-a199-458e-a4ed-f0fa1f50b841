"""
Tests for task management methods of WebsocketClient.
"""

import as<PERSON><PERSON>
import os
import sys
from unittest.mock import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

# Add the src directory to the path to import our local websocket module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from websocket.websocket_client import Websocket<PERSON><PERSON>, WebsocketConfig


class TestWebsocketClientTasks:
    """Test cases for task management in WebsocketClient."""

    def setup_method(self):
        """Reset singleton instance before each test."""
        WebsocketClient._instance = None

    @patch('websocket.websocket_client.AsyncClient')
    def test_start_token_rotation(self, mock_async_client):
        """Test starting token rotation task."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock existing task
        mock_existing_task = Mock()
        client.token_refresh_task = mock_existing_task
        
        with patch.object(client, 'ensure_task_running') as mock_ensure:
            mock_ensure.return_value = Mock()
            client.start_token_rotation()
            
            mock_existing_task.cancel.assert_called_once()
            mock_ensure.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    def test_stop_token_rotation(self, mock_async_client):
        """Test stopping token rotation task."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock existing task
        mock_task = Mock()
        client.token_refresh_task = mock_task
        
        client.stop_token_rotation()
        
        mock_task.cancel.assert_called_once()
        assert client.token_refresh_task is None

    @patch('websocket.websocket_client.AsyncClient')
    def test_stop_token_rotation_no_task(self, mock_async_client):
        """Test stopping token rotation when no task exists."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        client.token_refresh_task = None
        
        # Should not raise exception
        client.stop_token_rotation()

    @patch('websocket.websocket_client.AsyncClient')
    def test_start_ping(self, mock_async_client):
        """Test starting ping task."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock existing task
        mock_existing_task = Mock()
        client.ping_task = mock_existing_task
        
        with patch.object(client, 'ensure_task_running') as mock_ensure:
            mock_ensure.return_value = Mock()
            client.start_ping()
            
            mock_existing_task.cancel.assert_called_once()
            mock_ensure.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    def test_stop_ping(self, mock_async_client):
        """Test stopping ping task."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock existing task
        mock_task = Mock()
        client.ping_task = mock_task
        
        client.stop_ping()
        
        mock_task.cancel.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    def test_stop_ping_no_task(self, mock_async_client):
        """Test stopping ping when no task exists."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        client.ping_task = None
        
        # Should not raise exception
        client.stop_ping()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_ping_loop_success(self, mock_async_client):
        """Test ping loop with successful pings."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        client.ping_period = 0.01  # Very short for testing
        
        # Mock connected socket
        mock_sio = mock_async_client.return_value
        mock_sio.connected = True
        client.sio = mock_sio
        
        ping_count = 0
        original_ping = client.ping
        
        async def mock_ping(retries=3):
            nonlocal ping_count
            ping_count += 1
            if ping_count >= 2:  # Stop after 2 pings
                raise asyncio.CancelledError()
            
        client.ping = mock_ping
        
        # Should handle CancelledError gracefully
        await client.ping_loop()
        
        assert ping_count == 2

    @patch('websocket.websocket_client.AsyncClient')
    async def test_ping_loop_not_connected(self, mock_async_client):
        """Test ping loop when not connected."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        client.ping_period = 0.01  # Very short for testing
        
        # Mock not connected socket
        mock_sio = mock_async_client.return_value
        mock_sio.connected = False
        client.sio = mock_sio
        
        loop_count = 0
        
        async def mock_sleep(duration):
            nonlocal loop_count
            loop_count += 1
            if loop_count >= 2:  # Stop after 2 iterations
                raise asyncio.CancelledError()
        
        with patch('asyncio.sleep', side_effect=mock_sleep):
            await client.ping_loop()
        
        assert loop_count == 2

    @patch('websocket.websocket_client.AsyncClient')
    async def test_ping_loop_exception(self, mock_async_client):
        """Test ping loop handles exceptions."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        client.ping_period = 0.01  # Very short for testing
        
        # Mock connected socket
        mock_sio = mock_async_client.return_value
        mock_sio.connected = True
        client.sio = mock_sio
        
        ping_count = 0
        
        async def mock_ping(retries=3):
            nonlocal ping_count
            ping_count += 1
            if ping_count == 1:
                raise Exception("Ping failed")
            elif ping_count >= 2:
                raise asyncio.CancelledError()
        
        client.ping = mock_ping
        
        # Should handle exception and continue
        await client.ping_loop()
        
        assert ping_count == 2

    @patch('websocket.websocket_client.AsyncClient')
    @patch('websocket.websocket_client.WebsocketClient.generate_jwt')
    async def test_token_rotation_success(self, mock_generate_jwt, mock_async_client):
        """Test token rotation with successful refresh."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        client.token_refresh_period = 0.01  # Very short for testing
        
        # Mock JWT generation with short expiration
        from datetime import datetime, timedelta, timezone
        short_expiration = datetime.now(timezone.utc) + timedelta(seconds=0.005)
        mock_generate_jwt.return_value = ("test-token", short_expiration)
        
        refresh_count = 0
        
        async def mock_authenticate():
            nonlocal refresh_count
            refresh_count += 1
            if refresh_count >= 2:  # Stop after 2 refreshes
                raise asyncio.CancelledError()
        
        client.authenticate = mock_authenticate
        
        # Should handle CancelledError gracefully
        await client.token_rotation()
        
        assert refresh_count == 2


    @patch('websocket.websocket_client.AsyncClient')
    def test_register_internal_event_handlers(self, mock_async_client):
        """Test that internal event handlers are registered correctly."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        
        # Mock the AsyncClient and its event registration
        mock_sio = mock_async_client.return_value
        mock_sio.event = Mock()
        mock_sio.on = Mock()
        
        client = WebsocketClient(config)
        
        # Check that event handlers were registered
        assert mock_sio.event.call_count >= 3  # connect, disconnect, reconnect
        assert mock_sio.on.call_count >= 4  # error, connected, authenticated, pong

    @patch('websocket.websocket_client.AsyncClient')
    async def test_send_message_error_handling(self, mock_async_client):
        """Test send_message error handling."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock socket that raises exception
        mock_sio = mock_async_client.return_value
        mock_sio.connected = True
        mock_sio.emit = AsyncMock(side_effect=Exception("Send failed"))
        client.sio = mock_sio
        
        # Valid message
        message = {"clientID": "507f1f77bcf86cd799439011", "text": "test"}
        
        # Should not raise exception (error is logged)
        await client.send_message("test_event", message)

    def test_validate_jwt_missing_exp(self):
        """Test JWT validation with missing exp field."""
        import jwt
        
        jwt_secret = "test-secret"
        
        # Create token without exp field using HS256
        payload = {
            "serviceName": "chatbot",
            "isInternalService": True
        }
        token = jwt.encode(payload, jwt_secret, algorithm="HS256")
        
        # Mock the algorithm for validation
        with patch('websocket.websocket_client.JWT_ALGORITHM', 'HS256'):
            # Should fail validation
            assert WebsocketClient.validate_jwt(token, jwt_secret) is False

    def test_should_refresh_token_error(self):
        """Test should_refresh_token with invalid token (error case)."""
        invalid_token = "completely.invalid.token"
        
        # Should return True (default to refreshing on error)
        assert WebsocketClient.should_refresh_token(invalid_token) is True

    def test_should_refresh_token_no_iat(self):
        """Test should_refresh_token with token missing iat field."""
        from datetime import datetime, timedelta, timezone

        import jwt
        
        # Create token without iat field
        exp_time = datetime.now(timezone.utc) + timedelta(seconds=5)
        payload = {
            "exp": int(exp_time.timestamp())
        }
        token = jwt.encode(payload, "secret", algorithm="HS256")
        
        # Should use default validity period
        result = WebsocketClient.should_refresh_token(token)
        # With 5 seconds left and default 120 second validity, threshold is 12 seconds
        # So 5 seconds remaining should trigger refresh
        assert result is True 