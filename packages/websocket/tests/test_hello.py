"""
Basic module structure tests for websocket package.
"""

import os
import sys

# Add the src directory to the path to import our local websocket module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


def test_module_imports():
    """Test that the websocket module can be imported."""
    import websocket
    assert websocket is not None


def test_websocket_client_module_imports():
    """Test that websocket_client module can be imported."""
    from websocket import websocket_client
    assert websocket_client is not None


def test_constants():
    """Test that constants are defined correctly."""
    from websocket.websocket_client import JWT_ALGORITHM, ROOM_NAME_PATTERN
    
    assert JWT_ALGORITHM == "RS256"
    assert ROOM_NAME_PATTERN is not None
    
    # Test room name pattern
    assert ROOM_NAME_PATTERN.match("507f1f77bcf86cd799439011") is not None  # Valid 24-char hex
    assert ROOM_NAME_PATTERN.match("invalid-room") is None  # Invalid format 