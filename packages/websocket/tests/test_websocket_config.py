"""
Tests for WebsocketConfig class.
"""

import os
import sys

import pytest

# Add the src directory to the path to import our local websocket module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from websocket.websocket_client import WebsocketConfig


class TestWebsocketConfig:
    """Test cases for WebsocketConfig."""

    def test_config_creation_minimal(self):
        """Test creating config with minimal required fields."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        
        assert config.server_url == "ws://localhost:3000"
        assert config.jwt_secret == "test-secret"
        assert config.namespace is None
        assert config.token_refresh_period == 50
        assert config.max_queue_size == 1000

    def test_config_creation_full(self):
        """Test creating config with all fields."""
        config = WebsocketConfig(
            server_url="wss://example.com:8080",
            jwt_secret="my-secret-key",
            namespace="/chat",
            token_refresh_period=30,
            max_queue_size=500
        )
        
        assert config.server_url == "wss://example.com:8080"
        assert config.jwt_secret == "my-secret-key"
        assert config.namespace == "/chat"
        assert config.token_refresh_period == 30
        assert config.max_queue_size == 500

    def test_config_validation_missing_server_url(self):
        """Test that missing server_url raises validation error."""
        with pytest.raises(Exception):  # Pydantic validation error
            WebsocketConfig(jwt_secret="test-secret")

    def test_config_validation_missing_jwt_secret(self):
        """Test that missing jwt_secret raises validation error."""
        with pytest.raises(Exception):  # Pydantic validation error
            WebsocketConfig(server_url="ws://localhost:3000")

    def test_config_defaults(self):
        """Test that default values are properly set."""
        config = WebsocketConfig(
            server_url="ws://test.com",
            jwt_secret="secret"
        )
        
        # Test default values
        assert config.namespace is None
        assert config.token_refresh_period == 50
        assert config.max_queue_size == 1000

    def test_config_serialization(self):
        """Test that config can be serialized to dict."""
        config = WebsocketConfig(
            server_url="ws://test.com",
            jwt_secret="secret",
            namespace="/test"
        )
        
        config_dict = config.model_dump()
        
        assert config_dict["server_url"] == "ws://test.com"
        assert config_dict["jwt_secret"] == "secret"
        assert config_dict["namespace"] == "/test"
        assert config_dict["token_refresh_period"] == 50
        assert config_dict["max_queue_size"] == 1000 