"""
Tests for websocket module imports and version.
"""

import os
import sys

# Add the src directory to the path to import our local websocket module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from websocket import Websocket<PERSON>lient, WebsocketConfig, __version__


def test_imports():
    """Test that all main classes can be imported."""
    assert WebsocketClient is not None
    assert WebsocketConfig is not None


def test_version():
    """Test that version is defined."""
    assert isinstance(__version__, str)
    assert len(__version__) > 0


def test_all_exports():
    """Test that __all__ contains expected exports."""
    from websocket import __all__
    expected = ["WebsocketClient", "WebsocketConfig"]
    assert set(__all__) == set(expected) 