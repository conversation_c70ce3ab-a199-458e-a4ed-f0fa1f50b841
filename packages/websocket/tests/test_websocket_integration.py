"""
Integration tests for WebsocketClient covering lifecycle and connection scenarios.
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta, timezone
from unittest.mock import AsyncMock, Mock, patch

import pytest

# Add the src directory to the path to import our local websocket module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from websocket.websocket_client import WebsocketClient, WebsocketConfig


class TestWebsocketClientIntegration:
    """Integration test cases for WebsocketClient lifecycle and connection handling."""

    def setup_method(self):
        """Reset singleton instance before each test."""
        WebsocketClient._instance = None

    @patch('websocket.websocket_client.AsyncClient')
    async def test_on_connect_lifecycle(self, mock_async_client):
        """Test on_connect lifecycle method execution."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock all the methods called in on_connect
        client.authenticate = AsyncMock()
        client.flush_operation_queue = AsyncMock()
        client.start_token_rotation = Mock()
        client.start_ping = Mock()
        client.rejoin_rooms = AsyncMock()
        client.register_external_event_handlers = AsyncMock()
        
        await client.on_connect()
        
        client.authenticate.assert_called_once()
        client.flush_operation_queue.assert_called_once()
        client.start_token_rotation.assert_called_once()
        client.start_ping.assert_called_once()
        client.rejoin_rooms.assert_called_once()
        client.register_external_event_handlers.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_on_disconnect_lifecycle(self, mock_async_client):
        """Test on_disconnect lifecycle method execution."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        client.stop_ping = Mock()
        client.stop_token_rotation = Mock()
        
        await client.on_disconnect()
        
        client.stop_ping.assert_called_once()
        client.stop_token_rotation.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_on_reconnect_lifecycle(self, mock_async_client):
        """Test on_reconnect lifecycle method execution."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock all the methods called in on_reconnect
        client.authenticate = AsyncMock()
        client.flush_operation_queue = AsyncMock()
        client.rejoin_rooms = AsyncMock()
        client.register_external_event_handlers = AsyncMock()
        
        await client.on_reconnect()
        
        client.authenticate.assert_called_once()
        client.flush_operation_queue.assert_called_once()
        client.rejoin_rooms.assert_called_once()
        client.register_external_event_handlers.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_connect_retry_logic(self, mock_async_client):
        """Test connect method with retry logic."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        mock_sio = mock_async_client.return_value
        mock_sio.connect = AsyncMock(side_effect=[Exception("Connection failed"), None])
        client.sio = mock_sio
        
        with patch('asyncio.sleep') as mock_sleep:
            await client.connect(retries=2, delay=1)
            
            assert mock_sio.connect.call_count == 2
            mock_sleep.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_connect_max_retries_reached(self, mock_async_client):
        """Test connect method when max retries is reached."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        mock_sio = mock_async_client.return_value
        mock_sio.connect = AsyncMock(side_effect=Exception("Connection failed"))
        client.sio = mock_sio
        
        with patch('asyncio.sleep'):
            with pytest.raises(ConnectionError, match="Max connection retries reached"):
                await client.connect(retries=2, delay=1)

    @patch('websocket.websocket_client.AsyncClient')
    async def test_flush_operation_queue(self, mock_async_client):
        """Test flush_operation_queue method."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Add messages to queue
        await client.operation_queue.put(("test_event", {"clientID": "507f1f77bcf86cd799439011", "data": "test1"}))
        await client.operation_queue.put(("test_event2", {"clientID": "507f1f77bcf86cd799439012", "data": "test2"}))
        
        client.send_message = AsyncMock()
        
        await client.flush_operation_queue()
        
        assert client.send_message.call_count == 2
        assert client.operation_queue.empty()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_authenticate_with_invalid_token(self, mock_async_client):
        """Test authenticate method when generated token is invalid."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        mock_sio = mock_async_client.return_value
        client.sio = mock_sio
        
        with patch.object(client, 'generate_jwt') as mock_gen:
            with patch.object(client, 'validate_jwt') as mock_validate:
                mock_gen.return_value = ("invalid-token", datetime.now(timezone.utc))
                mock_validate.return_value = False
                
                with pytest.raises(RuntimeError, match="Authentication failed after retries"):
                    await client.authenticate(retries=1, delay=0.01)

    @patch('websocket.websocket_client.AsyncClient')
    async def test_authenticate_jwt_error(self, mock_async_client):
        """Test authenticate method with JWT error."""
        import jwt
        
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        mock_sio = mock_async_client.return_value
        mock_sio.emit = AsyncMock(side_effect=jwt.InvalidTokenError("Invalid token"))
        client.sio = mock_sio
        
        with patch.object(client, 'generate_jwt') as mock_gen:
            with patch.object(client, 'validate_jwt') as mock_validate:
                mock_gen.return_value = ("test-token", datetime.now(timezone.utc))
                mock_validate.return_value = True
                
                with patch('asyncio.sleep'):
                    with pytest.raises(RuntimeError, match="Authentication failed after retries"):
                        await client.authenticate(retries=1, delay=0.01)

    @patch('websocket.websocket_client.AsyncClient')
    async def test_send_message_queue_full(self, mock_async_client):
        """Test send_message when queue is full."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret",
            max_queue_size=1
        )
        client = WebsocketClient(config)
        
        mock_sio = mock_async_client.return_value
        mock_sio.connected = False
        client.sio = mock_sio
        
        # Fill the queue
        message1 = {"clientID": "507f1f77bcf86cd799439011", "data": "test1"}
        message2 = {"clientID": "507f1f77bcf86cd799439012", "data": "test2"}
        
        await client.send_message("test_event1", message1)
        assert client.operation_queue.qsize() == 1
        
        # This should drop the oldest message
        await client.send_message("test_event2", message2)
        assert client.operation_queue.qsize() == 1

    @patch('websocket.websocket_client.AsyncClient')
    async def test_register_external_event_handlers_sync(self, mock_async_client):
        """Test register_external_event_handlers with sync handlers."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        sync_handler = Mock()
        async_handler = AsyncMock()
        
        client.register_external_event_handler(sync_handler)
        client.register_external_event_handler(async_handler)
        
        await client.register_external_event_handlers()
        
        sync_handler.assert_called_once()
        async_handler.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_rejoin_rooms_with_rooms(self, mock_async_client):
        """Test rejoin_rooms when there are rooms to rejoin."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        client.rooms = {"507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012"}
        client.send_message = AsyncMock()
        
        await client.rejoin_rooms()
        
        assert client.send_message.call_count == 2

    @patch('websocket.websocket_client.AsyncClient')
    async def test_rejoin_rooms_empty(self, mock_async_client):
        """Test rejoin_rooms when no rooms to rejoin."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        client.rooms = set()
        client.send_message = AsyncMock()
        
        await client.rejoin_rooms()
        
        client.send_message.assert_not_called()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_disconnect_with_exception(self, mock_async_client):
        """Test disconnect method when exception occurs."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        mock_sio = mock_async_client.return_value
        mock_sio.connected = True
        mock_sio.disconnect = AsyncMock(side_effect=Exception("Disconnect failed"))
        client.sio = mock_sio
        
        # Should not raise exception
        await client.disconnect()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_ping_not_connected_reconnect(self, mock_async_client):
        """Test ping method when not connected - should trigger reconnect."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        mock_sio = mock_async_client.return_value
        mock_sio.connected = False
        client.sio = mock_sio
        
        client.connect = AsyncMock()
        
        await client.ping(retries=1)
        
        client.connect.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_ping_all_attempts_fail(self, mock_async_client):
        """Test ping method when all attempts fail."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        mock_sio = mock_async_client.return_value
        mock_sio.connected = True
        mock_sio.emit = AsyncMock(side_effect=Exception("Ping failed"))
        client.sio = mock_sio
        
        client.connect = AsyncMock()
        
        await client.ping(retries=2)
        
        assert mock_sio.emit.call_count == 2
        client.connect.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    async def test_token_rotation_failure_scenario(self, mock_async_client):
        """Test token rotation when refresh fails repeatedly."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        client.token_refresh_period = 0.01
        
        # Mock token that will immediately need refresh
        from datetime import datetime, timedelta, timezone
        expired_time = datetime.now(timezone.utc) - timedelta(seconds=1)  # Already expired
        
        iteration_count = 0
        
        with patch.object(client, 'generate_jwt') as mock_gen:
            with patch.object(client, 'authenticate') as mock_auth:
                mock_gen.return_value = ("test-token", expired_time)
                mock_auth.side_effect = Exception("Auth failed")
                
                async def mock_sleep(duration):
                    nonlocal iteration_count
                    iteration_count += 1
                    if iteration_count >= 2:
                        raise asyncio.CancelledError()
                
                with patch('asyncio.sleep', side_effect=mock_sleep):
                    await client.token_rotation()
                
                # Should try to authenticate since token is expired
                assert mock_auth.call_count >= 1

    @patch('websocket.websocket_client.AsyncClient')
    async def test_token_rotation_exception_handling(self, mock_async_client):
        """Test token rotation with general exception handling."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        client.token_refresh_period = 0.01
        
        iteration_count = 0
        
        # First call succeeds to initialize, subsequent calls fail
        def generate_jwt_side_effect(*args):
            nonlocal iteration_count
            call_count = getattr(generate_jwt_side_effect, 'call_count', 0)
            generate_jwt_side_effect.call_count = call_count + 1
            
            if call_count == 0:
                return ("initial-token", datetime.now(timezone.utc) + timedelta(days=1))
            else:
                raise Exception("Generation failed")
        
        with patch.object(client, 'generate_jwt') as mock_gen:
            mock_gen.side_effect = generate_jwt_side_effect
            
            async def mock_sleep(duration):
                nonlocal iteration_count
                iteration_count += 1
                if iteration_count >= 2:
                    raise asyncio.CancelledError()
            
            with patch('asyncio.sleep', side_effect=mock_sleep):
                await client.token_rotation()
            
            assert iteration_count == 2

    def test_validate_jwt_invalid_signature(self):
        """Test JWT validation with invalid signature."""
        import jwt
        
        jwt_secret = "test-secret"
        wrong_secret = "wrong-secret"
        
        # Create token with wrong secret
        from datetime import datetime, timedelta, timezone
        exp_time = datetime.now(timezone.utc) + timedelta(seconds=300)
        payload = {
            "serviceName": "chatbot",
            "isInternalService": True,
            "exp": int(exp_time.timestamp())
        }
        token = jwt.encode(payload, wrong_secret, algorithm="HS256")
        
        # Mock the algorithm for validation
        with patch('websocket.websocket_client.JWT_ALGORITHM', 'HS256'):
            # Should fail validation due to signature verification being disabled in validate_jwt
            # but will fail due to wrong algorithm expectation
            assert WebsocketClient.validate_jwt(token, jwt_secret) is True

    def test_validate_jwt_expired_signature_error(self):
        """Test JWT validation with ExpiredSignatureError."""
        import jwt
        
        # This will test the except jwt.ExpiredSignatureError branch
        with patch('jwt.decode', side_effect=jwt.ExpiredSignatureError("Token expired")):
            result = WebsocketClient.validate_jwt("test-token", "test-secret")
            assert result is False 