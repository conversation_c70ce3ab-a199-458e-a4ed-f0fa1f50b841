"""
Simplified tests for WebsocketClient class focusing on basic functionality.
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta, timezone
from unittest.mock import Mock, patch

import jwt
import pytest

# Add the src directory to the path to import our local websocket module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from websocket.websocket_client import Websocket<PERSON>lient, WebsocketConfig


class TestWebsocketClientSimple:
    """Simplified test cases for WebsocketClient."""

    def setup_method(self):
        """Reset singleton instance before each test."""
        WebsocketClient._instance = None

    def test_singleton_pattern(self):
        """Test that WebsocketClient follows singleton pattern."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        
        with patch('websocket.websocket_client.AsyncClient'):
            client1 = WebsocketClient(config)
            
            # Second instantiation should raise exception
            with pytest.raises(Exception, match="This class is a singleton!"):
                WebsocketClient(config)
            
            # get_instance should return the same instance
            client2 = WebsocketClient.get_instance(config)
            assert client1 is client2

    def test_get_instance_first_time(self):
        """Test get_instance when no instance exists."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        
        with patch('websocket.websocket_client.AsyncClient'):
            client = WebsocketClient.get_instance(config)
            assert client is not None
            assert WebsocketClient._instance is client

    def test_get_instance_no_config_error(self):
        """Test get_instance raises error when no config provided and no instance exists."""
        config = WebsocketConfig(server_url="", jwt_secret="test-secret")
        
        with pytest.raises(ValueError, match="A server URL must be provided for the first initialization"):
            WebsocketClient.get_instance(config)

    @patch('websocket.websocket_client.AsyncClient')
    def test_init_basic(self, mock_async_client):
        """Test basic initialization."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret",
            namespace="/test",
            token_refresh_period=30,
            max_queue_size=500
        )
        
        client = WebsocketClient(config)
        
        assert client.config == config
        assert client.server_url == "ws://localhost:3000"
        assert client.namespace == "/"
        assert client.token_refresh_period == 30
        assert client.max_queue_size == 500
        assert len(client.rooms) == 0
        assert len(client.event_handlers) == 0
        
        # Check that AsyncClient was initialized
        mock_async_client.assert_called_once()

    def test_generate_jwt(self):
        """Test JWT generation."""
        jwt_secret = "test-secret-key"
        
        # Mock the algorithm to use HS256 for testing
        with patch('websocket.websocket_client.JWT_ALGORITHM', 'HS256'):
            token, expiration = WebsocketClient.generate_jwt(jwt_secret)
            
            # Check that token is a string
            assert isinstance(token, str)
            assert len(token) > 0
            
            # Check that expiration is in the future
            assert isinstance(expiration, datetime)
            assert expiration > datetime.now(timezone.utc)
            
            # Decode and verify token contents
            decoded = jwt.decode(token, options={"verify_signature": False})
            assert decoded["serviceName"] == "chatbot"
            assert decoded["isInternalService"] is True
            assert "exp" in decoded

    def test_validate_jwt_valid(self):
        """Test JWT validation with valid token."""
        jwt_secret = "test-secret"
        
        # Create a valid token using HS256
        expiration = datetime.now(timezone.utc) + timedelta(hours=1)
        payload = {
            "serviceName": "chatbot",
            "isInternalService": True,
            "exp": int(expiration.timestamp())
        }
        token = jwt.encode(payload, jwt_secret, algorithm="HS256")
        
        # Mock the algorithm for validation
        with patch('websocket.websocket_client.JWT_ALGORITHM', 'HS256'):
            # Should validate successfully
            assert WebsocketClient.validate_jwt(token, jwt_secret) is True

    def test_validate_jwt_expired(self):
        """Test JWT validation with expired token."""
        jwt_secret = "test-secret"
        
        # Create an expired token using HS256
        expiration = datetime.now(timezone.utc) - timedelta(hours=1)
        payload = {
            "serviceName": "chatbot",
            "isInternalService": True,
            "exp": int(expiration.timestamp())
        }
        token = jwt.encode(payload, jwt_secret, algorithm="HS256")
        
        # Mock the algorithm for validation
        with patch('websocket.websocket_client.JWT_ALGORITHM', 'HS256'):
            # Should fail validation
            assert WebsocketClient.validate_jwt(token, jwt_secret) is False

    def test_validate_jwt_invalid_token(self):
        """Test JWT validation with invalid token."""
        jwt_secret = "test-secret"
        invalid_token = "invalid.token.here"
        
        # Should fail validation
        assert WebsocketClient.validate_jwt(invalid_token, jwt_secret) is False

    def test_validate_jwt_missing_exp(self):
        """Test JWT validation with missing exp field."""
        jwt_secret = "test-secret"
        
        # Create token without exp field using HS256
        payload = {
            "serviceName": "chatbot",
            "isInternalService": True
        }
        token = jwt.encode(payload, jwt_secret, algorithm="HS256")
        
        # Mock the algorithm for validation
        with patch('websocket.websocket_client.JWT_ALGORITHM', 'HS256'):
            # Should fail validation
            assert WebsocketClient.validate_jwt(token, jwt_secret) is False

    def test_should_refresh_token_needs_refresh(self):
        """Test should_refresh_token when token needs refresh."""
        # Create a token that expires soon
        exp_time = datetime.now(timezone.utc) + timedelta(seconds=5)
        iat_time = datetime.now(timezone.utc) - timedelta(seconds=115)
        payload = {
            "exp": int(exp_time.timestamp()),
            "iat": int(iat_time.timestamp())
        }
        token = jwt.encode(payload, "secret", algorithm="HS256")
        
        # Should need refresh (expires in 5 seconds, total validity ~120 seconds, 10% threshold = 12 seconds)
        assert WebsocketClient.should_refresh_token(token) is True

    def test_should_refresh_token_no_refresh_needed(self):
        """Test should_refresh_token when token doesn't need refresh."""
        # Create a token that expires in 1 hour
        exp_time = datetime.now(timezone.utc) + timedelta(hours=1)
        iat_time = datetime.now(timezone.utc)
        payload = {
            "exp": int(exp_time.timestamp()),
            "iat": int(iat_time.timestamp())
        }
        token = jwt.encode(payload, "secret", algorithm="HS256")
        
        # Should not need refresh
        assert WebsocketClient.should_refresh_token(token) is False

    def test_should_refresh_token_error(self):
        """Test should_refresh_token with invalid token (error case)."""
        invalid_token = "completely.invalid.token"
        
        # Should return True (default to refreshing on error)
        assert WebsocketClient.should_refresh_token(invalid_token) is True

    def test_should_refresh_token_no_iat(self):
        """Test should_refresh_token with token missing iat field."""
        # Create token without iat field
        exp_time = datetime.now(timezone.utc) + timedelta(seconds=5)
        payload = {
            "exp": int(exp_time.timestamp())
        }
        token = jwt.encode(payload, "secret", algorithm="HS256")
        
        # Should use default validity period
        result = WebsocketClient.should_refresh_token(token)
        # With 5 seconds left and default 120 second validity, threshold is 12 seconds
        # So 5 seconds remaining should trigger refresh
        assert result is True

    @patch('websocket.websocket_client.AsyncClient')
    def test_register_external_event_handler(self, mock_async_client):
        """Test registering external event handlers."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Test with valid handler
        async def test_handler():
            pass
            
        client.register_external_event_handler(test_handler)
        assert test_handler in client.event_handlers
        
        # Test with invalid handler
        with pytest.raises(ValueError, match="Handler must be callable and asynchronous"):
            client.register_external_event_handler("not_callable")

    @patch('websocket.websocket_client.AsyncClient')
    def test_join_room_valid(self, mock_async_client):
        """Test joining a room with valid room name."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Valid room name (24 character hex string)
        room = "507f1f77bcf86cd799439011"
        
        with patch.object(client, 'send_message') as mock_send:
            client.join_room(room)
            
        assert room in client.rooms

    @patch('websocket.websocket_client.AsyncClient')
    def test_join_room_invalid(self, mock_async_client):
        """Test joining a room with invalid room name."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Invalid room name
        with pytest.raises(AssertionError, match="Invalid room name"):
            client.join_room("invalid-room")

    @patch('websocket.websocket_client.AsyncClient')
    def test_leave_room_valid(self, mock_async_client):
        """Test leaving a room with valid room name."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Valid room name
        room = "507f1f77bcf86cd799439011"
        client.rooms.add(room)
        
        with patch.object(client, 'send_message') as mock_send:
            client.leave_room(room)
            
        assert room not in client.rooms

    @patch('websocket.websocket_client.AsyncClient')
    def test_leave_room_invalid(self, mock_async_client):
        """Test leaving a room with invalid room name."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Invalid room name
        with pytest.raises(AssertionError, match="Invalid room name"):
            client.leave_room("invalid-room")

    def test_ensure_task_running_new_task(self):
        """Test ensure_task_running creates new task when none exists."""
        async def dummy_method():
            await asyncio.sleep(0.01)
            
        task = WebsocketClient.ensure_task_running(None, dummy_method)
        
        assert task is not None
        assert isinstance(task, asyncio.Task)
        
        # Clean up
        task.cancel()

    def test_ensure_task_running_existing_task(self):
        """Test ensure_task_running returns existing task when it's still running."""
        # Mock an existing task instead of creating a real one
        existing_task = Mock()
        existing_task.done.return_value = False  # Task is still running
        
        async def dummy_method():
            await asyncio.sleep(1)
            
        task = WebsocketClient.ensure_task_running(existing_task, dummy_method)
        
        assert task is existing_task

    @patch('websocket.websocket_client.AsyncClient')
    def test_start_token_rotation(self, mock_async_client):
        """Test starting token rotation task."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock existing task
        mock_existing_task = Mock()
        client.token_refresh_task = mock_existing_task
        
        with patch.object(client, 'ensure_task_running') as mock_ensure:
            mock_ensure.return_value = Mock()
            client.start_token_rotation()
            
            mock_existing_task.cancel.assert_called_once()
            mock_ensure.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    def test_stop_token_rotation(self, mock_async_client):
        """Test stopping token rotation task."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock existing task
        mock_task = Mock()
        client.token_refresh_task = mock_task
        
        client.stop_token_rotation()
        
        mock_task.cancel.assert_called_once()
        assert client.token_refresh_task is None

    @patch('websocket.websocket_client.AsyncClient')
    def test_start_ping(self, mock_async_client):
        """Test starting ping task."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock existing task
        mock_existing_task = Mock()
        client.ping_task = mock_existing_task
        
        with patch.object(client, 'ensure_task_running') as mock_ensure:
            mock_ensure.return_value = Mock()
            client.start_ping()
            
            mock_existing_task.cancel.assert_called_once()
            mock_ensure.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    def test_stop_ping(self, mock_async_client):
        """Test stopping ping task."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        client = WebsocketClient(config)
        
        # Mock existing task
        mock_task = Mock()
        client.ping_task = mock_task
        
        client.stop_ping()
        
        mock_task.cancel.assert_called_once()

    @patch('websocket.websocket_client.AsyncClient')
    def test_register_internal_event_handlers(self, mock_async_client):
        """Test that internal event handlers are registered correctly."""
        config = WebsocketConfig(
            server_url="ws://localhost:3000",
            jwt_secret="test-secret"
        )
        
        # Mock the AsyncClient and its event registration
        mock_sio = mock_async_client.return_value
        mock_sio.event = Mock()
        mock_sio.on = Mock()
        
        client = WebsocketClient(config)
        
        # Check that event handlers were registered
        assert mock_sio.event.call_count >= 3  # connect, disconnect, reconnect
        assert mock_sio.on.call_count >= 4  # error, connected, authenticated, pong 