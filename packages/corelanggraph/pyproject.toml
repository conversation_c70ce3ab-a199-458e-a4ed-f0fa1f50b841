[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "corelanggraph"
version = "0.1.19"
description = "Language graph processing utilities for Cymulate"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
]
license = { text = "Proprietary" }
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "License :: Other/Proprietary License",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = [
    "langgraph==0.6.2",
    "langchain==0.3.26",
    "langchain-core==0.3.72",
    "pydantic==2.11.7",
    "trustcall>=0.0.38",
    "langfuse==3.1.3",
    "langgraph-supervisor==0.0.27",
    "langchain-mcp-adapters==0.1.9",
]

[project.urls]
"Homepage" = "https://bitbucket.org/cymulate/pythonlibs"
"Bug Tracker" = "https://bitbucket.org/cymulate/pythonlibs/jira"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]
namespaces = true

[tool.black]
line-length = 88
target-version = ["py38", "py39", "py310", "py311", "py312", "py313"]

[tool.isort]
profile = "black"
line_length = 88

[tool.uv.sources]
logger = { workspace = true }
slack_client = { workspace = true }

[tool.pytest.ini_options]
addopts = "--cov=corelanggraph --cov-report=term-missing --cov-config=.coveragerc"

[tool.ruff]
select = ["I", "F401"]
