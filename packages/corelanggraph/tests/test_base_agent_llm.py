from unittest import mock

import pytest

from corelanggraph.agents.base_agent_llm import BaseAgentLLM


class DummyLLM:
    pass

class DummyAgentLLM(BaseAgentLLM):
    async def execute(self, state):
        return state

@pytest.fixture
def agent(monkeypatch):
    # Patch LangfuseClient and ChatPromptTemplate at the correct path
    dummy_prompt = mock.Mock()
    dummy_prompt.get_langchain_prompt.return_value = "sys-prompt"
    dummy_langfuse_client = mock.Mock()
    dummy_langfuse_client.get_prompt.return_value = dummy_prompt
    monkeypatch.setattr(
        "corelanggraph.agents.base_agent.LangfuseClient",
        lambda: mock.Mock(client=dummy_langfuse_client)
    )
    monkeypatch.setattr(
        "corelanggraph.agents.base_agent_llm.ChatPromptTemplate",
        mock.Mock(from_messages=mock.Mock(return_value="chat-prompt"))
    )
    return DummyAgentLLM("AgentName", "desc", DummyLLM())

def test_prompt_label_env(agent, monkeypatch):
    monkeypatch.setenv("LANGFUSE_PROMPT_LABEL", "testlabel")
    assert agent.prompt_label == "testlabel"
    monkeypatch.delenv("LANGFUSE_PROMPT_LABEL", raising=False)
    assert agent.prompt_label == "production"

def test_langfuse_prompt(agent):
    assert agent.langfuse_prompt.get_langchain_prompt() == "sys-prompt"

def test_system_prompt(agent):
    assert agent.system_prompt == "chat-prompt"

def test_generate_chat_prompt_str(agent):
    result = agent.generate_chat_prompt("sys-prompt")
    assert result == "chat-prompt"

def test_generate_chat_prompt_list(agent):
    result = agent.generate_chat_prompt([("system", "sys-prompt")])
    assert result == "chat-prompt"

def test_create_extractor(agent):
    dummy_tool = mock.Mock()
    with mock.patch("corelanggraph.agents.base_agent_llm.create_extractor", return_value="extractor") as ce:
        result = agent.create_extractor([dummy_tool], "choice")
        ce.assert_called_once()
        assert result == "extractor" 