import pytest

from corelanggraph.agents.base_agent import AgentMetadata, BaseAgent


class DummyAgent(BaseAgent):
    async def execute(self, state):
        return {**state, "executed": True}

def test_agent_metadata_defaults():
    meta = AgentMetadata(name="A", description="desc")
    assert meta.name == "A"
    assert meta.description == "desc"
    assert meta.capabilities == []
    assert meta.requires_auth is False
    assert meta.version == "0.1.0"

@pytest.mark.asyncio
async def test_base_agent_call_and_callbacks(monkeypatch):
    agent = DummyAgent("TestAgent", "desc")
    called = {}
    def cb(name, input_data, output_data):
        called["cb"] = (name, input_data, output_data)
    agent.register_callback(cb)
    # Patch langfuse_client to avoid real dependency
    monkeypatch.setattr(agent, "langfuse_client", object())
    state = {"foo": 1}
    result = await agent(state)
    assert result["executed"] is True
    assert called["cb"][0] == "TestAgent"
    assert called["cb"][1] == {"foo": 1}
    assert called["cb"][2]["executed"] is True

@pytest.mark.asyncio
async def test_base_agent_call_error(monkeypatch):
    class ErrorAgent(BaseAgent):
        async def execute(self, state):
            raise RuntimeError("fail")
    agent = ErrorAgent("Err", "desc")
    monkeypatch.setattr(agent, "langfuse_client", object())
    state = {"error": None}
    result = await agent(state)
    assert result["error"] == "fail" 