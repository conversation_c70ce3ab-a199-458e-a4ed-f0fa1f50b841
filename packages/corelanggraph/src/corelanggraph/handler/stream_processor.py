"""
LangGraph Stream Processor

Consumes astream from LangGraph and emits normalized JSON records
for streaming responses to users while filtering internal operations.

Unlike LangGraphStreamEventProcessor which processes astream_events,
this processor handles the output from graph.astream() which has the format:
[path, stream_mode, data] where:
- path: List of agent/node identifiers in the execution hierarchy
- stream_mode: The type of stream mode ("values", "updates", "messages", "custom")
- data: The actual data payload
"""

from dataclasses import asdict, is_dataclass
import re
from typing import List, Dict, Any, Set, Optional, Union, Tuple
import logging
from corellm.providers.base import BaseModel
from langchain_core.load import dump
from langchain_core.messages import AIMessageChunk, BaseMessage, ToolMessage
from langchain.load.dump import dumps

logger = logging.getLogger(__name__)


def serialize(obj):
    """
    Generic serialization function that handles various object types.

    This function recursively converts complex objects to JSON-serializable formats,
    handling Pydantic models, dataclasses, LangChain objects, and other types.
    """
    # Handle None
    if obj is None:
        return None

    # Handle primitive types that are already JSON serializable
    if isinstance(obj, (str, int, float, bool)):
        return obj

    # Handle dataclasses
    if is_dataclass(obj) and not isinstance(obj, type):
        return {k: serialize(v) for k, v in asdict(obj).items()}

    # Handle Pydantic BaseModel instances (from corellm.providers.base or pydantic)
    if hasattr(obj, "model_dump") and callable(getattr(obj, "model_dump")):
        try:
            return {k: serialize(v) for k, v in obj.model_dump().items()}
        except Exception:
            # Fallback to dict conversion if model_dump fails
            return {k: serialize(v) for k, v in obj.__dict__.items()}

    # Handle older Pydantic models or objects with dict() method
    if hasattr(obj, "dict") and callable(getattr(obj, "dict")):
        try:
            return {k: serialize(v) for k, v in obj.dict().items()}
        except Exception:
            pass

    # Handle LangChain objects that have a serializable format
    if hasattr(obj, "to_dict") and callable(getattr(obj, "to_dict")):
        try:
            return serialize(obj.to_dict())
        except Exception:
            pass

    # Handle objects with __dict__ attribute
    if hasattr(obj, "__dict__"):
        try:
            # Filter out private attributes and methods
            filtered_dict = {
                k: v
                for k, v in obj.__dict__.items()
                if not k.startswith("_") and not callable(v)
            }
            return {k: serialize(v) for k, v in filtered_dict.items()}
        except Exception:
            pass

    # Handle dictionaries
    if isinstance(obj, dict):
        return {k: serialize(v) for k, v in obj.items()}

    # Handle lists, tuples, sets
    if isinstance(obj, (list, tuple)):
        return [serialize(i) for i in obj]

    if isinstance(obj, set):
        return [serialize(i) for i in obj]

    # Handle enum values
    if hasattr(obj, "value"):
        return serialize(obj.value)

    # Handle datetime objects
    if hasattr(obj, "isoformat"):
        try:
            return obj.isoformat()
        except Exception:
            pass

    # Fallback: convert to string representation
    try:
        return str(obj)
    except Exception:
        return f"<non-serializable: {type(obj).__name__}>"


class LangGraphStreamProcessor:
    """
    Processes LangGraph astream and emits normalized JSON records.

    Handles token streaming, tool calls, and conversation completion while
    filtering internal agent operations and handoff events.
    """

    def __init__(
        self,
        handoff_tool_patterns: Optional[List[str]] = None,
        internal_tags: Optional[Set[str]] = None,
        internal_agent_names: Optional[Set[str]] = None,
    ):
        """
        Initialize the stream processor.

        Args:
            handoff_tool_patterns: List of regex patterns for handoff tool names
                                  (default: common handoff patterns)
            internal_tags: Set of tags that indicate internal operations (default: {"supervisor"})
            internal_agent_names: Set of agent names to consider internal (default: empty set)
        """
        self.internal_tags = internal_tags or {"supervisor"}
        self.internal_agent_names = internal_agent_names or set()

        # Default handoff tool patterns
        default_handoff_patterns = [
            r".*handoff.*",
            r".*forward.*message.*",
            r".*delegate.*",
            r".*transfer.*",
            r".*redirect.*",
        ]
        self.handoff_tool_patterns = [
            re.compile(pattern, re.IGNORECASE)
            for pattern in (handoff_tool_patterns or default_handoff_patterns)
        ]

        # Track message consolidation for streaming
        self._active_streams: Dict[str, Dict[str, Any]] = {}

        # Track if we've already emitted the final isEnd event
        self._final_end_emitted = False

    def _is_internal_agent(self, path: List[str]) -> bool:
        """
        Determine if a path represents an internal agent operation.

        Args:
            path: List of agent/node identifiers from the execution path

        Returns:
            bool: True if this is from an internal agent
        """
        if len(path) > 2:
            return True
        return False


    def _is_message_internal(self, message_data: Any) -> bool:
        """
        Determine if a specific message is from an internal source.

        Args:
            message_data: Message data (LangChain object, dict, or string)

        Returns:
            bool: True if this message is from an internal source
        """
        # Handle None or invalid data
        if message_data is None:
            return True  # Treat None as internal to filter it out

        # Handle actual LangChain message objects
        if hasattr(message_data, "name"):
            message_name = getattr(message_data, "name", None)
            if message_name is not None:
                message_name = str(message_name).lower()
                if "supervisor" in message_name:
                    return True
                if any(
                    internal_name.lower() in message_name
                    for internal_name in self.internal_agent_names
                ):
                    return True
                # AI messages from knowledge base agents are typically the final response
                # that should be shown to users
                if hasattr(message_data, "type"):
                    message_type = getattr(message_data, "type", None)
                    if message_type is not None:
                        message_type = str(message_type).lower()
                        if (
                            message_type == "ai"
                            and "agent" in message_name
                            and "supervisor" not in message_name
                        ):
                            return False

        # Handle structured LangChain message format (serialized)
        elif isinstance(message_data, dict):
            # Check for LangChain constructor format
            if message_data.get("lc") == 1 and "kwargs" in message_data:
                kwargs = message_data["kwargs"]
                # Check message name/type
                message_name = kwargs.get("name", "").lower()
                message_type = kwargs.get("type", "").lower()

                # Check if it's from supervisor or internal agents
                if "supervisor" in message_name:
                    return True

                # Check configured internal agent names
                if any(
                    internal_name.lower() in message_name
                    for internal_name in self.internal_agent_names
                ):
                    return True

                # AI messages from knowledge base agents are typically the final response
                # that should be shown to users
                if (
                    message_type == "ai"
                    and "agent" in message_name
                    and "supervisor" not in message_name
                ):
                    return False

            # Direct dict format
            elif "name" in message_data:
                message_name = message_data.get("name", "").lower()
                if "supervisor" in message_name:
                    return True
                if any(
                    internal_name.lower() in message_name
                    for internal_name in self.internal_agent_names
                ):
                    return True

        # Default: not internal
        return False

    def _is_handoff_tool(self, tool_name: str) -> bool:
        """
        Check if a tool name matches handoff patterns.

        Args:
            tool_name: Name of the tool

        Returns:
            bool: True if this is a handoff tool
        """
        return any(pattern.match(tool_name) for pattern in self.handoff_tool_patterns)

    def _extract_message_content(self, message: tuple[AIMessageChunk, ...]) -> str:
        """
        Extract content from a message (LangChain message object, dict, or string).

        Args:
            message_data: Message data (LangChain object, dict, or string)

        Returns:
            str: Extracted content or empty string
        """
        message_data = message[0]
        # Handle None or invalid data

        if isinstance(message_data, ToolMessage):
            return ""

        # Handle actual LangChain message objects
        if message_data.content:
            # This is likely a BaseMessage or AIMessageChunk object
            content = getattr(message_data, "content", None)
            if content is not None:
                return str(content)
        return ""

    def _extract_values(self, data: Any) -> str:
        """
        Extract values from a data payload.
        """
        if hasattr(data, "messages"):
            # filter all message that tools are not in the handoff tool patterns
            data["messages"] = [
                message
                for message in data["messages"]
                if not self._is_handoff_tool(message.content)
            ]
            # take the last message
            data["messages"] = [data["messages"][-1]]
        # take just content
        data["messages"] = [message.content for message in data["messages"]]
        return data

    def _is_final_completion(
        self, path: List[str], stream_mode: str, data: Any
    ) -> bool:
        """
        Determine if this represents the final workflow completion.

        Args:
            path: Execution path
            stream_mode: The stream mode
            data: The data payload

        Returns:
            bool: True if this is the final completion
        """
        # Final completion typically has an empty path or minimal nesting

        if stream_mode == "values" or stream_mode == "custom":
            return True

        if stream_mode == "messages":
            return self._is_final_message(data)

        return False

    def _is_final_message(self, data: Any) -> bool:
        """
        Determine if this represents the final message completion.
        """
        if not data:
            return False

        message_data: BaseMessage = data[0]
        return message_data.response_metadata.get("finish_reason") == "stop"

    def _is_chunk(self, stream_mode: str) -> bool:
        return stream_mode == "messages"

    def _get_content(self, stream_mode: str, data: Any) -> str:
        if stream_mode == "values":
            return self._extract_values(data)
        elif stream_mode == "messages":
            return self._extract_message_content(data)
        elif stream_mode == "custom":
            return data
        return ""

    def _get_message_id(self, data: Any) -> str:
        """
        Extract message ID from data payload.
        """

        try:
            message_data = data[0]
            message_id = message_data.id
            return message_id
        except Exception:
            return ""

    def _process_event(self, event: Tuple[List[str], str, Any]) -> List[Dict[str, Any]]:
        """
        Process a single LangGraph astream event and return normalized records.

        Args:
            event: Tuple of [path, stream_mode, data] from LangGraph astream
        Returns:
            List[Dict[str, Any]]: List of normalized records (0 or more)
        """
        if not isinstance(event, (list, tuple)) or len(event) != 3:
            logger.warning(f"Invalid event format: {event}")
            return []

        path, stream_mode, data = event
        print("---" + stream_mode)

        if not isinstance(path, list):
            path = [path] if path else []

        is_internal = self._is_internal_agent(path)

        # Create base metadata for all records
        base_metadata = {
            "path": path,
            "stream_mode": stream_mode,
            "langgraph_data": serialize(data) if data is not None else {},
        }

        content = serialize(self._get_content(stream_mode, data))
        if not content:
            return []

        record = dump.dumpd(
            {
                "type": stream_mode,
                "isChunck": self._is_chunk(stream_mode),
                "isInternal": is_internal,
                "content": content,
                "metadata": base_metadata or {},
                "isEnd": self._is_final_completion(path, stream_mode, data),
                "messageId": self._get_message_id(data),
                "isHumain": False,
            }
        )
        return [record]
