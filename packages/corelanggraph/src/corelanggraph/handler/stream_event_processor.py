"""
LangGraph Stream Processor

Consumes astream_events from LangGraph and emits normalized JSON records
for streaming responses to users while filtering internal operations.
"""

import re
from typing import AsyncIterator, Dict, Any, List, Set, Optional, Union
import logging
from langchain_core.load import dump

logger = logging.getLogger(__name__)


class LangGraphStreamEventProcessor:
    """
    Processes LangGraph astream_events and emits normalized JSON records.

    Handles token streaming, tool calls, and conversation completion while
    filtering internal agent operations and handoff events.
    """

    def __init__(
        self,
        internal_agent_names: Optional[Set[str]] = None,
        handoff_tool_patterns: Optional[List[str]] = None,
        internal_tags: Optional[Set[str]] = None,
    ):
        """
        Initialize the stream processor.

        Args:
            internal_agent_names: Set of agent names to consider internal (default: empty set)
            handoff_tool_patterns: List of regex patterns for handoff tool names
                                  (default: common handoff patterns)
            internal_tags: Set of tags that indicate internal operations (default: {"supervisor"})
        """
        self.internal_agent_names = internal_agent_names or set()
        self.internal_tags = internal_tags or {"supervisor"}

        # Default handoff tool patterns
        default_handoff_patterns = [
            r".*handoff.*",
            r".*forward.*message.*",
            r".*delegate.*",
            r".*transfer.*",
            r".*redirect.*",
        ]
        self.handoff_tool_patterns = [
            re.compile(pattern, re.IGNORECASE)
            for pattern in (handoff_tool_patterns or default_handoff_patterns)
        ]

        # Track message consolidation for streaming
        self._active_streams: Dict[str, Dict[str, Any]] = {}

        # Track if we've already emitted the final isEnd event
        self._final_end_emitted = False

    def _is_internal_agent(self, event: Dict[str, Any]) -> bool:
        """
        Determine if an event originates from an internal agent.

        Args:
            event: LangGraph event dict

        Returns:
            bool: True if this is from an internal agent
        """
        name = event.get("name", "").lower()
        tags = event.get("tags", [])
        metadata = event.get("metadata", {})

        # Check if name contains or equals "supervisor"
        if "supervisor" in name:
            return True

        # Check if tags contain supervisor or internal tags
        if any(tag.lower() in self.internal_tags for tag in tags):
            return True

        # Check configured internal agent names
        if any(
            internal_name.lower() in name for internal_name in self.internal_agent_names
        ):
            return True

        # Check langgraph_checkpoint_ns for subagent operations
        checkpoint_ns = metadata.get("langgraph_checkpoint_ns", "")
        if checkpoint_ns:
            # If there are multiple agents in the checkpoint namespace (indicated by |),
            # this means we're in a subgraph/subagent operation
            if "|" in checkpoint_ns and "supervisor_agent" in checkpoint_ns:
                # Extract the current agent name from the path
                ns_parts = checkpoint_ns.split("|")
                if len(ns_parts) > 1:
                    # Get the last agent in the hierarchy that's not "tools"
                    for part in reversed(ns_parts):
                        if ":" in part and not part.startswith("tools:"):
                            agent_name = part.split(":")[0].lower()
                            # Mark non-supervisor agents as internal
                            if agent_name != "supervisor_agent":
                                return True

        return False

    def _is_handoff_tool(self, tool_name: str) -> bool:
        """
        Check if a tool name matches handoff patterns.

        Args:
            tool_name: Name of the tool

        Returns:
            bool: True if this is a handoff tool
        """
        return any(pattern.match(tool_name) for pattern in self.handoff_tool_patterns)

    def _extract_chunk_content(self, chunk_data: Any) -> str:
        """
        Extract content from a chunk object.

        Args:
            chunk_data: Chunk data from event (could be LangChain message object or string representation)

        Returns:
            str: Extracted content or empty string
        """
        # Handle actual LangChain message objects
        if hasattr(chunk_data, "content"):
            # This is likely a BaseMessage or AIMessageChunk object
            content = getattr(chunk_data, "content", "")
            return str(content) if content is not None else ""

        # Handle dictionary representation
        elif isinstance(chunk_data, dict):
            return str(chunk_data.get("content", ""))

        # Handle string representation (fallback for json.dumps with default=str)
        elif isinstance(chunk_data, str):
            # Parse string representation like "content='text' ..."
            content_match = re.search(r"content='([^']*)'", chunk_data)
            if content_match:
                return content_match.group(1)

        return ""

    def _get_message_id(self, event: Dict[str, Any]) -> str:
        """
        Extract stable message ID from event.

        Args:
            event: LangGraph event dict

        Returns:
            str: Message ID (preferring data.id, falling back to run_id)
        """
        data = event.get("data", {})

        # Try to get ID from data
        if isinstance(data, dict):
            if "id" in data:
                return str(data["id"])

            # Handle chunk object
            chunk = data.get("chunk")
            if chunk:
                # Try to get ID from actual LangChain message object
                if hasattr(chunk, "id"):
                    chunk_id = getattr(chunk, "id", None)
                    if chunk_id:
                        return str(chunk_id)

                # Fallback: parse string representation
                elif isinstance(chunk, str):
                    id_match = re.search(r"id='([^']*)'", chunk)
                    if id_match:
                        return id_match.group(1)

        # Fallback to run_id
        return event.get("run_id", "")

    def _extract_tool_calls(self, chunk_data: Any) -> List[Dict[str, Any]]:
        """
        Extract tool calls from chunk data.

        Args:
            chunk_data: Chunk data from event

        Returns:
            List[Dict[str, Any]]: List of tool call dictionaries
        """
        tool_calls = []

        # Handle actual LangChain message objects
        if hasattr(chunk_data, "tool_calls"):
            raw_tool_calls = getattr(chunk_data, "tool_calls", [])
            if raw_tool_calls:
                for tool_call in raw_tool_calls:
                    if hasattr(tool_call, "name") and hasattr(tool_call, "args"):
                        tool_calls.append(
                            {
                                "name": getattr(tool_call, "name", ""),
                                "args": getattr(tool_call, "args", {}),
                                "id": getattr(tool_call, "id", ""),
                                "type": getattr(tool_call, "type", "tool_call"),
                            }
                        )

        # Handle dictionary representation
        elif isinstance(chunk_data, dict):
            if "tool_calls" in chunk_data:
                raw_tool_calls = chunk_data["tool_calls"]
                if isinstance(raw_tool_calls, list):
                    tool_calls.extend(raw_tool_calls)

        # Handle string representation (fallback)
        elif isinstance(chunk_data, str):
            # Try to extract tool calls from string representation
            tool_calls_match = re.search(r"tool_calls=\[([^\]]*)\]", chunk_data)
            if tool_calls_match:
                # This would need more complex parsing, but for now we'll skip
                pass

        return tool_calls

    def _is_top_level_chain_end(self, event: Dict[str, Any]) -> bool:
        """
        Determine if this is the top-level chain end event (final completion).

        Args:
            event: LangGraph event dict

        Returns:
            bool: True if this is the final top-level completion
        """
        metadata = event.get("metadata", {})
        parent_ids = event.get("parent_ids", [])

        # Top-level chain end should have no parent_ids or minimal nesting
        if len(parent_ids) == 0:
            return True

        # Check if this is the root-level LangGraph completion
        checkpoint_ns = metadata.get("langgraph_checkpoint_ns")
        if not checkpoint_ns or "|" not in checkpoint_ns:
            return True

        return False

    def _extract_final_response_content(self, output_data: Any) -> str:
        """
        Extract the final response content from the output data.

        Args:
            output_data: The output data from the final chain end event

        Returns:
            str: The final response content
        """
        if not output_data:
            return ""

        # Handle structured response format
        if isinstance(output_data, dict):
            # Look for structured_response.messages
            structured_response = output_data.get("structured_response", {})
            if isinstance(structured_response, dict):
                messages = structured_response.get("messages", [])
                if isinstance(messages, list) and messages:
                    # Get the last AI message
                    for message in reversed(messages):
                        if isinstance(message, dict) and message.get("type") == "ai":
                            return message.get("content", "")

            # Look for messages array directly
            messages = output_data.get("messages", [])
            if isinstance(messages, list) and messages:
                # Get the last message content
                last_message = messages[-1]
                if isinstance(last_message, str):
                    # Parse string representation to extract content
                    content_match = re.search(r"content='([^']*)'", last_message)
                    if content_match:
                        # Unescape the content
                        content = content_match.group(1)
                        content = content.replace("\\'", "'").replace("\\n", "\n")
                        return content

        return ""

    def _create_record(
        self,
        is_chunk: bool = False,
        is_internal: bool = False,
        content: str = "",
        metadata: Optional[Dict[str, Any]] = None,
        is_end: bool = False,
        message_id: str = "",
        toolcalling_data: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Create a normalized JSON record.

        Returns:
            Dict[str, Any]: Normalized record following the schema
        """
        return dump.dumpd(
            {
                "isChunck": is_chunk,  # Note: preserving the spec spelling
                "isInternal": is_internal,
                "content": content,
                "metadata": metadata or {},
                "isEnd": is_end,
                "messageId": message_id,
                "toolcalling_data": toolcalling_data,
            }
        )

    def _process_event(self, event: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process a single LangGraph event and return normalized records.

        Args:
            event: Single LangGraph event dict

        Returns:
            List[Dict[str, Any]]: List of normalized records (0 or more)
        """
        event_type = event.get("event", "")
        name = event.get("name", "")
        data = event.get("data", {})
        run_id = event.get("run_id", "")
        tags = event.get("tags", [])

        is_internal = self._is_internal_agent(event)
        message_id = self._get_message_id(event)

        # Create base metadata for all records
        base_metadata = {
            "event": event_type,
            "name": name,
            "tags": tags,
            "parent_ids": event.get("parent_ids", []),
            "run_id": run_id,
        }

        records = []

        # Handle model token streaming
        if event_type in ("on_chat_model_stream", "on_llm_stream"):
            chunk_content = self._extract_chunk_content(data.get("chunk", ""))
            chunk_data = data.get("chunk")

            # Track streaming state
            if message_id not in self._active_streams:
                self._active_streams[message_id] = {
                    "content_parts": [],
                    "is_internal": is_internal,
                    "metadata": base_metadata,
                }

            self._active_streams[message_id]["content_parts"].append(chunk_content)

            # Check for tool calls in the chunk
            tool_calls = self._extract_tool_calls(chunk_data)

            # Emit chunk record for non-empty content
            if chunk_content:
                records.append(
                    self._create_record(
                        is_chunk=True,
                        is_internal=is_internal,
                        content=chunk_content,
                        metadata=base_metadata,
                        message_id=message_id,
                    )
                )

            # Emit tool call records if any tool calls are present and not internal
            if tool_calls and not is_internal:
                for tool_call in tool_calls:
                    # Check if this is a handoff tool
                    tool_name = tool_call.get("name", "")
                    if not self._is_handoff_tool(tool_name):
                        records.append(
                            self._create_record(
                                is_chunk=False,
                                is_internal=is_internal,
                                content="",
                                metadata=base_metadata,
                                message_id=message_id,
                                toolcalling_data={
                                    "tool_name": tool_name,
                                    "arguments": tool_call.get("args", {}),
                                    "call_id": tool_call.get("id", ""),
                                },
                            )
                        )

        # Handle model completion
        elif event_type in ("on_chat_model_end", "on_llm_end"):
            if message_id in self._active_streams:
                stream_data = self._active_streams[message_id]
                full_content = "".join(stream_data["content_parts"])

                # Emit final consolidated record
                records.append(
                    self._create_record(
                        is_chunk=False,
                        is_internal=stream_data["is_internal"],
                        content=full_content,
                        metadata=stream_data["metadata"],
                        message_id=message_id,
                    )
                )

                # Clean up tracking
                del self._active_streams[message_id]

        # Handle tool start events
        elif event_type == "on_tool_start":
            tool_name = name

            # Filter handoff tools completely
            if self._is_handoff_tool(tool_name):
                return records

            # Filter internal agent tool calls
            if is_internal:
                return records

            # Extract tool arguments - handle both direct input and nested structure
            tool_input = data.get("input", {})

            # Sometimes tool input is nested under different keys
            if isinstance(tool_input, dict):
                # Look for actual arguments
                tool_args = tool_input

                # Remove metadata fields to get clean arguments
                clean_args = {
                    k: v
                    for k, v in tool_args.items()
                    if k not in ["tool_call_id", "state", "config"]
                }

                tool_call_id = tool_input.get("tool_call_id", run_id)
            else:
                clean_args = tool_input if tool_input else {}
                tool_call_id = run_id

            records.append(
                self._create_record(
                    is_chunk=False,
                    is_internal=is_internal,
                    content="",
                    metadata=base_metadata,
                    message_id=message_id,
                    toolcalling_data={
                        "tool_name": tool_name,
                        "arguments": clean_args,
                        "call_id": tool_call_id,
                    },
                )
            )

        # Handle tool end events
        elif event_type == "on_tool_end":
            tool_name = name

            # Filter handoff tools completely
            if self._is_handoff_tool(tool_name):
                return records

            # Filter internal agent tool calls
            if is_internal:
                return records

            # Extract tool result
            tool_output = data.get("output", {})

            # Handle different output formats
            if hasattr(tool_output, "content"):
                # Tool result is a message object
                result_data = {
                    "content": getattr(tool_output, "content", ""),
                    "type": "message",
                }
            elif isinstance(tool_output, dict):
                result_data = tool_output
            else:
                result_data = {"result": str(tool_output)} if tool_output else {}

            records.append(
                self._create_record(
                    is_chunk=False,
                    is_internal=is_internal,
                    content="",
                    metadata=base_metadata,
                    message_id=message_id,
                    toolcalling_data={
                        "tool_name": tool_name,
                        "result": result_data,
                        "call_id": run_id,
                    },
                )
            )

        # Handle graph/chain completion
        elif event_type == "on_chain_end" and name == "LangGraph":
            # Only emit isEnd=True for the top-level chain completion
            if self._is_top_level_chain_end(event) and not self._final_end_emitted:
                self._final_end_emitted = True

                # Extract the final response content
                final_content = self._extract_final_response_content(
                    data.get("output", {})
                )

                records.append(
                    self._create_record(
                        is_chunk=False,
                        is_internal=False,
                        content=final_content,
                        metadata={
                            **base_metadata,
                            "final_output": data.get("output", {}),
                        },
                        is_end=True,
                        message_id=run_id,
                    )
                )

        return records
