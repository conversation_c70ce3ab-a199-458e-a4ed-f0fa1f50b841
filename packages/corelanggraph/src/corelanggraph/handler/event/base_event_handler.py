from abc import ABC, abstractmethod
from corelanggraph.handler.base_handler import <PERSON><PERSON><PERSON><PERSON>,T,U
from logger import logger

class BaseEventHandler(ABC):

    def __init__(self,handler: BaseHandler[T,U]):
        self.handler = handler
    
    @abstractmethod
    def add_listener(self):
        pass
    
    @abstractmethod
    def respond_event(self, event: T):
        pass

    async def on_event(self, event: U):
        try:
            logger.info(f"Processing event: {event}")
            response = await self.handler.run(event)
            logger.info(f"Response: {response}")
            self.respond_event(response)
            logger.info(f"Responded to event: {event}")
        except Exception as e:
            logger.error(f"Error on event: {e}")
            raise e
        