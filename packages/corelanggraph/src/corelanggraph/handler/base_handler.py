from abc import ABC, abstractmethod
from typing import Any, Awaitable, Callable, Optional, TypeVar, Generic
from langgraph.graph.state import CompiledStateGraph
from langchain_core.runnables import RunnableConfig
from logger import logger
from langfuse.langchain import CallbackHandler

T = TypeVar("T")
U = TypeVar("U")


class BaseHandler(ABC, Generic[T, U]):
    def __init__(self):
        pass

    @property
    @abstractmethod
    def type(self) -> str:
        pass

    @abstractmethod
    def get_workflow(self, checkpointer) -> CompiledStateGraph:
        pass

    @abstractmethod
    def init_state(self, input: U) -> T:
        pass

    def concat_state(self, input: U, state: T) -> T:
        """
        Concatenate the input with the state.
        Override this method to concatenate the input with the state.
        """
        return state

    @abstractmethod
    def get_config(self, input: U) -> RunnableConfig:
        pass

    async def get_checkpointer(self):
        return None, None

    def get_langfuse_handler(self):
        return CallbackHandler()

    async def get_state(
        self, graph: CompiledStateGraph, config: RunnableConfig, input: U
    ) -> Awaitable[T]:
        # Log the config and key being used
        logger.info(f"🔍 Retrieving state with config: {config}")
        logger.info(f"📊 Graph configuration: {graph}")
        try:
            checkpointerState = await graph.aget_state(config, subgraphs=True)
            logger.info(
                f"📌 State found in Redis: {checkpointerState.values is not {}}"
            )

            if not checkpointerState or not checkpointerState.values:
                logger.info("🆕 No existing state found, initializing new state")
                state = self.init_state(input)
                logger.info(f"✨ New state initialized")
            else:
                state = self.concat_state(input, checkpointerState.values)
                logger.info(f"🔄 Using existing state")
        except Exception as e:
            logger.error(f"🔍 Error retrieving state: {e}")
            state = self.init_state(input)

        return state

    async def initAll(self, input: U):

        logger.info("🚀 Starting handler execution")
        logger.debug(f"📥 Input received")

        logger.debug("🔄 Getting checkpointer")
        checkpointer, _ = await self.get_checkpointer()

        logger.debug("⚙️ Getting configuration")
        config = self.get_config(input)
        logger.debug(f"📋 Configuration loaded: {config}")

        logger.debug("📊 Getting workflow graph")
        graph = self.get_workflow(checkpointer)
        logger.debug(f"📈 Workflow graph loaded: {graph}")

        logger.debug("🔄 Retrieving state")
        state = await self.get_state(graph, config, input)

        return (graph, state, config)
