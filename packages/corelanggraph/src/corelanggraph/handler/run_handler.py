from typing import Any, Awaitable, Callable, Optional, TypeVar, Generic
from langgraph.graph.state import CompiledStateGraph
from langchain_core.runnables import RunnableConfig
from logger import logger
from langfuse.langchain import CallbackHandler

from .base_handler import <PERSON>Handler,T,U


class RunHandler(BaseHandler[T,U]):

    @property
    def type(self)->str:
        return "run"
    
    async def run(self,input:U):
        graph,state,config = await self.initAll(input)
        logger.info("▶️ Invoking workflow graph")
        result = await graph.ainvoke(
            state,
            config = config,
        )

        logger.info("🔄 Workflow graph invoked successfully")
        
        return result

