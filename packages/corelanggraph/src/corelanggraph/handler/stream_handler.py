from abc import abstractmethod
from typing import Any, Callable, List, Union
import uuid

from logger import logger
from .base_handler import BaseHandler, T, U


class StreamHandler(BaseHandler[T, U]):

    @property
    def type(self) -> str:
        return "stream"

    @property
    def subgraphs(self) -> bool:
        return True

    @abstractmethod
    def get_stream_mode(self) -> Union[str, List[str]]:
        pass

    async def stream(self, input: U, stream_handler: Callable):
        graph, state, config = await self.initAll(input)

        logger.info(f"🔄 Starting streaming")
        try:
            if self.type == "stream_events":
                async for chunk in graph.astream_events(
                    state, config, subgraphs=self.subgraphs
                ):
                    await stream_handler(chunk, config)
            else:
                async for chunk in graph.astream(
                    state,
                    config,
                    stream_mode=self.get_stream_mode(),
                    subgraphs=self.subgraphs,
                ):
                    await stream_handler(chunk, config)
        except Exception as e:
            logger.error(f"🔄 Streaming failed: {e}")
            raise e

        logger.info(f"🔄 Streaming completed")
