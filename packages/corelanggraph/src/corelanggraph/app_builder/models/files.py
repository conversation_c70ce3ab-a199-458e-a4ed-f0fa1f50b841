from enum import Enum
import tempfile
from typing import Optional, Dict, Set
from pydantic import field_validator, Field, BaseModel

class FileCategory(str, Enum):
    IMAGE = "IMAGE"
    DOCUMENT = "DOCUMENT"
    LOG = "LOG"
    ARCHIVE = "ARCHIVE"

MAX_FILE_SIZE_BYTES = 1024 * 1024 * 100  # 100MB


ALLOWED_EXTENSIONS: Dict[FileCategory, Set[str]] = {
    FileCategory.IMAGE: {
        "png", "jpeg", "jpg", "gif"
    },
    FileCategory.DOCUMENT: {
        "pdf", "doc", "docx"
    },
    FileCategory.LOG: {
        "log", "txt", "text", "json"
    },
    FileCategory.ARCHIVE: {
        "zip", "rar"
    },
}

class EventFile(BaseModel):
    name: str
    type: str
    size: int
    mimetype: str
    category: Optional[FileCategory] = Field(None)
    content: bytes
    file_path: Optional[str] = None

    def __init__(self, **data):
        super().__init__(**data)
        if not self.file_path and self.content:
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{self.type}") as tmp_file:
                tmp_file.write(self.content)
                self.file_path = tmp_file.name

    @field_validator("category", mode="before")
    def validate_file_category(cls, v, values) -> Optional[FileCategory]:
        file_type = values.data.get('type').lower()
        category = cls.get_category(file_type)
        if category is None:
            raise ValueError(f"Unsupported file type '{file_type}' for file '{values.data.get('name')}'")
        return category

    @field_validator("size")
    def validate_file_size(cls, v, values) -> int:
        if v > MAX_FILE_SIZE_BYTES:
            raise ValueError(f"File '{values.data.get('name')}' exceeds the size limit of {MAX_FILE_SIZE_BYTES} bytes.")
        return v

    @classmethod
    def get_category(cls, file_extension: str) -> Optional[FileCategory]:
        for category, extensions in ALLOWED_EXTENSIONS.items():
            if file_extension in extensions:
                return category
        return None

