from typing import Any, AsyncGenerator, List, Optional, Literal, Dict
from pydantic import BaseModel, Field, ConfigDict, field_validator, ValidationError
import json
import aiohttp
import asyncio

from .base_event import BaseEvent, BaseEventModel, T, U
from corelanggraph.handler.base_handler import BaseHandler
from corelanggraph.app_builder.models.files import EventFile, MAX_FILE_SIZE_BYTES


from slack_client import SlackClient
from logger import logger



class SlackBlockElement(BaseModel):
    """Represents an individual element within a block."""
    type: str
    elements: Optional[List[dict]] = None  # Nested elements

class SlackBlock(BaseModel):
    """Represents a block in Slack event data."""
    block_id: Optional[str] = None
    type: str
    elements: List[SlackBlockElement]


class SlackEvent(BaseEvent[T, U]):
    """
    Slack event implementation that supports streaming by sending multiple messages.
    Uses slack client to send results to Slack channels.
    """
    MAX_FILE_SIZE_MB = 20
    MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
    MAX_TOTAL_FILES = 20
    MAX_CONCURRENT_DOWNLOADS = 15

    def __init__(self,
                 handler: BaseHandler[T, U], 
                 slack_client: SlackClient, 
                 events: List[str], thread_ts: str = None):
        super().__init__(handler)
        self.slack_client = slack_client
        self.events = events
        self.thread_ts = thread_ts  # For threading messages
        
    @property
    def event_type(self) -> str:
        return "slack"
    
    @property
    def supports_streaming(self) -> bool:
        return True
    async def _extract_and_download_files(self, event: Dict[str, Any]) -> List["EventFile"]:
        files:list[dict] = event.get('files', [])
        if not files:
            logger.info("No files found in event.")
            return []

        valid_files:list[EventFile] = []
        for file_info in files:
            try:
                EventFile(
                    name=file_info.get("name", ""),
                    type=file_info.get("filetype", ""),
                    size=file_info.get("size", 0),
                    mimetype=file_info.get("mimetype", ""),
                    content=file_info.get("content", b"")
                )
                valid_files.append(file_info)
            except ValidationError as e:
                logger.warning(f"Skipping invalid file '{file_info.get('name')}': {e}")

        if len(valid_files) > self.MAX_TOTAL_FILES:
            logger.warning(
                f"File count exceeds limit: {len(valid_files)} files found, max allowed: {self.MAX_TOTAL_FILES}")
            valid_files = valid_files[:self.MAX_TOTAL_FILES]

        download_tasks = [self._download_file(file_info) for file_info in valid_files]
        results = await asyncio.gather(*download_tasks, return_exceptions=True)

        return [result for result in results if result is not None and isinstance(result, EventFile)]

    async def _download_file(self, file_info: Dict[str, Any]) -> Optional["EventFile"]:
        async with self.semaphore:
            file_name = file_info.get('name')
            file_type = file_info.get('filetype').lower()
            file_size = file_info.get('size')
            file_mimetype = file_info.get('mimetype', '')
            url_private = file_info.get('url_private')

            category = EventFile.get_category(file_type)
            if not all([file_name, file_type, url_private, category]):
                logger.error(f"Invalid file metadata for '{file_name}'. Skipping download.")
                return None

            try:
                file_content = await self._fetch_file_content(url_private)
                return EventFile(
                    name=file_name,
                    type=file_type,
                    size=file_size,
                    mimetype=file_mimetype,
                    content=file_content,
                    category=category
                ) if file_content else None
            except Exception as e:
                logger.error(f"Error downloading '{file_name}' of type '{file_type}': {e}")
                return None

    async def _fetch_file_content(self, url: str) -> Optional[bytes]:
        headers = {"Authorization": f"Bearer {self.client.client.token}"}
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        return await response.read()
                    else:
                        logger.error(f"Failed to fetch file from '{url}': HTTP {response.status}")
            except aiohttp.ClientError as e:
                logger.error(f"Network error accessing URL '{url}': {e}")
            return None
        

        
    async def emit_event(self, result: Any) -> None:
        """Emit final result to Slack channel"""
        try:
            message_text = f"🎯 **Final Result:**\n```json\n{json.dumps(result, indent=2)}\n```"
            await self.slack_client.post_message(
                channel=self.channel,
                text=message_text,
                thread_ts=self.thread_ts
            )
            logger.info(f"📤 Slack result emitted to {self.channel}: {result}")
        except Exception as e:
            logger.error(f"❌ Failed to emit Slack result: {str(e)}")
            await self.slack_client.post_message(
                channel=self.channel,
                text=f"❌ Error: {str(e)}",
                thread_ts=self.thread_ts
            )
            raise

    async def register_event_handlers(self)->None:
        """
        Run the handler and emit results back.
        """
        for event in self.events:
            @self.slack_client.app.event(event)
            async def event_handler(body, ack, say):
                if event == "message":
                    await ack()                
                input_data = await self.parse_message(self.event_type,body,event)
                await self.handle_event(input_data)
    
    async def run(self) -> Any:
        logger.info("[Slack EventHandler] Registering event handlers")
        await self.register_event_handlers()
        logger.info("[Slack EventHandler] Starting Slack client")
        await self.slack_client.start()
       
