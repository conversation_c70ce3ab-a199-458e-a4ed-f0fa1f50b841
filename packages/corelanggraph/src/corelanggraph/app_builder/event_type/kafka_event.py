from typing import Any, List, Dict
from kafka import manager
from .base_event import Base<PERSON>vent, T, U
from corelanggraph.handler.base_handler import <PERSON>H<PERSON><PERSON>
from logger import logger


class KafkaEvent(BaseEvent[T, U]):
    """
    Kafka event implementation that supports streaming.
    Uses kafka producer to publish results to topics.
    """

    def __init__(
        self,
        handler: BaseHandler[T, U],
        kafka_manager: manager.KafkaManager,
        group_id: str,
        topics: List[str],
        stream_topic: str = None,
    ):
        super().__init__(handler)
        self.kafka_manager = kafka_manager
        self.topics = topics
        self.stream_topic = stream_topic
        self.group_id = group_id

    @property
    def event_type(self) -> str:
        return "kafka"

    @property
    def supports_streaming(self) -> bool:
        return False

    async def _message_handler(self, data: Dict[str, Any], message: any) -> Any:
        input_data = await self.parse_message(self.event_type, data, message.topic())
        await self.handle_event(input_data)

    async def run(self) -> Any:
        self.kafka_manager.subscribe(
            self.topics, self._message_handler, group_id=self.group_id
        )
