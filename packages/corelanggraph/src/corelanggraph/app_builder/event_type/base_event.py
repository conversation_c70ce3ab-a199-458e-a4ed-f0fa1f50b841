from abc import ABC, abstractmethod
from typing import Any, Async<PERSON><PERSON>ator, Callable, TypeVar, Generic, Optional
from pydantic import BaseModel, Field, ConfigDict, field_validator
import uuid


from corelanggraph.handler.base_handler import BaseHandler
from corelanggraph.handler.run_handler import RunHandler
from corelanggraph.handler.stream_handler import StreamHandler


from logger import logger

T = TypeVar('T')
U = TypeVar('U')


class BaseEventModel(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True, use_enum_values=True)
    validate_user: bool = Field( default=False )
    listener: Any
    question_id: str = Field(default_factory=lambda: str(uuid.uuid4()), alias="questionID")
    text: str
    channel: str
    thread_ts: Optional[str] = Field(default=None, alias="thread_ts")


class BaseEvent(ABC, Generic[T, U]):
    """
    Abstract base class for handling different types of events (websocket, slack, zendesk, fastapi, kafka).
    Works with BaseHandler to either stream events or run them directly.
    
    Type Parameters:
        T: The type parameter for the handler
        U: The input data type
    """
    
    def __init__(self, handler: BaseHandler[T, U]) -> None:
        """
        Initialize the BaseEvent with a handler.
        
        Args:
            handler: The handler instance to process events
        """
        self.handler = handler
        
    @property
    @abstractmethod
    def event_type(self) -> str:
        """Return the type of event (websocket, slack, zendesk, fastapi, kafka)."""
        pass
    
    @property
    @abstractmethod
    def supports_streaming(self) -> bool:
        """Return whether this event type supports streaming."""
        pass

    @property
    def is_streaming(self) -> bool:
        """Check if the current configuration supports streaming."""
        return self.supports_streaming and isinstance(self.handler, StreamHandler)
    
    async def emit_event(self, result: Any) -> None:
        """
        Emit the result back to the appropriate destination.
        
        Args:
            result: The result to emit
        """
        pass
    
    @abstractmethod
    async def run(self, input_data: U) -> Any:
        """
        Run the handler and emit results back.
        This method is used to run the handler and emit results back.
        """
        pass


    async def parse_message(self,event_type:str,message:Any,path:str)->Any:
        """
        Parse the message from the request body.
        """
        return message


    async def handle_event(self, input_data: U) -> Any:
        """
        Run the handler and emit results back.
        For streaming-capable events, can optionally stream results.
        For non-streaming events (like FastAPI), runs and returns the final result.
        
        Args:
            input_data: The input data to process
            
        Returns:
            Any: The result for non-streaming events, None for streaming events
            
        Raises:
            Exception: Re-raises any exception that occurs during processing
        """
        logger.info(f"🎯 Running {self.event_type} event with input: {input_data}")
        
        try:
            if self.is_streaming:
                # Type guard ensures this is safe
                if not isinstance(self.handler, StreamHandler):
                    raise TypeError(f"Handler must be StreamHandler for streaming events, got {type(self.handler)}")
                
                logger.info(f"📡 Starting streaming execution for {self.event_type}")
                await self.handler.stream(input_data, self.emit_event)
                return None  # Explicit return for streaming case
            else:
                # Type guard ensures this is safe
                if not isinstance(self.handler, RunHandler):
                    raise TypeError(f"Handler must be RunHandler for non-streaming events, got {type(self.handler)}")
                
                logger.info(f"⚡ Running direct execution for {self.event_type}")
                result = await self.handler.run(input_data)
                await self.emit_event(result)
                return result
                
        except Exception as e:
            error_msg = f"❌ Error running {self.event_type} event: {str(e)}"
            logger.error(error_msg)
            
            # Emit error result but don't suppress the exception
            try:
                await self.emit_event({"error": str(e), "event_type": self.event_type})
            except Exception as emit_error:
                logger.error(f"Failed to emit error result: {emit_error}")
            
            raise  # Re-raise original exception
    