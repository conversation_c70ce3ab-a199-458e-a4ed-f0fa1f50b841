from typing import Any, AsyncGenerator, Callable, List, Tuple

from corelanggraph.handler.run_handler import <PERSON><PERSON><PERSON><PERSON>
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from .base_event import Base<PERSON>vent, T, U
from corelanggraph.handler.base_handler import <PERSON>H<PERSON>ler
from logger import logger
import json

class FastAPIEvent(BaseEvent[T, U]):
    """
    FastAPI event implementation that does NOT support streaming.
    Returns results directly to the current HTTP request.
    Designed for synchronous HTTP request/response patterns.
    """
    
    def __init__(self, handler: RunHandler[T, U], app: FastAPI, routes: List[Tuple[str, str]]):
        super().__init__(handler)
        self.app = app
        self._current_response = None
        # Register the route once during initialization
        for route in routes:
            self.app.add_api_route(route[0], self._route_handler, methods=[route[1]])

  
            
        
    @property
    def event_type(self) -> str:
        return "fastapi"
    
    @property
    def supports_streaming(self) -> bool:
        return False 
    
    async def emit_event(self, message: Any) -> Any:
        """
        Emit the result as the HTTP response.
        """
        if self._current_response is not None:
            # This is the first emit_event call, send the HTTP response
            return message
        return message
    
    

    async def _route_handler(self, request: Request) -> Any:
        """
        Internal route handler that processes the request and waits for the first emitted event.
        """
        try:
            # Properly await the request body
            body = await request.body()
            
            # Set up a way to capture the first emit_event call
     
            input_data = await self.parse_message(self.event_type,body,request.url.path)
            result =  await self.handle_event(input_data)
            return result
       
        except Exception as e:
            logger.error(f"Error in FastAPI route handler: {str(e)}")
            raise
    
    async def run(self) -> Any:
        """
        The route is already registered in __init__.
        This method can be used for any additional setup if needed.
        """
        pass