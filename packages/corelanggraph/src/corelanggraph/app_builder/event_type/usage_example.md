"""
Usage examples for different event types.
This demonstrates how to instantiate and use each event class with their respective clients.
"""

from typing import Any
from corelanggraph.handler.base_handler import <PERSON><PERSON>and<PERSON>
from .websocket_event import WebSocketEvent
from .kafka_event import KafkaE<PERSON>
from .slack_event import SlackE<PERSON>
from .zendesk_event import ZendeskEvent
from .fastapi_event import Fast<PERSON>IEvent

# Import the client packages
from websocket import websocket_client
from kafka import producer
from slack import slack_client
from zendesk import zendesk_client

async def websocket_example(handler: <PERSON>Hand<PERSON>, websocket_client_instance: websocket_client.WebSocketClient):
    """Example of using WebSocketEvent for streaming results"""
    event = WebSocketEvent(handler, websocket_client_instance)
    
    # Input data for the workflow
    input_data = {"query": "process this data", "user_id": "123"}
    
    # Run the event (will stream results through websocket)
    await event.run(input_data)

async def kafka_example(handler: <PERSON><PERSON><PERSON><PERSON>, kafka_producer_instance: producer.KafkaProducer):
    """Example of using KafkaEvent for publishing results to topics"""
    event = KafkaEvent(
        handler=handler,
        kafka_producer=kafka_producer_instance,
        result_topic="workflow_results",
        stream_topic="workflow_stream"
    )
    
    input_data = {"task": "analyze data", "batch_id": "batch_456"}
    
    # Run the event (will publish results to Kafka topics)
    await event.run(input_data)

async def slack_example(handler: BaseHandler, slack_client_instance: slack_client.SlackClient):
    """Example of using SlackEvent for sending results to Slack"""
    event = SlackEvent(
        handler=handler,
        slack_client=slack_client_instance,
        channel="#workflow-results",
        thread_ts=None  # Optional: for threading messages
    )
    
    input_data = {"operation": "generate report", "team": "data-science"}
    
    # Run the event (will send results to Slack channel)
    await event.run(input_data)

async def zendesk_example(handler: BaseHandler, zendesk_client_instance: zendesk_client.ZendeskClient):
    """Example of using ZendeskEvent for creating tickets and comments"""
    # Option 1: Use existing ticket
    event = ZendeskEvent(
        handler=handler,
        zendesk_client=zendesk_client_instance,
        ticket_id=12345
    )
    
    # Option 2: Create new ticket
    # event = ZendeskEvent(
    #     handler=handler,
    #     zendesk_client=zendesk_client_instance,
    #     create_new_ticket=True,
    #     ticket_subject="Automated Workflow Results"
    # )
    
    input_data = {"issue": "system analysis", "priority": "high"}
    
    # Run the event (will add comments to Zendesk ticket)
    await event.run(input_data)

async def fastapi_example(handler: BaseHandler):
    """Example of using FastAPIEvent for HTTP request/response"""
    event = FastAPIEvent(handler)
    
    input_data = {"request_id": "req_789", "action": "process_payment"}
    
    # Run the event and get result directly (no streaming)
    result = await event.run(input_data)
    
    # For FastAPI endpoints, you can return this result directly
    return result

# Example of how to use in a FastAPI endpoint:
"""
from fastapi import FastAPI
from .usage_example import fastapi_example

app = FastAPI()

@app.post("/workflow")
async def run_workflow(input_data: dict):
    # Assuming you have a handler instance
    handler = YourHandlerImplementation()
    result = await fastapi_example(handler)
    return result
""" 