from abc import abstractmethod
from typing import Any, As<PERSON><PERSON><PERSON>ator, <PERSON>, <PERSON><PERSON>
from pydantic import Field
from websocket import WebsocketClient
import json
import uuid
from datetime import datetime, timezone

from corelanggraph.handler.base_handler import <PERSON>Handler
from .base_event import BaseEvent, T, U
from corelanggraph.app_builder.models.event import ClientT<PERSON><PERSON><PERSON>

from logger import logger


class WebSocketEvent(BaseEvent[T, U]):
    """
    WebSocket event implementation that supports streaming.
    Uses websocket client to send results back to connected clients.
    """
    
    def __init__(self,
                  handler: BaseHandler[T, U],
                  websocket_client: WebsocketClient,
                  channels: List[str],
                  
                  ):
        super().__init__(handler)
        self.websocket_client: WebsocketClient = websocket_client
        self.channels = channels
        
    @property
    def event_type(self) -> str:
        return "websocket"
    
    @property
    def supports_streaming(self) -> bool:
        return True
    

    @abstractmethod
    async def parse_message_output(self,message:Any)->Tuple[str,Any]:
        """
        Parse the message from the request body.
        """
        pass

    
    async def emit_result(self, result: Any) -> None:
        """Emit final result through websocket"""
        try:
            event,message = await self.parse_message_output(result)
            await self.websocket_client.send_message(event,message)
            logger.info(f"📤 WebSocket result emitted: {result}")
        except Exception as e:
            logger.error(f"❌ Failed to emit WebSocket result: {str(e)}")
            raise
    
    async def register_event_handlers(self)->None:
        
        for channel in self.channels:
            @self.websocket_client.sio.on(channel, namespace=self.websocket_client.namespace)
            async def message(data):
                try:
                    input_data = await self.parse_message(self.event_type,data,channel)
                    await self.handle_event(input_data)
                except Exception as e:
                    logger.error(f"❌ Failed to handle WebSocket message: {str(e)}")

    async def run(self) -> None:
        """Emit streaming results through websocket"""
        logger.info("[WebSocket EventHandler] Registering event handlers")

        await self.register_event_handlers()
        self.websocket_client.register_external_event_handler(self.register_event_handlers)
        await self.websocket_client.connect()