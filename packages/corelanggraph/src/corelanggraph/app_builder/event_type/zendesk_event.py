import asyncio
from datetime import datetime, timezone, timedelta
from zendesk import zendesk_client
from .base_event import BaseEvent, T, U
from corelanggraph.handler.base_handler import BaseHandler
from logger import logger
import json

class ZendeskEvent(BaseEvent[T, U]):
    """
    Zendesk event implementation that supports streaming by adding comments to tickets.
    Uses zendesk client to create tickets and add comments for results.
    """
    
    def __init__(self, 
                 handler: BaseHandler[T, U], 
                 zendesk_client: zendesk_client.ZendeskClient,
                 interval: int = 10):
        super().__init__(handler)
        self.zendesk_client = zendesk_client
        self.task_queue = asyncio.Queue()
        self.semaphore = asyncio.Semaphore(10)
        self.shutdown_event = asyncio.Event()
        self.interval = interval
        self.processed_task_ids: set[int] = set()

    @property
    def event_type(self) -> str:
        return "zendesk"
    
    @property
    def supports_streaming(self) -> bool:
        return True
    

    async def _check_for_new_tasks(self) -> None:
        since = datetime.now(timezone.utc) - timedelta(seconds=self.interval)
        new_tasks = await asyncio.to_thread(self.zendesk_client.search_new_tasks, since)
        if new_tasks.count == 0:
            logger.info("No new tasks found.")
            return

        for task in new_tasks:
            if not (await self._has_cymulate_ai_comment(task)) and task.id not in self.processed_task_ids:
                await self._add_task_to_queue(task.id)
        logger.info("Checked for new tasks and updated queue.")
        asyncio.create_task(self._worker())

    async def _has_cymulate_ai_comment(self, task) -> bool:
        comments = await asyncio.to_thread(self.zendesk_client.get_ticket_comments, task.id)
        return any(
            comment.body.startswith(self.zendesk_client.ai_tag) and "An error occurred" not in comment.body for comment in
            comments)
    async def _add_task_to_queue(self, task_id: int, retries: int = 3) -> None:
        for attempt in range(retries):
            try:
                await self.task_queue.put(task_id)
                logger.info(f"Task {task_id} added to queue.")
                return
            except asyncio.QueueFull:
                logger.warning(f"Task queue is full; retrying to add task ID {task_id} (Attempt {attempt + 1})")
                await asyncio.sleep(2 ** attempt)

        logger.error(f"Failed to add task ID {task_id} to queue after {retries} attempts. Discarding task.")

    async def _worker(self) -> None:
        while not self.shutdown_event.is_set():
            try:
                task_id = await self.task_queue.get()  # Async get
                async with self.semaphore:
                    input_data = await self.parse_message(self.event_type,task_id,task_id)
                    await self.handle_event(input_data)
                self.task_queue.task_done()
            except asyncio.CancelledError:
                logger.info("Worker cancelled.")
                break

    async def run(self) -> None:
        """Emit streaming results through websocket"""
        logger.info("[WebSocket EventHandler] Registering event handlers")

        while not self.shutdown_event.is_set():
            try:
                await self._check_for_new_tasks()
            except Exception as e:
                logger.error(f"Error during polling: {e}")
            await asyncio.sleep(self.interval)