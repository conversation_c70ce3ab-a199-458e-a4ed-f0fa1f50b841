import base64
from urllib.parse import unquote
import jwt
from typing import Optional, Dict, Any
import re

from logger import logger


class CookieProcessor:
    def __init__(self, cookies_str: str, auth_token: str = None):

        if auth_token:
            self.auth_token = auth_token
        else:
            self.cookies_str = cookies_str
            self.cookies = self.parse_cookies()
            self.auth_token = self.extract_token_from_cookie()
        self.decoded_token = self.decode_and_verify_auth_token()

    def parse_cookies(self) -> Dict[str, str]:
        cookies = {}
        for cookie in self.cookies_str.split(";"):
            key, value = cookie.strip().split("=", 1)
            cookies[key] = value
        return cookies

    def extract_token_from_cookie(self) -> Optional[str]:
        cookie_value = self.cookies.get("authToken")
        if not cookie_value:
            raise ValueError("authToken not found in cookies")

        try:
            decoded_uri = unquote(cookie_value)
            decoded_token = base64.urlsafe_b64decode(decoded_uri + "==").decode("utf-8")
            match = re.match(r"^Bearer\s([\w-]+\.?[\w-]*\.?[\w-]*)$", decoded_token)
            return match.group(1) if match else None
        except Exception as error:
            print(f"Error extracting JWT from cookie: {error}")
            raise ValueError("Error extracting JWT from cookie")

    def decode_and_verify_auth_token(self) -> Optional[Dict[str, Any]]:
        try:
            if not self.auth_token:
                raise ValueError(
                    "authToken not found in cookies, headers, or data payload"
                )

            decoded_token = jwt.decode(
                self.auth_token, options={"verify_signature": False}
            )
            return decoded_token
        except (
            jwt.DecodeError,
            jwt.ExpiredSignatureError,
            jwt.InvalidTokenError,
            ValueError,
            IndexError,
        ) as e:
            print(f"Error decoding or verifying auth token: {e}")
            raise ValueError("Error decoding or verifying auth token")
