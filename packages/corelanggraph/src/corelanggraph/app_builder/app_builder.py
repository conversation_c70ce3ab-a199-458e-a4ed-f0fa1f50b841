from typing import List, Dict, Any, Optional, Union
from fastapi import FastAP<PERSON>
from corelanggraph.handler.base_handler import <PERSON>Handler
from corelanggraph.handler.run_handler import <PERSON>Handler
from corelanggraph.handler.stream_handler import <PERSON>Handler

# Import all event types
from corelanggraph.app_builder.event_type import (
    BaseEvent,
    WebSocketEvent,
    KafkaEvent,
    SlackEvent,
    ZendeskEvent,
    FastAPIEvent
)

# Import client packages (these might need to be installed)
from logger import logger


class AppBuilder:
    """
    Enhanced AppBuilder that creates applications with multiple event type support.
    Supports WebSocket, Kafka, Slack, Zendesk, and FastAPI event handlers.
    """

    def __init__(self):
        self.events: List[BaseEvent] = []
        self.handlers: Dict[str, BaseHandler] = {}
        self.config: Dict[str, Any] = {}
        self.fastapi_app: Optional[FastAPI] = None
        
    def add_handler(self, name: str, handler: BaseHandler) -> 'AppBuilder':
        """
        Add a handler that can be used by events.
        
        Args:
            name: Unique name for the handler
            handler: Handler instance (<PERSON><PERSON><PERSON><PERSON> or StreamHandler)
        
        Returns:
            Self for method chaining
        """
        self.handlers[name] = handler
        logger.info(f"✅ Added handler: {name} ({type(handler).__name__})")
        return self
    
    def with_fastapi(
        self, 
        handler_name: str, 
        route: str = "/workflow",
        app: Optional[FastAPI] = None
    ) -> 'AppBuilder':
        """
        Add FastAPI event support.
        
        Args:
            handler_name: Name of the handler to use (must be RunHandler)
            route: API route path
            app: Optional existing FastAPI app, creates new one if None
        
        Returns:
            Self for method chaining
        """
        if handler_name not in self.handlers:
            raise ValueError(f"Handler '{handler_name}' not found. Add it first using add_handler()")
        
        handler = self.handlers[handler_name]
        if not isinstance(handler, RunHandler):
            raise TypeError(f"FastAPI events require RunHandler, got {type(handler).__name__}")
        
        if app is None:
            app = FastAPI(title="LangGraph App", description="Auto-generated API")
        
        self.fastapi_app = app
        event = FastAPIEvent(handler, app, route)
        self.events.append(event)
        
        logger.info(f"🚀 Added FastAPI event: {route}")
        return self
    
    def with_websocket(
        self, 
        handler_name: str, 
        websocket_client
    ) -> 'AppBuilder':
        """
        Add WebSocket event support.
        
        Args:
            handler_name: Name of the handler to use
            websocket_client: WebSocket client instance
        
        Returns:
            Self for method chaining
        """
        if handler_name not in self.handlers:
            raise ValueError(f"Handler '{handler_name}' not found. Add it first using add_handler()")
        
        handler = self.handlers[handler_name]
        event = WebSocketEvent(handler, websocket_client)
        self.events.append(event)
        
        logger.info(f"🔌 Added WebSocket event")
        return self
    
    def with_kafka(
        self, 
        handler_name: str, 
        kafka_producer,
        result_topic: str,
        stream_topic: Optional[str] = None
    ) -> 'AppBuilder':
        """
        Add Kafka event support.
        
        Args:
            handler_name: Name of the handler to use
            kafka_producer: Kafka producer instance
            result_topic: Topic for final results
            stream_topic: Topic for streaming results (optional)
        
        Returns:
            Self for method chaining
        """
        if handler_name not in self.handlers:
            raise ValueError(f"Handler '{handler_name}' not found. Add it first using add_handler()")
        
        handler = self.handlers[handler_name]
        event = KafkaEvent(handler, kafka_producer, result_topic, stream_topic)
        self.events.append(event)
        
        logger.info(f"📡 Added Kafka event: {result_topic}")
        return self
    
    def with_slack(
        self, 
        handler_name: str, 
        slack_client,
        channel: str,
        thread_ts: Optional[str] = None
    ) -> 'AppBuilder':
        """
        Add Slack event support.
        
        Args:
            handler_name: Name of the handler to use
            slack_client: Slack client instance
            channel: Slack channel to send results to
            thread_ts: Optional thread timestamp for threading
        
        Returns:
            Self for method chaining
        """
        if handler_name not in self.handlers:
            raise ValueError(f"Handler '{handler_name}' not found. Add it first using add_handler()")
        
        handler = self.handlers[handler_name]
        event = SlackEvent(handler, slack_client, channel, thread_ts)
        self.events.append(event)
        
        logger.info(f"💬 Added Slack event: {channel}")
        return self
    
    def with_zendesk(
        self, 
        handler_name: str, 
        zendesk_client,
        ticket_id: Optional[int] = None,
        create_new_ticket: bool = False,
        ticket_subject: Optional[str] = None
    ) -> 'AppBuilder':
        """
        Add Zendesk event support.
        
        Args:
            handler_name: Name of the handler to use
            zendesk_client: Zendesk client instance
            ticket_id: Existing ticket ID (if updating existing ticket)
            create_new_ticket: Whether to create new tickets
            ticket_subject: Subject for new tickets
        
        Returns:
            Self for method chaining
        """
        if handler_name not in self.handlers:
            raise ValueError(f"Handler '{handler_name}' not found. Add it first using add_handler()")
        
        handler = self.handlers[handler_name]
        event = ZendeskEvent(
            handler, 
            zendesk_client, 
            ticket_id, 
            create_new_ticket, 
            ticket_subject
        )
        self.events.append(event)
        
        logger.info(f"🎫 Added Zendesk event")
        return self
    
    def set_config(self, key: str, value: Any) -> 'AppBuilder':
        """
        Set configuration value.
        
        Args:
            key: Configuration key
            value: Configuration value
        
        Returns:
            Self for method chaining
        """
        self.config[key] = value
        return self
    
    def build(self) -> Dict[str, Any]:
        """
        Build and return the complete application configuration.
        
        Returns:
            Dictionary containing all application components
        """
        if not self.events:
            logger.warning("⚠️  No events configured. Application might not respond to any inputs.")
        
        app_config = {
            "events": self.events,
            "handlers": self.handlers,
            "config": self.config,
            "fastapi_app": self.fastapi_app,
            "event_count": len(self.events),
            "handler_count": len(self.handlers)
        }
        
        logger.info(f"🎯 Application built successfully:")
        logger.info(f"   - {len(self.events)} events configured")
        logger.info(f"   - {len(self.handlers)} handlers registered")
        logger.info(f"   - FastAPI app: {'✅' if self.fastapi_app else '❌'}")
        
        return app_config
    
    def get_fastapi_app(self) -> Optional[FastAPI]:
        """
        Get the FastAPI application instance.
        
        Returns:
            FastAPI app instance if configured, None otherwise
        """
        return self.fastapi_app
    
    async def run_event(self, event_type: str, input_data: Any) -> Any:
        """
        Run a specific event type with input data.
        
        Args:
            event_type: Type of event to run
            input_data: Input data for the event
        
        Returns:
            Result from the event
        """
        matching_events = [e for e in self.events if e.event_type == event_type]
        
        if not matching_events:
            raise ValueError(f"No events configured for type: {event_type}")
        
        if len(matching_events) > 1:
            logger.warning(f"Multiple events found for type {event_type}, using first one")
        
        event = matching_events[0]
        return await event.handle_event(input_data)
    
    def list_events(self) -> List[Dict[str, Any]]:
        """
        Get information about all configured events.
        
        Returns:
            List of event information dictionaries
        """
        return [
            {
                "type": event.event_type,
                "supports_streaming": event.supports_streaming,
                "is_streaming": event.is_streaming,
                "handler_type": type(event.handler).__name__
            }
            for event in self.events
        ]
    
    