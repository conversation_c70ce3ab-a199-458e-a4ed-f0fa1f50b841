"""
Comprehensive usage examples for the enhanced AppBuilder class.
This demonstrates how to create applications with multiple event types.
"""

from typing import Any
from corelanggraph.app_builder.app_builder import AppBuilder
from corelanggraph.handler.run_handler import <PERSON><PERSON>andler
from corelanggraph.handler.stream_handler import <PERSON><PERSON>andler
from fastapi import FastAPI
import uvicorn


# Example handler implementations
class ExampleRunHandler(RunHandler):
    """Example handler that processes requests and returns results."""
    
    async def run(self, input_data: Any) -> Any:
        """Process input and return result."""
        return {
            "status": "completed",
            "input": input_data,
            "result": f"Processed: {input_data}"
        }


class ExampleStreamHandler(StreamHandler):
    """Example handler that streams results."""
    
    async def stream(self, input_data: Any, emit_callback) -> None:
        """Process input and stream results."""
        # Simulate streaming processing
        steps = ["Starting", "Processing", "Analyzing", "Completing"]
        
        for i, step in enumerate(steps):
            result = {
                "step": i + 1,
                "total_steps": len(steps),
                "status": step,
                "data": input_data
            }
            await emit_callback(result)


def create_basic_fastapi_app():
    """Create a basic FastAPI application with a single workflow endpoint."""
    
    # Create handlers
    run_handler = ExampleRunHandler()
    
    # Build the application
    app_config = (AppBuilder()
        .add_handler("main_handler", run_handler)
        .with_fastapi("main_handler", route="/workflow")
        .set_config("app_name", "Basic Workflow App")
        .build()
    )
    
    return app_config["fastapi_app"]


def create_multi_event_app():
    """Create an application that supports multiple event types."""
    
    # Create different types of handlers
    run_handler = ExampleRunHandler()
    stream_handler = ExampleStreamHandler()
    
    # Mock clients (in real usage, these would be actual client instances)
    class MockSlackClient:
        async def post_message(self, channel, text, thread_ts=None):
            print(f"Slack message to {channel}: {text}")
    
    class MockKafkaProducer:
        async def send(self, topic, message):
            print(f"Kafka message to {topic}: {message}")
    
    class MockWebSocketClient:
        async def send_message(self, message):
            print(f"WebSocket message: {message}")
    
    class MockZendeskClient:
        async def add_comment(self, ticket_id, comment):
            print(f"Zendesk comment on ticket {ticket_id}: {comment}")
    
    # Build comprehensive application
    app_config = (AppBuilder()
        # Add handlers
        .add_handler("sync_handler", run_handler)
        .add_handler("stream_handler", stream_handler)
        
        # Configure different event types
        .with_fastapi("sync_handler", route="/api/workflow")
        .with_slack("stream_handler", MockSlackClient(), channel="#workflows")
        .with_kafka("stream_handler", MockKafkaProducer(), 
                   result_topic="workflow_results", stream_topic="workflow_stream")
        .with_websocket("stream_handler", MockWebSocketClient())
        .with_zendesk("sync_handler", MockZendeskClient(), ticket_id=12345)
        
        # Set configuration
        .set_config("app_name", "Multi-Event Workflow App")
        .set_config("version", "1.0.0")
        .set_config("enable_logging", True)
        
        # Build the application
        .build()
    )
    
    return app_config


def create_streaming_focused_app():
    """Create an application focused on streaming capabilities."""
    
    stream_handler = ExampleStreamHandler()
    
    class MockStreamingClients:
        class WebSocket:
            async def send_message(self, message):
                print(f"🔌 WebSocket stream: {message}")
        
        class Kafka:
            async def send(self, topic, message):
                print(f"📡 Kafka stream to {topic}: {message}")
        
        class Slack:
            async def post_message(self, channel, text, thread_ts=None):
                print(f"💬 Slack stream to {channel}: {text}")
    
    clients = MockStreamingClients()
    
    app_config = (AppBuilder()
        .add_handler("streaming_handler", stream_handler)
        .with_websocket("streaming_handler", clients.WebSocket())
        .with_kafka("streaming_handler", clients.Kafka(), 
                   result_topic="final_results", stream_topic="live_updates")
        .with_slack("streaming_handler", clients.Slack(), channel="#live-updates")
        .set_config("streaming_mode", True)
        .build()
    )
    
    return app_config


async def run_example_workflows():
    """Demonstrate running workflows with different event types."""
    
    # Create multi-event app
    app_config = create_multi_event_app()
    app_builder = AppBuilder()
    
    # Load the configuration back into a new builder (for demonstration)
    for name, handler in app_config["handlers"].items():
        app_builder.add_handler(name, handler)
    
    # Example input data
    test_input = {"query": "analyze user behavior", "dataset": "user_sessions.csv"}
    
    print("🎯 Running example workflows:")
    print("=" * 50)
    
    # Note: In a real application, these would be triggered by actual events
    # This is just for demonstration purposes
    
    print("📋 Available events:")
    for event_info in app_config.get("events", []):
        if hasattr(event_info, 'event_type'):
            print(f"  - {event_info.event_type} ({'streaming' if event_info.supports_streaming else 'direct'})")


def main():
    """Main function to demonstrate different application configurations."""
    
    print("🚀 AppBuilder Examples")
    print("=" * 50)
    
    # Example 1: Basic FastAPI app
    print("\n1️⃣ Creating basic FastAPI application...")
    fastapi_app = create_basic_fastapi_app()
    print(f"   ✅ FastAPI app created: {fastapi_app.title}")
    
    # Example 2: Multi-event application
    print("\n2️⃣ Creating multi-event application...")
    multi_app = create_multi_event_app()
    print(f"   ✅ App created with {multi_app['event_count']} events and {multi_app['handler_count']} handlers")
    
    # Example 3: Streaming-focused application
    print("\n3️⃣ Creating streaming-focused application...")
    streaming_app = create_streaming_focused_app()
    print(f"   ✅ Streaming app created with {streaming_app['event_count']} streaming events")
    
    # Example 4: Show event information
    print("\n4️⃣ Event information:")
    for i, event in enumerate(multi_app["events"], 1):
        if hasattr(event, 'event_type'):
            print(f"   {i}. {event.event_type} - {'Streaming' if event.supports_streaming else 'Direct'}")
    
    print(f"\n🎉 All examples completed successfully!")
    
    # To run the FastAPI app, you would use:
    # uvicorn.run(fastapi_app, host="0.0.0.0", port=8000)


if __name__ == "__main__":
    main() 