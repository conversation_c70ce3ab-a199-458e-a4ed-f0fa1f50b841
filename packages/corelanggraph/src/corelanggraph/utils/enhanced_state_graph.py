import inspect
import asyncio
from typing import Any, Callable, Dict, List, Optional, Type, Union

from langgraph.graph import StateGraph
from langchain_core.runnables import RunnableConfig
from .run_with_state import get_global_event_loop
from langgraph.graph.state import CompiledStateGraph


class EnhancedStateGraph(StateGraph):
    """
    Enhanced StateGraph that ensures proper propagation of checkpoint configuration
    and simplifies working with stateful nodes.
    """

    @staticmethod
    def _prepare_call_args(
        callable_obj: Callable, state: Dict[str, Any], config: Any
    ) -> tuple:
        """
        Prepare arguments for calling a function/method based on its signature.

        Args:
            callable_obj: The function or method to inspect
            state: The state dictionary
            config: The config object

        Returns:
            Tuple of (args, kwargs) to use when calling the function
        """
        signature = inspect.signature(callable_obj)
        params = list(signature.parameters.keys())

        # Build kwargs based on signature
        kwargs = {}
        if "state" in params:
            kwargs["state"] = state
        if "config" in params:
            kwargs["config"] = config

        # Handle positional arguments for callables that don't use named parameters
        if not kwargs and len(params) >= 1:
            # If no named matches but callable expects parameters, pass state as first arg
            if len(params) >= 2:
                args = (state, config)
            else:
                args = (state,)
            return args, {}

        return (), kwargs

    def add_subgraph(
        self, name: str, subgraph: StateGraph, metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Add a subgraph, ensuring that checkpointing configuration is properly passed.

        Args:
            name: Name of the subgraph node
            subgraph: The StateGraph instance to add as a subgraph
            metadata: Optional metadata for the node
        """
        if metadata is None:
            metadata = {}

        # Add the wrapper as a node
        super().add_node(name, subgraph, metadata=metadata)

    def add_node(
        self,
        name: str,
        node: Union[Type, Callable],
        metadata: Optional[Dict[str, Any]] = None,
        auto_wrap: bool = True,  # Whether to auto-wrap with run_with_state
        **kwargs,
    ) -> None:
        """
        Add a node to the graph, with optional automatic state handling.

        Args:
            name: Name of the node
            node: The node class or function
            metadata: Optional metadata for the node
            auto_wrap: Whether to automatically wrap the node with run_with_state (default: True)
        """
        if metadata is None:
            metadata = {}

        # Handle different node types
        if auto_wrap and inspect.isclass(node):
            # For class nodes, implement run_with_state functionality directly

            async def class_wrapper(state: Dict[str, Any], config) -> Dict[str, Any]:
                # Check if the class constructor accepts a state argument
                init_signature = inspect.signature(node.__init__)
                init_params = list(init_signature.parameters.keys())

                # Create instance based on __init__ signature
                instance = node(state) if len(init_params) > 1 else node()

                # Ensure execute method exists
                if not hasattr(instance, "execute"):
                    raise NotImplementedError(
                        f"Class {node.__name__} does not have a 'execute' method"
                    )

                # Prepare arguments for execute method
                execute_method = instance.execute
                args, kwargs = self._prepare_call_args(execute_method, state, config)

                # Call execute method with appropriate arguments
                if inspect.iscoroutinefunction(execute_method):
                    result = await execute_method(*args, **kwargs)
                else:
                    result = execute_method(*args, **kwargs)

                # Handle nested coroutines
                if inspect.iscoroutine(result):
                    result = await result

                return result if result is not None else state

            # Add the wrapped class
            super().add_node(name, class_wrapper, metadata=metadata, **kwargs)

        elif (
            auto_wrap
            and inspect.isfunction(node)
            and not inspect.iscoroutinefunction(node)
        ):
            # For regular functions, wrap them in a coroutine
            async def func_wrapper(state: Dict[str, Any], config) -> Dict[str, Any]:
                # Prepare arguments for function call
                args, kwargs = self._prepare_call_args(node, state, config)

                # Call function with appropriate arguments
                result = node(*args, **kwargs)
                return result if result is not None else state

            super().add_node(name, func_wrapper, metadata=metadata, **kwargs)

        elif auto_wrap and inspect.iscoroutinefunction(node):
            # For coroutine functions, wrap with signature handling
            async def coro_wrapper(state: Dict[str, Any], config) -> Dict[str, Any]:
                # Prepare arguments for coroutine call
                args, kwargs = self._prepare_call_args(node, state, config)

                # Call coroutine with appropriate arguments
                result = await node(*args, **kwargs)
                return result if result is not None else state

            super().add_node(name, coro_wrapper, metadata=metadata, **kwargs)

        else:
            # For other node types (like pre-compiled graphs), add directly
            super().add_node(name, node, metadata=metadata, **kwargs)
