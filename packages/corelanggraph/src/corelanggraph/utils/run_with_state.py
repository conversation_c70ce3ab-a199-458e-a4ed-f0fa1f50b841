import inspect
import asyncio
from functools import wraps

# Create a global event loop for the entire application
_global_event_loop = None

def get_global_event_loop():
    global _global_event_loop
    if _global_event_loop is None:
        try:
            _global_event_loop = asyncio.get_event_loop()
        except RuntimeError:
            _global_event_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(_global_event_loop)
    return _global_event_loop

def run_with_state(cls):
    def execute(state=None):
        # Check if the class constructor accepts a state argument
        init_signature = inspect.signature(cls.__init__)
        init_params = list(init_signature.parameters.keys())
        
        # Create instance based on __init__ signature
        if len(init_params) > 1:
            instance = cls(state)
        else:
            instance = cls()

        # Ensure execute method exists
        if not hasattr(instance, "execute"):
            raise NotImplementedError(f"Class {cls.__name__} does not have a 'execute' method")

        # Get execute method signature
        execute_method = instance.execute
        execute_signature = inspect.signature(execute_method)
        execute_params = list(execute_signature.parameters.keys())

        async def _async_execute():
            if len(execute_params) >= 1:
                if inspect.iscoroutinefunction(execute_method):
                    result = await execute_method(state)
                else:
                    result = execute_method(state)
            else:
                if inspect.iscoroutinefunction(execute_method):
                    result = await execute_method()
                else:
                    result = execute_method()

            # Handle nested coroutines - if result is also a coroutine, await it
            if inspect.iscoroutine(result):
                result = await result

            return result if result is not None else state

        # Use the global event loop instead of creating a new one
        loop = get_global_event_loop()
        return loop.run_until_complete(_async_execute())

    return execute