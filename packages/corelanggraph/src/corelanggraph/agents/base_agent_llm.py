"""
Base Agent Class

This module provides the foundation for all agents in the system.
Each specialized agent will inherit from this base class.
"""

import os
from typing import Dict, List, Any, Optional, Callable
from abc import ABC, abstractmethod

from langchain_core.prompts import BasePromptTemplate
from pydantic import BaseModel

from corelanggraph.langfuse.langfuse import Langfuse<PERSON><PERSON>

from .base_agent import BaseAgent
from trustcall import create_extractor
from langchain_core.runnables import Runnable
from langchain_core.tools import BaseTool
from langchain_core.prompts import ChatPromptTemplate
from typing import Union


class BaseAgentLLM(BaseAgent):
    def __init__(
        self, name: str, description: str, llm: Runnable, prompt_name: str = None
    ):
        """
        Args:
            name (str): The name of the agent.
            description (str): The description of the agent.
            llm (Runnable): The LLM to use for the agent.
            prompt_name (str, optional): The name of the prompt to use for the agent. If not provided, the prompt will be inferred from the LANGFUSE_PROMPT_FOLDER environment variable.
        """
        super().__init__(name, description)
        self.llm = llm
        self.prompt_name = prompt_name

    @property
    def prompt_label(self):
        """
        The label of the prompt to use for the agent.
        Returns:
            str: The label of the prompt to use for the agent.
        """
        return os.getenv("LANGFUSE_PROMPT_LABEL", "production")

    @property
    def get_langfuse_prompt_name(self):
        """
        The name of the prompt to use for the agent.
        If prompt_name is provided, it will be used.
        If not, the prompt will be inferred from the LANGFUSE_PROMPT_FOLDER environment variable.
        If the LANGFUSE_PROMPT_FOLDER environment variable is not set, the prompt will be inferred from the name of the agent.
        Returns:
            str: The name of the prompt to use for the agent.
        """
        if self.prompt_name:
            return self.prompt_name
        folder = os.getenv("LANGFUSE_PROMPT_FOLDER", "")
        if folder:
            return f"{folder}/{self.metadata.name}"
        return self.metadata.name

    @property
    def langfuse_prompt(self):
        """
        The langfuse prompt to use for the agent.
        Returns:
            LangfusePrompt: The langfuse prompt for the agent.
        """
        langfuse_system_prompt = self.langfuse_client.get_prompt(
            self.get_langfuse_prompt_name, label=self.prompt_label
        )
        if not langfuse_system_prompt:
            raise ValueError(
                f"No langfuse system prompt found for {self.metadata.name}"
            )
        return langfuse_system_prompt

    @property
    def system_prompt(self) -> ChatPromptTemplate:
        """The system prompt for the agent
        Returns:
            ChatPromptTemplate: The system prompt for the agent.
        """
        langfuse_system_prompt = self.langfuse_prompt
        if not langfuse_system_prompt:
            raise ValueError(
                f"No langfuse system prompt found for {self.metadata.name}"
            )
        return self.generate_chat_prompt(langfuse_system_prompt.get_langchain_prompt())

    def generate_chat_prompt(
        self, langfuse_system_prompt: Union[str, list]
    ) -> ChatPromptTemplate:
        """Generate a chat prompt for the agent
        Args:
            langfuse_system_prompt (Union[str, list]): The langfuse system prompt to use for the agent.
        Returns:
            ChatPromptTemplate: The chat prompt for the agent.
        """
        if isinstance(langfuse_system_prompt, str):
            return ChatPromptTemplate.from_messages(
                [("system", langfuse_system_prompt)]
            )
        return ChatPromptTemplate.from_messages(
            langfuse_system_prompt,
        )

    def create_extractor(self, tools: list[BaseTool], tool_choice: str) -> Runnable:
        """Create an extractor for the agent
        Args:
            tools (list[BaseTool]): The tools to use for the agent.
            tool_choice (str): The tool choice to use for the agent.
        Returns:
            Runnable: The extractor for the agent.
        """
        return create_extractor(self.llm, tools=tools, tool_choice=tool_choice)
