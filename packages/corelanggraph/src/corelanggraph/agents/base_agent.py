"""
Base Agent Class

This module provides the foundation for all agents in the system.
Each specialized agent will inherit from this base class.
"""

from typing import Dict, List, Any, Optional, Callable, Literal
from abc import ABC, abstractmethod
from ..langfuse.langfuse import Langfuse<PERSON>lient
from pydantic import BaseModel, Field
from langchain_core.runnables import RunnableConfig
from langgraph.config import get_stream_writer
from logger import logger


class AgentMetadata(BaseModel):
    """Metadata about an agent"""

    name: str
    description: str
    capabilities: List[str] = Field(default_factory=list)
    requires_auth: bool = False
    version: str = "0.1.0"


class BaseAgent(ABC):
    """Base class for all agents in the system"""

    def __init__(self, name: str, description: str):
        """
        Initialize the base agent

        Args:
            name: Name of the agent
            description: Description of the agent's capabilities
        """
        self.metadata = AgentMetadata(name=name, description=description)
        self.callbacks: List[Callable] = []

    @property
    def langfuse_client(self):
        return LangfuseClient().client

    def register_callback(self, callback: Callable) -> None:
        """
        Register a callback function to be called after agent execution

        Args:
            callback: Function to call after agent execution
        """
        self.callbacks.append(callback)

    def _run_callbacks(
        self, input_data: Dict[str, Any], output_data: Dict[str, Any]
    ) -> None:
        """
        Run all registered callbacks

        Args:
            input_data: Input data to the agent
            output_data: Output data from the agent
        """
        for callback in self.callbacks:
            try:
                callback(self.metadata.name, input_data, output_data)
            except Exception as e:
                print(f"Error in callback: {e}")

    @abstractmethod
    async def execute(
        self, state: Dict[str, Any], config: RunnableConfig = None
    ) -> Dict[str, Any]:
        """
        Execute the agent's core functionality

        Args:
            state: Current workflow state

        Returns:
            Updated workflow state
        """
        pass

    async def __call__(
        self, state: Dict[str, Any], config: RunnableConfig = None
    ) -> Dict[str, Any]:
        """
        Make the agent callable

        Args:
            state: Current workflow state

        Returns:
            Updated workflow state
        """
        try:
            result = await self.execute(state, config)
            # Run callbacks after successful execution
            self._run_callbacks(state, result)
            return result
        except Exception as e:
            # Handle error and update state
            logger.error(f"🔍 Error in agent {self.metadata.name}: {e}")
            if isinstance(state, dict) and "error" in state:
                state["error"] = str(e)
            return state

    def write_stream(self, type: Literal["update", "error"], data: Any) -> None:
        """Write a stream to the stream writer
        Args:
            type (Literal['update', 'error']): The type of the stream.
            data (Any): The data to write to the stream.
        """
        get_stream_writer()({"type": type, "data": data})
