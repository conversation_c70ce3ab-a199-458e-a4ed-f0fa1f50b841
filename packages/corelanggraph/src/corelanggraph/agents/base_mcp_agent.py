"""
Base Agent Class

This module provides the foundation for all agents in the system.
Each specialized agent will inherit from this base class.
"""

from datetime import timedelta
from langchain_mcp_adapters.sessions import Connection
from langchain_mcp_adapters.client import MultiServerMCPClient
from .base_agent_llm import BaseAgentLLM
from langchain_core.runnables import Runnable
from langgraph.prebuilt.chat_agent_executor import Prompt
from langchain_core.tools import BaseTool
from langchain_mcp_adapters.sessions import StreamableHttpConnection


class BaseMCPAgent(BaseAgentLLM):

    mcp_client: MultiServerMCPClient | None = None

    def __init__(
        self,
        name: str,
        description: str,
        llm: Runnable,
        prompt_name: str | None = None,
    ):
        super().__init__(name, description, llm, prompt_name)

    def mcp_session(
        self,
        url: str,
        user_token: str = None,
    ) -> dict[str, StreamableHttpConnection]:
        mcp_server = {
            "transport": "streamable_http",
            "url": url,
            "timeout": timedelta(seconds=10),
            "terminate_on_close": True,
        }

        if user_token:
            mcp_server["headers"] = {
                "Authorization": f"Bearer {user_token}",
            }

        return {"mcp_server": mcp_server}

    def _initialize_mcp_client(
        self, mcp_session: dict[str, Connection]
    ) -> MultiServerMCPClient:
        return MultiServerMCPClient(mcp_session)

    async def get_tools(self, mcp_session: dict[str, Connection]) -> list[BaseTool]:
        session = self._initialize_mcp_client(mcp_session)

        return await session.get_tools()
