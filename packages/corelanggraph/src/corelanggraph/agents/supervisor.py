"""
Base Agent Class

This module provides the foundation for all agents in the system.
Each specialized agent will inherit from this base class.
"""

from langgraph_supervisor import create_supervisor
from langgraph.pregel import Pregel
from langgraph.prebuilt import ToolNode
from typing import Callable, Type, Any
from langgraph.prebuilt.chat_agent_executor import StateSchemaType

from .base_agent_llm import BaseAgentLLM
from langchain_core.runnables import Runnable
from langchain_core.tools import BaseTool


class Supervisor(BaseAgentLLM):
    def __init__(
        self,
        name: str,
        description: str,
        llm: Runnable,
        prompt_name: str = None,
    ):
        super().__init__(name, description, llm, prompt_name)

    def get_supervisor_agent(
        self,
        agents: list[Pregel],
        tools: list[BaseTool | Callable] | ToolNode | None = None,
        state_schema: Type[StateSchemaType] | None = None,
    ) -> Runnable:

        return create_supervisor(
            prompt=self.system_prompt,
            agents=agents,
            tools=tools,
            model=self.llm,
            state_schema=state_schema,
        )
