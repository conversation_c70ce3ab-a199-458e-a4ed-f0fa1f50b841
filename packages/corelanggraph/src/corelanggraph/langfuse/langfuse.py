from typing import <PERSON><PERSON>
from langfuse import Langfuse


class LangfuseClient:
    """
    Singleton class for Langfuse client.
    Ensures only one instance of the Langfuse client is created throughout the application lifecycle.
    """
    _instance: Optional['LangfuseClient'] = None
    _client: Optional[Langfuse] = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(LangfuseClient, cls).__new__(cls)
        return cls._instance

    def __init__(self, public_key: Optional[str] = None, secret_key: Optional[str] = None, host: Optional[str] = None):
        """
        Initialize the Langfuse client if it hasn't been initialized yet.
        
        Args:
            public_key: Langfuse public key
            secret_key: Langfuse secret key
            host: Langfuse host URL (optional)
        """
        if self._client is None:
            self._client = Langfuse(
                public_key=public_key,
                secret_key=secret_key,
                host=host
            )

    @property
    def client(self) -> Langfuse:
        """
        Get the Langfuse client instance.
        
        Returns:
            Langfuse: The Langfuse client instance
        """
        return self._client

    @classmethod
    def get_instance(cls, public_key: Optional[str] = None, secret_key: Optional[str] = None, host: Optional[str] = None) -> 'LangfuseClient':
        """
        Get the singleton instance of LangfuseClient.
        
        Args:
            public_key: Langfuse public key
            secret_key: Langfuse secret key
            host: Langfuse host URL (optional)
            
        Returns:
            LangfuseClient: The singleton instance
        """
        if cls._instance is None:
            cls._instance = cls(public_key, secret_key, host)
        return cls._instance
    
