# cymulate.corelanggraph

Language graph processing utilities for Cymulate.

## Requirements

- Python 3.12 or higher
- uv package manager

## Installation

```bash
uv pip install cymulate.corelanggraph
```

## Usage

```python
from corelanggraph import LangGraph
from corellm import LL<PERSON>lient

# Initialize the client and graph
llm_client = LLMClient(api_key="your-api-key")
graph = LangGraph(llm_client=llm_client)

# Add nodes and edges
graph.add_node("question", content="What is cybersecurity?")
graph.add_node("research", processor="web_search")
graph.add_node("answer", processor="summarize")

graph.add_edge("question", "research")
graph.add_edge("research", "answer")

# Execute the graph
result = graph.execute("question")
print(result)
```

## Features

- Graph-based language processing
- Integration with LLM providers
- Customizable node processors
- Visualization tools
- Execution tracing and debugging

## Development

### Setup

```bash
# Clone the repository
git clone https://bitbucket.org/cymulate/pythonlibs.git
cd pythonlibs

# Install development dependencies
uv pip install invoke
invoke install-dev
```

### Testing

```bash
invoke test
```

## License

Proprietary - Cymulate 