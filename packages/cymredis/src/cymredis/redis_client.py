import redis.asyncio as redis
import asyncio
from typing import Optional
from urllib.parse import urlparse
from redis.asyncio.client import Redis

from logger import logger


class RedisClient:
    _instance: Optional['RedisClient'] = None
    _lock: asyncio.Lock = asyncio.Lock()

    def __new__(cls, *args, **kwargs) -> 'RedisClient':
        if not cls._instance:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, redis_uri: str = None,decode_responses: bool = True) -> None:
        if not hasattr(self, 'initialized'):
            self.redis_uri: str = redis_uri 
            self.decode_responses: bool = decode_responses
            self._client: Optional[Redis] = None
            parsed_uri = urlparse(self.redis_uri)
            safe_netloc = f"{parsed_uri.username}:****@{parsed_uri.hostname}:{parsed_uri.port}"
            safe_uri = parsed_uri._replace(netloc=safe_netloc).geturl()

            logger.info(f"[Redis] Initializing with URI: {safe_uri}")
            self.initialized = True

    @property
    def client(self) -> Optional[Redis]:
        """Property to access the Redis client."""
        return self._client

    @client.setter
    def client(self, value: Optional[Redis]) -> None:
        """Setter for the Redis client."""
        self._client = value

    async def connect(self) -> None:
        """Establishes a connection to the Redis server."""
        if not self.client:
            async with self._lock:
                if not self.client:
                    try:
                        self.client = redis.from_url(
                            self.redis_uri,
                            decode_responses=self.decode_responses
                        )
                        await self.client.ping()
                        logger.info("[Redis] Connected")
                    except redis.RedisError as e:
                        logger.error(f"Failed to connect to Redis: {e}")
                        raise ConnectionError(f"Failed to connect to Redis: {e}")

    async def disconnect(self) -> None:
        """Closes the connection to the Redis server."""
        if self.client:
            await self.client.aclose()
            self.client = None
            logger.info("[Redis] Disconnected")

    async def __aenter__(self) -> 'RedisClient':
        """Support for async context manager."""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Close connection on exiting the async context."""
        await self.disconnect()
