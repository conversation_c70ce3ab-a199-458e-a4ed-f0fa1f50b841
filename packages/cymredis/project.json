{"name": "cym<PERSON>is", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "library", "sourceRoot": "packages/cymredis/cymredis", "targets": {"lock": {"executor": "@nxlv/python:lock", "options": {"update": false}}, "sync": {"executor": "@nxlv/python:sync", "options": {}}, "add": {"executor": "@nxlv/python:add", "options": {}}, "update": {"executor": "@nxlv/python:update", "options": {}}, "remove": {"executor": "@nxlv/python:remove", "options": {}}, "build": {"executor": "@nxlv/python:build", "outputs": ["{projectRoot}/dist"], "options": {"outputPath": "{projectRoot}/dist", "publish": true, "lockedVersions": true, "bundleLocalDependencies": true}, "cache": true}, "lint": {"executor": "@nxlv/python:ruff-check", "outputs": [], "options": {"lintFilePatterns": ["cym<PERSON>is", "tests"]}, "cache": true}, "format": {"executor": "@nxlv/python:ruff-format", "outputs": [], "options": {"filePatterns": ["cym<PERSON>is", "tests"]}, "cache": true}, "test": {"executor": "@nxlv/python:run-commands", "outputs": ["{workspaceRoot}/reports/{projectRoot}/unittests", "{workspaceRoot}/coverage/{projectRoot}"], "options": {"command": "uv run pytest tests/", "cwd": "{projectRoot}"}, "cache": true}, "install": {"executor": "@nxlv/python:install", "options": {"silent": false, "args": "", "verbose": false, "debug": false}}, "nx-release-publish": {"executor": "@nxlv/python:publish", "options": {}, "outputs": []}}, "tags": ["scope:python", "type:lib"], "release": {"version": {"generator": "@nxlv/python:release-version"}}}