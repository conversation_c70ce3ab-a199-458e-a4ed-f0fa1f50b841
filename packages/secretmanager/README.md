# cymulate.secretmanager

Secret management utilities for Cymulate.

## Requirements

- Python 3.12 or higher
- uv package manager

## Installation

```bash
# Basic installation
uv pip install cymulate.secretmanager

# With AWS support
uv pip install "cymulate.secretmanager[aws]"
```

## Usage

### Basic Usage

```python
from secretmanager import SecretManagerFactory

# Create a secret manager instance
secret_manager = SecretManagerFactory.create()

# Get secrets
secrets = secret_manager.get_secret()
print(secrets)
```

### AWS Secret Manager

```python
from secretmanager import SecretManagerFactory, SecretManagerType

# Create an AWS secret manager instance
secret_manager = SecretManagerFactory.create(SecretManagerType.AWS)

# Get secrets
secrets = secret_manager.get_secret()
print(secrets)
```

### File Secret Manager

```python
from secretmanager import SecretManagerFactory, SecretManagerType

# Create a file secret manager instance
secret_manager = SecretManagerFactory.create(SecretManagerType.FILE)

# Get secrets
secrets = secret_manager.get_secret()
print(secrets)
```

## Features

- Multiple secret manager providers:
  - AWS Secrets Manager
  - File-based secrets
- Environment-based configuration
- Type-safe interfaces

## Development

### Setup

```bash
# Clone the repository
git clone https://bitbucket.org/cymulate/pythonlibs.git
cd pythonlibs

# Install development dependencies
uv pip install invoke
invoke install-dev
```

### Testing

```bash
invoke test
```

## License

Proprietary - Cymulate 