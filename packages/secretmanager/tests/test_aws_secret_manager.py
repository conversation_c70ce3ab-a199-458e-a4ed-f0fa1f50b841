
import pytest

from secretmanager.aws_secret_manager.aws_secret_manager_service import (
    AwsSecretManagerService,
)
from secretmanager.models import SecretConfig


class DummyBoto3:
    def client(self, *a, **k):
        class Dummy:
            def get_secret_value(self, SecretId=None):
                return {"SecretString": '{"db": "sqlite://"}'}
            @property
            def _endpoint(self):
                class DummyEndpoint:
                    @property
                    def http_session(self):
                        class DummySession:
                            def close(self):
                                pass
                        return DummySession()
                return DummyEndpoint()
        return Dummy()

def test_aws_secret_manager_init(monkeypatch):
    monkeypatch.setenv("AWS_SECRET_ID", "arn:aws:secretsmanager:us-east-1:123456789012:secret:mysecret")
    monkeypatch.setattr("boto3.client", DummyBoto3().client)
    sm = AwsSecretManagerService()
    assert sm.secret_id.startswith("arn:")

def test_aws_secret_manager_get_secret(monkeypatch):
    monkeypatch.setenv("AWS_SECRET_ID", "arn:aws:secretsmanager:us-east-1:123456789012:secret:mysecret")
    monkeypatch.setattr("boto3.client", DummyBoto3().client)
    sm = AwsSecretManagerService()
    secret = sm.get_secret()
    assert isinstance(secret, SecretConfig)
    assert secret.db == "sqlite://"

def test_aws_secret_manager_no_secret_id(monkeypatch):
    monkeypatch.delenv("AWS_SECRET_ID", raising=False)
    with pytest.raises(ValueError):
        AwsSecretManagerService() 