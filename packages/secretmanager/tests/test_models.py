from secretmanager.models import SecretConfig, SQSConfig


def test_secret_config_parse_json():
    data = {"db": "sqlite://", "extra_field": 123}
    sc = SecretConfig.parse_json(data)
    assert sc.db == "sqlite://"
    assert "extra_field" in sc.extra_data

def test_sqs_config_extra_fields():
    sqs = SQSConfig(baseUrl="url", region="r", accessKeyId="id", secretAccessKey="sec", queueUrl="q", queueName="qn", extraX="x")
    assert "extraX" in sqs.extra_data

def test_secret_config_root_validator_sqs():
    data = {"sqs": {"baseUrl": "url"}}
    sc = SecretConfig.parse_json(data)
    assert sc.sqs["baseUrl"] == "url"

def test_secret_config_fields():
    sc = SecretConfig(db="sqlite://", dbPassword="pw")
    assert sc.db == "sqlite://"
    assert sc.dbPassword == "pw" 