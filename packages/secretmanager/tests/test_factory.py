import pytest

from secretmanager.factory import SecretManagerFactory
from secretmanager.interfaces import SecretManagerType


def test_factory_create_file(monkeypatch):
    monkeypatch.setenv("ENV", "local")
    sm = SecretManagerFactory.create()
    assert sm is not None
    assert hasattr(sm, "get_secret")

def test_factory_create_aws(monkeypatch):
    monkeypatch.setenv("ENV", "prod")
    monkeypatch.setenv("AWS_SECRET_ID", "arn:aws:secretsmanager:us-east-1:123456789012:secret:mysecret")
    sm = SecretManagerFactory.create(SecretManagerType.AWS)
    assert sm is not None
    assert hasattr(sm, "get_secret")

def test_factory_create_file_explicit():
    sm = SecretManagerFactory.create(SecretManagerType.FILE)
    assert sm is not None
    assert hasattr(sm, "get_secret")

def test_factory_create_invalid():
    with pytest.raises(ValueError):
        SecretManagerFactory.create("invalid") 