import json
import os
import tempfile

import pytest

from secretmanager.file_secret_manager.file_secret_manager_service import (
    FileSecretManagerService,
)
from secretmanager.models import SecretConfig


def test_get_env_file_path_default(monkeypatch):
    fsm = FileSecretManagerService()
    path = fsm.get_env_file_path("local")
    assert path.endswith("local.json")

def test_get_env_file_path_custom(monkeypatch):
    fsm = FileSecretManagerService()
    monkeypatch.setenv("ENV_CONFIG_PATH", "/tmp")
    path = fsm.get_env_file_path("test")
    assert path.startswith("/tmp")

def test_get_secret_success(monkeypatch):
    fsm = FileSecretManagerService()
    with tempfile.NamedTemporaryFile("w", suffix=".json", delete=False) as tf:
        json.dump({"db": "sqlite://"}, tf)
        tf.flush()
        monkeypatch.setenv("ENV", "testenv")
        monkeypatch.setenv("ENV_CONFIG_PATH", tf.name)
        fsm.env_dir = os.path.dirname(tf.name)
        config = fsm.get_secret()
        assert isinstance(config, SecretConfig)
        assert config.db == "sqlite://"
    os.remove(tf.name)

def test_get_secret_file_not_found(monkeypatch):
    fsm = FileSecretManagerService()
    monkeypatch.setenv("ENV", "notfound")
    monkeypatch.setenv("ENV_CONFIG_PATH", "/tmp/notfound.json")
    with pytest.raises(FileNotFoundError):
        fsm.get_secret()

def test_get_secret_invalid_json(monkeypatch):
    fsm = FileSecretManagerService()
    with tempfile.NamedTemporaryFile("w", suffix=".json", delete=False) as tf:
        tf.write("not a json")
        tf.flush()
        monkeypatch.setenv("ENV", "badjson")
        monkeypatch.setenv("ENV_CONFIG_PATH", tf.name)
        fsm.env_dir = os.path.dirname(tf.name)
        with pytest.raises(json.JSONDecodeError):
            fsm.get_secret()
    os.remove(tf.name) 