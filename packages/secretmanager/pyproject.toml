[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "secretmanager"
version = "0.1.11"
description = "Secret management utilities for Cymulate"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
]
license = { text = "Proprietary" }
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "License :: Other/Proprietary License",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = ["boto3>=1.28.0", "pydantic>=2.0.0", "typing-extensions>=4.7.0"]

[tool.ruff]
select = ["I", "F401"]

[project.optional-dependencies]
aws = ["boto3>=1.28.0"]

[project.urls]
"Homepage" = "https://bitbucket.org/cymulate/pythonlibs"
"Bug Tracker" = "https://bitbucket.org/cymulate/pythonlibs/jira"

[tool.setuptools]
package-dir = { "" = "src" }

[tool.setuptools.packages.find]
where = ["src"]
namespaces = true

[tool.setuptools.package-data]
"secretmanager" = ["envFile/*.json", "envFile/**/*.json"]

[tool.black]
line-length = 88
target-version = ["py312"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.uv.sources]
logger = { workspace = true }


[tool.pytest.ini_options]
addopts = "--cov=secretmanager --cov-report=term-missing"
