"""
AWS Secret Manager service implementation.
"""

import json
import os
from typing import Optional

import boto3
from botocore.config import Config
from botocore.exceptions import ClientError

from ..interfaces import SecretManagerService
from ..models import SecretConfig


class AwsSecretManagerService(SecretManagerService):
    """
    AWS Secret Manager service implementation.

    This class provides access to secrets stored in AWS Secrets Manager.
    """

    def __init__(self):
        """
        Initialize the AWS Secret Manager service.

        Raises:
            ValueError: If the AWS_SECRET_ID environment variable is not set
        """
        super().__init__()

        # Check if AWS_SECRET_ID is set
        self.secret_id = os.environ.get("AWS_SECRET_ID")
        if not self.secret_id:
            error_msg = f"AWS_SECRET_ID is not defined in env: {os.environ.get('ENV', 'unknown')}"
            self.logger.error(error_msg)
            raise ValueError("AWS_SECRET_ID is not defined")

        # Extract the region from the secret ID
        # Format: arn:aws:secretsmanager:region:account:secret:name
        parts = self.secret_id.split(":")
        if len(parts) >= 4:
            self.region = parts[3]
        else:
            self.region = os.environ.get("AWS_REGION", "us-east-1")

        # Initialize the AWS Secrets Manager client
        self.client = boto3.client(
            "secretsmanager",
            region_name=self.region,
            config=Config(retries={"max_attempts": 10}),
        )

    def get_secret(self) -> SecretConfig:
        """
        Get the secret configuration from AWS Secrets Manager.

        Returns:
            The secret configuration

        Raises:
            Exception: If there is an error retrieving the secret
        """
        # Return cached config if available
        if self._config is not None:
            return self._config

        try:
            # Get the secret value from AWS Secrets Manager
            response = self.client.get_secret_value(SecretId=self.secret_id)

            # Parse the secret string
            if "SecretString" in response:
                secret_data = json.loads(response["SecretString"])
                self._config = SecretConfig.parse_json(secret_data)
                return self._config
            else:
                raise ValueError("Secret value does not contain SecretString")
        except ClientError as e:
            error_msg = f"Failed to get secret from AWS Secret Manager: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise
        except Exception as e:
            error_msg = f"Unexpected error retrieving secret: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise

    def cleanup(self) -> None:
        """
        Clean up resources used by the AWS Secret Manager service.
        """
        if hasattr(self, "client") and self.client:
            # Close the boto3 client session
            self.client._endpoint.http_session.close()
            del self.client

        # Call the parent cleanup method
        super().cleanup()
