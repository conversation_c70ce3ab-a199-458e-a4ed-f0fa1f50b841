"""
Interfaces for the secret manager.
"""

import logging
from abc import ABC, abstractmethod
from enum import Enum, auto
from typing import Optional

from .models import SecretConfig


class SecretManagerType(Enum):
    """
    Enum for secret manager types.
    """

    AWS = auto()
    FILE = auto()


class SecretManagerService(ABC):
    """
    Abstract base class for secret manager services.

    This class defines the interface that all secret manager implementations must follow.
    """

    def __init__(self):
        """
        Initialize the secret manager service.
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self._config: Optional[SecretConfig] = None

    @property
    def secret(self) -> SecretConfig:
        """
        Get the current secret configuration.

        Returns:
            The current secret configuration

        Raises:
            ValueError: If the configuration has not been loaded
        """
        if self._config is None:
            raise ValueError("Secret configuration has not been loaded")
        return self._config

    @abstractmethod
    def get_secret(self) -> SecretConfig:
        """
        Get the secret configuration.

        This method must be implemented by all concrete secret manager services.

        Returns:
            The secret configuration
        """
        pass

    def cleanup(self) -> None:
        """
        Clean up resources used by the secret manager.

        This method should be called when the secret manager is no longer needed.
        """
        if self._config is not None:
            del self._config
            self._config = None
        self.logger.debug("Service was destroyed")
