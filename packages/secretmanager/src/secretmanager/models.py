"""
Data models for the secret manager.
"""

from typing import Any, Dict, Optional, List, Union
from pydantic import BaseModel, Field, root_validator


class MariaDBConfig(BaseModel):
    host: Optional[str] = Field(None, description="The hostname of the MariaDB server")
    user: Optional[str] = Field(None, description="Username for MariaDB authentication")
    password: Optional[str] = Field(None, description="Password for MariaDB authentication")
    database: Optional[str] = Field(None, description="Name of the MariaDB database")


class RedisConfig(BaseModel):
    default: Optional[str] = Field(None, description="Default Redis connection string")
    websocket: Optional[str] = Field(None, description="Redis connection string for websocket")
    bas: Optional[str] = Field(None, description="Redis connection string for BAS")
    findings: Optional[str] = Field(None, description="Redis connection string for findings")


class ElasticApiKey(BaseModel):
    id: Optional[str] = Field(None, description="Elasticsearch API key ID")
    name: Optional[str] = Field(None, description="Name of the Elasticsearch API key")
    api_key: Optional[str] = Field(None, description="Elasticsearch API key value")


class ElasticConfig(BaseModel):
    active: Optional[bool] = Field(None, description="Flag indicating if Elasticsearch is active")
    elasticsearchUrl: Optional[str] = Field(None, description="URL of the Elasticsearch instance")
    apmUrl: Optional[str] = Field(None, description="URL for Application Performance Monitoring")
    apmToken: Optional[str] = Field(None, description="Token for APM authentication")
    apiKey: Optional[ElasticApiKey] = Field(None, description="Elasticsearch API key configuration")
    cloudId: Optional[str] = Field(None, description="Elasticsearch Cloud ID")


class CloudFrontBucket(BaseModel):
    name: Optional[str] = Field(None, description="Name of the CloudFront bucket")
    subDomain: Optional[str] = Field(None, description="Subdomain associated with the bucket")
    isPublic: Optional[bool] = Field(False, description="Flag indicating if the bucket is public")


class CloudFrontConfig(BaseModel):
    url: Optional[str] = Field(None, description="CloudFront distribution URL")
    keyPairId: Optional[str] = Field(None, description="CloudFront key pair ID")
    privateKey: Optional[str] = Field(None, description="CloudFront private key")
    publicDomain: Optional[str] = Field(None, description="Public domain for CloudFront")
    domain: Optional[str] = Field(None, description="Domain name for CloudFront")
    buckets: Optional[List[CloudFrontBucket]] = Field(None, description="List of CloudFront buckets")
    mainDomain: Optional[Dict[str, Any]] = Field(None, description="Main domain configuration")
    decryptionKey: Optional[str] = Field(None, description="Key used for decryption")


class DatabaseConfig(BaseModel):
    db: Optional[str] = Field(None, description="Database connection string")
    tenantId: Optional[str] = Field(None, description="ID of the tenant")
    tenantName: Optional[str] = Field(None, description="Name of the tenant")
    env: Optional[str] = Field(None, description="Environment identifier")


class MonitoringConfig(BaseModel):
    datadog: Optional[Dict[str, str]] = Field(None, description="Datadog monitoring configuration")


class S3Config(BaseModel):
    region: Optional[str] = Field(None, description="AWS S3 region")
    accessKeyId: Optional[str] = Field(None, description="AWS S3 access key ID")
    secretAccessKey: Optional[str] = Field(None, description="AWS S3 secret access key")
    accessKeyIdPlain: Optional[str] = Field(None, description="Plain text AWS S3 access key ID")
    secretAccessKeyPlain: Optional[str] = Field(None, description="Plain text AWS S3 secret access key")


class KafkaConfig(BaseModel):
    broker: Optional[List[str]] = Field(None, description="List of Kafka broker addresses")
    sasl: Optional[Dict[str, str]] = Field(None, description="Kafka SASL configuration")


class UnleashConfig(BaseModel):
    url: Optional[str] = Field(None, description="Unleash server URL")
    apiToken: Optional[str] = Field(None, description="Unleash API token")


class JiraConfig(BaseModel):
    protocol: Optional[str] = Field(None, description="Jira connection protocol")
    host: Optional[str] = Field(None, description="Jira host address")
    username: Optional[str] = Field(None, description="Jira username")
    password: Optional[str] = Field(None, description="Jira password")
    apiVersion: Optional[str] = Field(None, description="Jira API version")
    strictSSL: Optional[bool] = Field(None, description="Flag for strict SSL verification")


class CybiSQSConfig(BaseModel):
    calculateAssetRiskScoreQueue: Optional[str] = Field(None, description="Queue for asset risk score calculation")
    calculateBusinessContextRiskScoreFunction: Optional[str] = Field(None, description="Function for business context risk score")
    calculateOrganizationRiskScoreFunction: Optional[str] = Field(None, description="Function for organization risk score")
    countTaskQueue: Optional[str] = Field(None, description="Queue for task counting")
    createEntitiesQueue: Optional[str] = Field(None, description="Queue for entity creation")
    createTaskQueue: Optional[str] = Field(None, description="Queue for task creation")
    enrichTaskQueue: Optional[str] = Field(None, description="Queue for task enrichment")


class CybiConfig(BaseModel):
    mongoUri: Optional[str] = Field(None, description="MongoDB connection URI")
    postgresUri: Optional[str] = Field(None, description="PostgreSQL connection URI")
    postgresConnection: Optional[str] = Field(None, description="PostgreSQL connection string")
    tokenizerSecret: Optional[str] = Field(None, description="Secret for tokenization")
    bucketAccessKey: Optional[str] = Field(None, description="Access key for bucket")
    bucketSecretAccessKey: Optional[str] = Field(None, description="Secret access key for bucket")
    sendgridApiKey: Optional[str] = Field(None, description="SendGrid API key")
    twilioAuthToken: Optional[str] = Field(None, description="Twilio authentication token")
    twilioAccountSid: Optional[str] = Field(None, description="Twilio account SID")
    secretManager: Optional[Dict[str, Any]] = Field(None, description="Secret manager configuration")
    sqs: Optional[CybiSQSConfig] = Field(None, description="SQS configuration for Cybi")


class AIModel(BaseModel):
    """Configuration for AI models."""
    type: Optional[str] = Field(None, description="Type of AI model (azure/openai)")
    name: Optional[str] = Field(None, description="Name of the model configuration")
    deployment_name: Optional[str] = Field(None, description="Azure deployment name")
    azure_endpoint: Optional[str] = Field(None, description="Azure endpoint URL")
    api_version: Optional[str] = Field(None, description="API version")
    model_name: Optional[str] = Field(None, description="Name of the model")
    api_key: Optional[str] = Field(None, description="API key for the model")
    active: Optional[bool] = Field(None, description="Whether the model is active")
    default: Optional[bool] = Field(None, description="Whether this is the default model")
    unsupported_fields: Optional[List[str]] = Field([], description="List of unsupported fields")
    reasoning_effort: Optional[str] = Field(None, description="Reasoning effort level")


class ChatbotAIConfig(BaseModel):
    """Configuration for Chatbot AI."""
    ELASTIC_CLOUD_ID: Optional[str] = Field(None, description="Elastic Cloud ID")
    ELASTIC_API_KEY: Optional[str] = Field(None, description="Elastic API key")
    ES_INDEX: Optional[str] = Field(None, description="Elasticsearch index name")
    ES_INDEX_CHAT_HISTORY: Optional[str] = Field(None, description="Elasticsearch index for chat history")
    LLM_TYPE: Optional[str] = Field(None, description="Type of LLM")
    AI_API_VERSION: Optional[str] = Field(None, description="AI API version")
    AI_ENDPOINT: Optional[str] = Field(None, description="AI endpoint URL")
    AI_API_KEY: Optional[str] = Field(None, description="AI API key")
    AI_DEPLOYMENT_NAME: Optional[str] = Field(None, description="AI deployment name")
    AI_MODEL_NAME: Optional[str] = Field(None, description="AI model name")
    SLACK_BOT_TOKEN: Optional[str] = Field(None, description="Slack bot token")
    SLACK_APP_TOKEN: Optional[str] = Field(None, description="Slack app token")
    DOCUMENT360_PROJECT_ID: Optional[str] = Field(None, description="Document360 project ID")
    DOCUMENT360_API_TOKEN: Optional[str] = Field(None, description="Document360 API token")
    INDEX_CHAT_HISTORY_METRICS: Optional[str] = Field(None, description="Index for chat history metrics")
    ELSER_MODEL: Optional[str] = Field(None, description="ELSER model name")
    WEBSOCKET_URL: Optional[str] = Field(None, description="WebSocket URL")
    JWT_SECRET: Optional[str] = Field(None, description="JWT secret key")
    SASHA_OPENAI_API_KEY: Optional[str] = Field(None, description="Sasha OpenAI API key")
    SASHA_CY_API_TOKEN: Optional[str] = Field(None, description="Sasha Cymulate API token")
    SASHA_CY_API_URL: Optional[str] = Field(None, description="Sasha Cymulate API URL")
    AI_DEPLOYMENT_NAME_EMBEDDINGS: Optional[str] = Field(None, description="AI deployment name for embeddings")
    ZENDESK_EMAIL: Optional[str] = Field(None, description="Zendesk email")
    ZENDESK_TOKEN: Optional[str] = Field(None, description="Zendesk token")
    ZENDESK_SUBDOMAIN: Optional[str] = Field(None, description="Zendesk subdomain")
    TAVILY_APY_KEY: Optional[str] = Field(None, description="Tavily API key")
    models: Optional[List[AIModel]] = Field(None, description="List of AI model configurations")


class SQSConfig(BaseModel):
    """Configuration for AWS SQS."""
    baseUrl: Optional[str] = Field(None, description="Base URL for SQS")
    region: Optional[str] = Field(None, description="AWS region for SQS")
    accessKeyId: Optional[str] = Field(None, description="AWS access key ID for SQS")
    secretAccessKey: Optional[str] = Field(None, description="AWS secret access key for SQS")
    queueUrl: Optional[str] = Field(None, description="SQS queue URL")
    queueName: Optional[str] = Field(None, description="SQS queue name")
    extra_data: Dict[str, Any] = Field(default_factory=dict, description="Additional SQS configuration")

    class Config:
        """Pydantic model configuration."""
        extra = "allow"  # Allow extra fields

    @root_validator(pre=True)
    def handle_extra_fields(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """Handle any extra fields by moving them to extra_data."""
        if not isinstance(values, dict):
            return values

        known_fields = set(cls.__fields__.keys())
        extra_fields = {k: v for k, v in values.items() if k not in known_fields}
        
        for field in extra_fields:
            values.pop(field, None)
        
        if extra_fields:
            values['extra_data'] = {**extra_fields, **(values.get('extra_data', {}))}
        
        return values


class SecretConfig(BaseModel):
    """
    Configuration model for secrets.

    This class represents the structure of secrets retrieved from a secret manager.
    All unknown keys will be stored in extra_data.
    """
    dbs: Optional[List[DatabaseConfig]] = Field(None, description="List of database configurations")
    replicas: Optional[List[Dict[str, str]]] = Field(None, description="List of replica configurations")
    db: Optional[str] = Field(None, description="Primary database connection string")
    dbPassword: Optional[str] = Field(None, description="Primary database password")
    redis: Optional[RedisConfig] = Field(None, description="Redis configuration")
    mariadb: Optional[Dict[str, MariaDBConfig]] = Field(None, description="MariaDB configurations")
    monitoring: Optional[MonitoringConfig] = Field(None, description="Monitoring configuration")
    elastic: Optional[ElasticConfig] = Field(None, description="Elasticsearch configuration")
    s3: Optional[S3Config] = Field(None, description="AWS S3 configuration")
    cloudFront: Optional[CloudFrontConfig] = Field(None, description="CloudFront configuration")
    s3AsmLogs: Optional[Dict[str, str]] = Field(None, description="S3 ASM logs configuration")
    rabbitAsmEndpoint: Optional[Dict[str, str]] = Field(None, description="RabbitMQ ASM endpoint configuration")
    rabbitAsmApiPort: Optional[str] = Field(None, description="RabbitMQ ASM API port")
    redisAsmEndpoint: Optional[str] = Field(None, description="Redis ASM endpoint")
    redisAsmPassword: Optional[str] = Field(None, description="Redis ASM password")
    postalserver: Optional[Dict[str, Dict[str, str]]] = Field(None, description="Postal server configuration")
    blob: Optional[Dict[str, str]] = Field(None, description="Blob storage configuration")
    kafka: Optional[KafkaConfig] = Field(None, description="Kafka configuration")
    unleash: Optional[UnleashConfig] = Field(None, description="Unleash feature flag configuration")
    jira: Optional[JiraConfig] = Field(None, description="Jira configuration")
    dynamicModules: Optional[Dict[str, Dict[str, str]]] = Field(None, description="Dynamic modules configuration")
    rundeck: Optional[Dict[str, str]] = Field(None, description="Rundeck configuration")
    sqs: Optional[Union[Dict[str, Any], SQSConfig]] = Field(None, description="SQS configuration")
    cybi: Optional[CybiConfig] = Field(None, description="Cybi specific configuration")
    chatbot_ai: Optional[ChatbotAIConfig] = Field(None, description="Chatbot AI configuration")
    n3: Optional[Dict[str, Union[ElasticConfig, CloudFrontConfig]]] = Field(None, description="N3 specific configuration")
    extra_data: Dict[str, Any] = Field(default_factory=dict, description="Additional configuration data")

    class Config:
        """Pydantic model configuration."""
        extra = "allow"  # Allow extra fields

    @root_validator(pre=True)
    def handle_extra_fields(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """
        Pre-process the input data to move unknown fields to extra_data.

        Args:
            values: The input dictionary data

        Returns:
            Processed dictionary with unknown fields moved to extra_data
        """
        if not isinstance(values, dict):
            return values

        # Handle SQS configuration
        if 'sqs' in values and not isinstance(values['sqs'], dict):
            try:
                values['sqs'] = SQSConfig(**values['sqs']).__dict__
            except Exception:
                # If conversion fails, store it in extra_data
                values['extra_data'] = values.get('extra_data', {})
                values['extra_data']['sqs'] = values.pop('sqs')

        # Handle chatbot-ai configuration
        if 'chatbot-ai' in values :
            values['chatbot_ai'] = values["chatbot-ai"]
            values.pop("chatbot-ai")
        
        
        known_fields = set(cls.__fields__.keys())
        extra_fields = {k: v for k, v in values.items() if k not in known_fields}

    
        # Remove extra fields from main data
        for field in extra_fields:
            values.pop(field, None)
        
        # Update or create extra_data with the extra fields
        if extra_fields:
            values['extra_data'] = {**extra_fields, **(values.get('extra_data', {}))}
        
        return values

    @classmethod
    def parse_json(cls, json_data: Dict[str, Any]) -> "SecretConfig":
        """
        Parse JSON data into a SecretConfig object.

        Args:
            json_data: The JSON data to parse

        Returns:
            A SecretConfig object
        """
        return cls(**json_data)
