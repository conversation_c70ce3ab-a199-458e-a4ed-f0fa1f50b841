{"dbs": [], "replicas": [], "db": "mongodb+srv://kevynks:<EMAIL>/cymulate?retryWrites=true&w=majority", "dbPassword": "a1976ce0927b185314d85b2fc48cf94b", "elastic": {"active": true, "elasticsearchUrl": "https://cymulate-stg.es.us-east-1.aws.found.io:9243", "apmUrl": "https://cymulate-stg.apm.us-east-1.aws.found.io", "apmToken": "lCgefJySLckomVHHjK", "apiKey": {"id": "qhqFXZMBRmILS2q8JCwx", "name": "cy-application", "api_key": "sQ6mqkARTfSnYNa3I-OTEA"}, "cloudId": "CymulateSTG:dXMtZWFzdC0xLmF3cy5mb3VuZC5pbyQxZjgyNGViYWQwNzc0OGQ2Yjk1YzI5MDkwN2FhOGYwMCRlNWExMjg2M2M5NmM0YTkzYjg4ZWI0ODMyMDFmOGY5MQ=="}, "s3": {"region": "eu-west-1", "accessKeyId": "340b8633af4bae9294212f637128b62da4868f8d20a4a256f09418dc722f99fd1af48778", "secretAccessKey": "ca58e08d65771741c11c90c3580bc4e1feae79add90a1a52f95671de98f33b583dd07244a9bcf243def3ac3e2ad3c53ffa9f2bd33c57d29b"}, "sqs": {"baseUrl": "https://sqs.us-east-1.amazonaws.com/118330362824/", "cybi": {"prefix": "dev-cybi-"}}, "cloudFront": {"region": "eu-west-1", "keyPairId": "K3MWCCZZST1931", "domain": "app.cymulate.com", "buckets": [{"name": "cym-reports", "subDomain": "cym-reports-csv"}, {"name": "cym-agent-actions", "subDomain": "agent-actions"}, {"name": "cy-sever-agent-actions", "subDomain": "cy-sever-agent-actions"}, {"name": "dlp-files", "subDomain": "dlp-resources"}, {"name": "cym-files-download", "subDomain": "edr-resources"}, {"name": "cy-agent", "subDomain": "agents"}], "privateKey": "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"}, "s3AsmLogs": {"accessKeyId": "", "secretAccessKey": ""}, "rabbitAsmEndpoint": {"host": "rabbitmq-headless", "port": "5672", "connection_attempts": "3", "heartbeat": "0", "username": "user", "password": "", "use_ssl": "False"}, "rabbitAsmApiPort": "15672", "redisAsmEndpoint": "redis://@asm-redis-replication-group-stg-001.asm-redis-replication-group-stg.bo8i56.use1.cache.amazonaws.com:6379", "redisAsmPassword": "", "blob": {"account": "usage2pbi", "accountKey": "64522976ffcb0007ec8713e9b481ad26ee6513eb08185380dd1d0fcbc5e0f5e0b3657e65e51a704ac5543efb3f6ea830387ebc1b5aef3c44be8bef06bbec5b49c65a4acc363958d52f0248708668e30c08191486f0c0aa2af03f88c8d7a03fc75049a5a71be97800"}, "kafka": {"broker": ["localhost:9092"]}, "unleash": {"url": "http://unleash-office.cymulatedev.com/ms/unleash", "apiToken": "*:development.9e563c41909ec0e6dd42694daa3173e2c43295338be01e4c27ae8d8e"}, "redis": {"default": "redis://localhost:6379", "bas": "redis://localhost:6379", "websocket": "redis://localhost:6379", "findings": "redis://localhost:6379"}, "rundeck": {"token": "87uD9KAdKduMzTlllENlMlPnNlWWj25u"}, "cybi": {"mongoUri": "mongodb://localhost:27017/cymulate", "postgresUri": "postgresql://cymulate:password@127.0.0.1:5432/cymulate"}, "dynamicModules": {"url": {"jenkinsGetCrumbURL": "https://cym-jenkins.cymulate.com/crumbIssuer/api/json", "jenkinsCiCdBuilderURL": "https://cym-jenkins.cymulate.com/job/bitbucket-repos/job/asm-engine/job/asm-engine-dynamic-modules/buildWithParameters?token=06DV9JzmAAfVvUOi7XsVe7JIuByVDaWx44MkozBrX3yrVfhhZrQelrZ4bMiazlPJ"}, "user": {"jenkinsUsername": "insert-user-here", "jenkinsPassword": "insert-password-here"}}, "chatbot-ai": {"ELASTIC_CLOUD_ID": "CymulateSTG:dXMtZWFzdC0xLmF3cy5mb3VuZC5pbzo0NDMkMWY4MjRlYmFkMDc3NDhkNmI5NWMyOTA5MDdhYThmMDAkZTVhMTI4NjNjOTZjNGE5M2I4OGViNDgzMjAxZjhmOTE=", "ELASTIC_API_KEY": "NlNQblJKQUJWbTBxR2lxUTJUVDE6YzBrOVhmYmlTRU95WWFmS2dWLVRTQQ==", "ES_INDEX": "cy-ai-docs", "ES_INDEX_CHAT_HISTORY": "cy-ai-docs-chat-history", "LLM_TYPE": "azure", "AI_API_VERSION": "2024-06-01", "AI_ENDPOINT": "https://eu.prompt.security", "AI_API_KEY": "********************************", "AI_DEPLOYMENT_NAME": "gpt-4o-mini-nonprod", "AI_MODEL_NAME": "gpt-4o-mini", "SLACK_BOT_TOKEN": "********************************************************", "SLACK_APP_TOKEN": "xapp-1-A0795H83146-7318122924035-26422f893209c5cf523be7e6c80afa248cc422ed7b3b5da908ecc676a3a9946a", "DOCUMENT360_PROJECT_ID": "9aa5ea7a-3151-42ca-b9ca-a716ac07dbb6", "DOCUMENT360_API_TOKEN": "1v9fUFV3l+4XZ+laMwCP8qox7UH2xHW0pCHyl53EjOMTMID8zw+lPdUNMl95VRvodNNOoRr6p4msKi2/o3F/7zTxb1hBBJVOn12/1YHKkUr9lnDlGnzGHDhcNFFu8PYW5ZoOKYl4DTCwRU2ulyvozA==", "INDEX_CHAT_HISTORY_METRICS": "cy-ai-docs-chat-history-metrics", "ELSER_MODEL": ".elser_model_2_linux-x86_64", "WEBSOCKET_URL": "ws://localhost:5555", "JWT_SECRET": "-----B<PERSON>IN RSA PRIVATE KEY-----\\nMIIEogIBAAKCAQEAnzyis1ZjfNB0bBgKFMSvvkTtwlvBsaJq7S5wA+kzeVOVpVWw\\nkWdVha4s38XM/pa/yr47av7+z3VTmvDRyAHcaT92whREFpLv9cj5lTeJSibyr/Mr\\nm/YtjCZVWgaOYIhwrXwKLqPr/11inWsAkfIytvHWTxZYEcXLgAXFuUuaS3uF9gEi\\nNQwzGTU1v0FqkqTBr4B8nW3HCN47XUu0t8Y0e+lf4s4OxQawWD79J9/5d3Ry0vbV\\n3Am1FtGJiJvOwRsIfVChDpYStTcHTCMqtvWbV6L11BWkpzGXSW4Hv43qa+GSYOD2\\nQU68Mb59oSk2OB+BtOLpJofmbGEGgvmwyCI9MwIDAQABAoIBACiARq2wkltjtcjs\\nkFvZ7w1JAORHbEufEO1Eu27zOIlqbgyAcAl7q+/1bip4Z/x1IVES84/yTaM8p0go\\namMhvgry/mS8vNi1BN2SAZEnb/7xSxbflb70bX9RHLJqKnp5GZe2jexw+wyXlwaM\\n+bclUCrh9e1ltH7IvUrRrQnFJfh+is1fRon9Co9Li0GwoN0x0byrrngU8Ak3Y6D9\\nD8GjQA4Elm94ST3izJv8iCOLSDBmzsPsXfcCUZfmTfZ5DbUDMbMxRnSo3nQeoKGC\\n0Lj9FkWcfmLcpGlSXTO+Ww1L7EGq+PT3NtRae1FZPwjddQ1/4V905kyQFLamAA5Y\\nlSpE2wkCgYEAy1OPLQcZt4NQnQzPz2SBJqQN2P5u3vXl+zNVKP8w4eBv0vWuJJF+\\nhkGNnSxXQrTkvDOIUddSKOzHHgSg4nY6K02ecyT0PPm/UZvtRpWrnBjcEVtHEJNp\\nbU9pLD5iZ0J9sbzPU/LxPmuAP2Bs8JmTn6aFRspFrP7W0s1Nmk2jsm0CgYEAyH0X\\n+jpoqxj4efZfkUrg5GbSEhf+dZglf0tTOA5bVg8IYwtmNk/pniLG/zI7c+GlTc9B\\nBwfMr59EzBq/eFMI7+LgXaVUsM/sS4Ry+yeK6SJx/otIMWtDfqxsLD8CPMCRvecC\\n2Pip4uSgrl0MOebl9XKp57GoaUWRWRHqwV4Y6h8CgYAZhI4mh4qZtnhKjY4TKDjx\\nQYufXSdLAi9v3FxmvchDwOgn4L+PRVdMwDNms2bsL0m5uPn104EzM6w1vzz1zwKz\\n5pTpPI0OjgWN13Tq8+PKvm/4Ga2MjgOgPWQkslulO/oMcXbPwWC3hcRdr9tcQtn9\\nImf9n2spL/6EDFId+Hp/7QKBgAqlWdiXsWckdE1Fn91/NGHsc8syKvjjk1onDcw0\\nNvVi5vcba9oGdElJX3e9mxqUKMrw7msJJv1MX8LWyMQC5L6YNYHDfbPF1q5L4i8j\\n8mRex97UVokJQRRA452V2vCO6S5ETgpnad36de3MUxHgCOX3qL382Qx9/THVmbma\\n3YfRAoGAUxL/Eu5yvMK8SAt/dJK6FedngcM3JEFNplmtLYVLWhkIlNRGDwkg3I5K\\ny18Ae9n7dHVueyslrb6weq7dTkYDi3iOYRW8HRkIQh06wEdbxt0shTzAJvvCQfrB\\njg/3747WSsf/zBTcHihTRBdAv6OmdhV4/dD5YBfLAkLrd+mX7iE=\\n-----END RSA PRIVATE KEY-----", "SASHA_OPENAI_API_KEY": "********************************************************************************************************************************************************************", "SASHA_CY_API_TOKEN": "5ffda1a29b865be4274b7a3507aa284f", "SASHA_CY_API_URL": "http://api.cymulate", "AI_DEPLOYMENT_NAME_EMBEDDINGS": "embeddings-attack-planner", "ZENDESK_EMAIL": "<EMAIL>", "ZENDESK_TOKEN": "RTEITWVhP2afj26YSY5lo6qEtABPZrO5fICDnilP", "ZENDESK_SUBDOMAIN": "cymulate", "TAVILY_APY_KEY": "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", "models": [{"type": "azure", "name": "azure_model_1", "deployment_name": "gpt-4o-mini-nonprod", "azure_endpoint": "https://cy-chatbot-nonprod.openai.azure.com", "api_version": "2024-12-01-preview", "model_name": "azure-gpt-4o-mini", "api_key": "********************************", "active": true}, {"type": "azure", "name": "azure_o3_mini", "deployment_name": "o3-mini", "azure_endpoint": "https://roys-m82y0gg9-eastus2.openai.azure.com", "api_version": "2024-12-01-preview", "model_name": "o3-mini", "api_key": "5Etcl5VIkyaQR2PJ0q8jlLmSelZ1574hzDZRwGNYNVaC1uX9qDwIJQQJ99BCACHYHv6XJ3w3AAAAACOGx7be", "unsupported_fields": ["max_tokens", "top_p", "temperature"], "reasoning_effort": "high", "active": true}, {"type": "azure", "name": "azure_model_2", "deployment_name": "gpt-4o-nonprod", "azure_endpoint": "https://roys-m82y0gg9-eastus2.openai.azure.com", "api_version": "2024-12-01-preview", "model_name": "gpt-4o", "api_key": "5Etcl5VIkyaQR2PJ0q8jlLmSelZ1574hzDZRwGNYNVaC1uX9qDwIJQQJ99BCACHYHv6XJ3w3AAAAACOGx7be", "active": true, "default": true}, {"type": "openai", "name": "openai_model_4.1", "model_name": "gpt-4.1", "api_key": "********************************************************************************************************************************************************************", "active": true, "default": false}, {"type": "openai", "name": "openai_model_1", "model_name": "chatgpt-4o-latest", "api_key": "********************************************************************************************************************************************************************", "active": true, "default": false}, {"type": "openai", "name": "openai_embedding_1", "model_name": "text-embedding-3-small", "api_key": "********************************************************************************************************************************************************************", "active": true, "default": false}, {"type": "openai", "name": "openai_embedding_ada_2", "model_name": "text-embedding-ada-002", "api_key": "********************************************************************************************************************************************************************", "active": true, "default": false}, {"type": "bedrock", "name": "bedrock_claude_3_haiku", "model_name": "us.anthropic.claude-3-haiku-20240307-v1:0", "region": "us-east-1", "active": true, "default": false}, {"type": "bedrock", "name": "bedrock_claude_3_sonnet", "model_name": "us.anthropic.claude-3-sonnet-20240229-v1:0", "region": "us-east-1", "active": true, "default": false}, {"type": "bedrock", "name": "bedrock_claude_sonnet_4", "model_name": "us.anthropic.claude-sonnet-4-20250514-v1:0", "region": "us-east-1", "active": true, "default": false}, {"type": "bedrock", "name": "bedrock_claude_opus_4", "model_name": "us.anthropic.claude-opus-4-20250514-v1:0", "region": "us-east-1", "active": true, "default": false}, {"type": "azure", "name": "azure_text_embedding_3_small", "deployment_name": "text-embedding-3-small", "azure_endpoint": "https://cy-chatbot-nonprod.openai.azure.com", "api_version": "2024-06-01", "model_name": "text-embedding-3-small", "api_key": "********************************", "active": true, "default": false}]}}