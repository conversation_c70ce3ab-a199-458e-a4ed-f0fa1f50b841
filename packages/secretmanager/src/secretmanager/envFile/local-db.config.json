{"hopperUIClientAddress": "", "hopperUIAddress": "", "generateReportAddress": "", "reconParserAddress": "", "reconParserLegacyAddress": "", "websocketInternalServerAddress": "ws://localhost:5555", "fileServerConfig": {"legacyMode": false, "oldUrl": ""}, "bucketNameForFileServerUploads": "", "cymulatePostIt": {"sites": [{"active": true, "apiKey": "", "author": "", "domain": ""}, {"active": false, "apiKey": "", "author": "", "domain": ""}]}, "policyReconstructThreshold": -1, "apiTokenValidityTimeInDays": 90, "appDomain": "", "apiCertServerAddress": "", "agentServerAddress": "", "agentLogsAddress": "", "cyAgentServerAddress": "", "dataKey": "", "webtoken": "", "logrocket": {"id": ""}, "elastic": {"apiVersion": "", "rumHost": "", "elasticHost": "", "apmHost": "", "apmSecretToken": ""}, "wsAdress": "", "cnc_url": "", "cnc_email": "", "cnc_connection_token": "", "blobUsage": {"apiKey": "", "connectionString": ""}, "blobAccountKey": "", "awsAccessKeyId": "", "awsSecretAccessKey": "", "redisServerAddress": "", "redisServerPublicAddress_remove": "", "redisServerPassword": "", "storageType": "s3", "jwtSecret": "", "jwtPrivate": "", "jwtPublic": "", "origin": "", "domain": "", "filesServerAddress": "", "findingsServerURL": "", "mailModuleValidationTimeOut": 1, "supportEmail": "", "adminEmail": "", "devEmails": "", "salesEmail": "", "phishingServer": {"host": "", "username": "", "privateKey": ""}, "notificonsServer": "", "dnsServer": "", "reportsServerAddress": "", "salesForce": {"tokenUrl": "", "api": "", "connection": {"grant_type": "", "client_id": "", "client_secret": "", "username": "", "password": ""}, "modules": {"mail": "", "browsing": "", "hopper": "", "phishing": "", "edr": "", "dlp": "", "waf": "", "immediateThreats": ""}}, "adminUrl": "", "attackServerAddress": "", "findingsURL": "", "reconIntervalServerAddress": "", "attackServerAddressPublic_remove": "", "insideSalesEmail": "", "paypalUrl": "", "paypalAuth": {"user": "", "pass": ""}, "registrationMail": "", "russianAdmin": "", "ssl_admin": {"pfx": "", "passphrase": ""}, "ssl_old": {"pfx": "", "passphrase": ""}, "ssl": {"pfx": "", "passphrase": ""}, "ssl_api": {"pfx": "", "passphrase": ""}, "ssl_apic": {"pfx": "", "passphrase": ""}, "templateUrl": "", "webAssessmentServer": "", "mailAccounts": {"donotreply": {"user": "", "password": ""}, "info": {"user": "", "password": ""}, "donotreply2": {"user": "", "password": ""}}, "SGapiKey": "", "SGapiKeyPhishing": "", "SGapiKeyOtherPool": "", "sg": "", "hotFilesApproveMail": "", "phishingIp": "", "apiUrl": "", "smtp": "", "dispatcherServerCrt": "", "dispatcherServerKey": "", "iv_php": "", "AWS_ACCESS_KEY_ID_php": "", "AWS_SECRET_ACCESS_KEY_php": "", "BLOB_ACCOUNT_KEY_php": "", "BLOB_ACCOUNT_NAME_php": "", "dlp-cred": {"DropboxAccessToken": "", "GoogleDriveTokenFIlePassword": "", "GoogleDriveUserName": "", "GoogleDriveTokenFileName": "", "SFTPPassword": "", "SFTPUserName": "", "SlackPostMessageURL": "", "SlackUploadFileToken": "", "ChannelName": "", "ChannelCode": "", "PortScanningServer": ""}, "unlayerId": "", "cymulateCustomerSuccess": "", "cyPackerConfig": {"ingressIp": "", "reconNamespace": "", "openvasPort": "", "cyPackerPort": "", "spiderfootPort": "", "cyReconPort": "", "cyReconApiKeys": {"hibp": "", "email_verify": "", "hunter": ""}, "spiderFootApiKeys": ["sfp_abuseipdb:api_key=suarez", "sfp_alienvault:api_key=****************************************************************", "sfp_apility:api_key=", "sfp_binaryedge:binaryedge_api_key=", "sfp_bingsearch:api_key=", "sfp_bingsharedip:api_key=", "sfp_botscout:api_key=", "sfp_builtwith:api_key=", "sfp_censys:censys_api_key_secret=", "sfp_censys:censys_api_key_uid=", "sfp_circllu:api_key_login=cymulate.com", "sfp_circllu:api_key_password=TxxyxJdCtWJFRkhH6M/b4urKxmSx8Ii+DXqK4VImfhw", "sfp_citadel:api_key=", "sfp_clearbit:api_key=", "sfp_fraudguard:fraudguard_api_key_account=", "sfp_fraudguard:fraudguard_api_key_password=", "sfp_fullcontact:api_key=", "sfp_googlemaps:api_key=AIzaSyBv9InE_e9Vf9Ab_Btqo8s98AQFfvr9jLQ", "sfp_googlesearch:api_key=AIzaSyBv9InE_e9Vf9Ab_Btqo8s98AQFfvr9jLQ", "sfp_greynoise:api_key=", "sfp_haveibeenpwned:api_key=1333c116c4674c4798393179f4dc2e1d", "sfp_honeypot:api_key=lqbbhebojaru", "sfp_hunter:api_key=", "sfp_iknowwhatyoudownload:api_key=", "sfp_intelx:api_key=9df61df0-84f7-4dc7-b34c-8ccfb8646ace", "sfp_ipinfo:api_key=", "sfp_ipstack:api_key=", "sfp_malwarepatrol:api_key=", "sfp_metadefender:api_key=", "sfp_neutrinoapi:api_key=", "sfp_numverify:api_key=", "sfp_onioncity:api_key=", "sfp_opencorporates:api_key=", "sfp_pastebin:api_key=AIzaSyA5lRnwhzCAayAlv7eUSf1PhNSFN76e9a4", "sfp_pulsedive:api_key=", "sfp_riskiq:api_key_login=<EMAIL>", "sfp_riskiq:api_key_password=5e565d86344d173ec7ba137159b327338b12014efba2a3c08c3fe4abea59548b", "sfp_securitytrails:api_key=", "sfp_shodan:api_key=********************************", "sfp_socialprofiles:bing_api_key=", "sfp_socialprofiles:google_api_key=", "sfp_spyonweb:api_key=", "sfp_viewdns:api_key=37ce74b2ef92b12a6e99083a0aac88d8adf65ac8", "sfp_virustotal:api_key=", "sfp_whatcms:api_key=", "sfp_whoisology:api_key=", "sfp_whoxy:api_key=", "sfp_wigle:api_key_encoded=AID931c279a1dde6eb33e10445384077ef6/0faa39f1c9bb8e63675b0bd22ba81468", "sfp_xforce:xforce_api_key=b9cc41fb-837c-4e4b-a5f0-e7c776e250ee", "sfp_xforce:xforce_api_key_password=bf0869b5-fa97-46e0-9df0-2bde796d8531"], "config": {"clusters": [{"name": "", "server": "", "caData": ""}], "users": [{"name": "", "password": "", "certData": "", "keyData": ""}], "contexts": [{"name": "", "user": "", "cluster": ""}], "currentContext": ""}}, "reconConfig": {"reconNamespace": ""}, "mailTransporters": {"sgTransporter": {"ignore": false, "displayName": "", "sgPlugin": true, "data": {"apiKey": ""}}, "sgOtherPoolTransporter": {"ignore": true, "displayName": "", "sgPlugin": true, "data": {"apiKey": ""}}, "sgMailGatewayTransporter": {"ignore": false, "displayName": "", "fromMailAddress": "", "sgPlugin": true, "data": {"apiKey": ""}}, "sgPhishingTransporter": {"ignore": true, "displayName": "", "sgPlugin": true, "data": {"apiKey": ""}}, "cymTransporter": {"ignore": false, "displayName": "", "fromMailAddress": "", "data": {"host": "", "secureConnection": false, "port": 25, "tls": {"rejectUnauthorized": false, "ciphers": ""}, "auth": {"user": "", "pass": ""}}}, "infoTransporter": {"ignore": false, "displayName": "", "data": {"host": "", "secureConnection": false, "port": 587, "tls": {"ciphers": ""}, "auth": {"user": "", "pass": ""}}}, "stagingTransporter": {"ignore": false, "displayName": "", "data": {"host": "", "port": 587, "auth": {"user": "", "pass": ""}, "tls": {"rejectUnauthorized": false, "ciphers": ""}}}, "donotreplyTransporter": {"ignore": true, "displayName": "", "data": {"host": "", "secureConnection": false, "port": 587, "tls": {"ciphers": ""}, "auth": {"user": "", "pass": ""}, "connectionTimeout": 5000}}, "russianTransporter": {"ignore": false, "displayName": "", "fromMailAddress": "", "proxy_url": "", "data": {"host": "", "port": 587, "auth": {"user": "", "pass": ""}, "tls": {"rejectUnauthorized": false, "ciphers": ""}}}, "railTransporter": {"ignore": false, "displayName": "", "fromMailAddress": "", "data": {"host": "", "secureConnection": false, "port": 587, "tls": {"ciphers": ""}, "auth": {"user": "", "pass": ""}, "connectionTimeout": 5000}}, "xnesTransporter": {"ignore": false, "displayName": "", "fromMailAddress": "", "data": {"host": "", "secureConnection": false, "port": 587, "tls": {"ciphers": ""}, "auth": {"user": "", "pass": ""}, "connectionTimeout": 5000}}}, "readOnlyWhitelist": [{"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"path": "", "method": ""}, {"path": "", "method": ""}, {"path": "", "method": ""}, {"path": "", "method": ""}, {"path": "", "method": ""}, {"path": "", "method": ""}, {"path": "", "method": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"path": "", "method": ""}, {"path": "", "method": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}, {"method": "", "path": ""}], "validSendersEmailGateway": ["<EMAIL>"], "featureFlagsForAllClients": ["findings"], "defaultHopperEncryptionKey": "", "hapi-key": "", "hapi-key-old": null, "queuesName": {"integration": {"progress": "", "startAttack": ""}}, "userInvitationPrivate": "", "userInvitationPublic": "", "2fa_excluded_domains": ["cymulate.com", "atheist.com", "zohomail.com"], "excluded_2fa_domains": ["cymulate.com", "atheist.com", "zohomail.com"], "cyFilesServerAddress": "", "integrationServerURL": "", "findingsRedisServerAddress": "", "3rd_party": {"document360": {"clientID": "", "clientSecret": "", "url": "", "callbackURL": ""}, "learnupon": {"callbackURL": "", "secret_key": ""}, "insided": {"callbackURL": ""}}, "taskManager": {"rundeckAddress": "", "rundeckApiVersion": 45, "taskManagerAddress": ""}}