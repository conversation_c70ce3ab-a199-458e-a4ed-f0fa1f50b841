{"dbs": [], "replicas": [], "db": "mongodb://localhost:27017/cymulate", "dbPassword": "a1976ce0927b185314d85b2fc48cf94b", "elastic": {"active": true, "elasticsearchUrl": "https://cymulate-stg.es.us-east-1.aws.found.io:9243", "apiKey": {"id": "x_b_UnsBoT_f8lHpyz3s", "name": "eks-services", "api_key": "5kSaJ7BWRs2uNw8XyUlsVw"}, "cloudId": "CymulateSTG:dXMtZWFzdC0xLmF3cy5mb3VuZC5pbyQxZjgyNGViYWQwNzc0OGQ2Yjk1YzI5MDkwN2FhOGYwMCRlNWExMjg2M2M5NmM0YTkzYjg4ZWI0ODMyMDFmOGY5MQ==", "apmUrl": "https://cymulate-stg.apm.us-east-1.aws.found.io", "apmToken": "lCgefJySLckomVHHjK"}, "s3": {"region": "eu-west-1", "accessKeyId": "340b8633af4bae9294212f637128b62da4868f8d20a4a256f09418dc722f99fd1af48778", "secretAccessKey": "ca58e08d65771741c11c90c3580bc4e1feae79add90a1a52f95671de98f33b583dd07244a9bcf243def3ac3e2ad3c53ffa9f2bd33c57d29b"}, "sqs": {"baseUrl": "https://sqs.us-east-1.amazonaws.com/118330362824/", "cybi": {"prefix": "dev-cybi-"}}, "kafka": {"broker": ["localhost:29092"]}, "unleash": {"url": "http://unleash-office.cymulatedev.com/ms/unleash", "apiToken": "*:development.9e563c41909ec0e6dd42694daa3173e2c43295338be01e4c27ae8d8e"}, "rundeck": {"token": "4cEJiLyHGVR9flI5BBSe6siKmFOme5aG"}, "cybi": {"mongoUri": "mongodb://localhost:27017/cymulate", "postgresUri": "postgresql://cymulate:password@127.0.0.1:5432/cymulate"}, "dynamicModules": {"url": {"jenkinsGetCrumbURL": "https://cym-jenkins.cymulate.com/crumbIssuer/api/json", "jenkinsCiCdBuilderURL": "https://cym-jenkins.cymulate.com/job/bitbucket-repos/job/asm-engine/job/asm-engine-dynamic-modules/buildWithParameters?token=06DV9JzmAAfVvUOi7XsVe7JIuByVDaWx44MkozBrX3yrVfhhZrQelrZ4bMiazlPJ"}, "user": {"jenkinsUsername": "insert-user-here", "jenkinsPassword": "insert-password-here"}}}