"""
Factory for creating secret manager instances.
"""

import os
from typing import Optional, Type

from .interfaces import SecretManagerService, SecretManagerType


class SecretManagerFactory:
    """
    Factory for creating secret manager instances.
    """

    @staticmethod
    def create(
        manager_type: Optional[SecretManagerType] = None,
    ) -> SecretManagerService:
        """
        Create a secret manager instance.

        Args:
            manager_type: The type of secret manager to create. If None, the type will be
                determined based on the environment.

        Returns:
            A secret manager instance
        """
        # Lazy import to avoid circular imports
        from .aws_secret_manager import AwsSecretManagerService
        from .file_secret_manager import FileSecretManagerService

        # If no type is specified, determine based on environment
        if manager_type is None:
            env = os.environ.get("ENV", "local").lower()
            config_provider = os.environ.get("ENV_CONFIG_PROVIDER", "").lower()

            if env in ("local", "test") or config_provider == "file":
                manager_type = SecretManagerType.FILE
            else:
                manager_type = SecretManagerType.AWS

        # Create the appropriate service
        if manager_type == SecretManagerType.AWS:
            return AwsSecretManagerService()
        elif manager_type == SecretManagerType.FILE:
            return FileSecretManagerService()
        else:
            raise ValueError(f"Unsupported secret manager type: {manager_type}")
