"""
MongoDB client singleton for managing database connections.
"""

from typing import Dict, Optional
from pymongo import MongoClient
from pymongo.database import Database

from secretmanager import SecretManagerFactory


class CymulateMongoClient:
    """
    Singleton class for managing MongoDB client connections.
    Handles both client creation and database loading.
    """
    
    _instances: Dict[str, 'CymulateMongoClient'] = {}
    _clients: Dict[str, 'CymulateMongoClient'] = {}
    
    def __new__(cls, id: str = None, url: str = None) -> 'CymulateMongoClient':
        """
        Create a new instance or return an existing one.
        
        Args:
            id: MongoDB connection ID (optional)
            url: MongoDB connection URL (optional)
            
        Returns:
            CymulateMongoClient instance
        """
        if id not in cls._instances:
            instance = super(CymulateMongoClient, cls).__new__(cls)
            instance._id = id
            instance._url = url
            instance._client = None
            instance._db = None
            cls._instances[id] = instance
        return cls._instances[id]
    
    def __init__(self, id: str = None, url: str = None):
        """
        Initialize the singleton instance.
        
        Args:
            id: MongoDB connection ID (optional)
            url: MongoDB connection URL (optional)
        """
        # Only set attributes if they don't exist yet
        if not hasattr(self, '_id'):
            self._id = id
            self._url = url
            self._client = None
            self._db = None
    
    @classmethod
    def _load_all_dbs(cls):
        """
        Load all databases from the secret.
        """
        from logger import logger
        try:
            logger.info("🔐 Loading MongoDB configurations from secrets...")
            secret = SecretManagerFactory.create().get_secret()
            
            logger.info(f"📊 Creating default MongoDB client...")
            cls._clients['default'] = CymulateMongoClient('default', secret.db)
            
            logger.info(f"📊 Loading {len(secret.dbs)} additional database configurations...")
            for db in secret.dbs:
                logger.debug(f"📊 Creating MongoDB client for tenant: {db.tenantId}")
                client = CymulateMongoClient(db.tenantId, db.db)
                cls._clients[db.tenantId] = client
            
            logger.success(f"✅ Successfully loaded {len(cls._clients)} MongoDB clients")
        except Exception as e:
            logger.error(f"❌ Failed to load MongoDB configurations: {e}")
            raise

    @classmethod
    def get_instance(cls, id: str = 'default') -> 'CymulateMongoClient':
        """
        Get an instance of the CymulateMongoClient.
        """
        if id not in cls._instances:
            if cls._instances is None:
                cls._load_all_dbs()
                if id not in cls._instances:
                    raise ValueError(f"No instance found for id: {id}")
            else:
                raise ValueError(f"No instance found for id: {id}")
        return cls._instances[id]
    
    @property
    def client(self) -> MongoClient:
        """
        Get the MongoDB client instance.
        
        Returns:
            MongoClient instance
        """
        if self._client is None:
            from logger import logger
            try:
                logger.info(f"🔌 Connecting to MongoDB with URL: {self._url[:50] if self._url else 'None'}...")
                self._client = MongoClient(self._url)
                # Test the connection
                self._client.admin.command('ping')
                logger.success(f"✅ Successfully connected to MongoDB (ID: {self._id})")
            except Exception as e:
                logger.error(f"❌ Failed to connect to MongoDB (ID: {self._id}): {e}")
                raise
        return self._client
    
    
    def get_db(self, db_name: str = 'cymulate') -> Database:
        """
        Get a database instance.
        
        Args:
            db_name: Name of the database
            
        Returns:
            Database instance
        """
        from logger import logger
        try:
            logger.debug(f"📊 Getting database: {db_name}")
            db = self.client[db_name]
            # Test database access
            collections = db.list_collection_names()
            logger.debug(f"✅ Successfully accessed database: {db_name} with {len(collections)} collections")
            return db
        except Exception as e:
            logger.error(f"❌ Failed to access database {db_name}: {e}")
            raise
    
    def close(self):
        """
        Close the MongoDB client connection.
        """
        if self._client is not None:
            self._client.close()
            self._client = None
            self._db = None
    
    @classmethod
    def close_all(cls):
        """
        Close all MongoDB client connections.
        """
        for instance in cls._instances.values():
            instance.close()
        cls._instances.clear() 