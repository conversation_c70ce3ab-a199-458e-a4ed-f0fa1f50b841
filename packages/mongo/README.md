# cymulate.mongo

mongo wrapper for cymulate usages

## Installation

```bash
# From the monorepo root
invoke install-dev

# Or directly
pip install cymulate.mongo
```

## Usage

```python
from mongo import example

# Example usage
result = example.function()
```

## Features

- Feature 1
- Feature 2
- Feature 3

## Development

This package is part of the Cymulate Python Libraries monorepo. See the root README.md for development instructions.

### Testing

```bash
# From the monorepo root
invoke test --package=mongo

# Or directly from the package directory
pytest
```

## License

Proprietary - Cymulate, Inc. 