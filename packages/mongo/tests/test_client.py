"""
Tests for the CymulateMongoClient class.
"""

from unittest.mock import MagicMock, patch

from pymongo.database import Database

from mongo.client import CymulateMongoClient


class TestCymulateMongoClient:
    """Test suite for CymulateMongoClient class."""

    def setup_method(self):
        """Setup method to reset the singleton instances before each test."""
        # Clear the instances dictionary directly instead of using close_all
        CymulateMongoClient._instances = {}

    def teardown_method(self):
        """Teardown method to clean up after each test."""
        # Clear the instances dictionary directly
        CymulateMongoClient._instances = {}

    def test_singleton_pattern(self):
        """Test that the client follows the singleton pattern."""
        # Create two instances with the same ID
        client1 = CymulateMongoClient(id="test", url="mongodb://localhost:27017")
        client2 = CymulateMongoClient(id="test", url="mongodb://localhost:27017")
        
        # They should be the same instance
        assert client1 is client2
        
        # Create an instance with a different ID
        client3 = CymulateMongoClient(id="test2", url="mongodb://localhost:27017")
        
        # This should be a different instance
        assert client1 is not client3

    @patch('cymulate.mongo.client.MongoClient')
    def test_client_initialization(self, mock_mongo_client):
        """Test that the client is initialized correctly."""
        # Setup mock
        mock_client = MagicMock()
        mock_mongo_client.return_value = mock_client
        
        # Create client and access the client property to trigger initialization
        client = CymulateMongoClient(id="test", url="mongodb://localhost:27017")
        _ = client.client
        
        # Check that MongoClient was called with the correct URL
        mock_mongo_client.assert_called_once_with("mongodb://localhost:27017")
        
        # Check that the client property returns the mock
        assert client.client is mock_client

    @patch('cymulate.mongo.client.MongoClient')
    def test_get_db(self, mock_mongo_client):
        """Test getting a database instance."""
        # Setup mock
        mock_client = MagicMock()
        mock_db = MagicMock(spec=Database)
        mock_client.__getitem__.return_value = mock_db
        mock_mongo_client.return_value = mock_client
        
        # Create client
        client = CymulateMongoClient(id="test", url="mongodb://localhost:27017")
        
        # Get database
        db = client.get_db("test_db")
        
        # Check that the database was accessed correctly
        mock_client.__getitem__.assert_called_once_with("test_db")
        assert db is mock_db

    @patch('cymulate.mongo.client.SecretManagerFactory')
    def test_load_all_dbs(self, mock_factory):
        """Test loading all databases from secrets."""
        # Setup mock
        mock_secret_manager = MagicMock()
        mock_factory.create.return_value = mock_secret_manager
        
        # Mock secret data
        mock_secret = MagicMock()
        mock_secret.db = "mongodb://default:27017"
        mock_secret.dbs = [
            MagicMock(tenantId="tenant1", db="mongodb://tenant1:27017"),
            MagicMock(tenantId="tenant2", db="mongodb://tenant2:27017")
        ]
        mock_secret_manager.get_secret.return_value = mock_secret
        
        # Call the method
        CymulateMongoClient._load_all_dbs()
        
        # Check that clients were created
        assert "default" in CymulateMongoClient._instances
        assert "tenant1" in CymulateMongoClient._instances
        assert "tenant2" in CymulateMongoClient._instances
        
        # Check that the clients have the correct URLs
        assert CymulateMongoClient._instances["default"]._url == "mongodb://default:27017"
        assert CymulateMongoClient._instances["tenant1"]._url == "mongodb://tenant1:27017"
        assert CymulateMongoClient._instances["tenant2"]._url == "mongodb://tenant2:27017"

    def test_get_client(self):
        """Test getting a client by ID."""
        # Create clients
        client1 = CymulateMongoClient(id="client1", url="mongodb://client1:27017")
        client2 = CymulateMongoClient(id="client2", url="mongodb://client2:27017")
        
        # Add clients to the _clients dictionary
        client1._clients = {"client1": client1, "client2": client2}
        
        # Get clients by ID
        retrieved_client1 = client1.get_client("client1")
        retrieved_client2 = client1.get_client("client2")
        retrieved_client3 = client1.get_client("client3")
        
        # Check that the correct clients were returned
        assert retrieved_client1 is client1
        assert retrieved_client2 is client2
        assert retrieved_client3 is None

    @patch('cymulate.mongo.client.MongoClient')
    def test_close(self, mock_mongo_client):
        """Test closing a client connection."""
        # Setup mock
        mock_client = MagicMock()
        mock_mongo_client.return_value = mock_client
        
        # Create client and initialize it
        client = CymulateMongoClient(id="test", url="mongodb://localhost:27017")
        _ = client.client  # This will initialize the client
        
        # Close the client
        client.close()
        
        # Check that the client was closed
        mock_client.close.assert_called_once()
        assert client._client is None
        assert client._db is None

    def test_close_all(self):
        """Test closing all client connections."""
        # Create clients
        client1 = CymulateMongoClient(id="client1", url="mongodb://client1:27017")
        client2 = CymulateMongoClient(id="client2", url="mongodb://client2:27017")
        
        # Mock the close method
        client1.close = MagicMock()
        client2.close = MagicMock()
        
        # Add clients to the instances dictionary
        CymulateMongoClient._instances = {"client1": client1, "client2": client2}
        
        # Close all clients
        CymulateMongoClient.close_all()
        
        # Check that all clients were closed
        client1.close.assert_called_once()
        client2.close.assert_called_once()
        
        # Check that the instances dictionary was cleared
        assert len(CymulateMongoClient._instances) == 0 