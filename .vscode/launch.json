{"version": "0.2.0", "configurations": [{"name": "Python Debugger: siem_rule", "type": "debugpy", "request": "launch", "justMyCode": false, "program": "./apps/product/siem_rule/server.py", "console": "integratedTerminal"}, {"name": "Python Debugger: code review", "type": "debugpy", "request": "launch", "justMyCode": false, "program": "./apps/innovations/code_review/server.py", "console": "integratedTerminal"}, {"name": "Attach to LangGraph", "type": "debugpy", "request": "attach", "justMyCode": false, "connect": {"host": "0.0.0.0", "port": 5678}}, {"name": "Python Debugger: cyber_security", "type": "debugpy", "request": "launch", "justMyCode": false, "program": "./apps/product/cyber_security/server.py", "console": "integratedTerminal", "autoReload": {"enable": true}}]}