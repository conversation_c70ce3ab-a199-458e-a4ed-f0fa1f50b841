{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default"], "sharedGlobals": []}, "nxCloudId": "68360d8c3bb2413f8dc177ee", "plugins": [{"plugin": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"plugin": "@nxlv/python", "options": {"packageManager": "uv"}}, {"plugin": "@nx-tools/nx-container", "options": {"defaultEngine": "docker", "defaultRegistry": "docker.io"}}], "targetDefaults": {"@nx/js:tsc": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}}