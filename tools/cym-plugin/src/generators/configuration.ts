import { NewAgentGeneratorSchema, NormalizedNewAgentGeneratorSchema } from "./schema";
import { kebabCase,camelCase,snakeCase, startCase } from "lodash";

export function normalizeName(name: string) {
    return name.replace(/[^a-zA-Z0-9]/g, '_');
}

export function normalizeType(type: string) {
    return type.replace(/[^a-zA-Z0-9]/g, '_');
}




export function normalizeOptions(options: NewAgentGeneratorSchema): NormalizedNewAgentGeneratorSchema {
    return {
        ...options,
        name: normalizeName(options.name),
        type: normalizeType(options.type),
        nameCamelCase: camelCase(options.name),
        namePascalCase: startCase(camelCase(options.name)).replace(/ /g, ''),
        nameSnakeCase: snakeCase(options.name),
        nameKebabCase: kebabCase(options.name),
    };
}