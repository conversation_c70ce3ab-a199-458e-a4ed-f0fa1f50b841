import { Tree } from "@nx/devkit";

import { addProjectConfiguration } from "@nx/devkit";
import { NormalizedNewAgentGeneratorSchema } from "./schema";

export function projectGenerator(tree: Tree, options: NormalizedNewAgentGeneratorSchema) {
    const {name, type, feature} = options;
    const projectRoot = `apps/${type}/${name}`;
    addProjectConfiguration(
        tree,
        options.name,
        {
          root: projectRoot,
          projectType: 'library',
          sourceRoot: `${projectRoot}/src`,
          tags: ['agent',...feature],
          release: {
            version: {
              versionActions: "@nxlv/python/src/release/version-actions",
            },
          },
          targets: {
            dev: {
              executor: "@nxlv/python:run-commands",
              options: {
                command: `uv run ${projectRoot}/server.py`,
              },
            },
            lock: {
              executor: "@nxlv/python:lock",
              options: {
                update: false,
              },
            },
            sync: {
              executor: "@nxlv/python:sync",
              options: {},
            },
            add: {
              executor: "@nxlv/python:add",
              options: {},
            },
            update: {
              executor: "@nxlv/python:update",
              options: {},
            },
            remove: {
              executor: "@nxlv/python:remove",
              options: {},
            },
            build: {
              executor: "@nxlv/python:build",
              outputs: ["{projectRoot}/dist"],
              options: {
                outputPath: "{projectRoot}/dist",
                publish: false,
                lockedVersions: true,
                bundleLocalDependencies: true,
              },
              cache: true,
            },
            lint: {
              executor: "@nxlv/python:ruff-check",
              outputs: [],
              options: {
                lintFilePatterns: [options.name, "tests"],
              },
              cache: true,
            },
            format: {
              executor: "@nxlv/python:ruff-format",
              outputs: [],
              options: {
                filePatterns: [options.nameSnakeCase, "tests"],
              },
              cache: true,
            },
            test: {
              executor: "@nxlv/python:run-commands",
              outputs: [
                "{workspaceRoot}/reports/{projectRoot}/unittests",
                "{workspaceRoot}/coverage/{projectRoot}",
              ],
              options: {
                command: "uv run pytest tests/",
                cwd: "{projectRoot}",
              },
              cache: true,
            },
            install: {
              executor: "@nxlv/python:install",
              options: {
                silent: false,
                args: "",
                verbose: false,
                debug: false,
              },
            },
            docker: {
              executor: "@nx-tools/nx-container:build",
              options: {
                file: "{workspaceRoot}/Dockerfile1",
                push: false,
                "build-args": ["APPS_PATH={projectRoot}", "PACKAGES_PATH=packages"],
                tags: [
                  `product/${options.nameSnakeCase}:latest`,
                  `product/${options.nameSnakeCase}:{projectVersion}`,
                ],
                metadata: {
                  images: [`product/${options.nameKebabCase}`],
                  tags: [
                    "type=schedule",
                    "type=ref,event=branch",
                    "type=ref,event=tag",
                    "type=ref,event=pr",
                    "type=semver,pattern={{version}}",
                    "type=semver,pattern={{major}}.{{minor}}",
                    "type=semver,pattern={{major}}",
                    "type=sha",
                  ],
                },
              },
              cache: true,
            },
        },
      }
    );
}
