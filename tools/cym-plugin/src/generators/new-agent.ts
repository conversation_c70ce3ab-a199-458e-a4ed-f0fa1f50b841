import { formatFiles, generateFiles, Tree } from "@nx/devkit";
import * as path from "path";
import { NewAgentGeneratorSchema } from "./schema";
import { projectGenerator } from "./project";
import { normalizeOptions } from "./configuration";

function addAppToPyprojectToml(tree: Tree, appPath: string) {
  const pyprojectPath = "pyproject.toml";

  if (!tree.exists(pyprojectPath)) {
    throw new Error("pyproject.toml not found in workspace root");
  }

  const content = tree.read(pyprojectPath, "utf-8");
  if (!content) {
    throw new Error("Failed to read pyproject.toml");
  }

  // Find the workspace members section and add the new app
  const lines = content.split("\n");
  let membersStartIndex = -1;
  let membersEndIndex = -1;

  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim() === "members = [") {
      membersStartIndex = i;
    }
    if (membersStartIndex !== -1 && lines[i].trim() === "]") {
      membersEndIndex = i;
      break;
    }
  }

  if (membersStartIndex === -1 || membersEndIndex === -1) {
    throw new Error(
      "Could not find workspace members section in pyproject.toml"
    );
  }

  // Insert the new app path before the closing bracket
  const newAppEntry = `    "${appPath}",`;
  lines.splice(membersEndIndex, 0, newAppEntry);

  tree.write(pyprojectPath, lines.join("\n"));
}

function addAppToVsCodeDebugConfig(
  tree: Tree,
  appName: string,
  appPath: string
) {
  const vscodeDebugConfigPath = ".vscode/launch.json";
  if (!tree.exists(vscodeDebugConfigPath)) {
    throw new Error("launch.json not found in workspace root");
  }

  const configToAdd = {
    name: `Python Debugger: ${appName}`,
    type: "debugpy",
    request: "launch",
    justMyCode: false,
    program: `./${appPath}/server.py`,
    console: "integratedTerminal",
  };

  const content = tree.read(vscodeDebugConfigPath, "utf-8");
  if (!content) {
    throw new Error("Failed to read launch.json");
  }

  const config = JSON.parse(content);
  config.configurations.push(configToAdd);
  tree.write(vscodeDebugConfigPath, JSON.stringify(config, null, 4));
}

function addAppToLanggraphJson(
  tree: Tree,
  appPath: string,
  appName: string,
  workflowPath: string
) {
  const langgraphPath = "langgraph.json";

  if (!tree.exists(langgraphPath)) {
    throw new Error("langgraph.json not found in workspace root");
  }

  const content = tree.read(langgraphPath, "utf-8");
  if (!content) {
    throw new Error("Failed to read langgraph.json");
  }

  try {
    const config = JSON.parse(content);

    // Add to graphs section
    if (!config.graphs) {
      config.graphs = {};
    }
    config.graphs[appName] = workflowPath;

    // Add to dependencies section
    if (!config.dependencies) {
      config.dependencies = [];
    }
    if (!config.dependencies.includes(appPath)) {
      config.dependencies.push(appPath);
    }

    tree.write(langgraphPath, JSON.stringify(config, null, 4));
  } catch (error) {
    throw new Error(`Failed to parse langgraph.json: ${error}`);
  }
}

export async function newAgentGenerator(
  tree: Tree,
  options: NewAgentGeneratorSchema
) {
  const normalizedOptions = normalizeOptions(options);
  const projectRoot = `apps/${normalizedOptions.type}/${normalizedOptions.name}`;

  projectGenerator(tree, normalizedOptions);
  generateFiles(
    tree,
    path.join(__dirname, "files"),
    projectRoot,
    normalizedOptions
  );

  // Add the app to configuration files
  addAppToPyprojectToml(tree, projectRoot);
  addAppToVsCodeDebugConfig(tree, normalizedOptions.name, projectRoot);
  // Generate workflow path for langgraph.json
  const workflowPath = `apps.${normalizedOptions.type}.${normalizedOptions.name}.${normalizedOptions.name}.workflow.${normalizedOptions.name}.${normalizedOptions.name}:compiled_workflow`;
  addAppToLanggraphJson(
    tree,
    projectRoot,
    normalizedOptions.name,
    workflowPath
  );

  await formatFiles(tree);
}

export default newAgentGenerator;
