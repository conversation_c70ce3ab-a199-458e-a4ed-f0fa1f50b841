

import os
from typing import Dict
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel

from langfuse import get_client, observe

# These imports will be replaced with the actual project name during template rendering
from <%= nameSnakeCase %>.handlers.handler import Handler<PERSON>ithCheckpointer
from <%= nameSnakeCase %>.workflow.<%= nameSnakeCase %>.<%= nameSnakeCase %> import base_workflow
from <%= nameSnakeCase %>.workflow.<%= nameSnakeCase %>.state import <%= namePascalCase %>State

class <%= namePascalCase %>Input(BaseModel):
    pull_request_id: str
    repository_slug: str
    workspace_slug: str


class <%= namePascalCase %>Handler(HandlerWithCheckpointer[<%= namePascalCase %>State,<%= namePascalCase %>Input]):

    def __init__(self):
        super().__init__()
    
    
    def parse_state(self,state:Dict)-><%= namePascalCase %>State:
        retState = <%= namePascalCase %>State(**state)
        retState.context=dict(state)
        return retState
    
    def get_workflow(self,checkpointer):
        return base_workflow(
            checkpointer=checkpointer
        )

    def init_state(self,input:<%= namePascalCase %>Input) -> <%= namePascalCase %>State:

        pr_id = input.pull_request_id
        repository = input.repository_slug
        workspace = input.workspace_slug

        review_description = f"Perform a code review on PR #{pr_id} in repository {repository} in workspace {workspace}"
        messages = [
                {
                    "role": "user",
                    "content": review_description
                }
            ]
    
        return <%= namePascalCase %>State(
            pull_request_id=pr_id,
            repository_slug=repository,
            workspace_slug=workspace,
            messages=messages,
        )

    @observe
    def get_config(self,input:<%= namePascalCase %>Input) -> RunnableConfig:
        return {
            "run_id": get_client().get_current_trace_id(),
            "callbacks": [self.get_langfuse_handler()],
            "configurable": {
                "thread_id": session_id,
            }
        }

    
    