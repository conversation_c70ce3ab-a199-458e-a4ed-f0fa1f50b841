"""
Supervisor Agent

This module implements the supervisor agent responsible for
delegating tasks to specialized agents and monitoring their execution.
"""

import os
from typing import Dict, List, Any, Optional, Literal, Union

from langgraph.graph import END

from langgraph.types import Command
from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm import ModelProvider
from logger import info, warning, error, debug, success, critical, exception
from corelanggraph.langfuse import Langfuse<PERSON><PERSON>

from langfuse import get_client

from <%= nameSnakeCase %>.models.agent_state import AgentState
from <%= nameSnakeCase %>.models.supervisor_models import SupervisorOutput


langfuse = LangfuseClient().client

class SupervisorAgent(BaseAgentLLM):
    """
    Supervisor agent that coordinates and manages other agents.
    Acts as the central orchestrator for complex workflows.
    """
    
    
    def __init__(
        self,
        model_name: str = "gpt-4",
        temperature: float = 0,
        available_agents: Optional[List[str]] = None
    ):
        """
        Initialize the supervisor agent
        
        Args:
            model_name: Name of the LLM model to use
            temperature: Temperature for LLM generation
            available_agents: List of available agent names
        """
        super().__init__(
            name="SupervisorAgent",
            description="Coordinates and delegates tasks to specialized agents",
            llm=ModelProvider().get_llm(model_name= os.getenv('MODEL_NAME','azure_model_2'), temperature=0.1)
        )
     
        # Get model from provider``
        info(f"🧠 Loaded LLM model for SupervisorAgent")

        self.available_agents = available_agents or []
        info(f"👥 Available agents for delegation: {', '.join(self.available_agents) if self.available_agents else 'None'}")
        
        # Add supervisor-specific capabilities
        self.metadata.capabilities.extend([
            "task_delegation",
            "workflow_orchestration",
            "error_handling"
        ])
        info(f"🔧 SupervisorAgent capabilities configured: {', '.join(self.metadata.capabilities)}")
        self.available_agents.append("end") if "end" not in self.available_agents else None

    def is_final_step(self, next_step: str) -> bool:
        return next_step == "end"
        
            
    async def execute(self, state: AgentState) -> Union[AgentState, Command[Literal["error_handler", "end"]]]:
        """
        Execute the supervisor agent's core functionality
        
        Args:
            state: Current workflow state
            
        Returns:
            Either an updated state object or a Command object directing flow to the next node
        """
        info(f"🚀 SupervisorAgent execution started")
        
  
        # Run the supervisor LLM using trustcall
        info(f"🧠 Invoking LLM for task analysis and delegation with trustcall")

        debug(f"🔄 Creating extractor for review results")
        get_client().update_current_span(
            level="DEBUG",
            status_message=f"Enter into {self.metadata.name}"
        )

        extractor = self.create_extractor(
            tools=[SupervisorOutput],
            tool_choice='SupervisorOutput'
        )

        try:
            prompt = self.system_prompt
            prompt.extend(state.messages)
            prompt = prompt.format_messages(
                available_agents=", ".join(self.available_agents),
            )
            result = await extractor.ainvoke(prompt)
            
            info(f"✅ Trustcall extractor response received successfully")
            
            supervisor_output:SupervisorOutput = result["responses"][0]
            # Extract the validated SupervisorOutput from the response
            if not supervisor_output:
                error(f"❌ No valid response from trustcall extractor")
                return Command(
                    update={"error": "Failed to get valid response from supervisor"},
                    goto="error_handler"
                )
            
            success(f"✅ Successfully extracted validated supervisor response")
            
            # Update state context with supervisor analysis
            analysis = supervisor_output.analysis
            info(f"📋 Added analysis to state: {analysis[:50]}{'...' if len(analysis) > 50 else ''}")
            
            # Update state with tasks
            if supervisor_output.task:
                task = supervisor_output.task.to_dict()
                info(f"📋 Added {len(task)} task to state")
                state.tasks.append(task)
            else:
                info(f"⚠️ No tasks found in supervisor output")
            
            # Set next steps using Command pattern for workflow routing
            info(f"🔜 Set next step: {supervisor_output.next_step}")
            next_step = supervisor_output.next_step


            if next_step:
                if self.is_final_step(next_step):
                    info(f"🏁 Workflow complete, ending execution")
                    return Command(update={"context": state.context}, goto=END)
                 
                # Otherwise return a Command to the first agent in next_steps
                if next_step not in self.available_agents:
                    raise ValueError(f"Invalid next step: {next_step}")
                
                info(f"⏭️ Directing workflow to {next_step}")
                return Command(update={"context": state.context}, goto=next_step)
            else:
                warning(f"⚠️ No next_steps found in supervisor output")
                # If no next steps, update state and return error handler command
                state.error = "No next steps found in supervisor output"
                return Command(
                    update=state,
                    goto="error_handler"
                )
                
                
        except Exception as e:
            critical(f"💥 Error during SupervisorAgent execution: {str(e)}")
            exception(f"Exception details: {e}")
            return Command(
                update={"error": f"SupervisorAgent execution failed: {str(e)}"},
                goto="error_handler"
            )
        
