[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mas.<%= nameSnakeCase %>"
version = "0.1.0"
description = "<%= description %>"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
license = "LicenseRef-Proprietary"
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = [
    # Core dependencies
    "fastapi>=0.109.0",
    "uvicorn>=0.25.0",
    "pydantic>=2.5.0",
    "httpx>=0.26.0",
    "corelanggraph",
    "corellm",
    "logger",
    "kafka",
]

[project.optional-dependencies]
dev = ["pytest>=7.4.0", "black>=23.12.0", "isort>=5.12.0", "mypy>=1.7.1"]

[project.scripts]
<%= nameSnakeCase %> = "server:main"

[project.urls]
"Homepage" = "https://bitbucket.org/cymulate/pythonlibs"
"Bug Tracker" = "https://bitbucket.org/cymulate/pythonlibs/jira"

[tool.setuptools.packages.find]
where = ["."]
include = ["*"]

# Use the same formatting and linting configuration as the root project
[tool.black]
line-length = 88
target-version = ["py312"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.uv.sources]
corelanggraph = { workspace = true }
corellm = { workspace = true }
logger = { workspace = true }
kafka = { workspace = true }


[tool.pytest.ini_options]
addopts = "--cov=<%= nameSnakeCase %> --cov-report=term-missing"

[tool.ruff]
select = ["I", "F401"]
