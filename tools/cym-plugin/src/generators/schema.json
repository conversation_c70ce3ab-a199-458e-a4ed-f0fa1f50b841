{"$schema": "https://json-schema.org/schema", "$id": "NewAgent", "title": "", "type": "object", "properties": {"name": {"type": "string", "description": "", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What name would you like to use?"}, "type": {"type": "string", "description": "Provide the agent type", "x-prompt": {"message": "Which type of agent would you like to generate?", "type": "list", "items": [{"value": "innovations", "label": "Innovation"}, {"value": "product", "label": "Product"}, {"value": "jobs", "label": "Jobs"}]}}, "feature": {"type": "array", "description": "Select the features to include in the agent", "minItems": 1, "x-prompt": {"message": "Which features would you like to include? (select at least one)", "type": "list", "multiselect": true, "items": [{"value": "supervisor", "label": "Supervisor"}, {"value": "mcp", "label": "MCP (Model Context Protocol)"}, {"value": "langfuse", "label": "<PERSON><PERSON>"}, {"value": "redis-checkpointer", "label": "<PERSON><PERSON>pointer"}]}}, "description": {"type": "string", "description": "Provide the agent description", "x-prompt": "What is the description of the agent?"}}, "required": ["name", "type"]}