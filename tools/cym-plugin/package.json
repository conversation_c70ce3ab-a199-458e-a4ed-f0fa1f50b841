{"name": "@cymulate-innovation/cym-plugin", "version": "0.0.1", "private": true, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"development": "./src/index.ts", "types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "nx": {"targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "tools/cym-plugin/dist", "main": "tools/cym-plugin/src/index.ts", "tsConfig": "tools/cym-plugin/tsconfig.lib.json", "rootDir": "tools/cym-plugin/src", "generatePackageJson": false, "assets": [{"input": "./tools/cym-plugin/src", "glob": "**/!(*.ts)", "output": "."}, {"input": "./tools/cym-plugin/src", "glob": "**/*.d.ts", "output": "."}]}}}}, "dependencies": {"@nx/devkit": "21.1.2", "tslib": "^2.3.0"}, "generators": "./generators.json", "files": ["dist", "!**/*.tsbuildinfo", "generators.json"]}