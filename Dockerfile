FROM python:3.12-slim as base

ARG APPS_PATH=''
ARG PACKAGES_PATH=''

WORKDIR /app

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install stage
FROM base as install

ADD https://astral.sh/uv/install.sh /uv-installer.sh
RUN sh /uv-installer.sh && rm /uv-installer.sh


ENV PATH="/root/.local/bin/:$PATH"

COPY . . 
RUN uv sync --all-packages 

FROM base as final
ARG APPS_PATH=apps/product/siem_rule
ARG PACKAGES_PATH=packages
WORKDIR /app


COPY --from=install /app/.venv /app/.venv

COPY --from=install /app/${APPS_PATH} /app/
COPY --from=install /app/${PACKAGES_PATH}/ /app/packages/


CMD ["/app/.venv/bin/python", "server.py"] 