# import os
# from corelanggraph.langfuse.langfuse import Langfuse<PERSON><PERSON>
from dotenv import load_dotenv

# from langchain_core.prompts import ChatPromptTemplate

load_dotenv(override=True)

# langfuse = LangfuseClient(
#     secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
#     public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
#     host=os.getenv("LANGFUSE_HOST"),
# ).client

# try:
#     prompt = langfuse.get_prompt(
#         "CyberSecurity/knowledgebase/manager"
#     ).get_langchain_prompt()
#     promptc = ChatPromptTemplate.from_messages(
#         prompt,
#     )

#     print(promptc)
# except Exception as e:
#     print(e)


# import asyncio
# from typing import TypedDict
# from langgraph.config import get_stream_writer
# from langgraph.graph import StateGraph, START, END
# from langchain_core.messages import BaseMessage

# class State(TypedDict):
#     topic: str
#     joke: str
#     stream_mode: str
#     messages: list[BaseMessage]

# def refine_topic(state: State):
#     state["stream_mode"] = "updates"
#     writer = get_stream_writer()
#     writer({"type": "updates", "data": "Hello"})
#     state["messages"].append(BaseMessage(content="Hello", type="human"))
#     state['topic'] = state["topic"] + " and cats"
#     return state

# def generate_joke(state: State):
#     state["messages"].append(BaseMessage(content="Hello", type="ai"))
#     return {"joke": f"This is a joke about {state['topic']}"}

# graph = (
#     StateGraph(State)
#     .add_node(refine_topic)
#     .add_node(generate_joke)
#     .add_edge(START, "refine_topic")
#     .add_edge("refine_topic", "generate_joke")
#     .add_edge("generate_joke", END)
#     .compile()
# )


# # Usage

# async def main():
#     for chunk in graph.stream(
#         {"topic": "ice cream", "messages": []},
#         stream_mode=["values","custom"],
#         subgraphs=True
#     ):
#         print(chunk)


# if __name__ == "__main__":
#     asyncio.run(main())


# # class A:
# #     def __init__(self):
# #         pass


# # class B(A):
# #     def __init__(self):
# #         super().__init__()

# # class C(B):
# #     def __init__(self):
# #         super().__init__()


# # a = A()
# # b = B()
# # c = C()

# # print(isinstance(a,A))
# # print(isinstance(b,A))
# # print(isinstance(c,A))

# # print(isinstance(a,B))
# # print(isinstance(b,B))
# # print(isinstance(c,B))

# # print(isinstance(a,C))
# # print(isinstance(b,C))
# # print(isinstance(c,C))


# from typing import List, TypedDict, Annotated
# from corellm.providers.provider import ModelProvider
# from langchain_core.messages import BaseMessage, HumanMessage
# from langgraph.graph import StateGraph, START, END
# from langgraph.prebuilt import create_react_agent
# import operator
# import asyncio
# import os
# from langgraph.managed import IsLastStep, RemainingSteps
# from secretmanager.factory import SecretManagerFactory


# class BaseState(TypedDict):
#     messages: Annotated[List[BaseMessage], operator.add]
#     my_list: List[int]
#     is_last_step: bool
#     remaining_steps: RemainingSteps


# graph = StateGraph(BaseState)


# async def main(model_smart):

#     agent = create_react_agent(
#         model=model_smart,
#         tools=[],
#         state_schema=BaseState,
#     )

#     graph.add_node("agent", agent)
#     graph.add_edge(START, "agent")
#     graph.add_edge("agent", END)
#     chain = graph.compile()

#     for s in chain.invoke(
#         {"messages": [HumanMessage(content="Hi")], "my_list": [1, 2, 3]}
#     ):
#         print(s)
#         print("---")


# if __name__ == "__main__":
#     os.environ["ENV"] = "local"
#     secret = SecretManagerFactory.create().get_secret()
#     model_provider = ModelProvider(
#         list(map(lambda x: x.model_dump(), secret.chatbot_ai.models))
#     )
#     model_smart = model_provider.get_model("claude-3-5-sonnet-20240620")
#     asyncio.run(main(model_smart))


from langchain_aws import ChatBedrockConverse
from typing_extensions import TypedDict
from pydantic import BaseModel
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langgraph_supervisor import create_supervisor
from langgraph.prebuilt.chat_agent_executor import AgentStateWithStructuredResponse
from langchain_core.tools import tool


worker_model = ChatBedrockConverse(
    model="us.anthropic.claude-3-haiku-20240307-v1:0",
    region_name="us-east-1",
)


class TaskSummary(BaseModel):
    task_name: str
    outcome: str
    success: bool
    reasonoffailure: str


@tool(name_or_callable="task_summary", description="Summarize the task")
def task_summary(task_name: str, outcome: str, success: bool) -> TaskSummary:
    return TaskSummary(task_name=task_name, outcome=outcome, success=success)


worker = create_react_agent(
    model=worker_model,
    tools=[task_summary],
    name="worker",
    response_format=TaskSummary,
    state_schema=AgentStateWithStructuredResponse,
)

app = create_supervisor(
    agents=[worker],
    model=worker_model,
    prompt="Delegate to worker",
    response_format=TaskSummary,
    state_schema=AgentStateWithStructuredResponse,
    output_mode="last_message",
).compile()

state = app.invoke({"messages": [{"role": "user", "content": "Run task"}]})
print(state)
