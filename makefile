run-test:
	@echo "Building package paths for test..."
	@PACKAGE_PATHS=$$(find packages -name "src" -type d | sed 's|^|$(PWD)/|' | tr '\n' ':' | sed 's/:$$//'); \
	echo "PACKAGE_PATHS: $$PACKAGE_PATHS"; \
	echo "PYTHONPATH will be: $(PWD):$$PACKAGE_PATHS"; \
	PYTHONPATH=$(PWD):$$PACKAGE_PATHS uv run langgraph dev

run-langgraph:
	PYTHONPATH=$(PWD) uv run langgraph dev

generate-lib:
	nx g @nxlv/python:uv-project slack --projectType=library --linter=ruff --unitTestRunner=pytest --codeCoverage=true --directory=libs --publishable=true --moduleName=slack --packageName=@cymulate/slack --tags=scope:python,type:lib