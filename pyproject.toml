[project]
name = "cymulate_platform"
version = "0.0.1"
description = "Modular Multi-Agent System with LangGraph"
readme = "README.md"
license = "MIT"
requires-python = ">=3.12"
dependencies = [
  "debugpy>=1.8.14",
  "langchain-community>=0.3.19",
  "prometheus-client>=0.22.0",
  "pytest-cov>=6.2.1",
  "elastic-apm>=6.23.0",
  "langgraph-checkpoint-mongodb>=0.1.4",
]

[project.optional-dependencies]
dev = ["mypy>=1.11.1", "ruff>=0.6.1"]

[build-system]
requires = ["setuptools>=73.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.package-data]
"*" = ["py.typed"]

[tool.setuptools.packages.find]
where = ["."]
include = ["apps*", "libs*"]
exclude = ["static*"]

[tool.ruff.lint]
select = ["E", "F", "I", "D", "D401", "T201", "UP"]
ignore = ["UP006", "UP007", "UP035", "D417", "E501"]

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["D", "UP"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.uv.workspace]
members = [
  "packages/corellm",
  "packages/langraph-redis-checkpointer",
  "packages/mongo",
  "packages/secretmanager",
  "packages/corelanggraph",
  "packages/kafka",
  "packages/logger",
  "packages/rag",
  "packages/storage",
  "packages/slack_client",
  "packages/websocket",
  "packages/zendesk",
  "packages/cymredis",
  "apps/product/siem_rule",
  "apps/innovations/code_review",
  "apps/product/cyber_security",
    "apps/jobs/kb_sync",
]

[dependency-groups]
dev = [
  "anyio>=4.7.0",
  "langgraph-cli[inmem]>=0.2.8",
  "mypy>=1.13.0",
  "pytest>=8.3.5",
  "ruff>=0.8.2",
  "autopep8>=2.3.1",
  "pytest-sugar>=1.0.0",
  "pytest-cov>=6.0.0",
  "pytest-html>=4.1.1",
  "langgraph-api>=0.2.115",
]
