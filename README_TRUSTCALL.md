# Trustcall Implementation for Bitbucket Agent

This document describes the implementation of the [trustcall](https://github.com/hinthornw/trustcall) library in the Bitbucket Agent.

## Overview

Trustcall is a library that improves the reliability of LLM tool calling by using JSON patch operations. It helps with:

1. Generating structured output more efficiently
2. Resilient retrying of validation errors for complex schemas
3. Accurate updates to existing schemas without information loss

## Implementation Details

### 1. Pydantic Models

We've created Pydantic models for all Bitbucket API operations in `mas/agents/bitbucket_models.py`:

- `CreatePullRequestModel`
- `UpdatePullRequestModel`
- `GetPullRequestModel`
- `ListPullRequestsModel`
- `MergePullRequestModel`
- `DeclinePullRequestModel`
- `CommentOnPullRequestModel`
- `CodeReviewPullRequestModel`
- `CodeReviewResponse`

These models provide validation and proper typing for all API operations.

### 2. Trustcall Integration

We've replaced the standard LangChain ReAct agent with trustcall's extractor in `BitbucketAgent`:

```python
# Create trustcall extractor for ReAct agent
self.agent_executor = create_extractor(
    llm=self.llm,
    tools=self.tools,
    tool_choice="any"
)
```

For code reviews, we've also implemented a dedicated trustcall extractor:

```python
# Create trustcall extractor for code review
code_review_llm = ModelProvider().get_llm('default_model')
code_review_extractor = create_extractor(
    llm=code_review_llm,
    tools=[CodeReviewResponse],
    tool_choice="CodeReviewResponse"
)
```

### 3. Consistent LLM Usage

All LLM interactions now use `ModelProvider().get_llm('default_model')` to ensure consistency across the application.

## Benefits

1. **Improved Reliability**: Trustcall handles validation errors gracefully by generating patches to fix issues rather than regenerating the entire output.

2. **Better Structure**: The Pydantic models provide clear structure and validation for all API operations.

3. **Consistent LLM Usage**: All LLM interactions use the same model provider, ensuring consistency.

## Usage

The Bitbucket Agent can be used as before, but with improved reliability for complex operations like code reviews.

Example:

```python
agent = BitbucketAgent(
    bitbucket_username="your_username",
    bitbucket_app_password="your_app_password",
    default_workspace="your_workspace",
    default_repository="your_repository"
)

result = await agent.execute(state)
```

## Dependencies

- trustcall>=0.0.38
- dydantic>=0.0.8
- pydantic>=2.5.0
- langchain>=0.1.0
- langgraph>=0.1.15
