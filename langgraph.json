{"graphs": {"cyber_security": "apps.product.cyber_security.cyber_security.workflow.cyber_security:compiled_workflow", "assessment": "apps.product.cyber_security.cyber_security.agents.assessment.workflow.assessment:compiled_workflow", "cymulate_knowledge_base": "apps.product.cyber_security.cyber_security.agents.cymulate_knowledge_base.workflow.cymulate_knowledge_base:compiled_workflow", "cyber_expert": "apps.product.cyber_security.cyber_security.agents.cyber_expert.workflow.cyber_expert:compiled_workflow", "template_generator": "apps.product.cyber_security.cyber_security.agents.template_generator.workflow.template_generator:compiled_workflow"}, "env": ".env", "dependencies": [".", "packages/corellm", "packages/logger", "packages/langraph-redis-checkpointer", "packages/secretmanager", "packages/rag", "packages/storage", "packages/corelanggraph", "packages/kafka", "packages/mongo", "apps/innovations/code_review", "apps/product/siem_rule", "apps/product/cyber_security"]}