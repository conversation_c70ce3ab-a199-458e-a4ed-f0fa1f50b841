# Building and Installing MAS

This guide explains how to properly build and install the MAS (Modular Multi-Agent System) package.

## Prerequisites

- Python 3.10 or higher
- pip (Python package installer)
- Recommended: A Python virtual environment

## Installation Steps

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd mas
   ```

2. Create and activate a virtual environment (recommended):
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install the package in development mode:
   ```bash
   pip install -e .
   ```

   This will install the package and its dependencies specified in `pyproject.toml`.

## Troubleshooting

If you encounter build errors related to `hatchling` or other build backends:

- Make sure you're using the latest version of pip:
  ```bash
  pip install --upgrade pip
  ```

- Make sure setuptools is installed:
  ```bash
  pip install setuptools wheel
  ```

- If you're still having issues, you can try installing the dependencies separately:
  ```bash
  pip install -r requirements.txt  # If available
  ```

## Running the Application

After installation, you can run the application using:

```bash
# Run the API server
python -m mas.api.app

# Or run the CLI
python -m main --message "Your message"
```

## Development Setup

For development, install the optional development dependencies:

```bash
pip install -e ".[dev]"
```

This will install additional tools for development, such as:
- black (code formatter)
- isort (import sorter)
- mypy (type checker)
- ruff (linter) 