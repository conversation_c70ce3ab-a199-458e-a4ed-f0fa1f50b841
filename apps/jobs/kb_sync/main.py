import json
import os

import boto3
import pymongo
from datetime import datetime, UTC
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor
from bs4 import BeautifulSoup
import requests
import re
import time
from requests.adapters import HTTPAdapter
from secretmanager.factory import SecretManagerFactory
from urllib3.util.retry import Retry
from dateutil import parser as date_parser

# Global clients
# add scret and access key to the dockerfile
s3_client = boto3.client("s3")


secrets_client = boto3.client("secretsmanager")
bedrock_client = boto3.client("bedrock-agent")
ses_client = boto3.client("ses")

_mongo_client = None
secret = SecretManagerFactory.create().get_secret()

MONGO_COLLECTION = "kb_job_state"
SLACK_WEBHOOK = os.getenv("SLACK_WEBHOOK")
EMAIL_TO = os.getenv("EMAIL_TO")

BUCKET_NAME = os.getenv("BUCKET_NAME", "cymulate360")
KB_ID = os.getenv("KB_ID", "4ZQN78FJCT")


def get_mongo_client() -> pymongo.MongoClient:
    global _mongo_client
    if _mongo_client is None:
        _mongo_client = pymongo.MongoClient(secret.db)
    return _mongo_client


def get_last_modified():
    try:
        db = get_mongo_client().get_database()
        collection = db.get_collection(MONGO_COLLECTION)
        result = collection.find_one({"job_name": "doc360_sync"})
        return result["last_modified_at"] if result else None
    except Exception as e:
        print(f"Error getting last modified: {e}")
        return None


def save_last_modified(last_modified_at):
    try:
        db = get_mongo_client().get_database()
        collection = db.get_collection(MONGO_COLLECTION)
        collection.update_one(
            {"job_name": "doc360_sync"},
            {
                "$set": {
                    "job_name": "doc360_sync",
                    "last_modified_at": last_modified_at,
                    "last_run": datetime.now(UTC).isoformat(),
                }
            },
            upsert=True,
        )
    except Exception as e:
        print(f"Error saving last modified: {e}")


def fetch_articles():
    url = f"https://apihub.document360.io/v2/ProjectVersions/{secret.chatbot_ai.DOCUMENT360_PROJECT_ID}/articles"
    headers = {"api_token": secret.chatbot_ai.DOCUMENT360_API_TOKEN}

    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.json().get("data", [])
    except Exception as e:
        print(f"Error fetching articles: {e}")
        return []


def fetch_article_details(article_id):
    url = f"https://apihub.document360.io/v2/Articles/{article_id}/en"
    headers = {"api_token": secret.chatbot_ai.DOCUMENT360_API_TOKEN}

    for attempt in range(3):
        try:
            time.sleep(3)  # Longer delay for local testing
            response = requests.get(url, headers=headers)

            if response.status_code == 429:
                wait_time = 5 * (attempt + 1)  # 5, 10, 15 seconds
                print(f"Rate limited, waiting {wait_time}s (attempt {attempt + 1}/3)")
                time.sleep(wait_time)
                continue

            response.raise_for_status()
            return response.json().get("data", {})

        except Exception as e:
            if attempt == 2:
                print(f"Error fetching article {article_id}: {e}")
                return {}
            time.sleep(2)

    return {}


def optimize_for_kb(content):
    html_content = content.get("html_content", "")
    clean_text = (
        BeautifulSoup(html_content, "html.parser").get_text(strip=True)
        if html_content
        else ""
    )

    return {
        "id": content.get("id"),
        "title": content.get("title"),
        "content": clean_text,
        "category_id": content.get("category_id"),
        "slug": content.get("slug"),
        "url": content.get("url"),
        "modified_at": content.get("modified_at"),
        "authors": [
            f"{author.get('first_name', '')} {author.get('last_name', '')}"
            for author in content.get("authors", [])
        ],
        "searchable_text": f"{content.get('title', '')} {clean_text}",
    }


def process_article(article, bucket_name):
    article_id = article.get("id")
    title = re.sub(r"[^\w\-_.]", "_", article.get("title", "untitled"))
    filename = f"{title}_{article_id}.json"

    try:
        content = fetch_article_details(article_id)
        if not content:
            return None, f"❌ No content for {filename}"

        optimized = optimize_for_kb(content)

        s3_client.put_object(
            Bucket=bucket_name,
            Key=filename,
            Body=json.dumps(optimized, separators=(",", ":")),
        )

        return filename, f"✅ Updated: {filename}"

    except Exception as e:
        return None, f"❌ Failed {filename}: {e}"


def sync_kb(kb_id):
    try:
        response = bedrock_client.start_ingestion_job(
            knowledgeBaseId=kb_id, dataSourceId="ZBPH8NJYKJ"
        )
        print(f"🔄 KB sync started: {response['ingestionJob']['ingestionJobId']}")
    except Exception as e:
        print(f"❌ KB sync failed: {e}")


def send_email_notification(message, email):
    try:
        ses_client.send_email(
            Source="<EMAIL>",
            Destination={"ToAddresses": [email]},
            Message={
                "Subject": {"Data": "KB Sync Notification"},
                "Body": {"Text": {"Data": message}},
            },
        )
        print(f"📧 Email notification sent to {email}")
    except Exception as e:
        print(f"❌ Email notification failed: {e}")


def send_slack_notification(result_data):
    try:
        webhook_url = SLACK_WEBHOOK
        if not webhook_url:
            print("❌ Slack webhook URL not found")
            return

        status = "✅ Success" if result_data.get("statusCode") == 200 else "❌ Failed"
        body = json.loads(result_data.get("body", "{}"))

        if "error" in body:
            message = f"{status}: KB Sync failed - {body['error']}"
        elif body.get("message") == "No updates needed":
            message = f"{status}: No updates needed ({body.get('total_articles', 0)} total articles)"
        else:
            message = f"{status}: KB Sync completed - {body.get('updated', 0)} articles updated out of {body.get('processed', 0)} processed"

        payload = {
            "text": message,
        }

        response = requests.post(webhook_url, json=payload)
        response.raise_for_status()
        print(f"📢 Slack notification sent: {message}")

    except Exception as e:
        print(f"❌ Slack notification failed: {e}")
        # Fallback to email
        try:
            if EMAIL_TO:
                send_email_notification(message, EMAIL_TO)
        except Exception as email_error:
            print(f"❌ Email fallback also failed: {email_error}")


def lambda_handler(context):
    bucket_name = BUCKET_NAME
    kb_id = KB_ID
    max_runtime = 13 * 60  # 13 minutes to leave buffer
    start_time = time.time()

    try:
        # Get last modified timestamp
        last_modified = get_last_modified()
        print(f"Last modified: {last_modified}")

        # Fetch all articles and filter by modified_at before API calls
        all_articles = fetch_articles()
        total_articles = len(all_articles)

        # Filter articles that need updating
        articles_to_process = []
        if last_modified:
            try:
                last_modified_dt = date_parser.parse(last_modified)
                articles_to_process = [
                    a
                    for a in all_articles
                    if date_parser.parse(a.get("modified_at", "1970-01-01T00:00:00Z"))
                    > last_modified_dt
                ]
            except:
                # Fallback to string comparison
                articles_to_process = [
                    a for a in all_articles if a.get("modified_at", "") > last_modified
                ]
        else:
            articles_to_process = all_articles  # First run, process all

        print(
            f"Found {len(articles_to_process)} articles to update (out of {total_articles} total)"
        )

        if not articles_to_process:
            result = {
                "statusCode": 200,
                "body": json.dumps(
                    {"message": "No updates needed", "total_articles": total_articles}
                ),
            }
            send_slack_notification(result)
            return result

        # Process articles sequentially with timeout check
        updated_files = []
        processed_count = 0

        for i, article in enumerate(articles_to_process):
            # Check if we're approaching timeout
            if time.time() - start_time > max_runtime:
                print(
                    f"⏰ Timeout approaching, stopping at {i}/{len(articles_to_process)}"
                )
                break

            try:
                filename, message = process_article(article, bucket_name)
                print(f"[{i + 1}/{len(articles_to_process)}] {message}")
                processed_count += 1
                if filename:
                    updated_files.append(filename)
            except Exception as e:
                print(f"Task failed: {e}")

        # Save latest modified_at and sync KB
        if updated_files:
            # Find the latest modified_at from processed articles
            latest_modified = max(
                [
                    a.get("modified_at", "")
                    for a in articles_to_process[:processed_count]
                ]
            )
            save_last_modified(latest_modified)
            sync_kb(kb_id)

        result = {
            "statusCode": 200,
            "body": json.dumps(
                {
                    "total_articles": total_articles,
                    "processed": processed_count,
                    "updated": len(updated_files),
                    "last_modified": last_modified,
                    "runtime_seconds": int(time.time() - start_time),
                }
            ),
        }
        send_slack_notification(result)
        return result

    except Exception as e:
        print(f"Lambda execution failed: {e}")
        result = {"statusCode": 500, "body": json.dumps({"error": str(e)})}
        send_slack_notification(result)
        return result
    finally:
        # Close MongoDB connection
        if _mongo_client:
            _mongo_client.close()


if __name__ == "__main__":
    if os.getenv("CRONEXEC") == "true":
        lambda_handler(None)
    else:
        print("Not enabled for cron execution")
