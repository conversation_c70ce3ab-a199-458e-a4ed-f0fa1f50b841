{"name": "kb_sync", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/jobs/kb_sync/src", "tags": ["agent"], "release": {"version": {"versionActions": "@nxlv/python/src/release/version-actions"}}, "targets": {"dev": {"executor": "@nxlv/python:run-commands", "options": {"command": "uv run apps/jobs/kb_sync/server.py"}}, "lock": {"executor": "@nxlv/python:lock", "options": {"update": false}}, "sync": {"executor": "@nxlv/python:sync", "options": {}}, "add": {"executor": "@nxlv/python:add", "options": {}}, "update": {"executor": "@nxlv/python:update", "options": {}}, "remove": {"executor": "@nxlv/python:remove", "options": {}}, "build": {"executor": "@nxlv/python:build", "outputs": ["{projectRoot}/dist"], "options": {"outputPath": "{projectRoot}/dist", "publish": false, "lockedVersions": true, "bundleLocalDependencies": true}, "cache": true}, "lint": {"executor": "@nxlv/python:ruff-check", "outputs": [], "options": {"lintFilePatterns": ["kb_sync", "tests"]}, "cache": true}, "format": {"executor": "@nxlv/python:ruff-format", "outputs": [], "options": {"filePatterns": ["kb_sync", "tests"]}, "cache": true}, "test": {"executor": "@nxlv/python:run-commands", "outputs": ["{workspaceRoot}/reports/{projectRoot}/unittests", "{workspaceRoot}/coverage/{projectRoot}"], "options": {"command": "uv run pytest tests/", "cwd": "{projectRoot}"}, "cache": true}, "install": {"executor": "@nxlv/python:install", "options": {"silent": false, "args": "", "verbose": false, "debug": false}}, "docker": {"executor": "@nx-tools/nx-container:build", "options": {"file": "{workspaceRoot}/Dockerfile1", "push": false, "build-args": ["APPS_PATH={projectRoot}", "PACKAGES_PATH=packages"], "tags": ["product/kb_sync:latest", "product/kb_sync:{projectVersion}"], "metadata": {"images": ["product/kb-sync"], "tags": ["type=schedule", "type=ref,event=branch", "type=ref,event=tag", "type=ref,event=pr", "type=semver,pattern={{version}}", "type=semver,pattern={{major}}.{{minor}}", "type=semver,pattern={{major}}", "type=sha"]}}, "cache": true}}}