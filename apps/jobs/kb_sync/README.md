# Knowledge Base Sync Job

This is the Knowledge Base Sync Job for the MAS (Modular Multi-Agent System).

## Description

The KB Sync job automatically synchronizes documentation from Document360 to AWS Bedrock Knowledge Base, ensuring the AI system has access to the latest documentation.

## What main.py Does

The `main.py` file implements a Lambda function that:

### 1. **Incremental Sync**
- Tracks the last sync timestamp in MongoDB
- Only processes articles modified since the last sync
- Optimizes performance by avoiding unnecessary API calls

### 2. **Document360 Integration**
- Fetches articles from Document360 API
- Retrieves detailed content for each modified article
- Handles API rate limiting with retry logic

### 3. **Content Processing**
- Converts HTML content to clean text using BeautifulSoup
- Optimizes content structure for knowledge base ingestion
- Creates searchable text combining title and content

### 4. **AWS S3 Storage**
- Uploads processed articles as JSON files to S3
- Uses sanitized filenames based on article titles and IDs
- Serves as the data source for Bedrock Knowledge Base

### 5. **Knowledge Base Sync**
- Triggers AWS Bedrock ingestion job after successful uploads
- Updates the knowledge base with new/modified content
- Maintains data consistency across the system

### 6. **State Management**
- Saves sync timestamps to MongoDB for incremental processing
- Tracks processing statistics and runtime metrics
- Handles timeout scenarios gracefully

### 7. **Notifications**
- Sends Slack notifications with sync results
- Falls back to email notifications if Slack fails
- Provides detailed status updates and error reporting

## Key Features

- **Incremental Processing**: Only syncs modified content
- **Rate Limit Handling**: Respects Document360 API limits
- **Timeout Management**: Handles Lambda execution time limits
- **Error Recovery**: Robust error handling and notifications
- **Content Optimization**: Prepares content for AI consumption
- **Multi-channel Notifications**: Slack + email fallback

## Environment Variables

- `BUCKET_NAME`: S3 bucket for storing processed articles (default: cymulate360)
- `KB_ID`: AWS Bedrock Knowledge Base ID (default: 4ZQN78FJCT)

## AWS Services Used

- **Lambda**: Serverless execution environment
- **S3**: Document storage
- **Bedrock**: Knowledge base and AI services
- **Secrets Manager**: Secure credential storage
- **SES**: Email notifications
- **MongoDB**: State tracking and metadata storage 