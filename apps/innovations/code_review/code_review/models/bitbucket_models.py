"""
Bitbucket Models

This module defines Pydantic models for Bitbucket API interactions.
These models are used with trustcall for reliable tool calling.
"""

from typing import Dict, List, Optional

from pydantic import BaseModel, Field, validator


class GetPullRequestModel(BaseModel):
    """Model for retrieving a pull request from Bitbucket"""
    repository: str = Field(description="Repository slug")
    workspace: str = Field(description="Workspace/project key")
    pr_id: int = Field(description="Pull request ID")

class CreatePullRequestModel(BaseModel):
    """Model for creating a pull request in Bitbucket"""
    repository: str = Field(description="Repository slug")
    workspace: str = Field(description="Workspace/project key")
    title: str = Field(description="Title of the pull request")
    source_branch: str = Field(description="Source branch name")
    destination_branch: str = Field(default="master", description="Destination branch name (defaults to 'master')")
    description: Optional[str] = Field(default=None, description="Description of the pull request")
    close_source_branch: bool = Field(default=False, description="Whether to close the source branch after merging")
    reviewers: Optional[List[str]] = Field(default=None, description="List of reviewer usernames")


class UpdatePullRequestModel(GetPullRequestModel):
    """Model for updating a pull request in Bitbucket"""
    title: Optional[str] = Field(default=None, description="New title for the pull request")
    description: Optional[str] = Field(default=None, description="New description for the pull request")
    reviewers: Optional[List[str]] = Field(default=None, description="New list of reviewer usernames")
    state: Optional[str] = Field(default=None, description="New state for the pull request (e.g., 'OPEN', 'MERGED', 'DECLINED')")




class ListPullRequestsModel(BaseModel):
    """Model for listing pull requests in Bitbucket"""
    repository: str = Field(description="Repository slug")
    workspace: str = Field(description="Workspace/project key")
    state: Optional[str] = Field(default=None, description="Filter by pull request state (e.g., 'OPEN', 'MERGED', 'DECLINED')")
    sort_by: Optional[str] = Field(default=None, description="Field to sort by (e.g., 'created_on', 'updated_on')")
    sort_direction: Optional[str] = Field(default=None, description="Sort direction ('asc' or 'desc')")
    limit: int = Field(default=10, description="Maximum number of pull requests to return")


class MergePullRequestModel(GetPullRequestModel):
    """Model for merging a pull request in Bitbucket"""
    close_source_branch: bool = Field(default=False, description="Whether to close the source branch after merging")




class CommentOnPullRequestModel(BaseModel):
    """Model for commenting on a pull request."""
    workspace: str = Field(..., description="Workspace/project key")
    repository: str = Field(..., description="Repository slug")
    pr_id: int = Field(..., description="Pull request ID")
    content: str = Field(..., description="Comment content")


class CodeReviewPullRequestModel(GetPullRequestModel):
    """Model for performing a code review on a pull request in Bitbucket"""
    files_to_review: Optional[List[str]] = Field(default=None, description="List of specific files to review")
    review_message: Optional[str] = Field(default=None, description="Custom message to include in the review")
    inline_comments: bool = Field(default=True, description="Whether to add comments inline (True) or as a single comment (False)")
    
    @validator("pr_id")
    def validate_pr_id(cls, v):
        """Validate that pr_id is a positive integer"""
        if v <= 0:
            raise ValueError("pr_id must be a positive integer")
        return v


class CodeReviewComment(BaseModel):
    """Model for a single code review comment"""
    file: str = Field(description="File path")
    line: int = Field(description="Line number")
    content: str = Field(description="Comment content")


class InlineCommentOnPullRequestModel(BaseModel):
    """Model for adding an inline comment to a pull request."""
    workspace: str = Field(..., description="Workspace/project key")
    repository: str = Field(..., description="Repository slug")
    pr_id: int = Field(..., description="Pull request ID")
    file_path: str = Field(..., description="Path to the file being commented on")
    line_number: int = Field(..., description="Line number to comment on")
    content: str = Field(..., description="Comment content")


class CodeReviewResponse(BaseModel):
    """Model for the response from a code review"""
    comments: Dict[str, str] = Field(description="Dictionary of comments where keys are 'filename:line_number' and values are the comments")
    overall: Optional[str] = Field(default=None, description="Overall comment about the pull request")
    
    @validator("comments")
    def validate_comments(cls, v):
        """Validate that comment keys follow the 'filename:line_number' format"""
        for key in v.keys():
            if key != "overall" and ":" not in key:
                raise ValueError(f"Comment key '{key}' must follow the format 'filename:line_number'")
        return v


class CreateCommentModel(BaseModel):
    """Model for creating a comment on a pull request with various contexts."""
    workspace: str = Field(..., description="Workspace/project key")
    repository: str = Field(..., description="Repository slug")
    pr_id: int = Field(..., description="Pull request ID")
    text: str = Field(..., description="Comment text content")
    comment_type: str = Field("general", description="Type of comment (general, reply, file, line)")
    parent_id: Optional[int] = Field(None, description="Parent comment ID (for replies)")
    file_path: Optional[str] = Field(None, description="Path to the file being commented on")
    line_number: Optional[int] = Field(None, description="Line number for line comments")
    from_hash: Optional[str] = Field(None, description="Source commit hash")
    to_hash: Optional[str] = Field(None, description="Destination commit hash")
    line_type: Optional[str] = Field(None, description="Type of line (ADDED, REMOVED, CONTEXT)")
    file_type: Optional[str] = Field(None, description="File type (FROM, TO)")
    severity: Optional[str] = Field(None, description="Comment severity (BLOCKER for tasks)")
    state: Optional[str] = Field(None, description="Comment state (PENDING for pending comments)") 






class ModifyCommentModel(BaseModel):
    """Model for creating a reply comment on a pull request."""
    content: str = Field(None, description="Comment content")
    comment_id: Optional[int] = Field(None, description="Comment ID required to if action is modify")

class CreateReplyCommentModel(BaseModel):
    """Model for creating a reply comment on a pull request."""
    content: str = Field(..., description="Comment content")
    parent_id: Optional[int] = Field(None, description="Parent comment ID required to if action is reply")

class CommentAnalysisModel(BaseModel):
    """Model for analyzing a comment on a pull request."""
    modify_comments: List[ModifyCommentModel] = Field(description="Modify comment if needed" ,default_factory=list )
    create_reply_comments: List[CreateReplyCommentModel] = Field(description="Create reply comment if needed" ,default_factory=list )