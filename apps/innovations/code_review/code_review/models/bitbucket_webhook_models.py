"""
Bitbucket Webhook Models

This module defines Pydantic models for Bitbucket webhook event payloads.
These models correspond to the event payloads documented at:
https://support.atlassian.com/bitbucket-cloud/docs/event-payloads/
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

# Common entities for event payloads

class Account(BaseModel):
    """Model for Bitbucket user account"""
    type: Optional[str] = Field(default="user")
    username: Optional[str] = None
    display_name: Optional[str] = None
    uuid: Optional[str] = None
    links: Optional[Dict[str, Any]] = None
    nickname: Optional[str] = None
    account_id: Optional[str] = None
    
    model_config = {"extra": "allow"}
class Workspace(BaseModel):
    """Model for Bitbucket workspace"""
    type: Optional[str] = Field(default="workspace")
    uuid: Optional[str] = None
    name: Optional[str] = None
    slug: Optional[str] = None
    links: Optional[Dict[str, Any]] = None
    
    model_config = {"extra": "allow"}

class Repository(BaseModel):
    """Model for Bitbucket repository"""
    type: Optional[str] = Field(default="repository")
    name: Optional[str] = None
    full_name: Optional[str] = None
    uuid: Optional[str] = None
    owner: Optional[Account] = None
    website: Optional[str] = None
    scm: Optional[str] = None
    is_private: Optional[bool] = None
    links: Optional[Dict[str, Any]] = None
    project: Optional[Dict[str, Any]] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class Project(BaseModel):
    """Model for Bitbucket project"""
    type: Optional[str] = Field(default="project")
    key: Optional[str] = None
    name: Optional[str] = None
    uuid: Optional[str] = None
    links: Optional[Dict[str, Any]] = None
    
    model_config = {"extra": "allow"}


class PullRequestRef(BaseModel):
    """Model for pull request branch reference"""
    branch: Optional[Dict[str, Any]] = None
    commit: Optional[Dict[str, Any]] = None
    repository: Optional[Repository] = None
    
    model_config = {"extra": "allow"}

class CommentContent(BaseModel):
    """Model for comment content"""
    raw: Optional[str] = None
    markup: Optional[str] = Field(default="markdown")
    html: Optional[str] = None
    
    model_config = {"extra": "allow"}

class CommentInline(BaseModel):
    """Model for inline comment details"""
    path: Optional[str] = None
    from_: Optional[int] = Field(default=None, alias="from")
    to: Optional[int] = None
    
    model_config = {"extra": "allow"}

class Comment(BaseModel):
    """Model for Bitbucket comment"""
    id: Optional[int] = None
    parent: Optional[Dict[str, Any]] = None
    content: Optional[CommentContent] = None
    inline: Optional[CommentInline] = None
    created_on: Optional[datetime] = None
    updated_on: Optional[datetime] = None
    links: Optional[Dict[str, Any]] = None
    user: Optional[Account] = None
    
    model_config = {"extra": "allow"}

class Issue(BaseModel):
    """Model for Bitbucket issue"""
    id: Optional[int] = None
    component: Optional[str] = None
    title: Optional[str] = None
    content: Optional[Dict[str, str]] = None
    priority: Optional[str] = None
    state: Optional[str] = None
    type: Optional[str] = None
    milestone: Optional[Dict[str, str]] = None
    version: Optional[Dict[str, str]] = None
    created_on: Optional[datetime] = None
    updated_on: Optional[datetime] = None
    links: Optional[Dict[str, Any]] = None
    
    model_config = {"extra": "allow"}

class PullRequest(BaseModel):
    """Model for Bitbucket pull request"""
    id: Optional[int] = None
    title: Optional[str] = None
    description: Optional[str] = None
    state: Optional[str] = None
    author: Optional[Account] = None
    source: Optional[PullRequestRef] = None
    destination: Optional[PullRequestRef] = None
    merge_commit: Optional[Dict[str, Any]] = None
    participants: Optional[List[Account]] = None
    reviewers: Optional[List[Account]] = None
    close_source_branch: Optional[bool] = None
    closed_by: Optional[Account] = None
    reason: Optional[str] = None
    created_on: Optional[datetime] = None
    updated_on: Optional[datetime] = None
    links: Optional[Dict[str, Any]] = None
    
    model_config = {"extra": "allow"}

# Webhook event payloads

class RepositoryPushEvent(BaseModel):
    """Model for repository push event payload"""
    push: Optional[Dict[str, Any]] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class RepositoryForkEvent(BaseModel):
    """Model for repository fork event payload"""
    fork: Optional[Repository] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class RepositoryUpdatedEvent(BaseModel):
    """Model for repository updated event payload"""
    changes: Optional[Dict[str, Any]] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class RepositoryTransferEvent(BaseModel):
    """Model for repository transfer event payload"""
    previous_owner: Optional[Account] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class CommitCommentCreatedEvent(BaseModel):
    """Model for commit comment created event payload"""
    comment: Optional[Comment] = None
    commit: Optional[Dict[str, Any]] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class CommitStatus(BaseModel):
    """Model for commit status"""
    name: Optional[str] = None
    description: Optional[str] = None
    state: Optional[str] = None
    key: Optional[str] = None
    url: Optional[str] = None
    type: Optional[str] = None
    created_on: Optional[datetime] = None
    updated_on: Optional[datetime] = None
    links: Optional[Dict[str, Any]] = None
    
    model_config = {"extra": "allow"}

class BuildStatusCreatedEvent(BaseModel):
    """Model for build status created event payload"""
    commit_status: Optional[CommitStatus] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class BuildStatusUpdatedEvent(BaseModel):
    """Model for build status updated event payload"""
    commit_status: Optional[CommitStatus] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class IssueCreatedEvent(BaseModel):
    """Model for issue created event payload"""
    issue: Optional[Issue] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class IssueUpdatedEvent(BaseModel):
    """Model for issue updated event payload"""
    issue: Optional[Issue] = None
    changes: Optional[Dict[str, Any]] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class IssueCommentCreatedEvent(BaseModel):
    """Model for issue comment created event payload"""
    comment: Optional[Comment] = None
    issue: Optional[Issue] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestCreatedEvent(BaseModel):
    """Model for pull request created event payload"""
    pullrequest: Optional[PullRequest] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestUpdatedEvent(BaseModel):
    """Model for pull request updated event payload"""
    pullrequest: Optional[PullRequest] = None
    changes: Optional[Dict[str, Any]] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestApprovedEvent(BaseModel):
    """Model for pull request approved event payload"""
    approval: Optional[Dict[str, Any]] = None
    pullrequest: Optional[PullRequest] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestApprovalRemovedEvent(BaseModel):
    """Model for pull request approval removed event payload"""
    approval: Optional[Dict[str, Any]] = None
    pullrequest: Optional[PullRequest] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestMergedEvent(BaseModel):
    """Model for pull request merged event payload"""
    pullrequest: Optional[PullRequest] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestDeclinedEvent(BaseModel):
    """Model for pull request declined event payload"""
    pullrequest: Optional[PullRequest] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestCommentCreatedEvent(BaseModel):
    """Model for pull request comment created event payload"""
    comment: Optional[Comment] = None
    pullrequest: Optional[PullRequest] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestCommentUpdatedEvent(BaseModel):
    """Model for pull request comment updated event payload"""
    comment: Optional[Comment] = None
    pullrequest: Optional[PullRequest] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestCommentDeletedEvent(BaseModel):
    """Model for pull request comment deleted event payload"""
    comment: Optional[Comment] = None
    pullrequest: Optional[PullRequest] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestChangesRequestCreatedEvent(BaseModel):
    """Model for pull request changes request created event payload"""
    request: Optional[Dict[str, Any]] = None
    pullrequest: Optional[PullRequest] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"}

class PullRequestChangesRequestRemovedEvent(BaseModel):
    """Model for pull request changes request removed event payload"""
    request: Optional[Dict[str, Any]] = None
    pullrequest: Optional[PullRequest] = None
    actor: Optional[Account] = None
    repository: Optional[Repository] = None
    workspace: Optional[Workspace] = None
    
    model_config = {"extra": "allow"} 