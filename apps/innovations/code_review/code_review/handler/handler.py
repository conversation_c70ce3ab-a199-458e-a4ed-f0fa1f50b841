from typing import TypeVar

from corelanggraph.handler import <PERSON><PERSON><PERSON><PERSON>
from langraph_redis_checkpointer import get_redis_checkpointer
from logger import logger
from secretmanager import SecretManagerFactory

T = TypeVar("T")
U = TypeVar("U")

class HandlerWithCheckpointer(RunHandler[T,U]):


    def __init__(self):
        secret =  SecretManagerFactory.create().get_secret()
        self.redis_url = secret.redis.default
        self.ttl = 8 * 60 * 60 * 1000
    
 
    
    async def get_checkpointer(self):
        logger.info("🔄 Initializing Redis checkpointer")
        checkpointer = await get_redis_checkpointer(self.ttl,self.redis_url)
        logger.info("✅ Redis checkpointer initialized successfully")
        return checkpointer
    
