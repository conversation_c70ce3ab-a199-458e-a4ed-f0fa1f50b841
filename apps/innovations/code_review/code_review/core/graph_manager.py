"""
Core LangGraph Manager for Multi-Agent System

This module manages the creation, execution, and monitoring of agent workflows
using LangGraph. It serves as the central coordinator for all agent interactions.
"""

import uuid
from typing import Any, Callable, Dict, List, Optional

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages
from pydantic import BaseModel, Field


class AgentState(BaseModel):
    """State model for the agent workflow"""
    messages: List[Dict[str, Any]] = Field(default_factory=list)
    context: Dict[str, Any] = Field(default_factory=dict)
    error: Optional[str] = None
    next_steps: List[str] = Field(default_factory=list)
    tasks: Optional[list[Dict[str, Any]]] = Field(default_factory=list)
class GraphManager:
    """Manager for creating and executing LangGraph agent workflows"""
    
    def __init__(self, use_redis: bool = False, redis_url: Optional[str] = None):
        """
        Initialize the graph manager
        
        Args:
            use_redis: Whether to use Redis for state persistence
            redis_url: Redis connection URL if using Redis
        """
        self.use_redis = use_redis
        self.redis_url = redis_url
        self.graphs: Dict[str, StateGraph] = {}
    
    def create_workflow(
        self, 
        name: str, 
        agents: Dict[str, Callable], 
        router: Callable,
        checkpointer_key: Optional[str] = None
    ) -> StateGraph:
        """
        Create a new agent workflow
        
        Args:
            name: Name of the workflow
            agents: Dictionary of agent callables
            router: Function that determines the next agent to call
            checkpointer_key: Optional key for the checkpointer
        
        Returns:
            The created StateGraph
        """
        # Create state
        workflow = StateGraph(AgentState)
        
        # Add agent nodes
        for agent_name, agent_fn in agents.items():
            workflow.add_node(agent_name, agent_fn)
        
        # Set the entry point using START
        workflow.add_conditional_edges(
            START,  # Use START instead of None
            router
        )
        
        # Define a condition function to check if we should end
        def should_end(state: AgentState):
            if "end" in state.next_steps:
                return END
            # Return the next step from next_steps if available
            return state.next_steps[0] if state.next_steps else None
        
        # Add conditional edges for ending the workflow
        for agent_name in agents.keys():
            workflow.add_conditional_edges(
                agent_name,
                should_end
            )
        
        # Configure state persistence
        # if self.use_redis and self.redis_url:
        #     checkpointer = RedisSaver(self.redis_url, checkpointer_key or name)
        # else:
        checkpointer = MemorySaver()
        
        # Compile the workflow
        compiled_workflow = workflow.compile(checkpointer=checkpointer)
        
        # Store the graph
        self.graphs[name] = compiled_workflow
        
        return compiled_workflow
    
    async def run_workflow(
        self, 
        name: str, 
        initial_input: Dict[str, Any],
        thread_id: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute a workflow asynchronously
        
        Args:
            name: Name of the workflow to execute
            initial_input: Initial input to the workflow
            thread_id: Optional thread ID for continuing an existing workflow
            config: Optional configuration for the workflow execution
        
        Returns:
            Final state of the workflow
        """
        if name not in self.graphs:
            raise ValueError(f"Workflow '{name}' not found")
        
        workflow = self.graphs[name]
        
        # Initialize state
        state = AgentState(context=initial_input)
        
        # Add initial message
        if "message" in initial_input:
            state.messages = add_messages(
                state.messages, 
                {"role": "user", "content": initial_input["message"]}
            )
        
        # Run the workflow
        workflow_config = {
            "configurable": {
                "thread_id": thread_id or f"{name}_{uuid.uuid4()}",
                "checkpoint_ns": name,
                "checkpoint_id": f"{name}_{uuid.uuid4()}"
            }
        }
        
        # Add any additional config
        if config:
            # Add recursion_limit if provided
            if "recursion_limit" in config:
                workflow_config["recursion_limit"] = config["recursion_limit"]
            
            # Add any other config options
            for key, value in config.items():
                if key != "recursion_limit" and key != "configurable":
                    workflow_config[key] = value
        
        result = await workflow.ainvoke(state, config=workflow_config)
        
        return result
    
    def get_workflow(self, name: str) -> Optional[StateGraph]:
        """Get a workflow by name"""
        return self.graphs.get(name) 