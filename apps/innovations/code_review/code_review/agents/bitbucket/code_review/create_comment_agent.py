"""
BitBucketCreateCommentAgent module for creating comments on pull requests.
"""


import os

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm import Model<PERSON>rovider
from langfuse import get_client
from langgraph.types import Command
from logger import logger

from code_review.agents.bitbucket.base.bitbucket_base_agent import Bitbu<PERSON><PERSON><PERSON><PERSON>gent
from code_review.integrations.bitbucket_client import Bitbu<PERSON><PERSON><PERSON>
from code_review.workflow.bitbucket.state import (
    BitbucketState,
    CreateCommentModelList,
)


class BitBucketCreateCommentAgent(BaseAgentLLM,BitbucketBaseAgent):
    """
    Agent responsible for creating comments on pull requests based on code analysis.
    This includes general comments on the PR and inline comments on specific code sections.
    """
    
    def __init__(self):
        """
        Initialize the BitBucketCreateCommentAgent
        
        Args:
            bitbucket_username: Bitbucket username
            bitbucket_app_password: Bitbucket app password
        """
        name = "BitBucketCreateCommentAgent"
        description = "Creates comments on pull requests based on code analysis"
        llm = ModelProvider().get_llm(
            model_name=os.getenv("MODEL_NAME",'azure_model_2'),
            temperature=0.1
        )
        BaseAgentLLM.__init__(
            self,
            name=name,
            description=description,
            llm=llm
        )
        BitbucketBaseAgent.__init__(
            self,
            name=name,
            description=description,
        )
        logger.info("🚀 Initialized BitBucketCreateCommentAgent")

    async def execute(self, state: BitbucketState) -> BitbucketState:
        """
        Execute the agent to create comments on the pull request
        
        Args:
            state: Current workflow state with analysis results
            
        Returns:
            Updated workflow state with comment information
        """
        logger.info(f"💬 Starting comment creation for PR #{state.pull_request_id}")
        
        if state.review_result is None:
            logger.error("❌ Review result not found, cannot create comments")
            state.error = "Review result not found"
            raise ValueError("Review result not found")
        
        logger.debug(f"🔌 Creating Bitbucket client connection")
        bitbucket_client = self.get_bitbucket_client(state)
        
        logger.info(f"📝 Updating PR description")
        await self.add_pull_request_description(state, bitbucket_client)

        if not state.review_result.comments:
            logger.info(f"ℹ️ No comments to publish, skipping comment creation")
            logger.info(f"➡️ Moving to next step: update_pull_request_approval")
            return Command(
                goto="update_pull_request_approval",
                update=state
            )

        logger.info(f"🔍 Extracting comments from review results")
        messages = self.system_prompt.format_messages(
            review_result=state.review_result.model_dump_json(),
            schema=CreateCommentModelList.model_json_schema(),
            input_diff=state.pull_request_data.get("diff", "")
            )
        
        logger.debug(f"🤖 Creating comments extractor")
        extractor = self.create_extractor(
            tools=[CreateCommentModelList],
            tool_choice="CreateCommentModelList"
        )
        
        logger.info(f"🧠 Invoking LLM to format comments")
        try:
            response = await extractor.ainvoke(messages)
            logger.info(f"✅ Successfully extracted comments")
        except Exception as e:
            logger.error(f"💥 Error extracting comments: {str(e)}")
            raise
        # Implementation will be added later

        comments: CreateCommentModelList = response["responses"][0]
        
        if comments.comments:
            comment_count = len(comments.comments)
            logger.info(f"📊 Publishing {comment_count} comments to PR #{state.pull_request_id}")
            await self.publish_comments(comments, state, bitbucket_client)
            logger.info(f"✅ Successfully published {comment_count} comments")
        else:
            logger.info(f"ℹ️ No comments extracted from review results")
        
        logger.info(f"➡️ Moving to next step: update_pull_request_approval")
        return Command(
                goto="update_pull_request_approval",
                update=state
            ) 
    
    async def add_pull_request_description(self, state: BitbucketState, bitbucket_client:BitbucketClient):
        """
        Add the pull request description to the state
        """
        # if not state.review_result.description:
        #     logger.info(f"ℹ️ No description to update for PR #{state.pull_request_id}")
        #     return
        
        trace_id = get_client().get_current_trace_id()

        feedback_buttons = f"""
* [👍 Like]({os.getenv('API_URL')}/api/feedback?type=like&trace_id={trace_id})
* [👎 Dislike]({os.getenv('API_URL')}/api/feedback?type=dislike&trace_id={trace_id})
        """

        description = f"{state.review_result.description}\n{feedback_buttons}"
        
        logger.info(f"📝 Updating PR description for PR #{state.pull_request_id}")
        try:
            await bitbucket_client.update_pull_request(
                workspace=state.workspace_slug,
                repository=state.repository_slug,
                pr_id=state.pull_request_id,
                description=description
            )
            logger.info(f"✅ Successfully updated PR description")
        except Exception as e:
            logger.error(f"❌ Error updating PR description: {str(e)}")
            raise

    async def publish_comments(self, comments: CreateCommentModelList, state: BitbucketState, bitbucket_client:BitbucketClient):
        """
        Publish the comments on the pull request
        """
        logger.info(f"🔄 Starting to publish {len(comments.comments)} comments")
        
        for i, comment in enumerate(comments.comments):
            logger.debug(f"💬 Publishing comment {i+1}/{len(comments.comments)}")
                        
            try:
                await bitbucket_client.comment_on_pull_request(
                    workspace=state.workspace_slug,
                    repository=state.repository_slug,
                    pr_id=state.pull_request_id,
                    data=comment.model_dump(exclude_none=True,by_alias=True)
                )
                logger.debug(f"✓ Comment {i+1} published successfully")
            except Exception as e:
                logger.error(f"❌ Error publishing comment {i+1}: {str(e)}")
                # Continue with other comments even if one fails
        
        logger.info(f"🎉 All comments published successfully")