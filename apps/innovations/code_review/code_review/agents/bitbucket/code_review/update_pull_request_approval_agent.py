"""
BitBucketUpdatePullRequestApprovalAgent module for approving or requesting changes on pull requests.
"""

from langgraph.graph import E<PERSON>
from langgraph.types import Command
from logger import logger

from code_review.agents.bitbucket.base.bitbucket_base_agent import Bitbucket<PERSON>aseAgent
from code_review.integrations.bitbucket_client import Bitbu<PERSON><PERSON><PERSON>
from code_review.workflow.bitbucket.state import BitbucketState


class BitBucketUpdatePullRequestApprovalAgent(BitbucketBaseAgent):
    """
    Agent responsible for making approval decisions on pull requests.
    This includes approving PRs that meet quality standards or requesting changes if issues are found.
    """
    
    def __init__(self):
        """
        Initialize the BitBucketUpdatePullRequestApprovalAgent
        
        Args:
            bitbucket_username: Bitbucket username
            bitbucket_app_password: Bitbucket app password
        """
        super().__init__(
            name="BitBucketUpdatePullRequestApprovalAgent",
            description="Makes approval decisions on pull requests based on analysis"
        )
        logger.info("🚀 Initialized BitBucketUpdatePullRequestApprovalAgent")
        
        
    async def execute(self, state: BitbucketState) -> BitbucketState:
        """
        Execute the agent to approve or request changes on the pull request
        
        Args:
            state: Current workflow state with analysis results and comments
            
        Returns:
            Updated workflow state with approval information
        """
        logger.info(f"⚖️ Starting PR approval decision process for PR #{state.pull_request_id}")

        if state.review_result is None:
            logger.error(f"❌ Review result not found for PR #{state.pull_request_id}")
            state.error = "Review result not found"
            raise ValueError("Review result not found")
        
        logger.info(f"🔍 Evaluating review approval status: {state.review_result.approval}")
        bitbucket_client = self.get_bitbucket_client(state)
        
        if not state.review_result.approval:
            logger.info(f"⛔ Changes requested for PR #{state.pull_request_id} due to issues found")
            logger.debug(f"🔌 Creating Bitbucket client connection")
            
            try:
                logger.info(f"🔄 Requesting changes via Bitbucket API")
                await bitbucket_client.request_changes(state.workspace_slug, state.repository_slug, state.pull_request_id)
                logger.info(f"✅ Successfully requested changes")
            except Exception as e:
                logger.error(f"💥 Error requesting changes: {str(e)}")
                state.error = f"Error requesting changes: {str(e)}"
                raise
        else:
            logger.info(f"✨ PR #{state.pull_request_id} approved! No blocking issues found")
            # Future implementation: Add formal approval via API
            try:
                
                await bitbucket_client.approve_pull_request(state.workspace_slug, state.repository_slug, state.pull_request_id)
            except Exception as e:
                logger.error(f"💥 Error approving pull request: {str(e)}")
                state.error = f"Error approving pull request: {str(e)}"
                raise

        logger.info(f"🏁 Approval decision process completed")
        await self.update_build_status(state, bitbucket_client)

        state.messages.append({
            "role": "assistant",
            "content": f"Finished code review process for PR #{state.pull_request_id}, can finish the workflow"
        })
        
        
        return Command(
            goto=END,
            update=state,
        )
        
    async def update_build_status(self, state: BitbucketState, bitbucket_client: BitbucketClient):
        """
        Update the build status of the pull request
        """
        
        try:
            build_state = 'SUCCESSFUL' if state.review_result.approval else 'FAILED'
            desc_status = 'approved' if state.review_result.approval else 'requested changes'
            await bitbucket_client.update_bitbucket_build_status(
                workspace=state.workspace_slug,
                repository=state.repository_slug,
                commit_sha=state.pull_request_data['source']['commit']['hash'],
                build_key="Code Review Agent",
                build_url=f"https://bitbucket.org/{state.workspace_slug}/{state.repository_slug}/pull-requests/{state.pull_request_id}",
                state=build_state,
                description=f"The code review agent finished for PR #{state.pull_request_id} with with status {desc_status}"
            )
        except Exception as e:
            logger.error(f"💥 Error updating build status: {str(e)}")
        
        