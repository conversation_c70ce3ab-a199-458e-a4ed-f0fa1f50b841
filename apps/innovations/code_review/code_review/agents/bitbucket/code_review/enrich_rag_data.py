import os

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm.providers import ModelProvider
from langgraph.graph import <PERSON><PERSON>
from langgraph.types import Command
from logger import logger

from code_review.integrations.code_analyser import CodeAnalyserRag
from code_review.workflow.bitbucket.state import BitbucketState, RAGFileList


class BitBucketEnrichRagDataAgent(BaseAgentLLM):
    """
    Agent responsible for generating improved queries for RAG based on pull request diffs
    """
    
    def __init__(self):
        super().__init__(
            name="BitBucketEnrichRagDataAgent",
            description="Generate improved queries for RAG based on pull request diffs",
            llm=ModelProvider().get_llm(model_name=os.getenv('MODEL_NAME','azure_model_2'), temperature=0.1)
        )

    async def execute(self, state: BitbucketState) -> BitbucketState:
        """
        Execute the agent to generate improved queries based on PR diff
        """
        logger.info("Starting RAG query generation process")
        
        if not state.pull_request_diff:
            logger.warning("No pull request diff found in state")
            state.error = "No pull request diff found in state"
            return Command(
                goto=END,
                update=state
            )
        
        
        # Create a tool extractor for the RAGFile objects
        extractor = self.create_extractor(
            tools=[RAGFileList],
            tool_choice='RAGFileList'
        )
        
        # Step 1: Send PR diff to LLM
        logger.info("Sending PR diff to LLM for query generation")
        
        try:
            # Prepare input for the LLM
            prompt = self.system_prompt.format_messages(
                repository=state.repository_slug,
                diff=state.pull_request_diff
            )
            
            # Step 2: Get improved queries from LLM
            result = await extractor.ainvoke(prompt)
            
            # Process the RAGFile objects from the result
            rag_files_list:RAGFileList = result["responses"][0]
            rag_files = rag_files_list.files
            


            
            logger.info(f"Generated {len(rag_files)} improved queries for RAG")
            code_analyser = CodeAnalyserRag()
            
            # Step 3 & 4: Embed queries and retrieve docs from vector DB
            enriched_files = []
            for rag_file in rag_files:
                try:
                    # Retrieve relevant documents from vector DB using the improved query
                    retrieved_docs = await code_analyser.get_related_files(
                        rag_file.query, 
                        state.repository_slug,
                        top_k=1
                    )
                    if retrieved_docs:
                        # Add to enriched files
                        enriched_files.append({
                        "file_path": rag_file.file_path,
                        "related_docs": retrieved_docs
                    })
                    
                    logger.info(f"Retrieved documents for file: {rag_file.file_path}")
                except Exception as e:
                    logger.error(f"Error retrieving documents for file {rag_file.file_path}: {str(e)}")
            
            # Step 5: Save in state
            state.retrieved_documents = enriched_files
            
            logger.info(f"Query generation and document retrieval completed for {len(enriched_files)} files")
            return Command(
                goto='analyze_code_diff',
                update=state
            )
            
        except Exception as e:
            logger.error(f"Error in RAG query generation process: {str(e)}")
        
        return state

