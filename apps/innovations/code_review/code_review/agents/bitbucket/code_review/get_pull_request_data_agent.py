"""
BitBucketGetPullRequestDataAgent module for retrieving pull request data from Bitbucket.
"""

import re

from langgraph.types import Command
from logger import logger

from code_review.agents.bitbucket.base.bitbucket_base_agent import Bitbu<PERSON><PERSON><PERSON>Agent
from code_review.integrations.bitbucket_client import <PERSON><PERSON><PERSON><PERSON><PERSON>
from code_review.workflow.bitbucket.state import BitbucketState


class BitBucketGetPullRequestDataAgent(BitbucketBaseAgent):
    """
    Agent responsible for fetching pull request data from Bitbucket.
    This includes the PR metadata, files changed, and other relevant information.
    """
    
    def __init__(self):
        """
        Initialize the BitBucketGetPullRequestDataAgent
        
        Args:
            bitbucket_username: Bitbucket username
            bitbucket_app_password: Bitbucket app password
        """
        super().__init__(
            name="BitBucketGetPullRequestDataAgent",
            description="Retrieves pull request data from Bitbucket API"
        )
        logger.info("🚀 Initialized BitBucketGetPullRequestDataAgent")
        
    async def execute(self, state: BitbucketState) -> BitbucketState:
        """
        Execute the agent to retrieve pull request data
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state with pull request data
        """
        logger.info(f"📥 Starting execution of PR data retrieval for PR #{state.pull_request_id}")
        
        logger.debug(f"🔌 Creating Bitbucket client connection")
        bitbucket_client = self.get_bitbucket_client(state)
        
        logger.info(f"🔍 Fetching data for PR #{state.pull_request_id} in {state.workspace_slug}/{state.repository_slug}")
        if not state.pull_request_data or not state.pull_request_diff:
            await self.get_pull_request_data(state, bitbucket_client)

        
        logger.info(f"✅ Successfully retrieved pull request data for PR #{state.pull_request_id}")
        logger.debug(f"📦 Data size: {len(state.pull_request_data) if state.pull_request_data else 0} characters")
        if not state.mission_id and self.extract_mission_id(state):
            logger.info(f"🔍 Extracted mission ID: {state.mission_id}")
            state.messages.append({
                "role": "assistant",
                "content": f"""
                The pull request is related to the mission {state.mission_id}.
                Need to retrieve the mission data from Jira.
                """
            })
            return Command(
                goto='supervisor_agent',
                update=state
            )
                
        if state.comment:
            logger.info(f"➡️ Moving to next step: analyze_comment")
            return Command(
                goto='analyze_comment',
                update=state
            )
        
        logger.info(f"➡️ Moving to next step: enrich_rag_data")

        return Command(
            goto='enrich_rag_data',
            update=state
        )
    
    def extract_mission_id(self, state: BitbucketState) -> bool:
        """
        Extract the mission ID from the pull request data title 
        Pattern: "CA-00000"
        """
        if state.pull_request_data and state.pull_request_data.get('title'):
            title = state.pull_request_data['title']
            match = re.search(r'CA-(\d+)', title)
            if match:
                state.mission_id = match.group(0)
                return True
        return False
    
    async def get_pull_request_data(self, state: BitbucketState, bitbucket_client: BitbucketClient):
        """
        Get pull request data from Bitbucket API
        """
        if 'pull_request_data' in state and state.pull_request_data is not None:
            logger.info(f"🔄 Using existing pull request data from state")
            return
        
        logger.info(f"📡 Calling Bitbucket API for PR #{state.pull_request_id}")
        try:
            state.pull_request_data = await bitbucket_client.get_pull_request(
                state.workspace_slug,
                state.repository_slug,
                state.pull_request_id
            )
            state.pull_request_diff =await bitbucket_client.get_pull_request_diff_text(
                state.workspace_slug,
                state.repository_slug,
                state.pull_request_id
            )

            
            if state.pull_request_data is None:
                logger.error(f"❌ Pull request data not found for PR #{state.pull_request_id}")
                state.error = f"Pull request data not found for PR #{state.pull_request_id}"
                raise ValueError(f"Pull request data not found for PR #{state.pull_request_id}")
            

            await self.update_build_status(state, bitbucket_client)
            
            logger.info(f"🎉 Successfully fetched pull request data")
        except Exception as e:
            logger.error(f"💥 Error retrieving pull request data: {str(e)}")
            state.error = f"Error retrieving pull request data: {str(e)}"
            raise
        
        return
    
    async def update_build_status(self, state: BitbucketState, bitbucket_client: BitbucketClient):
        """
        Update the build status of the pull request
        """
        try:
            if state.comment:
                return
            
            await bitbucket_client.update_bitbucket_build_status(
                workspace=state.workspace_slug,
                repository=state.repository_slug,
                commit_sha=state.pull_request_data['source']['commit']['hash'],
                build_key="Code Review Agent",
                build_url=f"https://bitbucket.org/{state.workspace_slug}/{state.repository_slug}/pull-requests/{state.pull_request_id}",
                state='INPROGRESS',
                description=f"Code Review Agent is running for PR #{state.pull_request_id}"
            )
        except Exception as e:
            logger.error(f"💥 Error updating build status: {str(e)}")