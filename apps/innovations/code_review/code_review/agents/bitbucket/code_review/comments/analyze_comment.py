"""
BitBucketAnalyzeCommentAgent module for analyzing comments in pull requests.
"""

import os
from typing import Dict, List

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corelanggraph.langfuse import LangfuseClient
from corellm.providers import ModelProvider
from langchain_community.tools import TavilySearchResults
from langchain_core.tools import tool
from langgraph.graph import END
from langgraph.types import Command
from logger import logger

from code_review.agents.bitbucket.base.bitbucket_base_agent import BitbucketBaseAgent
from code_review.integrations.bitbucket_client import <PERSON>bu<PERSON><PERSON>lient
from code_review.models.bitbucket_models import (
    CommentAnalysisModel,
    CreateReplyCommentModel,
    ModifyCommentModel,
)
from code_review.workflow.bitbucket.state import BitbucketState

langfuse = LangfuseClient().client


@tool
async def search_using_tavily(query: str):
    """
    Search on internet using tavily
    """
    return TavilySearchResults(
        max_results=5,
        query=query
    )

class BitBucketAnalyzeCommentAgent(BaseAgentLLM, BitbucketBaseAgent):
    """
    Agent responsible for analyzing comments in pull requests.
    This includes understanding comment context, sentiment analysis,
    and determining appropriate responses or actions.
    """
    
    def __init__(self):
        """
        Initialize the BitBucketAnalyzeCommentAgent
        """

        name = "BitBucketAnalyzeCommentAgent"
        description = "Analyzes comments in pull requests"
        llm = ModelProvider().get_llm(
            model_name=os.getenv("MODEL_NAME",'azure_model_2'),
            temperature=0.1
        )
        BaseAgentLLM.__init__(
            self,
            name=name,
            description=description,
            llm=llm
        )
        BitbucketBaseAgent.__init__(
            self,
            name=name,
            description=description,
        )
        
       
       

    async def get_pull_request_comments(self, workspace: str, repo_slug: str, pr_id: str,bitbucket_client: BitbucketClient) -> List[Dict]:
        """
        Get all comments for a pull request.
        """
        return await bitbucket_client.get_pull_request_comments(workspace, repo_slug, pr_id)

    async def create_reply_comment(self, content: CreateReplyCommentModel, state: BitbucketState,bitbucket_client: BitbucketClient):
        """
        Create a reply comment on a pull request.
        Args:
            content: The content of the reply comment
            state: The state of the workflow
        Returns:
            The created reply comment
        """
        logger.info(f"💬 Creating reply comment: {content}")
        workspace = state.workspace_slug
        repo_slug = state.repository_slug
        pr_id = state.pull_request_id

        await bitbucket_client.create_reply_comment(
            workspace=workspace,
            repository=repo_slug,
            pr_id=pr_id,
            parent_id=content.parent_id,
            content=content.content
        )

    async def modify_comment(self, comment: ModifyCommentModel, state: BitbucketState,bitbucket_client: BitbucketClient):
        """
        Modify a comment on a pull request.
        """
        logger.info(f"💬 Modifying comment: {comment}")
        workspace = state.workspace_slug
        repo_slug = state.repository_slug
        pr_id = state.pull_request_id

        await bitbucket_client.update_comment(
            workspace=workspace,
            repository=repo_slug,
            pr_id=pr_id,
            comment_id=comment.comment_id,
            content=comment.content
        )

    async def analyze_comment(self, comments: List[Dict], state: BitbucketState) -> CommentAnalysisModel:
        """
        Analyze a single comment using the LLM and search tools.
        
        Args:
            comments: The comments to analyze
            state: The state of the workflow
        """
        

        prompt = self.system_prompt.format_messages(
            language_to_write='english',
            current_comment=state.comment,
            all_comments=comments,
            pr_data=state.pull_request_data,
            jira_results=state.context.get("jira_results",[])
        )

        
        # Create a tool extractor for the CreateReplyCommentModel
        extractor = self.create_extractor(
            tools=[CommentAnalysisModel,search_using_tavily],
            tool_choice='CommentAnalysisModel'
        )

        try:
            response = await extractor.ainvoke(prompt)
            return response["responses"][0]
        except Exception as e:
            logger.error(f"Error analyzing comment: {str(e)}")
            return {"error": str(e)}


    async def execute(self, state: BitbucketState) -> BitbucketState:
        """
        Execute the agent to analyze comments
        
        Args:
            state: Current workflow state with pull request data
            
        Returns:
            Updated workflow state with comment analysis results
        """
        logger.info("🔍 Starting comment analysis for PR")
        
        # Extract PR details from state
        workspace = state.workspace_slug
        repo_slug = state.repository_slug
        pr_id = state.pull_request_id

        if not all([workspace, repo_slug, pr_id]):
            logger.error("Missing required PR information")
            return Command(
                goto=END,
                update=state
            )

        bitbucket_client = self.get_bitbucket_client(state)

        # Get all comments for the PR
        comments = await self.get_pull_request_comments(workspace, repo_slug, pr_id,bitbucket_client)
        logger.info(f"📝 Retrieved {len(comments)} comments for analysis")


        analysis = await self.analyze_comment(comments=comments, state=state)
          

        if analysis.create_reply_comments:
            for reply_comment in analysis.create_reply_comments:
                await self.create_reply_comment(reply_comment, state, bitbucket_client)
        
        if analysis.modify_comments:
            for modify_comment in analysis.modify_comments:
                await self.modify_comment(modify_comment, state, bitbucket_client)
        
        # Move to next step
        state.messages.append(
            {
                "role": "user",
                "content": f"finished analyzing comment and pull request review {state.pull_request_id}"
            }
        )
        return Command(
            goto=END,
            update=state
        )
