"""
BitBucketAnalyzeCodeDiffAgent module for analyzing code diffs in pull requests.
"""

import os

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corelanggraph.langfuse import LangfuseClient
from corellm.providers import ModelProvider
from langfuse import get_client
from langgraph.types import Command
from logger import logger

from code_review.workflow.bitbucket.state import BitbucketState, ReviewResult

langfuse = LangfuseClient().client

class BitBucketAnalyzeCodeDiffAgent(BaseAgentLLM):
    """
    Agent responsible for analyzing code diffs in pull requests.
    This includes code quality assessment, detecting potential issues, 
    and providing suggestions for improvements.
    """
    
    def __init__(self):
        """
        Initialize the BitBucketAnalyzeCodeDiffAgent
        
        Args:
            model_name: Name of the LLM model to use for code analysis
            temperature: Temperature setting for the model
        """
        super().__init__(
            name="BitBucketAnalyzeCodeDiffAgent",
            description="Analyzes code diffs in pull requests",
            llm=ModelProvider().get_llm(model_name= os.getenv('MODEL_NAME','azure_model_2'), temperature=0.1)
        )


    async def execute(self, state: BitbucketState) -> BitbucketState:
        """
        Execute the agent to analyze code diffs
        
        Args:
            state: Current workflow state with pull request data
            
        Returns:
            Updated workflow state with analysis results
        """
        logger.info("🔍 Starting code diff analysis for PR")
        
        logger.debug(f"🔄 Creating extractor for review results")
        get_client().update_current_span(
            level="DEBUG",
            status_message=f"Enter into {self.metadata.name}"
        )

        extractor = self.create_extractor(
            tools=[ReviewResult],
            tool_choice='ReviewResult'
        )

        logger.info(f"📋 Formatting prompt with PR data of length: {len(state.pull_request_data) if state.pull_request_data else 0}")
        prompt = self.system_prompt.format_messages(
            language_to_write='english',
            input_diff=state.pull_request_diff,
            pull_request_data=state.pull_request_data,
            retrieved_documents=state.retrieved_documents,
            jira_results=state.context.get("jira_results",[])
        )

        logger.info("🤖 Invoking LLM for code analysis")
        try:
            response = await extractor.ainvoke(prompt)
            logger.info(f"✅ Successfully received analysis response")
        except Exception as e:
            logger.error(f"❌ Error during code analysis: {str(e)}")
            raise

        logger.debug(f"📊 Processing analysis results")
        state.review_result = response["responses"][0]
        
        logger.info(f"🏁 Code analysis complete - Review approval: {state.review_result.approval}")
        if state.review_result.comments:
            logger.info(f"💬 Generated {len(state.review_result.comments)} review comments")
        
        # Implementation will be added later
        
        logger.info("➡️ Moving to next step: create_comment")
        return Command(
            goto="create_comment",
            update=state
        )