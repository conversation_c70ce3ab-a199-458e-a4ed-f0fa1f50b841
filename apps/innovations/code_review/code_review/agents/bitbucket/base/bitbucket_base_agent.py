

from corelanggraph import BaseAgent

from code_review.integrations.bitbucket_client import <PERSON><PERSON><PERSON><PERSON><PERSON>
from code_review.workflow.bitbucket.state import BitbucketState


class BitbucketBaseAgent(BaseAgent):
    """
    Base class for all Bitbucket agents.
    """
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def get_bitbucket_client(self, state: BitbucketState) -> BitbucketClient:
        """
        Get a Bitbucket client using the provided username and app password
        
        Args:
            state: Current workflow state containing username and app password

        Returns:
            BitbucketClient instance configured with the provided credentials
        """
        return BitbucketClient(
            username=state.username,
            app_password=state.app_password
        )
