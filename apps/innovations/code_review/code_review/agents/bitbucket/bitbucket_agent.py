"""BitbucketAgent module for handling Bitbucket operations."""

import json
import os
from typing import Any, Dict, List, Union

from corelanggraph.agents.base_agent import BaseAgent
from corellm import ModelProvider
from langchain.agents import AgentExecutor, create_tool_calling_agent
from langchain_core.prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langchain_core.runnables import Run<PERSON>ble
from langchain_core.tools import tool
from logger import logger

from code_review.core.graph_manager import Agent<PERSON>tate
from code_review.integrations.bitbucket_client import BitbucketClient
from code_review.models.bitbucket_models import (
    CreatePullRequestModel,
    GetPullRequestModel,
    ListPullRequestsModel,
    MergePullRequestModel,
    UpdatePullRequestModel,
)

client = BitbucketClient(
            username=os.environ.get("BITBUCKET_USERNAME"),
            app_password=os.environ.get("BITBUCKET_PASSWORD"),
            api_base_url='https://api.bitbucket.org/2.0',
        )


@tool
async def approve_pull_request(data: Union[Dict[str, Any], GetPullRequestModel]) -> Dict[str, Any]:
    """
    Approve a pull request.

    Args:
        data: Model containing repository, workspace, and pr_id

    Returns:
        Dictionary with approval details
    """
    logger.info(f"👍 Approving pull request #{data.get('pr_id', 'unknown')} in {data.get('repository', 'unknown')}")
    if isinstance(data, dict):
        data = GetPullRequestModel(**data)

    result = await client.approve_pull_request(
        workspace=data.workspace,
        repository=data.repository,
        pr_id=data.pr_id,
    )
    logger.info(f"✅ Pull request #{data.pr_id} approved successfully")
    return result

@tool
async def request_changes(data: Union[Dict[str, Any], GetPullRequestModel]) -> Dict[str, Any]:
    """
    Request changes on a pull request.

    Args:
        data: Model containing repository, workspace, and pr_id

    Returns:
        Dictionary with approval details
    """
    logger.info(f"👍 Approving pull request #{data.get('pr_id', 'unknown')} in {data.get('repository', 'unknown')}")
    if isinstance(data, dict):
        data = GetPullRequestModel(**data)

    result = await client.request_changes(
        workspace=data.workspace,
        repository=data.repository,
        pr_id=data.pr_id,
    )
    logger.info(f"✅ Pull request #{data.pr_id} approved successfully")
    return result

@tool
async def create_pull_request(data: Union[Dict[str, Any], CreatePullRequestModel]) -> Dict[str, Any]:
    """
    Create a new pull request in Bitbucket.

    Args:
        data: Pull request data containing repository, workspace, title, source_branch, etc.

    Returns:
        Dictionary with pull request details
    """
    logger.info(f"Creating pull request: {data}")
    if isinstance(data, dict):
        data = CreatePullRequestModel(**data)

    result = await client.create_pull_request(
        workspace=data.workspace,
        repository=data.repository,
        title=data.title,
        source_branch=data.source_branch,
        destination_branch=data.destination_branch,
        description=data.description,
        close_source_branch=data.close_source_branch,
        reviewers=data.reviewers,
    )
    return result
@tool
async def update_pull_request(data: Union[Dict[str, Any], UpdatePullRequestModel]) -> Dict[str, Any]:
    """
    Update an existing pull request in Bitbucket.

    Args:
        data: Pull request update data containing repository, workspace, pr_id, etc.

    Returns:
        Dictionary with updated pull request details
    """
    logger.info(f"Updating pull request: {data}")
    if isinstance(data, dict):
        data = UpdatePullRequestModel(**data)

    result = await client.update_pull_request(
        workspace=data.workspace,
        repository=data.repository,
        pr_id=data.pr_id,
        title=data.title,
        description=data.description,
        reviewers=data.reviewers,
        state=data.state,
    )
    return result


@tool
async def get_pull_request( data: Union[Dict[str, Any], GetPullRequestModel]) -> Dict[str, Any]:
    """
    Get details of a specific pull request.

    Args:
        data: Model containing repository, workspace, and pr_id

    Returns:
        Dictionary with pull request details
    """
    logger.info(f"Getting pull request: {data}")
    if isinstance(data, dict):
        data = GetPullRequestModel(**data)

    result =  await client.get_pull_request(
        workspace=data.workspace,
        repository=data.repository,
        pr_id=data.pr_id,
    )
    return result

@tool
async def list_pull_requests( data: Union[Dict[str, Any], ListPullRequestsModel]) -> List[Dict[str, Any]]:
    """
    List pull requests in a repository.

    Args:
        data: Model containing repository, workspace, and optional filters

    Returns:
        List of dictionaries with pull request details
    """
    logger.info(f"Listing pull requests: {data}")
    if isinstance(data, dict):
        data = ListPullRequestsModel(**data)

    query_params = {}
    if data.state:
        query_params["state"] = data.state
    if data.sort_by:
        query_params["sort"] = data.sort_by
    if data.sort_direction:
        query_params["direction"] = data.sort_direction
    if data.limit:
        query_params["limit"] = data.limit

    result = await client.list_pull_requests(
        workspace=data.workspace,
        repository=data.repository,
        query_params=query_params,
    )
    return result

@tool
async def merge_pull_request( data: Union[Dict[str, Any], MergePullRequestModel]) -> Dict[str, Any]:
    """
    Merge a pull request.

    Args:
        data: Model containing repository, workspace, pr_id, and close_source_branch flag

    Returns:
        Dictionary with merged pull request details
    """
    logger.info(f"Merging pull request: {data}")
    if isinstance(data, dict):
        data = MergePullRequestModel(**data)

    result = await client.merge_pull_request(
        workspace=data.workspace,
        repository=data.repository,
        pr_id=data.pr_id,
        close_source_branch=data.close_source_branch,
    )
    return result

@tool
async def decline_pull_request( data: Union[Dict[str, Any], GetPullRequestModel]) -> Dict[str, Any]:
    """
    Decline a pull request.

    Args:
        data: Model containing repository, workspace, and pr_id

    Returns:
        Dictionary with declined pull request details
    """
    logger.info(f"Declining pull request: {data}")  
    if isinstance(data, dict):
        data = DeclinePullRequestModel(**data)

    result = await client.decline_pull_request(
        workspace=data.workspace,
        repository=data.repository,
        pr_id=data.pr_id,
    )
    return result



class BitbucketAgent(BaseAgent):
    """Agent for interacting with Bitbucket API."""

    def __init__(
        self,
    ):
        """
        Initialize the BitbucketAgent.

        Args:
        """
        super().__init__(
            name="bitbucket_agent",
            description="Agent for interacting with Bitbucket API to manage pull requests and code reviews"
        )


        self.metadata.capabilities=[
                "Create, update, merge, and decline pull requests",
                "List and retrieve pull request details",
                "Add comments to pull requests",
                "Add inline comments to specific lines in pull requests",
                "Perform code reviews and provide feedback",
                "Request changes on pull requests",
                "Analyze code diffs for quality, security, and performance issues"
            ]
        self.metadata.requires_auth=True
        self.metadata.version="0.2.0"
        
        
        self.llm = ModelProvider().get_llm(model_name=os.getenv('MODEL_NAME','azure_model_2'), temperature=0.1)
        
        # Use provided credentials or fall back to environment variables
       
        # Initialize tools as a list directly
        self.tools = [
            create_pull_request,
            update_pull_request,
            get_pull_request,
            list_pull_requests,
            merge_pull_request,
            decline_pull_request,
            request_changes,
         
        ]
        
        # Create the agent chain
        self.agent = self._create_agent()

    def _create_agent(self) -> Runnable:
        """
        Create the agent chain with tools.

        Returns:
            The configured agent chain
        """
        # Use the tools directly from the list
        available_tools = self.tools
        
        # Create the prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", """<n>BitbucketAgent</n>
                <role>You are a specialized BitbucketAgent that helps users manage pull requests and other Bitbucket operations.</role>

                <objectives>
                    - Help users manage Bitbucket pull requests and operations
                    - Provide tools for creating, updating, and managing pull requests
                    - Conduct thorough code reviews and provide meaningful feedback
                    - Identify security issues and code quality concerns
                    - Help maintain high code standards through detailed analysis
                </objectives>

                <capabilities>
                    - Creating, updating, retrieving, and listing pull requests
                    - Merging or declining pull requests
                    - Adding comments to pull requests
                    - Adding inline comments to specific lines in pull requests
                    - Retrieving pull request diffs for code review
                    - Performing code reviews and providing feedback
                </capabilities>

                <tool_usage_examples>
                When performing a code review, ALWAYS provide the required parameters. For example:
                
                1. To get a pull request:
                   get_pull_request({{"repository": "my-repo", "workspace": "my-workspace", "pr_id": 123}})
                
                2. To analyze a pull request:
                   analyze_code_diff({{"repository": "my-repo", "workspace": "my-workspace", "pr_id": 123, "focus_areas": ["security", "performance"]}})
                
                3. To request changes:
                   request_changes({{"repository": "my-repo", "workspace": "my-workspace", "pr_id": 123, "change_requests": ["Fix security issue in line 45"], "summary": "Security issues found"}})
                </tool_usage_examples>
                
                <code_review_guidelines>
                When performing code reviews:
                <step1>Use the tool - > get_pull_request_diff : to analyze changes </step1>
                <step 2> ANALYZE THE CODE DIFFS AND SEARCH FOR:  
                - Code quality (  for example refactor, decomposing functions, etc )
                - Coding style issues (for example bad naming, etc)
                - Potential bugs or logic errors  
                - Security vulnerabilities (hardcoded secrets, etc)
                - Performance concerns (optimize for example nested loops, etc)
                - Missing tests or documentation ( each function must have a docstring and unit tests ) 
                </step2>  
                <step3>Provide constructive feedback with specific suggestions</step3>  
                <step4> after analysing the code must take a decision if the PR can be merged or not</step4>  
                    <step 4.1>*IMPORTANT*: if the PR do not meet the quality standards, add a comment() to the PR with the suggested changes and set the status to CHANGE REQUESTED</step4.1>  
                        <step 4.1.1> write a comment with the suggested changes and the reason of the change request the comment must be in the correct place in the PR</step4.1.1>  
                            *IMPORTANT*: The response must be a JSON object where the key for each piece of feedback is the filename and line number in the file where the feedback must be left, and the value is the feedback itself as a string.
                        <step 4.1.2>AUTOMATICALLY block the PR (set status to CHANGE REQUESTED) when bad code or security issues are found</step4.1.2>
                    <step 4.2> if the PR meet the quality standards set the status to APPROVED</step4.2>   
                <step7>ALWAYS update the PR description with a review summary</step7>  
 
                </code_review_guidelines>

                <summary_of_important_behaviors>
                1. Every code review MUST update the PR description with a summary of findings
                2. When issues are found, ALWAYS add detailed comments explaining the problems
                3. When security issues or critical problems are found, AUTOMATICALLY block the PR
                </summary_of_important_behaviors>
                """),
            MessagesPlaceholder(variable_name="chat_history", optional=True),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])
        
        # Create the agent using latest LangChain patterns

        agent = create_tool_calling_agent(self.llm, available_tools, prompt)
        agent_executor = AgentExecutor(
            agent=agent, 
            tools=available_tools, 
            verbose=True,
            return_intermediate_steps=True,
        )
        
        return agent_executor

      
    async def execute(self, state: AgentState) -> AgentState:
        """
        Execute the BitbucketAgent.
        
        Args:
            state: The current state of the agent
            
        Returns:
            The updated state
        """
        
        # Get the messages from the state
        messages = state.context.get("messages", [])
        user_input = messages[-1]["content"] if messages else ""
        bitbucket_tasks = self.extract_bitbucket_task_context(state)
        
        try:
            # Format the task information as a string for the input
            task_description = ""
            if bitbucket_tasks:
                task_description = "Tasks to perform:\n"
                for i, task in enumerate(bitbucket_tasks):
                    task_description += f"{i+1}. {task.get('description', '')}\n"
                    if 'parameters' in task:
                        task_description += f"   Parameters: {json.dumps(task.get('parameters', {}))}\n"
            
            # Log available tools for debugging
            tool_names = [getattr(tool, 'name', str(tool)) for tool in self.tools]
            logger.debug(f"BitbucketAgent has the following tools available: {', '.join(tool_names)}")
            
            # Run the agent with the task description as input
            result = await self.agent.ainvoke({
                "input": f"{user_input}\n\n{task_description}".strip()
            })
            
            # Store the result in the context
            state.context["bitbucket_result"] = result.get("output", "")
            state.context["bitbucket_steps"] = result.get("intermediate_steps", [])
        
            state.next_steps = ["end"] 
            
            if "next_steps" in result:
                state.next_steps = result["next_steps"]
            
        except Exception as e:
            logger.error(f"Error executing Bitbucket agent: {str(e)}")
            state.error = f"Failed to execute Bitbucket agent: {str(e)}"
            state.next_steps = ["error_handler"]
        
        return state

    
    def extract_bitbucket_task_context(self, state: AgentState) -> Dict[str, Any]:
        bitbucket_tasks = []
        for task in state.context.get("tasks", []):
            if task.get("agent") == "bitbucket_agent":
                bitbucket_tasks.append(task)
        return bitbucket_tasks
    