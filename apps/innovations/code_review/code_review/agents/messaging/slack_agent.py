"""
Slack Agent

This agent handles interactions with <PERSON><PERSON>ck, including sending
notifications and processing Slack commands.
"""

import json
from typing import Any, Dict, List, Optional

from corelanggraph.agents.base_agent import BaseAgent
from langchain.prompts.chat import Chat<PERSON><PERSON>ptT<PERSON>plate
from langchain_openai import <PERSON>t<PERSON><PERSON><PERSON><PERSON>
from pydantic import BaseModel

from code_review.core.graph_manager import AgentState
from code_review.integrations.slack_client import SlackClient


class SlackMessage(BaseModel):
    """Model representing a Slack message"""
    channel: str
    text: str
    blocks: Optional[List[Dict[str, Any]]] = None
    thread_ts: Optional[str] = None
    attachments: Optional[List[Dict[str, Any]]] = None
    unfurl_links: bool = False
    unfurl_media: bool = True


class SlackOperation(BaseModel):
    """Model representing a Slack operation"""
    operation_type: str  # send_message, update_message, post_ephemeral, etc.
    message: SlackMessage
    user_id: Optional[str] = None  # For ephemeral messages


class SlackAgent(BaseAgent):
    """
    Agent for interacting with <PERSON>la<PERSON>.
    <PERSON>les sending notifications and processing Slack commands.
    """
    
    def __init__(
        self,
        slack_token: str,
        default_channel: str = "general",
        model_name: str = "gpt-4o"
    ):
        """
        Initialize the Slack agent
        
        Args:
            slack_token: Slack API token
            default_channel: Default channel to send messages to
            model_name: Name of the LLM model to use
        """
        super().__init__(
            name="slack_agent",
            description="Sends notifications and processes Slack commands"
        )
        
        # Add Slack-specific capabilities
        self.metadata.capabilities.extend([
            "send_notification",
            "process_command",
            "update_message",
            "post_ephemeral"
        ])
        self.metadata.requires_auth = True
        
        # Initialize Slack client
        self.slack_client = SlackClient(slack_token=slack_token)
        self.default_channel = default_channel
        
        # Initialize LLM
        self.model = ChatOpenAI(model=model_name, temperature=0)
        
        # Create the Slack prompt
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """
            You are a Slack Agent that interacts with Slack on behalf of users.
            Your job is to:
            1. Understand the user's Slack-related request
            2. Translate it into a specific Slack operation
            3. Format the operation as JSON for execution
            
            Respond with JSON:
            {{
                "analysis": "Your understanding of the request",
                "operations": [
                    {{
                        "operation_type": "send_message|update_message|post_ephemeral",
                        "message": {{
                            "channel": "channel_name",
                            "text": "Message text",
                            "blocks": [], # Optional Slack blocks
                            "thread_ts": "timestamp", # Optional for threading
                            "attachments": [] # Optional attachments
                        }},
                        "user_id": "U12345" # Only for ephemeral messages
                    }}
                ],
                "next_steps": ["agent_name", "end"]
            }}
            """),
            ("user", "{user_input}")
        ])
    
    async def execute(self, state: AgentState) -> AgentState:
        """
        Execute the Slack agent's core functionality
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state
        """
        # Find Slack tasks from state
        slack_tasks = []
        for task in state.context.get("tasks", []):
            if task.get("agent") == "slack_agent":
                slack_tasks.append(task)
        
        # Extract the task description if available
        task_input = ""
        if slack_tasks:
            task_input = "\n".join([task.get("description", "") for task in slack_tasks])
        else:
            # If no specific tasks, use the last message
            task_input = state.messages[-1]["content"] if state.messages else ""
        
        # Run the Slack LLM to determine operations
        chain = self.prompt | self.model | (lambda x: x.content)
        
        response = await chain.ainvoke({"user_input": task_input})
        
        try:
            # Parse operations
            data = json.loads(response)
            operations = []
            
            for op_data in data.get("operations", []):
                message_data = op_data.get("message", {})
                
                # Set default channel if not specified
                if "channel" not in message_data:
                    message_data["channel"] = self.default_channel
                
                # Create SlackMessage
                message = SlackMessage(**message_data)
                
                # Create SlackOperation
                operation = SlackOperation(
                    operation_type=op_data.get("operation_type", "send_message"),
                    message=message,
                    user_id=op_data.get("user_id")
                )
                
                operations.append(operation)
            
            next_steps = data.get("next_steps", ["end"])
            
            # Execute operations
            results = []
            for op in operations:
                if op.operation_type == "send_message":
                    result = await self.slack_client.send_message(
                        channel=op.message.channel,
                        text=op.message.text,
                        blocks=op.message.blocks,
                        thread_ts=op.message.thread_ts,
                        attachments=op.message.attachments,
                        unfurl_links=op.message.unfurl_links,
                        unfurl_media=op.message.unfurl_media
                    )
                    results.append({"operation": "send_message", "result": result})
                    
                elif op.operation_type == "update_message":
                    result = await self.slack_client.update_message(
                        channel=op.message.channel,
                        ts=op.message.thread_ts,
                        text=op.message.text,
                        blocks=op.message.blocks,
                        attachments=op.message.attachments
                    )
                    results.append({"operation": "update_message", "result": result})
                    
                elif op.operation_type == "post_ephemeral":
                    if not op.user_id:
                        raise ValueError("User ID is required for ephemeral messages")
                    
                    result = await self.slack_client.post_ephemeral(
                        channel=op.message.channel,
                        user=op.user_id,
                        text=op.message.text,
                        blocks=op.message.blocks,
                        attachments=op.message.attachments
                    )
                    results.append({"operation": "post_ephemeral", "result": result})
            
            # Update state with results
            state.context["slack_results"] = results
            state.next_steps = next_steps
            
        except (json.JSONDecodeError, ValueError) as e:
            state.error = f"Failed to process Slack operations: {str(e)}"
            state.next_steps = ["error_handler"]
        
        return state 