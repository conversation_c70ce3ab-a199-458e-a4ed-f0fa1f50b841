"""
Error Handler Agent

This agent handles errors that occur during workflow execution,
providing error recovery strategies and detailed error reporting.
"""

import asyncio
import json
import os
from typing import Any, Dict, Optional

from corelanggraph.agents.base_agent import BaseAgent
from langchain.prompts.chat import Chat<PERSON>romptTemplate
from langchain_openai import Chat<PERSON><PERSON>A<PERSON>
from pydantic import BaseModel

from code_review.core.graph_manager import AgentState


class ErrorStrategy(BaseModel):
    """Model representing an error recovery strategy"""
    strategy_type: str  # retry, fallback, abort, notify
    agent_name: Optional[str] = None  # Agent to retry or fallback to
    max_retries: int = 1  # Maximum number of retries
    delay_seconds: int = 0  # Delay between retries
    message: Optional[str] = None  # Message for notification
    fallback_action: Optional[Dict[str, Any]] = None  # Fallback action to take


class ErrorHandlerAgent(BaseAgent):
    """
    Agent for handling errors in the workflow.
    Provides error recovery strategies and detailed error reporting.
    """
    
    def __init__(
        self,
        model_name: str = "gpt-4",
        temperature: float = 0
    ):
        """
        Initialize the error handler agent
        
        Args:
            model_name: Name of the LLM model to use
            temperature: Temperature for LLM generation
        """
        super().__init__(
            name="error_handler",
            description="Handles errors and provides recovery strategies"
        )
        
        # Add error handler-specific capabilities
        self.metadata.capabilities.extend([
            "error_analysis",
            "error_recovery",
            "error_reporting"
        ])
        
        # Initialize LLM
        self.model = ChatOpenAI(model=model_name, temperature=temperature, api_key= os.getenv("OPENAI_API_KEY"))
        
        # Create the error handler prompt
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """
            You are an Error Handler Agent that analyzes and recovers from errors in a multi-agent system.
            Your job is to:
            1. Analyze the error that occurred
            2. Determine the best recovery strategy
            3. Provide detailed error information for reporting
            
            Current workflow state:
            {current_state}
            
            Error that occurred:
            {error}
            
            Available agents: {available_agents}
            
            Respond with JSON:
            {{
                "analysis": "Your analysis of the error",
                "error_type": "Type of error (e.g., API error, authentication error, etc.)",
                "recovery_strategy": {{
                    "strategy_type": "retry|fallback|abort|notify",
                    "agent_name": "Agent to retry or fallback to (if applicable)",
                    "max_retries": 1,
                    "delay_seconds": 0,
                    "message": "Message for notification",
                    "fallback_action": {{}} // Optional fallback action
                }},
                "report": "Detailed error report for logging",
                "next_steps": ["agent_name", "end"]
            }}
            """),
            ("user", "Handle the error in the current workflow.")
        ])
    
    async def execute(self, state: AgentState) -> AgentState:
        """
        Execute the error handler agent's core functionality
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state
        """
        # Extract error information
        error = state.error
        
        if not error:
            # No error to handle, just pass through
            state.next_steps = ["end"]
            return state
        
        # Format the current state for context
        current_state = json.dumps(state.context, indent=2)
        
        # Get available agents from context or use empty list
        available_agents = state.context.get("available_agents", [])
        
        # Run the error handler LLM
        chain = self.prompt | self.model | (lambda x: x.content)
        
        response = await chain.ainvoke({
            "current_state": current_state,
            "error": error,
            "available_agents": ", ".join(available_agents)
        })
        
        try:
            # Parse response as JSON
            handler_output = json.loads(response)
            
            # Update state with error analysis
            state.context["error_analysis"] = handler_output.get("analysis", "")
            state.context["error_type"] = handler_output.get("error_type", "unknown")
            state.context["error_report"] = handler_output.get("report", "")
            
            # Get recovery strategy
            recovery_strategy = handler_output.get("recovery_strategy", {})
            strategy = ErrorStrategy(**recovery_strategy)
            
            # Apply recovery strategy
            if strategy.strategy_type == "retry":
                # Retry the failed agent
                if strategy.agent_name:
                    state.context["retry_count"] = state.context.get("retry_count", 0) + 1
                    if state.context["retry_count"] <= strategy.max_retries:
                        # Add delay if specified
                        if strategy.delay_seconds > 0:
                            await asyncio.sleep(strategy.delay_seconds)
                        # Set next step to retry the agent
                        state.next_steps = [strategy.agent_name]
                    else:
                        # Max retries exceeded, abort
                        state.context["retry_exceeded"] = True
                        state.next_steps = ["end"]
                else:
                    # No agent specified, abort
                    state.next_steps = ["end"]
                    
            elif strategy.strategy_type == "fallback":
                # Fallback to another agent
                if strategy.agent_name:
                    state.context["fallback_action"] = strategy.fallback_action
                    state.next_steps = [strategy.agent_name]
                else:
                    # No fallback agent specified, abort
                    state.next_steps = ["end"]
                    
            elif strategy.strategy_type == "notify":
                # Just notify about the error and continue
                state.context["notification"] = strategy.message
                state.next_steps = ["end"]
                
            else:  # abort or unknown strategy
                # Abort the workflow
                state.next_steps = ["end"]
            
            # Clear the error if we're continuing
            if state.next_steps and state.next_steps[0] != "end":
                state.error = None
            
        except (json.JSONDecodeError, ValueError) as e:
            # Error in error handler, just end the workflow
            state.context["error_handler_error"] = str(e)
            state.next_steps = ["end"]
        
        return state 