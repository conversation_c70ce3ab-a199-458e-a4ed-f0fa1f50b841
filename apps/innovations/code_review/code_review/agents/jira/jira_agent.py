"""
Jira Agent

This agent handles interactions with <PERSON><PERSON>, including creating,
updating, and querying tickets.
"""

import json
import os
from typing import Any, Dict, List, Optional

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm import ModelProvider
from langgraph.types import Command
from pydantic import BaseModel, Field

from code_review.core.graph_manager import Agent<PERSON><PERSON>
from code_review.integrations.jira_client import JiraClient


class JiraOperation(BaseModel):
    """Model representing a Jira operation"""
    operation_type: str  = Field(description="The type of operation to perform",examples=["create", "update", "query", "search"])
    issue_key: Optional[str] = Field(description="The key of the issue to update",examples=["KEY-123"])
    project_key: Optional[str] = Field(description="The key of the project to create the issue in",examples=["PROJ"])
    summary: Optional[str] = Field(description="The summary of the issue",examples=["Issue summary"])
    description: Optional[str] = Field(description="The description of the issue",examples=["Issue description"])
    issue_type: Optional[str] = Field(description="The type of issue to create",examples=["Bug", "Task", "Story"])
    assignee: Optional[str] = Field(description="The assignee of the issue",examples=["user"])
    priority: Optional[str] = Field(description="The priority of the issue",examples=["High", "Medium", "Low"])
    status: Optional[str] = Field(description="The status of the issue",examples=["To Do", "In Progress", "Done"])
    fields: Dict[str, Any] = Field(default_factory=dict)
    jql: Optional[str] = Field(description="The JQL query to search for issues",examples=["project = PROJ AND status = 'Open'"])


class JiraOperations(BaseModel):
    """Model representing a list of Jira operations"""
    operations: List[JiraOperation] = Field(description="The list of Jira operations to perform")

class JiraAgent(BaseAgentLLM):
    """
    Agent for interacting with Jira.
    Handles creating, updating, and querying Jira tickets.
    """
    
    def __init__(
        self,
    ):
        """
        Initialize the Jira agent
        
        Args:
     
        """
        super().__init__(
            name="JiraAgent",
            description="Creates, updates, and queries Jira tickets",
            llm= ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME",'azure_model_2'),
                temperature=0.1
            )
        )
        
        # Add Jira-specific capabilities
        self.metadata.capabilities.extend([
            "create_issue",
            "update_issue",
            "query_issue",
            "search_issues"
        ])
        self.metadata.requires_auth = True
        
        # Initialize Jira client
        self.jira_client = JiraClient()
        
        
    
    async def _parse_jira_operations(self, text: str) -> List[JiraOperation]:
        """Parse Jira operations from LLM output"""
        try:
            data = json.loads(text)
            operations = []
            
            for op_data in data.get("operations", []):
                operations.append(JiraOperation(**op_data))
            
            return operations, data.get("next_steps", ["end"])
        except (json.JSONDecodeError, ValueError) as e:
            raise ValueError(f"Failed to parse Jira operations: {str(e)}")
    


    async def execute(self, state: AgentState) -> AgentState:
        """
        Execute the Jira agent's core functionality
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state
        """
        # Find Jira tasks from state
        
        # If no specific tasks, use the last message
        task_input = state.messages[-1]["content"] if state.messages else ""
        
    

        extractor = self.create_extractor(
            tools=[JiraOperations],
            tool_choice='JiraOperations'
        )

        prompt = self.system_prompt
        prompt.extend(state.messages)
        prompt = prompt.format_messages(
            user_input=task_input
        )
    
        result = await extractor.ainvoke(prompt)
        
        try:
            # Parse operations
            jira_operations:JiraOperations = result["responses"][0]
            operations = jira_operations.operations
            # Execute operations
            results = []
            for op in operations:
                if op.operation_type == "create":
                    result = await self.jira_client.create_issue(
                        project_key=op.project_key,
                        summary=op.summary,
                        description=op.description,
                        issue_type=op.issue_type,
                        fields=op.fields
                    )
                    results.append({"operation": "create", "result": result})
                    
                elif op.operation_type == "update":
                    result = await self.jira_client.update_issue(
                        issue_key=op.issue_key,
                        fields={
                            "summary": op.summary,
                            "description": op.description,
                            "assignee": op.assignee,
                            "priority": op.priority,
                            "status": op.status,
                            **op.fields
                        }
                    )
                    results.append({"operation": "update", "result": result})
                    
                elif op.operation_type == "query":
                    result = await self.jira_client.get_issue(issue_key=op.issue_key)
                    results.append({"operation": "query", "result": result})
                    
                elif op.operation_type == "search":
                    result = await self.jira_client.search_issues(jql=op.jql)
                    results.append({"operation": "search", "result": result})
            
            # Update state with results
            state.context["jira_results"] = results

            state.messages.append({
                "role": "assistant",
                "content": f"Jira operations completed saved in context"
            })
            
            return Command(
                update=state,
                goto='supervisor_agent',
            )
            
        except (json.JSONDecodeError, ValueError) as e:
            state.error = f"Failed to process Jira operations: {str(e)}"
            state.messages.append({
                "role": "assistant",
                "content": f"Jira operations failed with error {state.error} skipping to supervisor agent"
            })
            return Command(
                update=state,
                goto='supervisor_agent',
            )