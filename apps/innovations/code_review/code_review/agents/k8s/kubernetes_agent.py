"""
Kubernetes Agent

This agent handles interactions with Kubernetes, including deploying
and monitoring jobs and services.
"""

import json
from typing import Any, Dict, Optional

from corelanggraph.agents.base_agent import BaseAgent
from langchain.prompts.chat import Chat<PERSON>romptTemplate
from langchain_openai import Chat<PERSON>penAI
from pydantic import BaseModel, <PERSON>

from code_review.core.graph_manager import AgentState
from code_review.integrations.kubernetes_client import KubernetesClient


class KubernetesResource(BaseModel):
    """Model representing a Kubernetes resource"""
    kind: str  # Deployment, Job, Service, etc.
    name: str
    namespace: str = "default"
    spec: Dict[str, Any] = Field(default_factory=dict)
    labels: Dict[str, str] = Field(default_factory=dict)
    annotations: Dict[str, str] = Field(default_factory=dict)


class KubernetesOperation(BaseModel):
    """Model representing a Kubernetes operation"""
    operation_type: str  # create, update, delete, get, list, apply_yaml
    resource: Optional[KubernetesResource] = None
    yaml_content: Optional[str] = None
    selector: Optional[str] = None
    field_selector: Optional[str] = None
    timeout_seconds: int = 300


class KubernetesAgent(BaseAgent):
    """
    Agent for interacting with Kubernetes.
    Handles deploying and monitoring jobs and services.
    """
    
    def __init__(
        self,
        kubeconfig_path: Optional[str] = None,
        context_name: Optional[str] = None,
        default_namespace: str = "default",
        model_name: str = "gpt-4o"
    ):
        """
        Initialize the Kubernetes agent
        
        Args:
            kubeconfig_path: Path to kubeconfig file
            context_name: Kubernetes context name
            default_namespace: Default namespace
            model_name: Name of the LLM model to use
        """
        super().__init__(
            name="kubernetes_agent",
            description="Deploys and monitors jobs and services in Kubernetes"
        )
        
        # Add Kubernetes-specific capabilities
        self.metadata.capabilities.extend([
            "deploy_job",
            "deploy_service",
            "monitor_resources",
            "delete_resources",
            "get_logs",
            "apply_yaml"
        ])
        self.metadata.requires_auth = True
        
        # Initialize Kubernetes client
        self.kubernetes_client = KubernetesClient(
            kubeconfig_path=kubeconfig_path,
            context_name=context_name
        )
        self.default_namespace = default_namespace
        
        # Initialize LLM
        self.model = ChatOpenAI(model=model_name, temperature=0)
        
        # Create the Kubernetes prompt
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """
            You are a Kubernetes Agent that interacts with Kubernetes on behalf of users.
            Your job is to:
            1. Understand the user's Kubernetes-related request
            2. Translate it into a specific Kubernetes operation
            3. Format the operation as JSON for execution
            
            Respond with JSON:
            {{
                "analysis": "Your understanding of the request",
                "operations": [
                    {{
                        "operation_type": "create|update|delete|get|list|apply_yaml",
                        "resource": {{
                            "kind": "Deployment|Job|Service|ConfigMap|Secret",
                            "name": "resource-name",
                            "namespace": "namespace-name",
                            "spec": {{
                                // Resource specification
                            }},
                            "labels": {{"key": "value"}},
                            "annotations": {{"key": "value"}}
                        }},
                        "yaml_content": "YAML content for apply_yaml operation",
                        "selector": "label selector for list operation",
                        "field_selector": "field selector for list operation",
                        "timeout_seconds": 300
                    }}
                ],
                "next_steps": ["agent_name", "end"]
            }}
            """),
            ("user", "{user_input}")
        ])
    
    async def execute(self, state: AgentState) -> AgentState:
        """
        Execute the Kubernetes agent's core functionality
        
        Args:
            state: Current workflow state
            
        Returns:
            Updated workflow state
        """
        # Find Kubernetes tasks from state
        kubernetes_tasks = []
        for task in state.context.get("tasks", []):
            if task.get("agent") == "kubernetes_agent":
                kubernetes_tasks.append(task)
        
        # Extract the task description if available
        task_input = ""
        if kubernetes_tasks:
            task_input = "\n".join([task.get("description", "") for task in kubernetes_tasks])
        else:
            # If no specific tasks, use the last message
            task_input = state.messages[-1]["content"] if state.messages else ""
        
        # Run the Kubernetes LLM to determine operations
        chain = self.prompt | self.model | (lambda x: x.content)
        
        response = await chain.ainvoke({"user_input": task_input})
        
        try:
            # Parse operations
            data = json.loads(response)
            operations = []
            
            for op_data in data.get("operations", []):
                # Set default namespace if not specified
                if "resource" in op_data and "namespace" not in op_data["resource"]:
                    op_data["resource"]["namespace"] = self.default_namespace
                
                # Create KubernetesOperation
                operation = KubernetesOperation(**op_data)
                operations.append(operation)
            
            next_steps = data.get("next_steps", ["end"])
            
            # Execute operations
            results = []
            for op in operations:
                if op.operation_type == "create":
                    if not op.resource:
                        raise ValueError("Resource is required for create operation")
                    
                    result = await self.kubernetes_client.create_resource(
                        kind=op.resource.kind,
                        name=op.resource.name,
                        namespace=op.resource.namespace,
                        spec=op.resource.spec,
                        labels=op.resource.labels,
                        annotations=op.resource.annotations
                    )
                    results.append({"operation": "create", "result": result})
                    
                elif op.operation_type == "update":
                    if not op.resource:
                        raise ValueError("Resource is required for update operation")
                    
                    result = await self.kubernetes_client.update_resource(
                        kind=op.resource.kind,
                        name=op.resource.name,
                        namespace=op.resource.namespace,
                        spec=op.resource.spec,
                        labels=op.resource.labels,
                        annotations=op.resource.annotations
                    )
                    results.append({"operation": "update", "result": result})
                    
                elif op.operation_type == "delete":
                    if not op.resource:
                        raise ValueError("Resource is required for delete operation")
                    
                    result = await self.kubernetes_client.delete_resource(
                        kind=op.resource.kind,
                        name=op.resource.name,
                        namespace=op.resource.namespace
                    )
                    results.append({"operation": "delete", "result": result})
                    
                elif op.operation_type == "get":
                    if not op.resource:
                        raise ValueError("Resource is required for get operation")
                    
                    result = await self.kubernetes_client.get_resource(
                        kind=op.resource.kind,
                        name=op.resource.name,
                        namespace=op.resource.namespace
                    )
                    results.append({"operation": "get", "result": result})
                    
                elif op.operation_type == "list":
                    if not op.resource:
                        raise ValueError("Resource is required for list operation")
                    
                    result = await self.kubernetes_client.list_resources(
                        kind=op.resource.kind,
                        namespace=op.resource.namespace,
                        selector=op.selector,
                        field_selector=op.field_selector
                    )
                    results.append({"operation": "list", "result": result})
                    
                elif op.operation_type == "apply_yaml":
                    if not op.yaml_content:
                        raise ValueError("YAML content is required for apply_yaml operation")
                    
                    result = await self.kubernetes_client.apply_yaml(
                        yaml_content=op.yaml_content,
                        namespace=op.resource.namespace if op.resource else self.default_namespace
                    )
                    results.append({"operation": "apply_yaml", "result": result})
            
            # Update state with results
            state.context["kubernetes_results"] = results
            state.next_steps = next_steps
            
        except (json.JSONDecodeError, ValueError) as e:
            state.error = f"Failed to process Kubernetes operations: {str(e)}"
            state.next_steps = ["error_handler"]
        
        return state 