import os
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field

from code_review.core.graph_manager import AgentState


class CommentContent(BaseModel):
    raw: Optional[str] = Field(description="The comment content")

class CommentInline(BaseModel):
    from_: Optional[int] = Field( alias="from", description="The comment's anchor line in the old version of the file. If the Comment is related to new code, this value will be None")
    to: Optional[int] = Field( description="The comment's anchor line in the new version of the file. if the Comment is related to old code, this value will be None")
    path: str = Field( description="The path of the file this comment is anchored to.")


class CreateCommentModel(BaseModel):
    content: CommentContent = Field(description="The content of the comment.")
    inline: Optional[CommentInline] = Field(description="The comment's inline anchor information if its a file line comment.")
    

class CreateCommentModelList(BaseModel):
    """
    List of CreateCommentModel
    """
    comments: List[CreateCommentModel] = Field(description="The comments of the review", default_factory=list)

class Comment(BaseModel):
    """
    A comment on a pull request
    """
    text: str = Field(description="The text of the comment")
    file_path: Optional[str] = Field(None, description="The path of the file if its a file comment")
    line_number: Optional[int] = Field(None, description="The line number of the comment if its a line comment")

class ReviewResult(BaseModel):
    """
    Result of the review
    """
    comments: List[Comment] = Field(description="The comments of the review", default_factory=list)
    approval: bool = Field(description="The approval of the review", default=True)
    description: str = Field(description="The pull request description", default="")

class BitbucketState(AgentState):
    """
    State for the Bitbucket workflow.
    """
    pull_request_id: str = Field(description="The ID of the pull request")
    repository_slug: str = Field(description="The slug of the repository")
    workspace_slug: str = Field(description="The slug of the workspace")

    username: str = Field(default=os.getenv("BITBUCKET_USERNAME"),description="The username of the Bitbucket user")
    app_password: str = Field(default=os.getenv("BITBUCKET_PASSWORD"),description="The app password of the Bitbucket user")

    mission_id: Optional[str] = Field(None, description="The ID of the mission")

    pull_request_data: Optional[Dict[str, Any]] = Field(None,description="The data of the pull request")
    pull_request_diff: Optional[str] = Field(None,description="The diff of the pull request")
    review_result: Optional[ReviewResult] = Field(description="The result of the analysis", default=None)

    comment: Optional[Dict[str, Any]] = Field(None, description="The comment of the review to analyze")

    retrieved_documents: Optional[List[Dict[str, Any]]] = Field(None, description="Retrieved documents from vector store based on improved queries")



class RAGFile(BaseModel):
    """
    A file from the pull request with an optimized search query for RAG retrieval
    """
    file_path: str = Field(description="The path to the file")
    query: str = Field(description="The improved query (keywords) for the vector search to retrieve the most relevant code")

class RAGFileList(BaseModel):
    """
    List of RAGFile
    """
    files: List[RAGFile] = Field(description="The files of the pull request")
