import os

from corelanggraph import Enhanced<PERSON>tate<PERSON>raph
from langgraph.checkpoint.base import Base<PERSON>heckpointSaver
from langgraph.graph import START
from langgraph.graph.state import CompiledStateGraph

from code_review.agents.bitbucket.code_review.analyze_code_diff_agent import (
    BitBucketAnalyzeCodeDiffAgent,
)
from code_review.agents.bitbucket.code_review.comments.analyze_comment import (
    BitBucketAnalyzeCommentAgent,
)
from code_review.agents.bitbucket.code_review.create_comment_agent import (
    BitBucketCreateCommentAgent,
)
from code_review.agents.bitbucket.code_review.enrich_rag_data import (
    BitBucketEnrichRagDataAgent,
)
from code_review.agents.bitbucket.code_review.get_pull_request_data_agent import (
    BitBucketGetPullRequestDataAgent,
)
from code_review.agents.bitbucket.code_review.update_pull_request_approval_agent import (
    BitBucketUpdatePullRequestApprovalAgent,
)
from code_review.agents.global_agent.supervisor_agent import SupervisorAgent
from code_review.agents.jira.jira_agent import <PERSON>ra<PERSON>gent
from code_review.workflow.bitbucket.state import BitbucketState


def code_review_workflow(checkpointer: BaseCheckpointSaver):

    workflow_builder = EnhancedStateGraph(BitbucketState)

    workflow_builder.add_node("get_pull_request_data", BitBucketGetPullRequestDataAgent)
    workflow_builder.add_node("enrich_rag_data", BitBucketEnrichRagDataAgent)
    workflow_builder.add_node("analyze_code_diff", BitBucketAnalyzeCodeDiffAgent)
    workflow_builder.add_node("create_comment", BitBucketCreateCommentAgent)
    workflow_builder.add_node("update_pull_request_approval", BitBucketUpdatePullRequestApprovalAgent)
    workflow_builder.add_node("analyze_comment", BitBucketAnalyzeCommentAgent)

    workflow_builder.add_edge(START , "get_pull_request_data")

    return workflow_builder.compile(checkpointer=checkpointer)






def bitbucket_workflow( checkpointer: BaseCheckpointSaver) -> CompiledStateGraph:
    """
    A workflow that uses the Bitbucket API to get the pull request details and the files to review.
    """

    workflow_builder = EnhancedStateGraph(BitbucketState)


    # bitbucket_agent = BitbucketAgent(    )

    # workflow_builder.add_node("bitbucket_agent", bitbucket_agent)
    workflow_builder.add_node("code_review_workflow", code_review_workflow(checkpointer))

    workflow_builder.add_edge(START , "code_review_workflow")



    return workflow_builder.compile(checkpointer=checkpointer)

def jira_workflow(checkpointer: BaseCheckpointSaver) -> CompiledStateGraph:
    workflow_builder = EnhancedStateGraph(BitbucketState)

    workflow_builder.add_node("jira_agent", JiraAgent)

    workflow_builder.add_edge(START , "jira_agent")

    return workflow_builder.compile(checkpointer=checkpointer)

def base_workflow(checkpointer: BaseCheckpointSaver) -> CompiledStateGraph:
    
    workflow_builder = EnhancedStateGraph(BitbucketState)
        
    supervisor_agent = SupervisorAgent(
        model_name=os.getenv('MODEL_NAME','azure_model_2'),
        temperature=0.1,
        available_agents=[
            "bitbucket_agent",
            "jira_agent",
            "supervisor_agent",
        ]
    )
    



    workflow_builder.add_node("supervisor_agent", supervisor_agent)
    workflow_builder.add_node("bitbucket_agent", bitbucket_workflow(checkpointer))
    workflow_builder.add_node("jira_agent", jira_workflow(checkpointer))

    
    workflow_builder.add_edge(START , "supervisor_agent")
    workflow_builder.add_edge("jira_agent", "supervisor_agent")
    workflow_builder.add_edge("bitbucket_agent", "supervisor_agent")

    return workflow_builder.compile(checkpointer=checkpointer)


if os.getenv("LANGGRAPH_UI_BUNDLER") == "true":
        workflow_builder = base_workflow(None)