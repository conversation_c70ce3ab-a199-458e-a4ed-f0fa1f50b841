"""
Workflow Builder

This module provides utilities for building and managing agent workflows.
"""

from typing import Any, Callable, Dict, List, Optional

from corelanggraph.agents.base_agent import BaseAgent
from langgraph.graph import StateGraph
from logger import logger

from code_review.agents.global_agent.supervisor_agent import SupervisorAgent
from code_review.core.graph_manager import AgentState, GraphManager


class WorkflowBuilder:
    """
    Builder for creating and managing agent workflows.
    Provides a high-level interface for constructing agent networks.
    """
    
    def __init__(
        self,
        name: str,
        use_redis: bool = False,
        redis_url: Optional[str] = None,
    ):
        """
        Initialize the workflow builder
        
        Args:
            name: Name of the workflow
            use_redis: Whether to use Redis for state persistence
            redis_url: Redis connection URL if using Redis
        """
        self.name = name
        self.agents: Dict[str, BaseAgent] = {}
        self.graph_manager = GraphManager(use_redis=use_redis, redis_url=redis_url)
    
    def add_agent(self, agent:BaseAgent ) -> "WorkflowBuilder":
        """
        Add an agent to the workflow
        
        Args:
            agent: Agent to add
        
        Returns:
            Self for chaining
        """
        self.agents[agent.metadata.name] = agent
        return self
    
    def add_component(self, component: StateGraph, name: str) -> "WorkflowBuilder":
        """
        Add a component to the workflow
        """
        self.agents[name] = component
        return self
    
    def create_supervisor(
        self,
        model_name: str = "gpt-4",
        temperature: float = 0
    ) -> "WorkflowBuilder":
        """
        Create and add a supervisor agent
        
        Args:
            model_name: Name of the LLM model to use
            temperature: Temperature for LLM generation
        
        Returns:
            Self for chaining
        """
        # Get names of all available agents
        available_agents = list(self.agents.keys())
        
        # Create supervisor agent
        supervisor = SupervisorAgent(
            model_name=model_name,
            temperature=temperature,
            available_agents=available_agents
        )
        
        # Add supervisor to agents
        self.add_agent(supervisor)
        
        return self
    
    def build_router(self) -> Callable:
        """
        Build a router function for the workflow
        
        Returns:
            Router function
        """
        def router(state: AgentState) -> List[str]:
            """
            Determine the next agent(s) to call
            
            Args:
                state: Current workflow state
            
            Returns:
                List of agent names to call next
            """
            # If there's an error, route to the error handler
            if state.error:
                return ["error_handler"] if "error_handler" in self.agents else ["supervisor"]
            
            # If next_steps is specified, use it
            if state.next_steps:
                return state.next_steps
                
            # Default to the supervisor
            return ["supervisor"]
        
        return router
    
    def build(self) -> GraphManager:
        """
        Build the workflow and return the graph manager
        
        Returns:
            Configured graph manager
        """
        # Ensure we have a supervisor
        if "supervisor" not in self.agents:
            self.create_supervisor()
            
        # Create the workflow
        router = self.build_router()
        self.graph_manager.create_workflow(
            name=self.name,
            agents={name: agent for name, agent in self.agents.items()},
            router=router
        )
        
        return self.graph_manager
    
    async def run(
        self,
        input_data: Dict[str, Any],
        thread_id: Optional[str] = None,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Run the workflow
        
        Args:
            input_data: Input data for the workflow
            thread_id: Optional thread ID for continuing an existing workflow
            config: Optional configuration for the workflow execution
        
        Returns:
            Final workflow state
        """
        logger.info(f"Running workflow: {self.name} 🚀")
        # Build if not already built
        if self.name not in self.graph_manager.graphs:
            self.build()
        
        # Prepare config
        workflow_config = {}
        if config:
            workflow_config.update(config)
            
        # Run the workflow
        return await self.graph_manager.run_workflow(
            name=self.name,
            initial_input=input_data,
            thread_id=thread_id,
            config=workflow_config
        ) 