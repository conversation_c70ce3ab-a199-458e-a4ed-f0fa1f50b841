import logging
import os
from typing import Any, Dict, Optional

from corellm import ModelProvider
from langchain_community.embeddings import HuggingFaceEmbeddings
from rag.database.milvus import MilvusClientArgs
from rag.stores import MilvusVectorStore, VectorStoreFactory

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("CodeAnalyserRag")


class CodeAnalyserRag:
    """Client for interacting with CodeAnalyser's RAG"""
    
    def __init__(
        self,
        api_base_url: str = "http://localhost:8080"
    ):
        """
        Initialize the Bitbucket client
        
        Args:
            api_base_url: Code analyser API base URL
        """
        logger.info(f"Initializing CodeAnalyserRag with API base URL: {api_base_url}")
        
        milvus_uri = os.getenv("MILVUS_URI", "http://localhost:19530")
        logger.info(f"Using Milvus URI: {milvus_uri}")
        
        self.client = MilvusClientArgs(
            uri=milvus_uri,
            # token=MILVUS_TOKEN,
            # db_name=kwargs.get("db_name"),
            # user=kwargs.get("user"),
            # password=kwargs.get("password"),
            # timeout=kwargs.get("timeout")
        )
        logger.debug("MilvusClientArgs initialized")
        
        logger.info("Loading embedding model: azure_text_embedding_3_small")
        self.embeddings = self.embedding_model()
        
        self.collection_name:str = os.getenv("COLLECTION_NAME", 'codebase_embeddings')
        logger.info(f"Using collection name: {self.collection_name}")
        
        self.db_name:str = os.getenv("DB_NAME", 'cymulate_codebase')
        logger.info(f"Using database name: {self.db_name}")

    async def get_related_files(self, query: str, namespace: str, top_k: int = 10, hybrid_weights: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        logger.info(f"Getting related files for query: '{query}', namespace: '{namespace}', top_k: {top_k}")
        
        original_namespace = namespace
        namespace = namespace.replace("-", "_")
        if original_namespace != namespace:
            logger.debug(f"Namespace transformed from '{original_namespace}' to '{namespace}'")
        
        logger.debug(f"Initializing vector store with client and db_name: {self.db_name}")
        vector_store = VectorStoreFactory.get_vector_store('milvus', client=self.client, db_name=self.db_name, embedding_model=self.embeddings)
        logger.debug("Vector store initialized successfully")
        
        # Use hybrid search for Milvus if requested
        vector_store_type = os.getenv("VECTOR_STORE_TYPE", "hybrid")
        logger.info(f"Using vector store type: {vector_store_type}")
        
        if vector_store_type == "hybrid":
            logger.info("Performing hybrid search")
            if hybrid_weights:
                logger.debug(f"Using custom hybrid weights: {hybrid_weights}")
            else:
                logger.debug("Using default hybrid weights")
            
            vector_store: MilvusVectorStore = vector_store
            results = vector_store.query_hybrid(query, self.collection_name, namespace, top_k, hybrid_weights)
            logger.info(f"Hybrid search completed, found {len(results)} results")
            return results
        
        logger.info("Performing standard vector search")
        results = vector_store.query(query, self.collection_name, namespace, top_k)
        logger.info(f"Standard search completed, found {len(results)} results")
        return results
    
    def embedding_model(self):
        embedding_model_name = os.getenv("EMBEDDING_MODEL")
        logger.info(f"Getting embedding model, EMBEDDING_MODEL env var: {embedding_model_name}")
        
        if embedding_model_name == "all-MiniLM-L6-v2":
            logger.info("Using HuggingFaceEmbeddings: all-MiniLM-L6-v2")
            return HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")
        else:
            logger.info("Using default Azure Text Embedding model: azure_text_embedding_3_small")
            return ModelProvider().get_embeddings('azure_text_embedding_3_small')
    
    

    
   