"""
Kubernetes Client

This module provides a client for interacting with Kubernetes API.
"""

import asyncio
import logging
import os
import tempfile
from typing import Any, Dict, List, Optional

from kubernetes import client, config, utils
from kubernetes.client.rest import ApiException

logger = logging.getLogger(__name__)


class KubernetesClient:
    """Client for interacting with Kubernetes API"""
    
    def __init__(
        self,
        kubeconfig_path: Optional[str] = None,
        context_name: Optional[str] = None
    ):
        """
        Initialize the Kubernetes client
        
        Args:
            kubeconfig_path: Path to kubeconfig file
            context_name: Kubernetes context name
        """
        try:
            if kubeconfig_path:
                config.load_kube_config(
                    config_file=kubeconfig_path,
                    context=context_name
                )
            else:
                # Try loading from default location
                try:
                    config.load_kube_config(context=context_name)
                except config.config_exception.ConfigException:
                    # If not available, try in-cluster config
                    config.load_incluster_config()
            
            # Initialize API clients
            self.core_v1 = client.CoreV1Api()
            self.apps_v1 = client.AppsV1Api()
            self.batch_v1 = client.BatchV1Api()
            self.custom_objects = client.CustomObjectsApi()
            
            logger.info("Kubernetes client initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Kubernetes client: {e}")
            raise ValueError(f"Failed to initialize Kubernetes client: {str(e)}")
    
    async def create_resource(
        self,
        kind: str,
        name: str,
        namespace: str,
        spec: Dict[str, Any],
        labels: Optional[Dict[str, str]] = None,
        annotations: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Create a Kubernetes resource
        
        Args:
            kind: Resource kind (Deployment, Job, Service, etc.)
            name: Resource name
            namespace: Namespace
            spec: Resource specification
            labels: Resource labels
            annotations: Resource annotations
        
        Returns:
            Created resource data
        """
        try:
            # Run in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            
            metadata = client.V1ObjectMeta(
                name=name,
                namespace=namespace,
                labels=labels or {},
                annotations=annotations or {}
            )
            
            if kind.lower() == "deployment":
                body = client.V1Deployment(
                    metadata=metadata,
                    spec=client.V1DeploymentSpec(**spec)
                )
                result = await loop.run_in_executor(
                    None,
                    lambda: self.apps_v1.create_namespaced_deployment(
                        namespace=namespace,
                        body=body
                    )
                )
                
            elif kind.lower() == "service":
                body = client.V1Service(
                    metadata=metadata,
                    spec=client.V1ServiceSpec(**spec)
                )
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.create_namespaced_service(
                        namespace=namespace,
                        body=body
                    )
                )
                
            elif kind.lower() == "job":
                body = client.V1Job(
                    metadata=metadata,
                    spec=client.V1JobSpec(**spec)
                )
                result = await loop.run_in_executor(
                    None,
                    lambda: self.batch_v1.create_namespaced_job(
                        namespace=namespace,
                        body=body
                    )
                )
                
            elif kind.lower() == "configmap":
                body = client.V1ConfigMap(
                    metadata=metadata,
                    data=spec.get("data", {})
                )
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.create_namespaced_config_map(
                        namespace=namespace,
                        body=body
                    )
                )
                
            elif kind.lower() == "secret":
                body = client.V1Secret(
                    metadata=metadata,
                    type=spec.get("type", "Opaque"),
                    data=spec.get("data", {})
                )
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.create_namespaced_secret(
                        namespace=namespace,
                        body=body
                    )
                )
                
            else:
                raise ValueError(f"Unsupported resource kind: {kind}")
            
            # Convert result to dict
            return self._convert_k8s_obj_to_dict(result)
            
        except ApiException as e:
            logger.error(f"Error creating Kubernetes resource: {e}")
            raise ValueError(f"Failed to create Kubernetes resource: {str(e)}")
    
    async def update_resource(
        self,
        kind: str,
        name: str,
        namespace: str,
        spec: Dict[str, Any],
        labels: Optional[Dict[str, str]] = None,
        annotations: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """
        Update a Kubernetes resource
        
        Args:
            kind: Resource kind (Deployment, Job, Service, etc.)
            name: Resource name
            namespace: Namespace
            spec: Resource specification
            labels: Resource labels
            annotations: Resource annotations
        
        Returns:
            Updated resource data
        """
        try:
            # First, get the current resource
            current = await self.get_resource(kind, name, namespace)
            
            # Run in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            
            # Update metadata
            metadata = client.V1ObjectMeta(
                name=name,
                namespace=namespace,
                labels=labels or current.get("metadata", {}).get("labels", {}),
                annotations=annotations or current.get("metadata", {}).get("annotations", {}),
                resource_version=current.get("metadata", {}).get("resourceVersion")
            )
            
            if kind.lower() == "deployment":
                body = client.V1Deployment(
                    metadata=metadata,
                    spec=client.V1DeploymentSpec(**spec)
                )
                result = await loop.run_in_executor(
                    None,
                    lambda: self.apps_v1.replace_namespaced_deployment(
                        name=name,
                        namespace=namespace,
                        body=body
                    )
                )
                
            elif kind.lower() == "service":
                body = client.V1Service(
                    metadata=metadata,
                    spec=client.V1ServiceSpec(**spec)
                )
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.replace_namespaced_service(
                        name=name,
                        namespace=namespace,
                        body=body
                    )
                )
                
            elif kind.lower() == "job":
                # Jobs cannot be updated, so we need to delete and recreate
                await self.delete_resource(kind, name, namespace)
                return await self.create_resource(kind, name, namespace, spec, labels, annotations)
                
            elif kind.lower() == "configmap":
                body = client.V1ConfigMap(
                    metadata=metadata,
                    data=spec.get("data", {})
                )
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.replace_namespaced_config_map(
                        name=name,
                        namespace=namespace,
                        body=body
                    )
                )
                
            elif kind.lower() == "secret":
                body = client.V1Secret(
                    metadata=metadata,
                    type=spec.get("type", "Opaque"),
                    data=spec.get("data", {})
                )
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.replace_namespaced_secret(
                        name=name,
                        namespace=namespace,
                        body=body
                    )
                )
                
            else:
                raise ValueError(f"Unsupported resource kind: {kind}")
            
            # Convert result to dict
            return self._convert_k8s_obj_to_dict(result)
            
        except ApiException as e:
            logger.error(f"Error updating Kubernetes resource: {e}")
            raise ValueError(f"Failed to update Kubernetes resource: {str(e)}")
    
    async def delete_resource(
        self,
        kind: str,
        name: str,
        namespace: str
    ) -> Dict[str, Any]:
        """
        Delete a Kubernetes resource
        
        Args:
            kind: Resource kind (Deployment, Job, Service, etc.)
            name: Resource name
            namespace: Namespace
        
        Returns:
            Status of the delete operation
        """
        try:
            # Run in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            
            if kind.lower() == "deployment":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.apps_v1.delete_namespaced_deployment(
                        name=name,
                        namespace=namespace
                    )
                )
                
            elif kind.lower() == "service":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.delete_namespaced_service(
                        name=name,
                        namespace=namespace
                    )
                )
                
            elif kind.lower() == "job":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.batch_v1.delete_namespaced_job(
                        name=name,
                        namespace=namespace,
                        propagation_policy="Background"
                    )
                )
                
            elif kind.lower() == "configmap":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.delete_namespaced_config_map(
                        name=name,
                        namespace=namespace
                    )
                )
                
            elif kind.lower() == "secret":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.delete_namespaced_secret(
                        name=name,
                        namespace=namespace
                    )
                )
                
            else:
                raise ValueError(f"Unsupported resource kind: {kind}")
            
            # Convert result to dict
            return self._convert_k8s_obj_to_dict(result)
            
        except ApiException as e:
            logger.error(f"Error deleting Kubernetes resource: {e}")
            raise ValueError(f"Failed to delete Kubernetes resource: {str(e)}")
    
    async def get_resource(
        self,
        kind: str,
        name: str,
        namespace: str
    ) -> Dict[str, Any]:
        """
        Get a Kubernetes resource
        
        Args:
            kind: Resource kind (Deployment, Job, Service, etc.)
            name: Resource name
            namespace: Namespace
        
        Returns:
            Resource data
        """
        try:
            # Run in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            
            if kind.lower() == "deployment":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.apps_v1.read_namespaced_deployment(
                        name=name,
                        namespace=namespace
                    )
                )
                
            elif kind.lower() == "service":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.read_namespaced_service(
                        name=name,
                        namespace=namespace
                    )
                )
                
            elif kind.lower() == "job":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.batch_v1.read_namespaced_job(
                        name=name,
                        namespace=namespace
                    )
                )
                
            elif kind.lower() == "configmap":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.read_namespaced_config_map(
                        name=name,
                        namespace=namespace
                    )
                )
                
            elif kind.lower() == "secret":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.read_namespaced_secret(
                        name=name,
                        namespace=namespace
                    )
                )
                
            else:
                raise ValueError(f"Unsupported resource kind: {kind}")
            
            # Convert result to dict
            return self._convert_k8s_obj_to_dict(result)
            
        except ApiException as e:
            logger.error(f"Error getting Kubernetes resource: {e}")
            raise ValueError(f"Failed to get Kubernetes resource: {str(e)}")
    
    async def list_resources(
        self,
        kind: str,
        namespace: str,
        selector: Optional[str] = None,
        field_selector: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        List Kubernetes resources
        
        Args:
            kind: Resource kind (Deployment, Job, Service, etc.)
            namespace: Namespace
            selector: Label selector
            field_selector: Field selector
        
        Returns:
            List of resources
        """
        try:
            # Run in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            
            if kind.lower() == "deployment":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.apps_v1.list_namespaced_deployment(
                        namespace=namespace,
                        label_selector=selector,
                        field_selector=field_selector
                    )
                )
                
            elif kind.lower() == "service":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.list_namespaced_service(
                        namespace=namespace,
                        label_selector=selector,
                        field_selector=field_selector
                    )
                )
                
            elif kind.lower() == "job":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.batch_v1.list_namespaced_job(
                        namespace=namespace,
                        label_selector=selector,
                        field_selector=field_selector
                    )
                )
                
            elif kind.lower() == "configmap":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.list_namespaced_config_map(
                        namespace=namespace,
                        label_selector=selector,
                        field_selector=field_selector
                    )
                )
                
            elif kind.lower() == "secret":
                result = await loop.run_in_executor(
                    None,
                    lambda: self.core_v1.list_namespaced_secret(
                        namespace=namespace,
                        label_selector=selector,
                        field_selector=field_selector
                    )
                )
                
            else:
                raise ValueError(f"Unsupported resource kind: {kind}")
            
            # Convert result to dict
            items = result.items if hasattr(result, "items") else []
            return [self._convert_k8s_obj_to_dict(item) for item in items]
            
        except ApiException as e:
            logger.error(f"Error listing Kubernetes resources: {e}")
            raise ValueError(f"Failed to list Kubernetes resources: {str(e)}")
    
    async def apply_yaml(
        self,
        yaml_content: str,
        namespace: str = "default"
    ) -> Dict[str, Any]:
        """
        Apply YAML content to create or update resources
        
        Args:
            yaml_content: YAML content
            namespace: Default namespace for resources without namespace specified
        
        Returns:
            Result of the operation
        """
        try:
            # Create a temporary file for the YAML content
            with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as temp_file:
                temp_file.write(yaml_content)
                temp_file_path = temp_file.name
            
            try:
                # Run in a separate thread to avoid blocking
                loop = asyncio.get_event_loop()
                
                # Apply the YAML
                result = await loop.run_in_executor(
                    None,
                    lambda: utils.create_from_yaml(
                        k8s_client=client.ApiClient(),
                        yaml_file=temp_file_path,
                        namespace=namespace
                    )
                )
                
                # Convert result to dict
                if isinstance(result, list):
                    return {
                        "success": True,
                        "resources": [self._convert_k8s_obj_to_dict(item) for item in result]
                    }
                else:
                    return {
                        "success": True,
                        "resources": [self._convert_k8s_obj_to_dict(result)]
                    }
                
            finally:
                # Clean up the temporary file
                os.unlink(temp_file_path)
                
        except Exception as e:
            logger.error(f"Error applying YAML: {e}")
            raise ValueError(f"Failed to apply YAML: {str(e)}")
    
    def _convert_k8s_obj_to_dict(self, obj: Any) -> Dict[str, Any]:
        """
        Convert Kubernetes object to dictionary
        
        Args:
            obj: Kubernetes object
        
        Returns:
            Dictionary representation
        """
        if hasattr(obj, "to_dict"):
            return obj.to_dict()
        elif isinstance(obj, dict):
            return obj
        else:
            return {"raw": str(obj)} 