"""
Jira Client

This module provides a client for interacting with Jira's API.
"""

import asyncio
import logging
import os
from typing import Any, Dict, List, Optional

from atlassian import <PERSON>ra
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class JiraIssue(BaseModel):
    """Model representing a Jira issue"""
    key: str
    summary: str
    description: Optional[str] = None
    issue_type: str
    status: Optional[str] = None
    assignee: Optional[str] = None
    priority: Optional[str] = None
    created: Optional[str] = None
    updated: Optional[str] = None
    fields: Dict[str, Any] = Field(default_factory=dict)


class JiraClient:
    """Client for interacting with Jira's API"""
    
    def __init__(
        self,
        jira_url: str = os.getenv("ATLASSIAN_DOMAIN"),
        jira_username: str = os.getenv("ATLASSIAN_USERNAME"),
        jira_api_token: str = os.getenv("ATLASSIAN_API_TOKEN")
    ):
        """
        Initialize the Jira client
        
        Args:
            jira_url: Jira instance URL
            jira_username: Jira username
            jira_api_token: Jira API token
        """
        logger.info(f"Initializing Jira client with url {jira_url}, username {jira_username}, and token {jira_api_token}")
        self.jira = Jira(
            url=f"https://{jira_url}",
            username=jira_username,
            password=jira_api_token
        )
    
    async def create_issue(
        self,
        project_key: str,
        summary: str,
        description: Optional[str] = None,
        issue_type: str = "Task",
        fields: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a new Jira issue
        
        Args:
            project_key: Key of the project to create the issue in
            summary: Issue summary
            description: Issue description
            issue_type: Type of issue (Bug, Task, Story, etc.)
            fields: Additional fields to set
        
        Returns:
            Created issue data
        """
        try:
            # Run in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            issue_data = await loop.run_in_executor(
                None,
                lambda: self.jira.issue_create(
                    fields={
                        "project": {"key": project_key},
                        "summary": summary,
                        "description": description,
                        "issuetype": {"name": issue_type},
                        **(fields or {})
                    }
                )
            )
            
            # Parse response into JiraIssue model
            issue = JiraIssue(
                key=issue_data["key"],
                summary=summary,
                description=description,
                issue_type=issue_type,
                fields=fields or {}
            )
            
            return issue.model_dump()
            
        except Exception as e:
            logger.error(f"Error creating Jira issue: {e}")
            raise ValueError(f"Failed to create Jira issue: {str(e)}")
    
    async def update_issue(
        self,
        issue_key: str,
        fields: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Update an existing Jira issue
        
        Args:
            issue_key: Key of the issue to update
            fields: Fields to update
        
        Returns:
            Updated issue data
        """
        try:
            # Prepare fields for update
            update_fields = {}
            
            # Handle special fields
            if "summary" in fields:
                update_fields["summary"] = fields.pop("summary")
            
            if "description" in fields:
                update_fields["description"] = fields.pop("description")
            
            if "assignee" in fields:
                update_fields["assignee"] = {"name": fields.pop("assignee")}
            
            if "priority" in fields:
                update_fields["priority"] = {"name": fields.pop("priority")}
            
            # Add remaining fields
            update_fields.update(fields)
            
            # Run in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.jira.issue_update(
                    issue_key=issue_key,
                    fields=update_fields
                )
            )
            
            # Get updated issue
            updated_issue = await self.get_issue(issue_key)
            return updated_issue
            
        except Exception as e:
            logger.error(f"Error updating Jira issue: {e}")
            raise ValueError(f"Failed to update Jira issue: {str(e)}")
    
    async def get_issue(self, issue_key: str) -> Dict[str, Any]:
        """
        Get a Jira issue by key
        
        Args:
            issue_key: Key of the issue to get
        
        Returns:
            Issue data
        """
        try:
            # Run in a separate thread to avoid blocking
            logger.info(f"Getting Jira issue: {issue_key} from {self.jira.url} with username {self.jira.username} and token {self.jira.password}")
            loop = asyncio.get_event_loop()
            issue_data = await loop.run_in_executor(
                None,
                lambda: self.jira.issue(issue_key)
            )
            
            # Parse fields
            fields = issue_data["fields"]
            
            # Create JiraIssue model
            issue = JiraIssue(
                key=issue_key,
                summary=fields.get("summary", ""),
                description=fields.get("description", ""),
                issue_type=fields.get("issuetype", {}).get("name", ""),
                status=fields.get("status", {}).get("name", ""),
                assignee=fields.get("assignee", {}).get("displayName", ""),
                priority=fields.get("priority", {}).get("name", ""),
                created=fields.get("created", ""),
                updated=fields.get("updated", ""),
                fields={k: v for k, v in fields.items() if k not in [
                    "summary", "description", "issuetype", "status", 
                    "assignee", "priority", "created", "updated"
                ]}
            )
            
            return issue.model_dump()
            
        except Exception as e:
            logger.error(f"Error getting Jira issue: {e}")
            raise ValueError(f"Failed to get Jira issue: {str(e)}")
    
    async def search_issues(self, jql: str) -> List[Dict[str, Any]]:
        """
        Search for Jira issues using JQL
        
        Args:
            jql: Jira Query Language string
        
        Returns:
            List of matching issues
        """
        try:
            # Run in a separate thread to avoid blocking
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                None,
                lambda: self.jira.jql(jql)
            )
            
            issues = []
            for issue_data in results.get("issues", []):
                fields = issue_data["fields"]
                
                issue = JiraIssue(
                    key=issue_data["key"],
                    summary=fields.get("summary", ""),
                    description=fields.get("description", ""),
                    issue_type=fields.get("issuetype", {}).get("name", ""),
                    status=fields.get("status", {}).get("name", ""),
                    assignee=fields.get("assignee", {}).get("displayName", ""),
                    priority=fields.get("priority", {}).get("name", ""),
                    created=fields.get("created", ""),
                    updated=fields.get("updated", ""),
                    fields={k: v for k, v in fields.items() if k not in [
                        "summary", "description", "issuetype", "status", 
                        "assignee", "priority", "created", "updated"
                    ]}
                )
                
                issues.append(issue.model_dump())
            
            return issues
            
        except Exception as e:
            logger.error(f"Error searching Jira issues: {e}")
            raise ValueError(f"Failed to search Jira issues: {str(e)}") 