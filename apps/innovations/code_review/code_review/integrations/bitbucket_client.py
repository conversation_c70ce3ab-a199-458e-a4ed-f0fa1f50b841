"""
Bitbucket Client

This module provides a client for interacting with Bitbucket's API.
"""

import base64
import json
from typing import Any, Dict, List, Optional, Union

import httpx
from logger import error, info

# No need for logger initialization as it's handled by our custom logger


class BitbucketClient:
    """Client for interacting with Bitbucket's API"""
    
    def __init__(
        self,
        username: str,
        app_password: str,
        api_base_url: str = "https://api.bitbucket.org/2.0"
    ):
        """
        Initialize the Bitbucket client
        
        Args:
            username: Bitbucket username
            app_password: Bitbucket app password
            api_base_url: Bitbucket API base URL
        """
        self.username = username
        self.app_password = app_password
        self.api_base_url = api_base_url
        
        # Create auth header
        auth_str = f"{username}:{app_password}"
        auth_bytes = auth_str.encode("ascii")
        auth_b64 = base64.b64encode(auth_bytes).decode("ascii")
        self.auth_header = f"Basic {auth_b64}"
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, Any]] = None,
        raw_response: bool = False
    ) -> Union[Dict[str, Any], str]:
        """
        Make a request to the Bitbucket API
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint
            params: Query parameters
            json_data: JSON data for POST/PUT requests
            raw_response: If True, return the raw response text instead of parsing as JSON
        
        Returns:
            API response as dict or raw text depending on raw_response parameter
        """
        url = f"{self.api_base_url}/{endpoint}"
        if headers is None:
            headers = {
                "Content-Type": "application/json"
            }
        headers.update({
            "Authorization": self.auth_header,
        })
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.request(
                    method=method,
                    url=url,
                    params=params,
                    json=json_data,
                    headers=headers,
                    timeout=30.0,
                    follow_redirects=True
                )
                
                response.raise_for_status()
                
                if response.status_code == 204:  # No content
                    return {"success": True}
                
                # Check if raw_response is requested or if content type is not JSON
                content_type = response.headers.get("content-type", "")
                if raw_response or not content_type.startswith("application/json"):
                    return response.text
                
                return response.json()
                
            except httpx.HTTPStatusError as e:
                info(f"""
                    curl -X {method} {url} \
                    -H "Authorization: {self.auth_header}" \
                    -H "Content-Type: application/json" \
                    -d '{json.dumps(json_data)}' \
                    -d '{params}'
                    """)
                error(f"HTTP error: {e.response.status_code} - {e.response.text}")
                raise ValueError(f"Bitbucket API error: {e.response.status_code} - {e.response.text}")
                
            except httpx.RequestError as e:
                error(f"Request error: {str(e)}")
                raise ValueError(f"Bitbucket API request error: {str(e)}")
    
    async def create_pull_request(
        self,
        workspace: str,
        repository: str,
        title: str,
        source_branch: str,
        destination_branch: str = "master",
        description: Optional[str] = None,
        close_source_branch: bool = False,
        reviewers: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Create a pull request
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            title: PR title
            source_branch: Source branch
            destination_branch: Destination branch
            description: PR description
            close_source_branch: Whether to close the source branch after merge
            reviewers: List of reviewer usernames
        
        Returns:
            Created pull request data
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests"
        
        # Prepare reviewers data
        reviewers_data = None
        if reviewers:
            reviewers_data = [{"username": username} for username in reviewers]
        
        data = {
            "title": title,
            "description": description or "",
            "source": {
                "branch": {
                    "name": source_branch
                }
            },
            "destination": {
                "branch": {
                    "name": destination_branch
                }
            },
            "close_source_branch": close_source_branch
        }
        
        if reviewers_data:
            data["reviewers"] = reviewers_data
        
        return await self._make_request("POST", endpoint, json_data=data)
    
    async def update_pull_request(
        self,
        workspace: str,
        repository: str,
        pr_id: int,
        title: Optional[str] = None,
        description: Optional[str] = None,
        reviewers: Optional[List[str]] = None,
        state: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update a pull request
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            pr_id: Pull request ID
            title: New PR title
            description: New PR description
            reviewers: New list of reviewer usernames
            state: New state (OPEN, MERGED, DECLINED)
        
        Returns:
            Updated pull request data
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}"
        
        # Prepare data
        data = {}
        
        if title is not None:
            data["title"] = title
            
        if description is not None:
            data["description"] = description
            
        if reviewers is not None:
            data["reviewers"] = [{"username": username} for username in reviewers]
            
        if state is not None:
            data["state"] = state
        
        return await self._make_request("PUT", endpoint, json_data=data)
    
    async def get_pull_request(
        self,
        workspace: str,
        repository: str,
        pr_id: int
    ) -> Dict[str, Any]:
        """
        Get a pull request by ID
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            pr_id: Pull request ID
        
        Returns:
            Pull request data
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}"
        return await self._make_request("GET", endpoint)
    
    async def list_pull_requests(
        self,
        workspace: str,
        repository: str,
        query_params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        List pull requests for a repository
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            query_params: Query parameters (state, sort, etc.)
        
        Returns:
            List of pull requests
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests"
        response = await self._make_request("GET", endpoint, params=query_params)
        return response.get("values", [])
    
    async def merge_pull_request(
        self,
        workspace: str,
        repository: str,
        pr_id: int,
        close_source_branch: bool = False
    ) -> Dict[str, Any]:
        """
        Merge a pull request
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            pr_id: Pull request ID
            close_source_branch: Whether to close the source branch
        
        Returns:
            Merged pull request data
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}/merge"
        data = {
            "close_source_branch": close_source_branch
        }
        return await self._make_request("POST", endpoint, json_data=data)
    
    async def decline_pull_request(
        self,
        workspace: str,
        repository: str,
        pr_id: int
    ) -> Dict[str, Any]:
        """
        Decline a pull request
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            pr_id: Pull request ID
        
        Returns:
            Declined pull request data
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}/decline"
        return await self._make_request("POST", endpoint)
    
    async def approve_pull_request(
        self,
        workspace: str,
        repository: str,
        pr_id: int,
        type: str = "add"
    ) -> Dict[str, Any]:
        """
        Approve a pull request
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            pr_id: Pull request ID
        
        Returns:
            Approved pull request data
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}/approve"
        return await self._make_request(
            method= "POST" if type == "add" else "DELETE",
            endpoint=endpoint,
            headers={
                "Accept": "application/json"
            }
        )
    
    async def request_changes(
        self,
        workspace: str,
        repository: str,
        pr_id: int,
        type: str = "add"
    ) -> Dict[str, Any]:
        """
        Request changes to a pull request
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            pr_id: Pull request ID
        
        Returns:
            Request changes data
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}/request-changes"
        return await self._make_request(
            method= "POST" if type == "add" else "DELETE",
            endpoint=endpoint,
            headers={
                "Accept": "application/json"
            }
        )
    
    
    async def get_pull_request_diff_text(
        self,
        workspace: str,
        repository: str,
        pr_id: int
    ) -> str:
        """
        Get the diff text for a pull request
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}/diff"
        return await self._make_request("GET", endpoint, raw_response=True)

    async def get_pull_request_diff(
        self,
        workspace: str,
        repository: str,
        pr_id: int
    ) -> List[Dict[str, Any]]:
        """
        Get the diff for a pull request
        
        Args:
            workspace: Bitbucket workspace
            repository: Repository name
            pr_id: Pull request ID
            
        Returns:
            Structured diff as a list of file changes
        """
        try:
            # Get the full diff for the PR
            diff_text = await self.get_pull_request_diff_text(workspace, repository, pr_id)
            
            # Parse the diff into a structured format
            structured_diff = []
            
            # If we have a diff_text, parse it directly
            if diff_text:
                try:
                    lines = diff_text.split('\n')
                    current_file = None
                    current_hunk = None
                    line_index = 0
                    
                    while line_index < len(lines):
                        line = lines[line_index]
                        
                        # Check for file header lines (diff --git a/path b/path)
                        if line.startswith('diff --git'):
                            # If we have a current file with hunks, add it to the structured diff
                            if current_file and current_file.get("hunks"):
                                structured_diff.append(current_file)
                            
                            # Extract the file paths
                            parts = line.split(' ')
                            if len(parts) >= 4:
                                a_path = parts[2][2:]  # Remove the 'a/' prefix
                                b_path = parts[3][2:]  # Remove the 'b/' prefix
                                
                                # Create a new file entry
                                current_file = {
                                    "old": {"path": a_path},
                                    "new": {"path": b_path},
                                    "status": "modified",  # Default status
                                    "lines_added": 0,
                                    "lines_removed": 0,
                                    "hunks": [],
                                    "raw_header": [line]
                                }
                                
                                # Look ahead for index, ---, +++ lines
                                line_index += 1
                                while line_index < len(lines):
                                    next_line = lines[line_index]
                                    if next_line.startswith('index '):
                                        current_file["raw_header"].append(next_line)
                                        line_index += 1
                                    elif next_line.startswith('--- '):
                                        current_file["raw_header"].append(next_line)
                                        # Update old path if available
                                        if next_line.startswith('--- a/'):
                                            current_file["old"]["path"] = next_line[6:]
                                        elif next_line.startswith('--- /dev/null'):
                                            current_file["status"] = "added"
                                            current_file["old"]["path"] = "/dev/null"
                                        line_index += 1
                                    elif next_line.startswith('+++ '):
                                        current_file["raw_header"].append(next_line)
                                        # Update new path if available
                                        if next_line.startswith('+++ b/'):
                                            current_file["new"]["path"] = next_line[6:]
                                        elif next_line.startswith('+++ /dev/null'):
                                            current_file["status"] = "deleted"
                                            current_file["new"]["path"] = "/dev/null"
                                        line_index += 1
                                    elif next_line.startswith('@@ '):
                                        # We've reached a hunk header, break out
                                        break
                                    else:
                                        # Unknown header line, add it and continue
                                        current_file["raw_header"].append(next_line)
                                        line_index += 1
                                
                                # Don't increment line_index here as we want to process the @@ line next
                                continue
                        
                        # Check for hunk header lines (@@ -start,length +start,length @@)
                        elif line.startswith('@@ ') and current_file:
                            # Start a new hunk
                            if current_hunk:
                                current_file["hunks"].append(current_hunk)
                            
                            # Parse the hunk header
                            try:
                                # Extract the hunk range information
                                header_parts = line.split(' ', 3)
                                old_range = header_parts[1]  # -start,length
                                new_range = header_parts[2]  # +start,length
                                
                                # Create the hunk
                                current_hunk = {
                                    "header": line,
                                    "old_range": old_range,
                                    "new_range": new_range,
                                    "lines": []
                                }
                            except Exception as e:
                                error(f"Error parsing hunk header: {str(e)}")
                                current_hunk = None
                        
                        # Add content lines to the current hunk
                        elif current_hunk is not None and current_file:
                            current_hunk["lines"].append(line)
                            
                            # Count added/removed lines
                            if line.startswith('+') and not line.startswith('+++'):
                                current_file["lines_added"] += 1
                            elif line.startswith('-') and not line.startswith('---'):
                                current_file["lines_removed"] += 1
                        
                        line_index += 1
                    
                    # Add the last file and hunk if there is one
                    if current_hunk and current_file:
                        current_file["hunks"].append(current_hunk)
                    if current_file and current_file.get("hunks"):
                        structured_diff.append(current_file)
                    
                except Exception as e:
                    error(f"Error parsing diff text: {str(e)}")
                    # If parsing fails, try to return a simplified version of the diff
                    try:
                        return self._parse_simple_diff(diff_text)
                    except Exception as e2:
                        error(f"Error parsing simple diff: {str(e2)}")
            
            return structured_diff
        except Exception as e:
            error(f"Error in get_pull_request_diff: {str(e)}")
            return []
        
    async def get_pull_request_comments(
        self,
        workspace: str,
        repository: str,
        pr_id: int
    ) -> List[Dict[str, Any]]:
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}/comments?pagelen=100"
        response = await self._make_request("GET", endpoint)
        if isinstance(response, dict) and 'values' in response:
            return response.get("values")
        else:
            return []
    
    async def comment_on_pull_request(
        self,
        workspace: str,
        repository: str,
        pr_id: int,
        data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Add a comment to a pull request
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            pr_id: Pull request ID
            content: Comment content
        
        Returns:
            Created comment data
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}/comments"

        return await self._make_request("POST", endpoint, json_data=data)

    async def update_comment(
        self,
        workspace: str,
        repository: str,
        pr_id: int,
        comment_id: int,
        content: str
    ) -> Dict[str, Any]:
        """
        Update a comment on a pull request
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}/comments/{comment_id}"
        data = { "content": { "raw": content } }
        return await self._make_request("PUT", endpoint, json_data=data)

    async def create_reply_comment(
        self,
        workspace: str,
        repository: str,
        pr_id: int,
        parent_id: int,
        content: str
    ) -> Dict[str, Any]:
        """
        Create a reply comment to a pull request
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}/comments"
        data = {
            "content": { "raw": content },
            "parent": { "id": parent_id }
        }
        return await self._make_request("POST", endpoint, json_data=data)

    async def add_inline_comment(
        self,
        workspace: str,
        repository: str,
        pr_id: int,
        file_path: str,
        line_number: int,
        content: str
    ) -> Dict[str, Any]:
        """
        Add an inline comment to a specific line in a pull request
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            pr_id: Pull request ID
            file_path: Path to the file being commented on
            line_number: Line number to comment on
            content: Comment content
        
        Returns:
            Created comment data
        """
        endpoint = f"repositories/{workspace}/{repository}/pullrequests/{pr_id}/comments"
        data = {
            "content": {
                "raw": content
            },
            "inline": {
                "path": file_path,
                "to": line_number
            }
        }
        return await self._make_request("POST", endpoint, json_data=data)
    

    def _parse_simple_diff(self, diff_text: str) -> List[Dict[str, Any]]:
        """
        Parse a simple diff format when the more complex parsing fails
        
        Args:
            diff_text: Raw diff text
            
        Returns:
            Simplified structured diff
        """
        structured_diff = []
        
        try:
            lines = diff_text.split('\n')
            current_file = None
            
            for line in lines:
                # Check for file header lines (diff --git a/path b/path)
                if line.startswith('diff --git'):
                    # Extract file paths
                    parts = line.split(' ')
                    if len(parts) >= 4:
                        a_path = parts[2][2:] if parts[2].startswith('a/') else parts[2]
                        b_path = parts[3][2:] if parts[3].startswith('b/') else parts[3]
                        
                        # Create a new file entry
                        current_file = {
                            "old": {"path": a_path},
                            "new": {"path": b_path},
                            "status": "modified",
                            "lines_added": 0,
                            "lines_removed": 0,
                            "content": line + "\n"
                        }
                        structured_diff.append(current_file)
                elif current_file:
                    # Append all other lines to the content
                    current_file["content"] += line + "\n"
                    
                    # Count added/removed lines
                    if line.startswith('+') and not line.startswith('+++'):
                        current_file["lines_added"] += 1
                    elif line.startswith('-') and not line.startswith('---'):
                        current_file["lines_removed"] += 1
        except Exception as e:
            print(f"Error in _parse_simple_diff: {str(e)}")
        
        return structured_diff

    async def update_bitbucket_build_status(
        self,
        workspace: str,
        repository: str,
        commit_sha: str,
        build_key: str,
        build_url: str,
        state: str,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update the build status for a commit in Bitbucket
        
        Args:
            workspace: Workspace/project key
            repository: Repository slug
            commit_sha: Commit SHA to update status for
            build_key: Unique identifier for the build
            build_url: URL to the build details
            state: Build state (SUCCESSFUL, FAILED, INPROGRESS, STOPPED)
            description: Optional description of the build status
        
        Returns:
            API response data
        """
        endpoint = f"repositories/{workspace}/{repository}/commit/{commit_sha}/statuses/build"
        
        data = {
            "key": build_key,
            "url": build_url,
            "state": state
        }
        
        if description:
            data["description"] = description
            
        return await self._make_request("POST", endpoint, json_data=data) 