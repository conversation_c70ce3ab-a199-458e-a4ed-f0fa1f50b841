"""
Authentication and Authorization Manager

This module provides authentication and authorization functionality
for the multi-agent system.
"""

import hashlib
import logging
import os
import secrets
import time
from typing import Dict, List, Optional, Set

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class User(BaseModel):
    """Model representing a user"""
    username: str
    password_hash: str
    roles: List[str] = Field(default_factory=list)
    api_keys: List[str] = Field(default_factory=list)
    is_active: bool = True


class Permission(BaseModel):
    """Model representing a permission"""
    name: str
    description: str


class Role(BaseModel):
    """Model representing a role"""
    name: str
    description: str
    permissions: List[str] = Field(default_factory=list)


class AuthManager:
    """
    Authentication and Authorization Manager
    
    Handles user authentication, API key validation, and role-based access control.
    """
    
    def __init__(
        self,
        jwt_secret: Optional[str] = None,
        token_expiry: int = 3600,  # 1 hour
        api_key_prefix: str = "mas_"
    ):
        """
        Initialize the auth manager
        
        Args:
            jwt_secret: Secret for JWT token signing
            token_expiry: Token expiry time in seconds
            api_key_prefix: Prefix for generated API keys
        """
        self.jwt_secret = jwt_secret or os.environ.get("JWT_SECRET") or secrets.token_hex(32)
        self.token_expiry = token_expiry
        self.api_key_prefix = api_key_prefix
        
        # In-memory storage (in production, use a database)
        self.users: Dict[str, User] = {}
        self.roles: Dict[str, Role] = {}
        self.permissions: Dict[str, Permission] = {}
        
        # Initialize default roles and permissions
        self._initialize_defaults()
    
    def _initialize_defaults(self) -> None:
        """Initialize default roles and permissions"""
        # Default permissions
        self.add_permission("read:agents", "Read agent information")
        self.add_permission("write:agents", "Modify agent configuration")
        self.add_permission("execute:agents", "Execute agent workflows")
        self.add_permission("admin", "Full administrative access")
        
        # Default roles
        self.add_role("viewer", "Read-only access", ["read:agents"])
        self.add_role("operator", "Operational access", ["read:agents", "execute:agents"])
        self.add_role("admin", "Administrator", ["read:agents", "write:agents", "execute:agents", "admin"])
    
    def add_user(
        self,
        username: str,
        password: str,
        roles: List[str] = None
    ) -> User:
        """
        Add a new user
        
        Args:
            username: Username
            password: Plain text password
            roles: List of role names
        
        Returns:
            Created user
        """
        if username in self.users:
            raise ValueError(f"User '{username}' already exists")
        
        # Hash the password
        password_hash = self._hash_password(password)
        
        # Create user
        user = User(
            username=username,
            password_hash=password_hash,
            roles=roles or []
        )
        
        # Generate an API key
        api_key = self.generate_api_key(username)
        user.api_keys.append(api_key)
        
        # Store user
        self.users[username] = user
        
        return user
    
    def authenticate(self, username: str, password: str) -> Optional[str]:
        """
        Authenticate a user with username and password
        
        Args:
            username: Username
            password: Plain text password
        
        Returns:
            JWT token if authentication successful, None otherwise
        """
        user = self.users.get(username)
        
        if not user or not user.is_active:
            return None
        
        # Check password
        if not self._verify_password(password, user.password_hash):
            return None
        
        # Generate JWT token
        return self._generate_token(username)
    
    def validate_token(self, token: str) -> Optional[str]:
        """
        Validate a JWT token
        
        Args:
            token: JWT token
        
        Returns:
            Username if token is valid, None otherwise
        """
        try:
            payload = jwt.decode(token, self.jwt_secret, algorithms=["HS256"])
            
            # Check expiry
            if payload.get("exp", 0) < time.time():
                return None
            
            # Get username
            username = payload.get("sub")
            
            # Check if user exists and is active
            user = self.users.get(username)
            if not user or not user.is_active:
                return None
            
            return username
            
        except jwt.PyJWTError:
            return None
    
    def validate_api_key(self, api_key: str) -> Optional[str]:
        """
        Validate an API key
        
        Args:
            api_key: API key
        
        Returns:
            Username if API key is valid, None otherwise
        """
        # Check all users for matching API key
        for username, user in self.users.items():
            if api_key in user.api_keys and user.is_active:
                return username
        
        return None
    
    def generate_api_key(self, username: str) -> str:
        """
        Generate a new API key for a user
        
        Args:
            username: Username
        
        Returns:
            Generated API key
        """
        # Generate a random token
        random_token = secrets.token_hex(16)
        
        # Create API key with prefix
        api_key = f"{self.api_key_prefix}{username}_{random_token}"
        
        # Add to user if exists
        user = self.users.get(username)
        if user:
            user.api_keys.append(api_key)
        
        return api_key
    
    def revoke_api_key(self, username: str, api_key: str) -> bool:
        """
        Revoke an API key
        
        Args:
            username: Username
            api_key: API key to revoke
        
        Returns:
            True if successful, False otherwise
        """
        user = self.users.get(username)
        
        if not user:
            return False
        
        if api_key in user.api_keys:
            user.api_keys.remove(api_key)
            return True
        
        return False
    
    def add_role(
        self,
        name: str,
        description: str,
        permissions: List[str] = None
    ) -> Role:
        """
        Add a new role
        
        Args:
            name: Role name
            description: Role description
            permissions: List of permission names
        
        Returns:
            Created role
        """
        if name in self.roles:
            raise ValueError(f"Role '{name}' already exists")
        
        # Create role
        role = Role(
            name=name,
            description=description,
            permissions=permissions or []
        )
        
        # Store role
        self.roles[name] = role
        
        return role
    
    def add_permission(
        self,
        name: str,
        description: str
    ) -> Permission:
        """
        Add a new permission
        
        Args:
            name: Permission name
            description: Permission description
        
        Returns:
            Created permission
        """
        if name in self.permissions:
            raise ValueError(f"Permission '{name}' already exists")
        
        # Create permission
        permission = Permission(
            name=name,
            description=description
        )
        
        # Store permission
        self.permissions[name] = permission
        
        return permission
    
    def has_permission(self, username: str, permission: str) -> bool:
        """
        Check if a user has a specific permission
        
        Args:
            username: Username
            permission: Permission name
        
        Returns:
            True if user has permission, False otherwise
        """
        user = self.users.get(username)
        
        if not user or not user.is_active:
            return False
        
        # Get all permissions for user's roles
        user_permissions = self._get_user_permissions(user)
        
        return permission in user_permissions
    
    def has_role(self, username: str, role: str) -> bool:
        """
        Check if a user has a specific role
        
        Args:
            username: Username
            role: Role name
        
        Returns:
            True if user has role, False otherwise
        """
        user = self.users.get(username)
        
        if not user or not user.is_active:
            return False
        
        return role in user.roles
    
    def _hash_password(self, password: str) -> str:
        """
        Hash a password
        
        Args:
            password: Plain text password
        
        Returns:
            Password hash
        """
        # In production, use a proper password hashing library like bcrypt
        salt = secrets.token_hex(8)
        hash_obj = hashlib.sha256((password + salt).encode())
        return f"{salt}${hash_obj.hexdigest()}"
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """
        Verify a password against a hash
        
        Args:
            password: Plain text password
            password_hash: Password hash
        
        Returns:
            True if password matches hash, False otherwise
        """
        # Split salt and hash
        salt, hash_value = password_hash.split("$", 1)
        
        # Hash the password with the salt
        hash_obj = hashlib.sha256((password + salt).encode())
        
        # Compare hashes
        return hash_obj.hexdigest() == hash_value
    
    def _generate_token(self, username: str) -> str:
        """
        Generate a JWT token for a user
        
        Args:
            username: Username
        
        Returns:
            JWT token
        """
        # Set expiry time
        expiry = int(time.time()) + self.token_expiry
        
        # Create payload
        payload = {
            "sub": username,
            "exp": expiry,
            "iat": int(time.time())
        }
        
        # Generate token
        return jwt.encode(payload, self.jwt_secret, algorithm="HS256")
    
    def _get_user_permissions(self, user: User) -> Set[str]:
        """
        Get all permissions for a user based on their roles
        
        Args:
            user: User object
        
        Returns:
            Set of permission names
        """
        permissions = set()
        
        # Add permissions from each role
        for role_name in user.roles:
            role = self.roles.get(role_name)
            if role:
                permissions.update(role.permissions)
        
        return permissions 