"""
Tests for the Graph Manager

This module contains tests for the core graph manager functionality.
"""


import pytest

from code_review.core.graph_manager import Agent<PERSON><PERSON>, GraphManager


@pytest.fixture
def graph_manager():
    """Create a graph manager for testing"""
    return GraphManager(use_redis=False)


@pytest.fixture
def mock_agent():
    """Create a mock agent for testing"""
    async def mock_agent_fn(state: AgentState) -> AgentState:
        # Simulate agent processing
        state.context["agent_called"] = True
        state.next_steps = ["end"]
        return state
    
    return mock_agent_fn


@pytest.fixture
def mock_router():
    """Create a mock router for testing"""
    def router(state: AgentState):
        # Always route to the test agent
        return ["test_agent"]
    
    return router


def test_create_workflow(graph_manager, mock_agent, mock_router):
    """Test creating a workflow"""
    # Create a workflow
    workflow = graph_manager.create_workflow(
        name="test-workflow",
        agents={"test_agent": mock_agent},
        router=mock_router
    )
    
    # Check the workflow was created
    assert "test-workflow" in graph_manager.graphs
    assert workflow is not None


@pytest.mark.asyncio
async def test_run_workflow(graph_manager, mock_agent, mock_router):
    """Test running a workflow"""
    # Create a workflow
    graph_manager.create_workflow(
        name="test-workflow",
        agents={"test_agent": mock_agent},
        router=mock_router
    )
    
    # Run the workflow
    result = await graph_manager.run_workflow(
        name="test-workflow",
        initial_input={"test": "data"}
    )
    
    # Check the result
    assert result is not None
    assert result.context["test"] == "data"
    assert result.context["agent_called"] is True


@pytest.mark.asyncio
async def test_workflow_with_message(graph_manager, mock_agent, mock_router):
    """Test running a workflow with a message"""
    # Create a workflow
    graph_manager.create_workflow(
        name="test-workflow",
        agents={"test_agent": mock_agent},
        router=mock_router
    )
    
    # Run the workflow with a message
    result = await graph_manager.run_workflow(
        name="test-workflow",
        initial_input={"message": "Test message"}
    )
    
    # Check the result
    assert result is not None
    assert len(result.messages) > 0
    assert result.messages[0]["role"] == "user"
    assert result.messages[0]["content"] == "Test message"


def test_get_workflow(graph_manager, mock_agent, mock_router):
    """Test getting a workflow by name"""
    # Create a workflow
    workflow = graph_manager.create_workflow(
        name="test-workflow",
        agents={"test_agent": mock_agent},
        router=mock_router
    )
    
    # Get the workflow
    retrieved_workflow = graph_manager.get_workflow("test-workflow")
    
    # Check the workflow was retrieved
    assert retrieved_workflow is workflow
    
    # Try getting a non-existent workflow
    non_existent = graph_manager.get_workflow("non-existent")
    assert non_existent is None


@pytest.mark.asyncio
async def test_run_non_existent_workflow(graph_manager):
    """Test running a non-existent workflow"""
    # Try running a non-existent workflow
    with pytest.raises(ValueError, match="not found"):
        await graph_manager.run_workflow(
            name="non-existent",
            initial_input={"test": "data"}
        ) 