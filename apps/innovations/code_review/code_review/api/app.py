"""
API Server

This module provides a FastAPI application for interacting with
the multi-agent system via RESTful API.
"""

import logging
import os

from elasticapm.contrib.starlette import ElasticAPM
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from code_review.api.bitbucket_webhooks import router as bitbucket_webhook_router
from code_review.api.feedback import router as feedback_router

from ..infra import init

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)




os.environ["LANGFUSE_TRACING_ENVIRONMENT"] = os.getenv("ENVIRONMENT", "local")

# Create FastAPI app
app = FastAPI(
    title="Multi-Agent System API",
    description="API for interacting with a modular multi-agent system built with LangGraph",
    version="0.1.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Update with actual origins in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
app.add_middleware(ElasticAPM, client=init.infra.apm)

# Include routers
app.include_router(bitbucket_webhook_router)  # Keep original webhook route unchanged
app.include_router(feedback_router, prefix="/api", tags=["feedback"])


os.environ["TAVILY_API_KEY"] = init.infra.secret.chatbot_ai.TAVILY_APY_KEY

@app.get("/health")
async def health_check():
    """Check the health of the API."""
    logger.debug("Health check endpoint called")
    
    
    # Check Milvus connectivity if URI is set
    milvus_ok = False
    MILVUS_URI = os.getenv("MILVUS_URI", None)
    if MILVUS_URI:
        try:
            # Import pymilvus here to avoid dependency issues if not installed
            from pymilvus import connections
            # Check if we can establish a connection
            connections.connect(alias="health_check", uri=MILVUS_URI)
            milvus_ok = connections.has_connection("health_check")
            # Close the connection
            if milvus_ok:
                connections.disconnect("health_check")
        except Exception as e:
            logger.error(f"Milvus health check failed: {str(e)}")
            milvus_ok = False
    
    return {
        "status": "healthy", 
        "milvus": milvus_ok,
    } 





