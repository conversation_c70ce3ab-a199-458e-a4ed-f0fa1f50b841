"""
Bitbucket Webhooks API

This module provides API routes for handling Bitbucket webhook events.
"""

import hashlib
import hmac
import os
from typing import Any, Dict, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, Header, HTTPException, Request

# Configure logging
from logger import logger

from code_review.handler.bitbucket_handler import BitbucketHand<PERSON>, BitbucketInput
from code_review.models.bitbucket_webhook_models import (
    Account,
    BuildStatusCreatedEvent,
    BuildStatusUpdatedEvent,
    CommitCommentCreatedEvent,
    IssueCommentCreatedEvent,
    IssueCreatedEvent,
    IssueUpdatedEvent,
    PullRequestApprovalRemovedEvent,
    PullRequestApprovedEvent,
    PullRequestChangesRequestCreatedEvent,
    PullRequestChangesRequestRemovedEvent,
    PullRequestCommentCreatedEvent,
    PullRequestCommentDeletedEvent,
    PullRequestCommentUpdatedEvent,
    PullRequestCreatedEvent,
    PullRequestDeclinedEvent,
    PullRequestMergedEvent,
    PullRequestUpdatedEvent,
    RepositoryForkEvent,
    RepositoryPushEvent,
    RepositoryTransferEvent,
    RepositoryUpdatedEvent,
)

# Create router
router = APIRouter(prefix="/webhooks/bitbucket", tags=["bitbucket"])

# Get webhook secret from environment
WEBHOOK_SECRET = os.environ.get("BITBUCKET_WEBHOOK_SECRET", "")

async def verify_webhook_signature(request: Request, x_hub_signature: Optional[str] = Header(None)) -> bool:
    """
    Verify the webhook signature to ensure it came from Bitbucket.
    
    Args:
        request: The incoming request
        x_hub_signature: The signature header from Bitbucket
        
    Returns:
        True if signature is valid or verification is disabled
    
    Raises:
        HTTPException: If signature verification fails
    """
    # If no webhook secret is configured, skip verification
    if not WEBHOOK_SECRET:
        logger.warning("BITBUCKET_WEBHOOK_SECRET not set. Webhook signature verification disabled.")
        return True
    
    # If no signature provided, reject the request
    if not x_hub_signature:
        raise HTTPException(status_code=401, detail="Missing X-Hub-Signature header")
    
    # Get the raw request body
    body = await request.body()
    
    # Calculate signature
    signature = hmac.new(
        WEBHOOK_SECRET.encode('utf-8'),
        body,
        hashlib.sha256
    ).hexdigest()
    
    # Compare signatures
    expected_signature = f"sha256={signature}"
    if not hmac.compare_digest(expected_signature, x_hub_signature):
        raise HTTPException(status_code=401, detail="Invalid webhook signature")
    
    return True


async def itsMe(actor: Account) -> bool:
    """
    Check if the actor is the user we are looking for
    """
    if actor.account_id == "712020:1ccb271d-b7fc-4f0f-af42-b530f1a7846e" or actor.display_name == "MAS Project" or actor.uuid == "{a46b4179-61e8-4b9b-8a84-bcb5215ceb03}":
        return True
    return False

@router.post("/", status_code=202)
async def handle_bitbucket_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    verified: bool = Depends(verify_webhook_signature)
):
    """
    Generic handler for all Bitbucket webhook events.
    This endpoint will parse the event type and dispatch to the appropriate handler.
    """
    # Get the request body as JSON
    payload = await request.json()
    
    # Get the event type from headers
    event_key = request.headers.get("X-Event-Key", "")
    
    if not event_key:
        raise HTTPException(status_code=400, detail="Missing X-Event-Key header")
    
    logger.info(f"Received Bitbucket webhook event: {event_key}")

    background_tasks.add_task(process_webhook_event, event_key, payload)
    
    # Return immediate response
    return {"status": "ok", "message": "Webhook received and will be processed in background"}
async def process_webhook_event(event_key: str, payload: Dict[str, Any]):

    
    # Dispatch to the appropriate handler based on event type
    try:
        if event_key == "repo:push":
            return await handle_repository_push(RepositoryPushEvent(**payload))
        elif event_key == "repo:fork":
            return await handle_repository_fork(RepositoryForkEvent(**payload))
        elif event_key == "repo:updated":
            return await handle_repository_updated(RepositoryUpdatedEvent(**payload))
        elif event_key == "repo:transfer":
            return await handle_repository_transfer(RepositoryTransferEvent(**payload))
        elif event_key == "repo:commit_comment_created":
            return await handle_commit_comment_created(CommitCommentCreatedEvent(**payload))
        elif event_key == "repo:commit_status_created":
            return await handle_build_status_created(BuildStatusCreatedEvent(**payload))
        elif event_key == "repo:commit_status_updated":
            return await handle_build_status_updated(BuildStatusUpdatedEvent(**payload))
        elif event_key == "issue:created":
            return await handle_issue_created(IssueCreatedEvent(**payload))
        elif event_key == "issue:updated":
            return await handle_issue_updated(IssueUpdatedEvent(**payload))
        elif event_key == "issue:comment_created":
            return await handle_issue_comment_created(IssueCommentCreatedEvent(**payload))
        elif event_key == "pullrequest:created":
            return await handle_pull_request_created(PullRequestCreatedEvent(**payload))
        elif event_key == "pullrequest:updated":
            return await handle_pull_request_updated(PullRequestUpdatedEvent(**payload))
        elif event_key == "pullrequest:approved":
            return await handle_pull_request_approved(PullRequestApprovedEvent(**payload))
        elif event_key == "pullrequest:unapproved":
            return await handle_pull_request_approval_removed(PullRequestApprovalRemovedEvent(**payload))
        elif event_key == "pullrequest:fulfilled":
            return await handle_pull_request_merged(PullRequestMergedEvent(**payload))
        elif event_key == "pullrequest:rejected":
            return await handle_pull_request_declined(PullRequestDeclinedEvent(**payload))
        elif event_key == "pullrequest:comment_created":
            return await handle_pull_request_comment_created(PullRequestCommentCreatedEvent(**payload))
        elif event_key == "pullrequest:comment_updated":
            return await handle_pull_request_comment_updated(PullRequestCommentUpdatedEvent(**payload))
        elif event_key == "pullrequest:comment_deleted":
            return await handle_pull_request_comment_deleted(PullRequestCommentDeletedEvent(**payload))
        elif event_key == "pullrequest:changes_request_created":
            return await handle_pull_request_changes_request_created(PullRequestChangesRequestCreatedEvent(**payload))
        elif event_key == "pullrequest:changes_request_removed":
            return await handle_pull_request_changes_request_removed(PullRequestChangesRequestRemovedEvent(**payload))
        else:
            logger.warning(f"Unsupported Bitbucket webhook event: {event_key}")
            return {"status": "ignored", "event": event_key}
    except Exception as e:
        logger.exception(f"Error processing Bitbucket webhook event {event_key}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing webhook: {str(e)}")

# Repository event handlers

async def handle_repository_push(event: RepositoryPushEvent) -> Dict[str, Any]:
    """Handle repository push event"""
    logger.info(f"Repository push event in {event.repository.full_name} by {event.actor.username}")
    
    # Process push event
    # You can add your business logic here or call the BitbucketAgent
    
    return {
        "status": "processed",
        "event": "repo:push",
        "repository": event.repository.full_name,
        "actor": event.actor.username
    }

async def handle_repository_fork(event: RepositoryForkEvent) -> Dict[str, Any]:
    """Handle repository fork event"""
    logger.info(f"Repository fork event: {event.repository.full_name} forked to {event.fork.full_name}")
    
    return {
        "status": "processed",
        "event": "repo:fork",
        "repository": event.repository.full_name,
        "fork": event.fork.full_name
    }

async def handle_repository_updated(event: RepositoryUpdatedEvent) -> Dict[str, Any]:
    """Handle repository updated event"""
    logger.info(f"Repository updated event: {event.repository.full_name} by {event.actor.username}")
    
    return {
        "status": "processed",
        "event": "repo:updated",
        "repository": event.repository.full_name
    }

async def handle_repository_transfer(event: RepositoryTransferEvent) -> Dict[str, Any]:
    """Handle repository transfer event"""
    logger.info(f"Repository transfer event: {event.repository.full_name} from {event.previous_owner.username}")
    
    return {
        "status": "processed",
        "event": "repo:transfer",
        "repository": event.repository.full_name,
        "previous_owner": event.previous_owner.username,
        "new_owner": event.repository.owner.username
    }

async def handle_commit_comment_created(event: CommitCommentCreatedEvent) -> Dict[str, Any]:
    """Handle commit comment created event"""
    logger.info(f"Commit comment created in {event.repository.full_name} by {event.actor.username}")
    
    return {
        "status": "processed",
        "event": "repo:commit_comment_created",
        "repository": event.repository.full_name,
        "commit": event.commit.get("hash", "")
    }

async def handle_build_status_created(event: BuildStatusCreatedEvent) -> Dict[str, Any]:
    """Handle build status created event"""
    logger.info(f"Build status created in {event.repository.full_name}")
    
    return {
        "status": "processed",
        "event": "repo:commit_status_created",
        "repository": event.repository.full_name
    }

async def handle_build_status_updated(event: BuildStatusUpdatedEvent) -> Dict[str, Any]:
    """Handle build status updated event"""
    logger.info(f"Build status updated in {event.repository.full_name}")
    
    return {
        "status": "processed",
        "event": "repo:commit_status_updated",
        "repository": event.repository.full_name
    }

# Issue event handlers

async def handle_issue_created(event: IssueCreatedEvent) -> Dict[str, Any]:
    """Handle issue created event"""
    logger.info(f"Issue created in {event.repository.full_name}: {event.issue.title}")
    
    return {
        "status": "processed",
        "event": "issue:created",
        "repository": event.repository.full_name,
        "issue_id": event.issue.id,
        "title": event.issue.title
    }

async def handle_issue_updated(event: IssueUpdatedEvent) -> Dict[str, Any]:
    """Handle issue updated event"""
    logger.info(f"Issue updated in {event.repository.full_name}: {event.issue.title}")
    
    return {
        "status": "processed",
        "event": "issue:updated",
        "repository": event.repository.full_name,
        "issue_id": event.issue.id,
        "title": event.issue.title
    }

async def handle_issue_comment_created(event: IssueCommentCreatedEvent) -> Dict[str, Any]:
    """Handle issue comment created event"""
    logger.info(f"Issue comment created in {event.repository.full_name} on issue #{event.issue.id}")
    
    return {
        "status": "processed",
        "event": "issue:comment_created",
        "repository": event.repository.full_name,
        "issue_id": event.issue.id
    }

# Pull request event handlers
async def handle_pull_request_created(event: PullRequestCreatedEvent) -> Dict[str, Any]:
    """Handle pull request created event"""
    logger.info(f"Pull request created in {event.repository.full_name}: {event.pullrequest.title}")
    
    # Create a BitbucketAgent instance and process the pull request
    
    # Example: Review the pull request with the BitbucketAgent
    # This would typically be done asynchronously in a production environment
    try:
        handler = BitbucketHandler()
        workspace = event.repository.workspace.slug
        repository = event.repository.full_name.replace(f"{workspace}/", "")
        pr_id = event.pullrequest.id

        result = await handler.run(
            BitbucketInput(
                pull_request_id=f"{pr_id}",
                repository_slug=repository,
                workspace_slug=workspace
                )
            )

        
        # Process the pull request asynchronously
        # In production, you would typically queue this for background processing
        # result = await bitbucket_agent.execute({"messages": [], "context": context})
        
        return {
            "status": "processing",
            "event": "pullrequest:created",
            "repository": event.repository.full_name,
            "pull_request_id": event.pullrequest.id,
            "title": event.pullrequest.title
        }
    except Exception as e:
        logger.error(f"Error processing pull request: {str(e)}")
        return {
            "status": "error",
            "event": "pullrequest:created",
            "error": str(e),
            "repository": event.repository.full_name,
            "pull_request_id": event.pullrequest.id
        }

async def handle_pull_request_updated(event: PullRequestUpdatedEvent) -> Dict[str, Any]:
    """Handle pull request updated event"""
    logger.info(f"Pull request updated in {event.repository.full_name}: {event.pullrequest.title}")
    
    try:
        if await itsMe(event.actor):
            logger.info(f"Pull request updated by {event.actor.display_name} is skipped")
            return {
                "status": "Skipped",
                "event": "pullrequest:updated",
                "repository": event.repository.full_name,
                "pull_request_id": event.pullrequest.id
            }
        
        handler = BitbucketHandler()
        workspace = event.repository.workspace.slug
        repository = event.repository.full_name.replace(f"{workspace}/", "")
        pr_id = event.pullrequest.id

        result = await handler.run(
            BitbucketInput(
                pull_request_id=f"{pr_id}",
                repository_slug=repository,
                workspace_slug=workspace
                )
            )


        
        # Process the pull request asynchronously
        # In production, you would typically queue this for background processing
        # result = await bitbucket_agent.execute({"messages": [], "context": context})
        
        return {
            "status": "processing",
            "event": "pullrequest:created",
            "repository": event.repository.full_name,
            "pull_request_id": event.pullrequest.id,
            "title": event.pullrequest.title
        }
    except Exception as e:
        logger.error(f"Error processing pull request: {str(e)}")
        return {
            "status": "error",
            "event": "pullrequest:created",
            "error": str(e),
            "repository": event.repository.full_name,
            "pull_request_id": event.pullrequest.id
        }

async def handle_pull_request_approved(event: PullRequestApprovedEvent) -> Dict[str, Any]:
    """Handle pull request approved event"""
    logger.info(f"Pull request approved in {event.repository.full_name} by {event.actor.username}")
    
    return {
        "status": "processed",
        "event": "pullrequest:approved",
        "repository": event.repository.full_name,
        "pull_request_id": event.pullrequest.id,
        "approver": event.actor.username
    }

async def handle_pull_request_approval_removed(event: PullRequestApprovalRemovedEvent) -> Dict[str, Any]:
    """Handle pull request approval removed event"""
    logger.info(f"Pull request approval removed in {event.repository.full_name} by {event.actor.username}")
    
    return {
        "status": "processed",
        "event": "pullrequest:unapproved",
        "repository": event.repository.full_name,
        "pull_request_id": event.pullrequest.id,
        "actor": event.actor.username
    }

async def handle_pull_request_merged(event: PullRequestMergedEvent) -> Dict[str, Any]:
    """Handle pull request merged event"""
    logger.info(f"Pull request merged in {event.repository.full_name} by {event.actor.username}")
    
    return {
        "status": "processed",
        "event": "pullrequest:fulfilled",
        "repository": event.repository.full_name,
        "pull_request_id": event.pullrequest.id,
        "merger": event.actor.username
    }

async def handle_pull_request_declined(event: PullRequestDeclinedEvent) -> Dict[str, Any]:
    """Handle pull request declined event"""
    logger.info(f"Pull request declined in {event.repository.full_name} by {event.actor.username}")
    
    return {
        "status": "processed",
        "event": "pullrequest:rejected",
        "repository": event.repository.full_name,
        "pull_request_id": event.pullrequest.id,
        "decliner": event.actor.username
    }

async def handle_pull_request_comment_created(event: PullRequestCommentCreatedEvent) -> Dict[str, Any]:
    """Handle pull request comment created event"""
    logger.info(f"Pull request comment created in {event.repository.full_name} by {event.actor.username}")
    
    return await handle_pull_request_comment(event)

async def handle_pull_request_comment_updated(event: PullRequestCommentUpdatedEvent) -> Dict[str, Any]:
    """Handle pull request comment updated event"""
    logger.info(f"Pull request comment updated in {event.repository.full_name} by {event.actor.username}")
    
    return await handle_pull_request_comment(event)

async def handle_pull_request_comment_deleted(event: PullRequestCommentDeletedEvent) -> Dict[str, Any]:
    """Handle pull request comment deleted event"""
    logger.info(f"Pull request comment deleted in {event.repository.full_name}")
    
    return {
        "status": "processed",
        "event": "pullrequest:comment_deleted",
        "repository": event.repository.full_name,
        "pull_request_id": event.pullrequest.id,
        "comment_id": event.comment.id
    }

async def handle_pull_request_changes_request_created(event: PullRequestChangesRequestCreatedEvent) -> Dict[str, Any]:
    """Handle pull request changes request created event"""
    logger.info(f"Pull request changes requested in {event.repository.full_name} by {event.actor.username}")
    
    return {
        "status": "processed",
        "event": "pullrequest:changes_request_created",
        "repository": event.repository.full_name,
        "pull_request_id": event.pullrequest.id,
        "requester": event.actor.username
    }

async def handle_pull_request_changes_request_removed(event: PullRequestChangesRequestRemovedEvent) -> Dict[str, Any]:
    """Handle pull request changes request removed event"""
    logger.info(f"Pull request changes request removed in {event.repository.full_name} by {event.actor.username}")
    
    return {
        "status": "processed",
        "event": "pullrequest:changes_request_removed",
        "repository": event.repository.full_name,
        "pull_request_id": event.pullrequest.id,
        "actor": event.actor.username
    } 


async def handle_pull_request_comment(event: PullRequestCommentCreatedEvent) -> Dict[str, Any]:
    try:
        if await itsMe(event.actor):
            logger.info(f"Pull request comment created by {event.actor.display_name} is skipped")
            return {
                "status": "Skipped",
                "event": "pullrequest:comment_created",
                "repository": event.repository.full_name,
                "pull_request_id": event.pullrequest.id,
                "comment_id": event.comment.id
            }
        
        handler = BitbucketHandler()
        workspace = event.repository.workspace.slug
        repository = event.repository.full_name.replace(f"{workspace}/", "")
        pr_id = event.pullrequest.id
        if event.comment.user.account_id == "603e1253c668f4006afbd5aa":
            return {
            "status": "Skipped",
            "event": "pullrequest:comment_created",
            "repository": event.repository.full_name,
            "pull_request_id": event.pullrequest.id,
            "comment_id": event.comment.id
        }

        result = await handler.run(
            BitbucketInput(
                pull_request_id=f"{pr_id}",
                repository_slug=repository,
                workspace_slug=workspace,
                comment=event.comment.model_dump()
                )
            )

        
        # Process the pull request asynchronously
        # In production, you would typically queue this for background processing
        # result = await bitbucket_agent.execute({"messages": [], "context": context})
        
        return {
            "status": "processing",
            "event": "pullrequest:created",
            "repository": event.repository.full_name,
            "pull_request_id": event.pullrequest.id,
            "title": event.pullrequest.title
        }
    except Exception as e:
        logger.error(f"Error processing pull request: {str(e)}")
        return {
            "status": "error",
            "event": "pullrequest:created",
            "error": str(e),
            "repository": event.repository.full_name,
            "pull_request_id": event.pullrequest.id
        }