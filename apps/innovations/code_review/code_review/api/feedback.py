import os
from typing import Literal

from corelanggraph.langfuse import LangfuseClient
from fastapi import APIRouter, Depends, HTTPException
from logger import logger
from pydantic import BaseModel

router = APIRouter()

class FeedbackRequest(BaseModel):
    type: Literal['like', 'dislike']
    trace_id: str  # The trace ID from the code review process

langfuse = LangfuseClient(
    secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
    public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
    host=os.getenv("LANGFUSE_HOST")
).client

@router.get("/feedback")
async def handle_feedback(feedback: FeedbackRequest = Depends()):
    """
    Handle feedback for code reviews.
    Adds 1 for like and 0 for dislike to the user feedback metric evaluation.
    Also adds the score to the Langfuse trace.
    """
    try:
        # Calculate metric value (1 for like, 0 for dislike)
        metric_value = 1 if feedback.type == 'like' else 0
        
        # Add score to Langfuse trace using the provided trace ID
        langfuse.score(
            name="code_review_feedback",
            value=metric_value,
            trace_id=feedback.trace_id,
            comment=f"User {feedback.type}d the code review"
        )
        
        # TODO: Store the feedback in your metrics database
        # This is where you would implement the actual storage logic
        logger.info(f"Received {feedback.type} feedback. Metric value: {metric_value}")
        
        return {
            "status": "success",
            "message": f"Feedback recorded",
            "metric_value": metric_value
        }
        
    except Exception as e:
        logger.error(f"Error processing feedback: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 