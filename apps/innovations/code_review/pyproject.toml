[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mas.code-review"
version = "0.1.0"
description = "Code Review Application for MAS"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "reouven<PERSON>@cymulate.com" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
]
license = "LicenseRef-Proprietary"
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = [
    # Core dependencies
    "fastapi>=0.109.0",
    "uvicorn>=0.25.0",
    "pydantic>=2.5.0",
    "httpx>=0.26.0",
    # Workspace packages
    "corelanggraph",
    "logger",
    "secretmanager",
    "storage",
    "atlassian-python-api>=4.0.4",
    "sentence-transformers==3.3.1",
    "ratelimit==2.2.1",
]

[project.optional-dependencies]
dev = ["pytest>=7.4.0", "black>=23.12.0", "isort>=5.12.0", "mypy>=1.7.1"]

[tool.ruff]
select = ["I", "F401"]


[project.scripts]
code-review = "server:main"

[project.urls]
"Homepage" = "https://bitbucket.org/cymulate/pythonlibs"
"Bug Tracker" = "https://bitbucket.org/cymulate/pythonlibs/jira"

[tool.setuptools.packages.find]
where = ["."]
include = ["*"]

# Use the same formatting and linting configuration as the root project
[tool.black]
line-length = 88
target-version = ["py312"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.uv.sources]
logger = { workspace = true }
corelanggraph = { workspace = true }
secretmanager = { workspace = true }
storage = { workspace = true }
