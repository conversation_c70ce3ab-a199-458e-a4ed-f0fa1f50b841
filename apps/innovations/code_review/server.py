"""
Main entry point for the Codebase RAG application.
"""

import os
import traceback
import uvicorn
# The app is now correctly exported from app.main

def main():
    uvicorn.run("code_review.api.app:app", host="0.0.0.0", port=int(os.getenv("PORT", 19000)), reload=True) 




if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Error: {e}")
        print(f"Error type: {type(e)}")
        print(f"Error traceback: {traceback.format_exc()}")
        print(f"Error message: {str(e)}")
        print(f"Error args: {e.args}")