# Code Review Application

This is the Code Review Application for the MAS (Modular Multi-Agent System).

## Description

The Code Review Application provides automated code review capabilities using AI agents and integrations with various development tools.

## Features

- AI-powered code analysis
- Integration with Bitbucket
- Security scanning
- Workflow orchestration
- Multi-agent collaboration

## Structure

- `agents/` - AI agents for code review
- `api/` - REST API endpoints
- `core/` - Core functionality
- `handler/` - Event handlers
- `integrations/` - External service integrations
- `models/` - Data models
- `orchestration/` - Workflow orchestration
- `security/` - Security scanning components
- `utils/` - Utility functions
- `workflow/` - Workflow definitions 