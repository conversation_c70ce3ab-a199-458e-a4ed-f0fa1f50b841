{"name": "code_review", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/innovations/code_review", "targets": {"dev": {"executor": "@nxlv/python:run-commands", "options": {"command": "uv run apps/innovations/code_review/server.py"}}, "lock": {"executor": "@nxlv/python:lock", "options": {"update": false}}, "sync": {"executor": "@nxlv/python:sync", "options": {}}, "add": {"executor": "@nxlv/python:add", "options": {}}, "update": {"executor": "@nxlv/python:update", "options": {}}, "remove": {"executor": "@nxlv/python:remove", "options": {}}, "build": {"executor": "@nxlv/python:build", "outputs": ["{projectRoot}/dist"], "options": {"outputPath": "{projectRoot}/dist", "publish": false, "lockedVersions": true, "bundleLocalDependencies": true}, "cache": true}, "lint": {"executor": "@nxlv/python:ruff-check", "outputs": [], "options": {"lintFilePatterns": ["code_review", "tests"]}, "cache": true}, "format": {"executor": "@nxlv/python:ruff-format", "outputs": [], "options": {"filePatterns": ["code_review", "tests"]}, "cache": true}, "test": {"executor": "@nxlv/python:run-commands", "outputs": ["{workspaceRoot}/reports/{projectRoot}/unittests", "{workspaceRoot}/coverage/{projectRoot}"], "options": {"command": "uv run pytest tests/", "cwd": "{projectRoot}"}, "cache": true}, "install": {"executor": "@nxlv/python:install", "options": {"silent": false, "args": "", "verbose": false, "debug": false}}, "docker": {"executor": "@nx-tools/nx-container:build", "options": {"file": "{workspaceRoot}/Dockerfile", "push": false, "build-args": ["APPS_PATH={projectRoot}", "PACKAGES_PATH=packages"], "tags": ["innovations/code_review:latest", "innovations/code_review:{projectVersion}"], "metadata": {"images": ["product/siem_rule"], "tags": ["type=schedule", "type=ref,event=branch", "type=ref,event=tag", "type=ref,event=pr", "type=semver,pattern={{version}}", "type=semver,pattern={{major}}.{{minor}}", "type=semver,pattern={{major}}", "type=sha"]}}, "cache": true}}, "tags": [], "release": {"version": {"versionActions": "@nxlv/python/src/release/version-actions"}}}