# Cyber Security Application

This is the Cyber Security Application for the MAS (Modular Multi-Agent System).

## Description

The Cyber Security Application provides automated security assessment and template generation capabilities using AI agents and LangGraph workflows. It integrates with external services like Elasticsearch, MongoDB, and various security platforms to create tailored attack simulation templates.

## Features

- **Template Generator Agent**: Interactive AI agent that creates customized security assessment templates
- **Multi-Agent Workflow**: LangGraph-based orchestration of specialized security agents
- **Interactive Conversation Flow**: Guided questioning to collect security requirements
- **Scenario Selection**: Intelligent selection of attack scenarios based on organization profile
- **Integration Support**: Connects with Elasticsearch, MongoDB, and security platforms
- **Configurable Infrastructure**: Optional external service dependencies for development
- **LLM-Based Guardrails**: Intelligent content filtering using Claude via AWS Bedrock
- **Context-Aware Safety**: Understands legitimate vs malicious requests through AI classification

## Template Generator Agent

### What It Does

The Template Generator Agent is an interactive AI system that creates tailored cybersecurity assessment templates through a conversational interface:

1. **Requirements Gathering**: Asks users about their security focus areas:
   - Cloud infrastructure testing
   - Security control validation (EDR, SIEM, WAF, etc.)
   - Compliance framework alignment (ISO 27001, PCI-DSS, HIPAA, etc.)

2. **Interactive Flow**: Guides users through structured questions to collect:
   - Organization details and industry sector
   - Security controls and platforms in use
   - Team size and assessment cadence preferences
   - Operating systems and cloud environments
   - Compliance requirements

3. **Scenario Selection**: Uses collected requirements to:
   - Query threat intelligence databases
   - Select relevant attack scenarios from BAS2 scenarios
   - Match APT groups to organization profile
   - Calculate appropriate simulation volume

4. **Template Generation**: Creates comprehensive assessment templates with:
   - Customized attack scenarios
   - Time-based execution planning
   - Compliance-aligned testing procedures
   - Industry-specific threat modeling

### Agent Architecture

The template generator uses a multi-agent workflow with specialized components:

- **PreRequirementsAgent**: Handles interactive conversation and requirement collection
- **ClassifierAgent**: Categorizes user inputs and determines workflow paths
- **ThreatQueryAgent**: Queries threat intelligence and scenario databases
- **TemplateBuilderAgent**: Assembles final assessment templates
- **FreeTextAgent**: Handles unstructured user inputs

## Guardrails System

### Overview

The application implements intelligent content filtering using LLM-based guardrails to ensure conversations stay focused on cybersecurity topics while preventing malicious requests.

### Architecture

**Workflow Flow**: `START → GuardrailsAgent → SupervisorAgent → END`

1. **GuardrailsAgent**: First line of defense that filters all incoming messages
2. **SupervisorAgent**: Routes approved messages to specialized agents
3. **Specialized Agents**: Handle domain-specific cybersecurity tasks

### Guardrails Implementation

**LLM-Based Classification**:
- Uses Claude 3.5 Haiku via AWS Bedrock for intelligent content analysis
- Context-aware filtering (distinguishes "make cake" from "make malware")
- Configurable prompts stored in `guardrails/config.yml`

**Content Policy**:
- **ALLOW**: Cybersecurity defense, assessments, platform features, security education
- **BLOCK**: Off-topic content (cooking, general topics), malicious requests

**Key Features**:
- **No False Positives**: Understands context and intent
- **Message Deduplication**: Prevents repetitive blocking messages
- **Single Processing**: Each state processed once to avoid loops
- **Graceful Fallback**: Continues operation if LLM classification fails

### Configuration

```yaml
# guardrails/config.yml
models:
  - type: main
    engine: bedrock
    model: us.anthropic.claude-3-haiku-20240307-v1:0

prompts:
  - task: content_classification
    content: |
      You are a content filter for a cybersecurity AI assistant.
      
      ALLOW if the message is about:
      - Cybersecurity defense, best practices, education
      - Security assessments, threat analysis
      - Platform features and documentation
      
      BLOCK if the message is about:
      - Off-topic subjects (cooking, recipes, general topics)
      - Harmful or malicious content
      
      Message: "{{ user_input }}"
      
      Respond with JSON: {{"allow": true/false, "reason": "brief explanation"}}
```

### Integration Status

**Completed:**
- ✅ Agent integration into MAS framework
- ✅ LangGraph workflow implementation
- ✅ Interactive conversation flow
- ✅ Message format compatibility fixes
- ✅ State management with add_messages reducer
- ✅ Optional infrastructure dependencies
- ✅ Session handling for development
- ✅ LLM-based guardrails implementation
- ✅ Context-aware content filtering
- ✅ Message deduplication system
- ✅ AWS Bedrock Claude integration

**Remaining Tasks:**

1. **Configuration Integration**
   - [ ] Extract client configuration from `config["configurable"]`
   - [ ] Replace hardcoded infrastructure with configuration-driven resources
   - [ ] Implement proper client_id and user_token handling
   - [ ] Add configuration validation for required fields

2. **State Management**
   - [ ] Populate user_profile from configuration
   - [ ] Set metadata with client information
   - [ ] Initialize event_data for proper tracking
   - [ ] Handle session continuity across workflow runs

3. **Resource Management**
   - [ ] Make Elasticsearch index names configurable per client
   - [ ] Implement dynamic model selection based on configuration
   - [ ] Add client-specific embedding model support
   - [ ] Configure database connections per environment

4. **Error Handling**
   - [ ] Add proper fallback mechanisms for missing configuration
   - [ ] Implement graceful degradation when external services unavailable
   - [ ] Add comprehensive logging for debugging
   - [ ] Handle authentication failures properly

5. **Testing & Validation**
   - [ ] Test with real client configurations
   - [ ] Validate conversation flow with actual user scenarios
   - [ ] Test template generation with various requirement combinations
   - [ ] Verify integration with external security platforms

6. **Performance Optimization**
   - [ ] Optimize scenario selection algorithms
   - [ ] Implement caching for frequently accessed data
   - [ ] Add async processing for large scenario datasets
   - [ ] Optimize LLM calls and token usage

## Structure

- `agents/` - AI agents for security analysis
  - `template_generator/` - Template generation workflow and agents
    - `agents/` - Individual agent implementations
    - `graph/` - LangGraph workflow definitions
    - `models/` - Data models and schemas
    - `prompts/` - System prompts for agents
    - `services/` - Business logic services
    - `tools/` - Agent tools and utilities
    - `workflow/` - Workflow orchestration
  - `utilities/` - Utility agents
    - `guardrails_agent.py` - LLM-based content filtering agent
  - `supervisor.py` - Main supervisor agent for workflow orchestration
- `api/` - REST API endpoints
- `events/` - Event processing
- `guardrails/` - Guardrails configuration
  - `config.yml` - LLM models and classification prompts
- `handlers/` - Event handlers
- `helpers/` - Helper utilities
- `infra/` - Infrastructure components
- `integrations/` - External service integrations
- `models/` - Data models
- `triggers/` - Event triggers
- `workflow/` - Workflow definitions
  - `cyber_security.py` - Main workflow with guardrails integration

## Development

### Running Template Generator

1. Start LangGraph Studio:
   ```bash
   cd mas/apps/product/cyber_security
   langgraph dev
   ```

2. Access the workflow at: `https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024`

3. Use the `template_generator` graph for interactive testing

### Environment Variables

```bash
# Required
OPENAI_API_KEY=your_openai_key
MODEL_NAME=azure_model_2
EMBEDDING_MODEL_NAME=azure_text_embedding_3_small

# AWS Bedrock (for guardrails)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1

# Optional (for full functionality)
ELASTICSEARCH_URL=your_elasticsearch_url
MONGODB_URL=your_mongodb_url
BAS2_SCENARIOS_INDEX=cy-ai-bas2-scenarios
``` 