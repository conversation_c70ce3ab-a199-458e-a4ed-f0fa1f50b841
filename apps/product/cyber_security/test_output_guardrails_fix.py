#!/usr/bin/env python3
"""
Quick test to verify output guardrails message replacement works correctly.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from langchain_core.messages import HumanMessage, AIMessage
from cyber_security.workflow.state import CyberSecuritySupervisorState
from cyber_security.agents.utilities import OutputGuardrailsAgent


async def test_message_replacement():
    """Test that output guardrails properly replaces unsafe messages"""
    print("🧪 Testing Output Guardrails Message Replacement...")
    
    agent = OutputGuardrailsAgent()
    
    # Test case 1: Safe message (should pass through)
    print("\n  Test 1: Safe cybersecurity message")
    safe_message = AIMessage(
        content="To improve your security posture, implement multi-factor authentication and keep systems updated.",
        id="safe_msg_123"
    )
    
    state = CyberSecuritySupervisorState(
        messages=[
            HumanMessage(content="How can I improve security?"),
            safe_message
        ]
    )
    
    result = await agent.execute(state, None)
    print(f"    Result type: {type(result)}")
    print(f"    Messages in result: {len(result.get('messages', []))}")
    
    if result.get('messages'):
        last_msg = result['messages'][-1]
        print(f"    Last message content: {last_msg.content[:50]}...")
        print(f"    Has guardrails metadata: {'output_guardrails_checked' in (last_msg.metadata or {})}")
    
    # Test case 2: Unsafe message (should be replaced)
    print("\n  Test 2: Unsafe message with exploit code")
    unsafe_message = AIMessage(
        content="Here's a SQL injection payload: ' OR 1=1; DROP TABLE users; --",
        id="unsafe_msg_456"
    )
    
    state = CyberSecuritySupervisorState(
        messages=[
            HumanMessage(content="Show me SQL injection"),
            unsafe_message
        ]
    )
    
    result = await agent.execute(state, None)
    print(f"    Result type: {type(result)}")
    print(f"    Messages in result: {len(result.get('messages', []))}")
    print(f"    Output blocked flag: {result.get('output_blocked_by_guardrails', False)}")
    
    if result.get('messages'):
        # Should have RemoveMessage and new safe message
        for i, msg in enumerate(result['messages']):
            print(f"    Message {i}: {type(msg).__name__}")
            if hasattr(msg, 'content'):
                print(f"      Content: {msg.content[:50]}...")
    
    # Test case 3: Message without ID (edge case)
    print("\n  Test 3: Message without ID")
    no_id_message = AIMessage(
        content="This is a test message without ID"
    )
    
    state = CyberSecuritySupervisorState(
        messages=[
            HumanMessage(content="Test question"),
            no_id_message
        ]
    )
    
    result = await agent.execute(state, None)
    print(f"    Result type: {type(result)}")
    print(f"    Messages in result: {len(result.get('messages', []))}")


async def test_workflow_integration():
    """Test that the workflow can be compiled with the updated agent"""
    print("\n🧪 Testing Workflow Integration...")
    
    try:
        from cyber_security.workflow.cyber_security import base_workflow
        
        # Try to compile the workflow
        workflow = base_workflow(checkpointer=None)
        print("    ✅ Workflow compiles successfully")
        
        # Check nodes
        nodes = list(workflow.graph.nodes.keys())
        print(f"    Nodes: {nodes}")
        
        has_output_guardrails = "output_guardrails_agent" in nodes
        print(f"    {'✅' if has_output_guardrails else '❌'} Output guardrails agent present")
        
    except Exception as e:
        print(f"    ❌ Workflow compilation failed: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Run all tests"""
    print("🛡️ Testing Output Guardrails Fix\n")
    
    # Set up environment warning
    if not os.getenv("AWS_ACCESS_KEY_ID"):
        print("⚠️  Warning: AWS credentials not set. LLM classification will use fallback.")
    
    try:
        await test_message_replacement()
        await test_workflow_integration()
        
        print("\n🎉 All tests completed!")
        print("\n📝 Next steps:")
        print("   1. Test with real workflow execution")
        print("   2. Verify message replacement in LangGraph Studio")
        print("   3. Check that users receive the safe responses")
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
