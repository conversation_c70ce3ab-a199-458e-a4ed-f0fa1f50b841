# MongoDB Configuration Guide

## Current Issue
The template generator can't connect to MongoDB to fetch scenarios, causing the error:
```
ERROR | scenario_service.py:197 | 💥 Unhandled exception in fetch_scenarios
WARNING | No scenarios found for the given pre-requirements.
```

## Configuration Options

### Option 1: Environment Variables (Recommended for Development)
Add these to your `.env` file:

```bash
# MongoDB Configuration
MONGODB_URL=mongodb://localhost:27017/cymulate
MONGODB_DATABASE=cymulate

# Alternative: MongoDB Atlas
# MONGODB_URL=mongodb+srv://username:<EMAIL>/cymulate

# Disable external dependencies for development
SKIP_INFRASTRUCTURE_INIT=true
```

### Option 2: Secret Manager Configuration
The app uses `SecretManagerFactory` to load MongoDB configurations. This requires:
- AWS credentials configured
- Secret Manager access
- Proper secret structure with `db` and `dbs` fields

### Option 3: Mock Data for Development
Create a mock scenario service that returns sample data instead of querying MongoDB.

## Debugging Steps

### 1. Check Infrastructure Logs
Look for these log messages:
- 🚀 Starting infrastructure initialization...
- 🔐 Loading secrets...
- 📊 Loading MongoDB databases...
- ✅ Infrastructure initialization completed successfully!

### 2. Check MongoDB Connection Logs
Look for these log messages:
- 🔌 Connecting to MongoDB with URL: ...
- ✅ Successfully connected to MongoDB (ID: default)
- 📊 Getting database: cymulate
- ✅ Successfully accessed database: cymulate with X collections

### 3. Check for Required Collections
The template generator needs these MongoDB collections:
- `bas2_scenarios` - Attack scenarios
- `_mitreGroups` - APT group information

## Quick Fix for Development

Add this to your environment to skip MongoDB dependency:

```bash
# In your .env file
SKIP_MONGODB=true
USE_MOCK_SCENARIOS=true
```

Then modify the scenario service to return mock data when `USE_MOCK_SCENARIOS=true`.

## Testing Connection

Run this Python script to test your MongoDB connection:

```python
from pymongo import MongoClient
import os

# Test connection
try:
    client = MongoClient(os.getenv('MONGODB_URL', 'mongodb://localhost:27017'))
    client.admin.command('ping')
    print("✅ MongoDB connection successful!")
    
    # Test database access
    db = client['cymulate']
    collections = db.list_collection_names()
    print(f"📊 Found {len(collections)} collections: {collections}")
    
    # Test scenario collection
    if 'bas2_scenarios' in collections:
        count = db['bas2_scenarios'].count_documents({})
        print(f"🎯 Found {count} scenarios in bas2_scenarios collection")
    else:
        print("❌ bas2_scenarios collection not found")
        
except Exception as e:
    print(f"❌ MongoDB connection failed: {e}")
```