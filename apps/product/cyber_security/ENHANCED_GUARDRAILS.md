# Enhanced Guardrails System

## Overview

The cybersecurity application now implements a **dual-layer guardrails system** that validates both incoming user messages and outgoing AI responses. This ensures comprehensive content filtering and safety measures.

## Architecture

### Workflow Flow
```
START → Input Guardrails → Supervisor Agent → Output Guardrails → END
```

### Components

1. **Input Guardrails Agent** (`GuardrailsAgent`)
   - **Purpose**: Validates incoming user messages
   - **Position**: First node after START
   - **Action**: Blocks inappropriate user requests

2. **Output Guardrails Agent** (`OutputGuardrailsAgent`)
   - **Purpose**: Validates AI responses before sending to users
   - **Position**: Last node before END
   - **Action**: Replaces unsafe AI responses with safe alternatives

## Features

### Input Guardrails
- **LLM-Based Classification**: Uses Claude 3.5 Haiku via AWS Bedrock
- **Context-Aware Filtering**: Understands legitimate vs malicious requests
- **Content Policy**:
  - ✅ **ALLOW**: Cybersecurity defense, assessments, platform features, security education
  - ❌ **BLOCK**: Off-topic content, malicious requests, harmful activities

### Output Guardrails
- **Response Validation**: Checks AI responses before delivery
- **Safe Replacement**: Replaces blocked content with appropriate messages
- **Content Policy**:
  - ✅ **ALLOW**: Security best practices, educational content, threat analysis
  - ❌ **BLOCK**: Actual malware code, exploit instructions, harmful tools

## Configuration

### Guardrails Config (`guardrails/config.yml`)

```yaml
models:
  - type: main
    engine: bedrock
    model: us.anthropic.claude-3-haiku-20240307-v1:0

prompts:
  - task: content_classification
    content: |
      You are a content filter for a cybersecurity AI assistant.
      
      ALLOW if the message is about:
      - Cybersecurity defense, best practices, education
      - Security assessments, threat analysis
      - Platform features and documentation
      
      BLOCK if the message is about:
      - Creating malware, exploits, or attack tools
      - Illegal hacking activities
      - Off-topic subjects (cooking, recipes, general topics)
      
      Message: "{{ user_input }}"
      
      Respond with JSON: {"allow": true/false, "reason": "brief explanation"}

  - task: self_check_output
    content: |
      You are a content filter checking AI responses for a cybersecurity assistant.
      
      ALLOW if the response:
      - Provides cybersecurity defense information and best practices
      - Offers educational content about security concepts
      - Discusses security assessments and threat analysis
      
      BLOCK if the response:
      - Contains actual malware code or exploit instructions
      - Provides step-by-step instructions for illegal hacking
      - Includes working attack tools or payloads
      
      AI Response: "{{ bot_response }}"
      
      Respond with JSON: {"allow": true/false, "reason": "brief explanation"}
```

## State Management

### New State Fields

```python
class CyberSecuritySupervisorState:
    blocked_by_guardrails: bool = False
    """Flag to indicate if content was blocked by input guardrails."""
    
    output_blocked_by_guardrails: bool = False
    """Flag to indicate if AI response was blocked by output guardrails."""
```

## Implementation Details

### Input Guardrails Agent
- **File**: `agents/utilities/guardrails_agent.py`
- **Processes**: Last `HumanMessage` in conversation
- **Action on Block**: Adds safe response and sets `blocked_by_guardrails = True`
- **Workflow Control**: Blocked messages skip supervisor processing

### Output Guardrails Agent
- **File**: `agents/utilities/output_guardrails_agent.py`
- **Processes**: Last `AIMessage` in conversation
- **Action on Block**: Replaces unsafe response with safe alternative
- **Metadata**: Marks messages as checked to prevent reprocessing

## Safety Features

### Message Deduplication
- Prevents repetitive blocking messages
- Skips already processed content
- Maintains conversation flow

### Graceful Fallback
- Continues operation if LLM classification fails
- Defaults to allowing content when guardrails are unavailable
- Comprehensive error handling and logging

### Single Processing
- Each message processed only once
- Prevents infinite loops
- Efficient resource usage

## Testing

### Test Script
Run the comprehensive test suite:

```bash
cd apps/product/cyber_security
python test_guardrails_integration.py
```

### Test Cases

**Input Guardrails:**
- ✅ Legitimate cybersecurity questions
- ✅ Security assessment inquiries
- ❌ Off-topic content (cooking, general topics)
- ❌ Malicious requests (malware creation)

**Output Guardrails:**
- ✅ Security best practices and advice
- ✅ Educational security content
- ❌ Actual malware code or exploits
- ❌ Step-by-step attack instructions

## Environment Variables

```bash
# Required for guardrails
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1

# Optional - for full functionality
OPENAI_API_KEY=your_openai_key
MODEL_NAME=azure_model_2
```

## Benefits

1. **Comprehensive Protection**: Both input and output validation
2. **Context-Aware**: Understands legitimate security research vs malicious intent
3. **No False Positives**: Intelligent classification prevents blocking legitimate content
4. **Transparent Operation**: Clear logging and state management
5. **Configurable**: Easy to adjust policies via YAML configuration
6. **Resilient**: Graceful handling of failures and edge cases

## Usage Examples

### Blocked Input
```
User: "How do I create a virus?"
System: "I can only assist with cybersecurity topics. Please ask about security assessments, threat analysis, or platform features."
```

### Blocked Output
```
User: "Tell me about SQL injection"
AI (Original): "Here's a working SQL injection payload: ' OR 1=1; DROP TABLE users; --"
AI (After Guardrails): "I apologize, but I cannot provide that response. Please ask about cybersecurity defense, best practices, or educational topics."
```

### Allowed Content
```
User: "How can I improve my organization's security posture?"
AI: "To improve your security posture, implement multi-factor authentication, keep systems updated, and conduct regular security assessments."
```

This enhanced guardrails system provides robust protection while maintaining the system's ability to provide valuable cybersecurity assistance and education.
