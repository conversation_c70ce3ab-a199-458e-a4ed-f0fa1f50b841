  <conversational_flow_handling>
    <template_generator_rules>
      <rule priority="CRITICAL">
        <name>Maintain Template Generation Conversation</name>
        <condition>When template_generator is asking questions to gather requirements</condition>
        <action>Route ALL subsequent user responses directly to template_generator without analysis</action>
        <rationale>Preserve interactive conversation flow for requirement gathering</rationale>
      </rule>
      
      <conversation_indicators>
        <indicator>Template generator asking: "What industry are you in?"</indicator>
        <indicator>Template generator asking: "What platforms do you use?"</indicator>
        <indicator>Template generator asking: "What security controls do you have?"</indicator>
        <indicator>Template generator asking: "What's your team size?"</indicator>
        <indicator>User providing lists (platforms, controls, industries)</indicator>
        <indicator>User answering specific organizational questions</indicator>
        <indicator>User clarifying template requirements</indicator>
      </conversation_indicators>
      
      <routing_override>
        <condition>Active template generation conversation detected</condition>
        <action>Route to template_generator regardless of query content</action>
        <exception>Only route away if user explicitly changes topic or says "stop"</exception>
      </routing_override>
    </template_generator_rules>
  </conversational_flow_handling>