"""Test cases for Assessment Agent evaluation"""

from typing import List
from langchain_core.messages import HumanMessage
from ..framework import TestCase


def get_assessment_test_cases() -> List[TestCase]:
    """Get comprehensive test cases for the Assessment Agent"""
    
    return [
        TestCase(
            id="assessment_basic_query",
            name="Basic Assessment Query",
            description="Should provide security assessment information",
            input_data={
                "messages": [HumanMessage(content="What are the key findings from our last security assessment?")]
            },
            expected_tools=["retrieve_assessment_data"],
            expected_behavior={
                "provides_findings": True,
                "uses_tool": True,
                "includes_metrics": True
            },
            tags=["assessment", "basic"],
            metadata={"category": "assessment"}
        ),
        
        TestCase(
            id="assessment_trend_analysis",
            name="Assessment Trend Analysis",
            description="Should analyze assessment trends over time",
            input_data={
                "messages": [HumanMessage(content="Show me the trend of our security posture over the last 6 months")]
            },
            expected_tools=["retrieve_assessment_data", "retrieve_history"],
            expected_behavior={
                "provides_trends": True,
                "uses_multiple_tools": True,
                "includes_visualization": False
            },
            tags=["assessment", "trends"],
            metadata={"category": "assessment"}
        ),
        
        TestCase(
            id="assessment_compliance_check",
            name="Compliance Assessment",
            description="Should check compliance against frameworks",
            input_data={
                "messages": [HumanMessage(content="Are we compliant with ISO 27001 requirements?")]
            },
            expected_tools=["retrieve_assessment_data"],
            expected_behavior={
                "checks_compliance": True,
                "provides_gaps": True,
                "suggests_improvements": True
            },
            tags=["assessment", "compliance"],
            metadata={"category": "assessment"}
        )
    ]