"""Evaluation utilities for loading configuration and using Langfuse prompts"""

import yaml
import os
from typing import Dict, List, Any, Optional
from pathlib import Path
from langfuse import Langfuse


def load_agent_config(agent_name: str) -> Dict[str, Any]:
    """Load configuration for a specific agent"""
    
    # Find the agent directory - look in the parent directory of framework
    current_dir = Path(__file__).parent
    evaluation_dir = current_dir.parent  # Go up to evaluation directory
    agent_dir = evaluation_dir / agent_name
    config_file = agent_dir / "config.yaml"
    
    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_file}")
    
    with open(config_file, 'r') as f:
        return yaml.safe_load(f)


def get_enabled_metrics(config: Dict[str, Any], metric_type: str) -> List[Dict[str, Any]]:
    """Get enabled metrics of a specific type from config"""
    metrics = config.get(metric_type, [])
    return [m for m in metrics if m.get('enabled', True)]


def get_langfuse_prompt(prompt_name: str, **kwargs) -> str:
    """Get a prompt from Langfuse and render it with parameters"""
    
    # Set environment for CyberSecurity prompts
    os.environ["LANGFUSE_PROMPT_FOLDER"] = "CyberSecurity"
    
    client = Langfuse()
    
    try:
        # Get prompt from Langfuse (same pattern as BaseAgentLLM)
        langfuse_prompt = client.get_prompt(prompt_name, label="production")
        
        if not langfuse_prompt:
            raise ValueError(f"No Langfuse prompt found: {prompt_name}")
        
        # Render the prompt with parameters
        if hasattr(langfuse_prompt, 'render'):
            rendered = langfuse_prompt.render(**kwargs)
            return rendered
        else:
            chain_prompt = langfuse_prompt.get_langchain_prompt()
            # Render the langchain prompt with parameters
            rendered = chain_prompt.format(**kwargs)
            return rendered
            
    except Exception as e:
        raise RuntimeError(f"Failed to get evaluation prompt {prompt_name}: {str(e)}")


async def evaluate_with_llm_judge(prompt_name: str, query: str, response: str, context: Dict[str, Any] = None) -> Dict[str, float]:
    """Evaluate using an LLM judge with a Langfuse prompt"""
    
    from corellm.providers.provider import ModelProvider
    from langchain_core.messages import HumanMessage, SystemMessage
    import re
    
    # Get the evaluation prompt
    evaluation_context = context or {}
    evaluation_context.update({
        "query": query,
        "response": response,
    })
    
    prompt_template = get_langfuse_prompt(prompt_name, **evaluation_context)
    
    # Create LLM messages
    messages = [
        SystemMessage(content="You are an expert cybersecurity evaluator. Follow the evaluation instructions precisely."),
        HumanMessage(content=prompt_template)
    ]
    
    # Get evaluation from LLM
    llm = ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME", "azure_model_2"))
    llm_response = await llm.ainvoke(messages)
    
    # Parse the response to extract score
    response_text = llm_response.content
    
    try:
        # Look for score pattern
        score_match = re.search(r'Score:\s*(\d+(?:\.\d+)?)', response_text)
        if score_match:
            score = float(score_match.group(1))
            # Normalize to 0-1 range if needed
            if score > 1.0:
                score = score / 10.0
            
            return {
                "score": score,
                "reasoning": response_text
            }
        else:
            # No fallback - require proper score format from Langfuse prompts
            raise ValueError("No score pattern found in evaluation response")
                
    except Exception as e:
        print(f"Error parsing evaluation response: {str(e)}")
        raise e