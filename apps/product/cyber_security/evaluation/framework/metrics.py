"""Metrics and evaluation calculations"""

from typing import Any, Dict, List, Optional, TYPE_CHECKING
from dataclasses import dataclass
from abc import ABC, abstractmethod
import numpy as np

if TYPE_CHECKING:
    from .base import EvaluationResult


from ragas import evaluate

from ragas.metrics import (
    Faithfulness,
    ResponseRelevancy,
    LLMContextPrecisionWithoutReference,
    ContextRecall,
    AnswerSimilarity,
    AnswerCorrectness
)

# Create instances for backward compatibility
faithfulness = Faithfulness()
answer_relevancy = ResponseRelevancy()
context_precision = LLMContextPrecisionWithoutReference()
context_recall = ContextRecall()
answer_similarity = AnswerSimilarity()
answer_correctness = AnswerCorrectness()

from datasets import Dataset
from langchain_core.embeddings import Embeddings
from langchain_openai import OpenAIEmbeddings
from sklearn.metrics.pairwise import cosine_similarity
import json


class BaseMetrics(ABC):
    """Abstract base class for agent-specific metrics"""
    
    @abstractmethod
    def get_ragas_metrics(self) -> List[str]:
        """Return list of RAGAS metrics to use for this agent type"""
        pass
    
    @abstractmethod
    async def evaluate_custom(self, test_case: Any, output: Any) -> Dict[str, float]:
        """Custom evaluation logic specific to the agent type"""
        pass


@dataclass
class RAGASResult:
    """Enhanced RAGAS result for comprehensive evaluation"""
    scores: Dict[str, float]
    execution_time: float
    cache_hits: int
    quality_gate_passed: bool
    metadata: Dict[str, Any]


class RagasMetrics(BaseMetrics):
    """Enhanced RAGAS evaluation metrics with advanced features"""
    
    def __init__(
        self,
        metrics: Optional[List[str]] = None,
        embeddings: Optional[Embeddings] = None,
        config: Optional[Dict[str, Any]] = None
    ):
        # Use provided metrics or get from subclass
        if metrics is None:
            metrics = self.get_ragas_metrics()
        
        # Map metric names to instances
        available_metrics = {
            "faithfulness": faithfulness,
            "answer_relevancy": answer_relevancy,
            "context_precision": context_precision,
            "context_recall": context_recall,
            "answer_similarity": answer_similarity,
            "answer_correctness": answer_correctness,
        }
        
        self.metrics = [
            available_metrics[m] for m in metrics 
            if m in available_metrics and available_metrics[m] is not None
        ]
        self.embeddings = embeddings or OpenAIEmbeddings()
        
        # Enhanced features
        self.config = config or {}
        self.advanced_config = self.config.get("advanced_ragas_config", {})
        self.cache = {}  # Response caching
        self.batch_size = self.advanced_config.get("batch_size", 5)
        self.parallel_enabled = self.advanced_config.get("parallel_evaluation", True)
        self.quality_gates = self.advanced_config.get("quality_gates", {})
    
    def get_ragas_metrics(self) -> List[str]:
        """Default RAGAS metrics - can be overridden"""
        return ["faithfulness", "answer_relevancy", "context_precision", "context_recall"]
    
    async def evaluate_custom(self, test_case: Any, output: Any) -> Dict[str, float]:
        """Default custom evaluation - can be overridden"""
        return {}
    
    async def evaluate_batch(
        self,
        test_cases: List[Any],
        outputs: List[Any],
        strategy: str = "comprehensive_suite"
    ) -> List[RAGASResult]:
        """Batch evaluation with enhanced features"""
        import asyncio
        import time
        
        start_time = time.time()
        results = []
        
        # Get metric strategy if available
        if hasattr(self, 'config'):
            strategy_config = self.config.get("metric_selection_strategy", {})
            selected_metrics = strategy_config.get(strategy, self.get_ragas_metrics())
        else:
            selected_metrics = self.get_ragas_metrics()
        
        # Process in batches for efficiency
        for i in range(0, len(test_cases), self.batch_size):
            batch_cases = test_cases[i:i + self.batch_size]
            batch_outputs = outputs[i:i + self.batch_size]
            
            if self.parallel_enabled:
                # Parallel evaluation for better performance
                batch_tasks = [
                    self._evaluate_single_enhanced(case, output, selected_metrics)
                    for case, output in zip(batch_cases, batch_outputs)
                ]
                batch_results = await asyncio.gather(*batch_tasks)
            else:
                # Sequential evaluation
                batch_results = []
                for case, output in zip(batch_cases, batch_outputs):
                    result = await self._evaluate_single_enhanced(case, output, selected_metrics)
                    batch_results.append(result)
            
            results.extend(batch_results)
        
        total_time = time.time() - start_time
        
        # Add batch metadata
        for result in results:
            result.metadata.update({
                "batch_evaluation": True,
                "total_batch_time": total_time,
                "strategy_used": strategy
            })
        
        return results
    
    async def _evaluate_single_enhanced(
        self,
        test_case: Any,
        output: Any,
        selected_metrics: List[str]
    ) -> RAGASResult:
        """Single test evaluation with enhanced features"""
        import time
        
        start_time = time.time()
        cache_hits = 0
        
        # Extract evaluation data
        query = self._extract_query(test_case)
        answer = self._extract_answer(output)
        contexts = self._extract_contexts(output)
        ground_truth = getattr(test_case, 'ground_truth', None)
        
        # Generate cache key
        cache_key = f"{hash(query)}_{hash(answer)}_{hash(str(contexts))}"
        
        if self.advanced_config.get("caching_enabled", True) and cache_key in self.cache:
            cache_hits += 1
            scores = self.cache[cache_key]
        else:
            # Run RAGAS evaluation with selected metrics
            scores = await self.evaluate(query, answer, ground_truth, contexts)
            
            # Cache results
            if self.advanced_config.get("caching_enabled", True):
                self.cache[cache_key] = scores
        
        # Apply quality gates (fail-fast evaluation)
        quality_gate_passed = self._check_quality_gates(scores)
        
        execution_time = time.time() - start_time
        
        return RAGASResult(
            scores=scores,
            execution_time=execution_time,
            cache_hits=cache_hits,
            quality_gate_passed=quality_gate_passed,
            metadata={
                "selected_metrics": selected_metrics,
                "context_count": len(contexts) if contexts else 0,
                "query_length": len(query),
                "answer_length": len(answer)
            }
        )
    
    def _check_quality_gates(self, scores: Dict[str, float]) -> bool:
        """Check if evaluation passes quality gates (fail-fast pattern)"""
        quality_gates = self.quality_gates
        
        # Check minimum thresholds
        if "min_faithfulness" in quality_gates:
            if scores.get("faithfulness", 0) < quality_gates["min_faithfulness"]:
                return False
        
        if "min_answer_relevancy" in quality_gates:
            if scores.get("answer_relevancy", 0) < quality_gates["min_answer_relevancy"]:
                return False
        
        # Check maximum thresholds (e.g., hallucination rate)
        if "max_hallucination_rate" in quality_gates:
            hallucination_rate = 1.0 - scores.get("faithfulness", 1.0)
            if hallucination_rate > quality_gates["max_hallucination_rate"]:
                return False
        
        return True
    
    def _extract_query(self, test_case: Any) -> str:
        """Extract query from test case"""
        if hasattr(test_case, 'input_data') and test_case.input_data.get("messages"):
            query_msg = test_case.input_data["messages"][0]
            return query_msg.content if hasattr(query_msg, "content") else str(query_msg)
        return str(test_case)
    
    def _extract_answer(self, output: Any) -> str:
        """Extract answer from agent output"""
        if hasattr(output, "messages") and output.messages:
            for msg in reversed(output.messages):
                if hasattr(msg, "content") and not hasattr(msg, "tool_call_id"):
                    return msg.content
        return str(output)
    
    def _extract_contexts(self, output: Any) -> Optional[List[str]]:
        """Extract retrieved contexts from agent output"""
        contexts = []
        if hasattr(output, "messages") and output.messages:
            for msg in output.messages:
                if hasattr(msg, "tool_call_id") and hasattr(msg, "content"):
                    contexts.append(msg.content)
        return contexts if contexts else None
    
    async def evaluate(
        self,
        question: str,
        answer: str,
        ground_truth: Optional[str] = None,
        contexts: Optional[List[str]] = None
    ) -> Dict[str, float]:
        if contexts and not isinstance(contexts[0], list):
            contexts = [contexts]
        
        data = {
            "question": [question],
            "answer": [answer]
        }
        
        if ground_truth:
            data["ground_truth"] = [ground_truth]
        
        if contexts:
            data["contexts"] = contexts
        
        dataset = Dataset.from_dict(data)
        
        try:
            result = evaluate(
                dataset,
                metrics=self.metrics
            )
            
            scores = {}
            for metric in self.metrics:
                metric_name = metric.__class__.__name__ if hasattr(metric, '__class__') else str(metric)
                if hasattr(result, metric_name):
                    scores[metric_name] = float(getattr(result, metric_name))
                elif metric_name in result:
                    scores[metric_name] = float(result[metric_name])
            
            return scores
        except Exception:
            return {}
    
    async def evaluate_tool_output(
        self,
        output: Any,
        ground_truth: Any
    ) -> Dict[str, float]:
        
        output_str = json.dumps(output) if not isinstance(output, str) else output
        ground_truth_str = json.dumps(ground_truth) if not isinstance(ground_truth, str) else ground_truth
        
        return await self.evaluate(
            question="Tool execution",
            answer=output_str,
            ground_truth=ground_truth_str,
            contexts=[output_str]
        )


class ToolCallMetrics:
    
    async def evaluate(
        self,
        expected_tools: List[str],
        actual_tools: List[Dict[str, Any]]
    ) -> Dict[str, float]:
        
        actual_tool_names = [
            tool.get("name", tool.get("tool", "")) 
            for tool in actual_tools
        ]
        
        expected_set = set(expected_tools)
        actual_set = set(actual_tool_names)
        
        if not expected_set:
            return {"tool_accuracy": 1.0 if not actual_set else 0.0}
        
        precision = len(expected_set & actual_set) / len(actual_set) if actual_set else 0.0
        recall = len(expected_set & actual_set) / len(expected_set) if expected_set else 0.0
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
        
        correct_order = self._check_tool_order(expected_tools, actual_tool_names)
        
        return {
            "tool_precision": precision,
            "tool_recall": recall,
            "tool_f1": f1,
            "tool_order_accuracy": correct_order,
            "tool_accuracy": f1
        }
    
    def _check_tool_order(
        self,
        expected: List[str],
        actual: List[str]
    ) -> float:
        
        if not expected or not actual:
            return 0.0
        
        matches = 0
        actual_idx = 0
        
        for exp_tool in expected:
            for i in range(actual_idx, len(actual)):
                if actual[i] == exp_tool:
                    matches += 1
                    actual_idx = i + 1
                    break
        
        return matches / len(expected)


class AgentPerformanceMetrics:
    
    def __init__(self, embeddings: Optional[Embeddings] = None):
        self.embeddings = embeddings or OpenAIEmbeddings()
    
    async def evaluate(
        self,
        agent_output: Any,
        expected_output: Any,
        execution_time: float,
        tool_calls: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, float]:
        
        metrics = {}
        
        metrics["execution_time"] = execution_time
        
        if expected_output:
            metrics["semantic_similarity"] = await self._calculate_semantic_similarity(
                str(agent_output),
                str(expected_output)
            )
        
        if tool_calls:
            metrics["tool_efficiency"] = self._calculate_tool_efficiency(tool_calls)
            metrics["tool_diversity"] = self._calculate_tool_diversity(tool_calls)
        
        metrics["response_completeness"] = self._calculate_completeness(
            agent_output,
            expected_output
        )
        
        return metrics
    
    async def _calculate_semantic_similarity(
        self,
        text1: str,
        text2: str
    ) -> float:
        
        try:
            emb1 = await self.embeddings.aembed_query(text1)
            emb2 = await self.embeddings.aembed_query(text2)
            
            similarity = cosine_similarity(
                np.array(emb1).reshape(1, -1),
                np.array(emb2).reshape(1, -1)
            )[0][0]
            
            return float(similarity)
        except Exception:
            return 0.0
    
    def _calculate_tool_efficiency(
        self,
        tool_calls: List[Dict[str, Any]]
    ) -> float:
        
        if not tool_calls:
            return 1.0
        
        successful_calls = sum(
            1 for call in tool_calls 
            if not call.get("error") and call.get("output")
        )
        
        return successful_calls / len(tool_calls)
    
    def _calculate_tool_diversity(
        self,
        tool_calls: List[Dict[str, Any]]
    ) -> float:
        
        if not tool_calls:
            return 0.0
        
        unique_tools = len(set(
            call.get("name", call.get("tool", ""))
            for call in tool_calls
        ))
        
        return unique_tools / len(tool_calls)
    
    def _calculate_completeness(
        self,
        agent_output: Any,
        expected_output: Any
    ) -> float:
        
        if not expected_output:
            return 1.0
        
        if isinstance(expected_output, dict):
            expected_keys = set(expected_output.keys())
            actual_keys = set(agent_output.keys()) if isinstance(agent_output, dict) else set()
            
            if not expected_keys:
                return 1.0
            
            return len(expected_keys & actual_keys) / len(expected_keys)
        
        return 1.0 if agent_output else 0.0


def calculate_similarity(expected: Any, actual: Any) -> float:
    
    if expected == actual:
        return 1.0
    
    if isinstance(expected, dict) and isinstance(actual, dict):
        return _dict_similarity(expected, actual)
    
    if isinstance(expected, list) and isinstance(actual, list):
        return _list_similarity(expected, actual)
    
    if isinstance(expected, (str, int, float)):
        return _value_similarity(str(expected), str(actual))
    
    return 0.0


def _dict_similarity(expected: dict, actual: dict) -> float:
    
    if not expected:
        return 1.0 if not actual else 0.0
    
    all_keys = set(expected.keys()) | set(actual.keys())
    if not all_keys:
        return 1.0
    
    similarities = []
    for key in all_keys:
        if key in expected and key in actual:
            sim = calculate_similarity(expected[key], actual[key])
            similarities.append(sim)
        else:
            similarities.append(0.0)
    
    return sum(similarities) / len(similarities)


def _list_similarity(expected: list, actual: list) -> float:
    
    if not expected:
        return 1.0 if not actual else 0.0
    
    if len(expected) != len(actual):
        penalty = abs(len(expected) - len(actual)) / max(len(expected), len(actual))
        base_score = 1.0 - penalty
    else:
        base_score = 1.0
    
    if not expected or not actual:
        return base_score
    
    min_len = min(len(expected), len(actual))
    similarities = [
        calculate_similarity(expected[i], actual[i])
        for i in range(min_len)
    ]
    
    if similarities:
        content_score = sum(similarities) / len(expected)
        return (base_score + content_score) / 2
    
    return base_score


def _value_similarity(expected: str, actual: str) -> float:
    
    if expected == actual:
        return 1.0
    
    expected_lower = expected.lower().strip()
    actual_lower = actual.lower().strip()
    
    if expected_lower == actual_lower:
        return 0.95
    
    if expected_lower in actual_lower or actual_lower in expected_lower:
        return 0.7
    
    common_chars = set(expected_lower) & set(actual_lower)
    if common_chars:
        return len(common_chars) / max(len(set(expected_lower)), len(set(actual_lower))) * 0.5
    
    return 0.0


@dataclass
class EvaluationSummary:
    
    total_tests: int
    passed_tests: int
    failed_tests: int
    skipped_tests: int
    average_score: float
    average_execution_time: float
    metrics_summary: Dict[str, float]
    
    @property
    def pass_rate(self) -> float:
        if self.total_tests == 0:
            return 0.0
        return self.passed_tests / self.total_tests
    
    @classmethod
    def from_results(cls, results: List["EvaluationResult"]) -> "EvaluationSummary":
        
        if not results:
            return cls(0, 0, 0, 0, 0.0, 0.0, {})
        
        total = len(results)
        passed = sum(1 for r in results if r.status == "passed")
        failed = sum(1 for r in results if r.status == "failed")
        skipped = sum(1 for r in results if r.status == "skipped")
        
        scores = [r.overall_score for r in results if r.scores]
        avg_score = sum(scores) / len(scores) if scores else 0.0
        
        exec_times = [r.execution_time for r in results]
        avg_exec_time = sum(exec_times) / len(exec_times) if exec_times else 0.0
        
        all_metrics = {}
        for result in results:
            for key, value in result.metrics.items():
                if isinstance(value, (int, float)):
                    if key not in all_metrics:
                        all_metrics[key] = []
                    all_metrics[key].append(value)
        
        metrics_summary = {
            key: sum(values) / len(values)
            for key, values in all_metrics.items()
        }
        
        return cls(
            total_tests=total,
            passed_tests=passed,
            failed_tests=failed,
            skipped_tests=skipped,
            average_score=avg_score,
            average_execution_time=avg_exec_time,
            metrics_summary=metrics_summary
        )