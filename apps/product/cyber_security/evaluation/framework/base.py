"""Base evaluation framework"""

from typing import Any, Dict, List, Optional, Type
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from cyber_security.infra.init import infra
from langgraph.types import RunnableConfig
from corelanggraph.agents.base_agent import BaseAgent


class TestCaseStatus(str, Enum):
    PASSED = "passed"
    FAILED = "failed"


@dataclass
class TestCase:
    id: str
    name: str
    input_data: Dict[str, Any]
    expected_tools: List[str] = field(default_factory=list)
    expected_behavior: Dict[str, Any] = field(default_factory=dict)
    ground_truth: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    
    def to_state(self, state_class: Type) -> Any:
        return state_class(**self.input_data)


@dataclass
class EvaluationResult:
    test_case_id: str
    status: TestCaseStatus
    scores: Dict[str, float]
    metrics: Dict[str, Any]
    execution_time: float
    timestamp: datetime
    error: Optional[str] = None
    raw_output: Optional[Any] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    
    @property
    def overall_score(self) -> float:
        if not self.scores:
            return 0.0
        numeric_scores = [v for v in self.scores.values() if isinstance(v, (int, float))]
        if not numeric_scores:
            return 0.0
        return sum(numeric_scores) / len(numeric_scores)
    
    @property
    def passed(self) -> bool:
        return self.status == TestCaseStatus.PASSED


class AgentEvaluator:
    def __init__(self, agent_class: Type[BaseAgent]):
        self.agent_class = agent_class
        self.agent = agent_class()
        self.langfuse = infra.langfuse
    
    async def evaluate(
        self,
        test_case: TestCase,
        state_class: Type,
        config: Optional[RunnableConfig] = None
    ) -> EvaluationResult:
        start_time = datetime.now()
        
        try:
            state = test_case.to_state(state_class)
            if config is None:
                config = RunnableConfig(configurable={})
            
            output = await self.agent.execute(state, config)
            
            scores = {}
            
            tool_scores = await self._evaluate_tools(test_case, output)
            scores.update(tool_scores)
            
            ragas_scores = await self._evaluate_ragas(test_case, output)
            scores.update(ragas_scores)
            
            judge_score = await self._evaluate_llm_judge(test_case, output)
            scores.update(judge_score)
            
            # Calculate overall score (exclude non-numeric values like reasoning)
            numeric_scores = {k: v for k, v in scores.items() if isinstance(v, (int, float))}
            overall_score = sum(numeric_scores.values()) / len(numeric_scores) if numeric_scores else 0.0
            scores["overall"] = overall_score
            
            status = TestCaseStatus.PASSED if overall_score >= 0.7 else TestCaseStatus.FAILED
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return EvaluationResult(
                test_case_id=test_case.id,
                status=status,
                scores=scores,
                metrics={"execution_time": execution_time},
                execution_time=execution_time,
                timestamp=datetime.now(),
                raw_output=output,
                tool_calls=self._extract_tool_calls(output)
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            return EvaluationResult(
                test_case_id=test_case.id,
                status=TestCaseStatus.FAILED,
                scores={},
                metrics={},
                execution_time=execution_time,
                timestamp=datetime.now(),
                error=str(e)
            )
    
    
    async def _evaluate_tools(self, test_case: TestCase, output: Any) -> Dict[str, float]:
        if not test_case.expected_tools:
            return {}
        
        actual_tools = self._extract_tool_calls(output)
        actual_tool_names = [t.get("name", "") for t in actual_tools]
        
        expected_set = set(test_case.expected_tools)
        actual_set = set(actual_tool_names)
        
        precision = len(expected_set & actual_set) / len(actual_set) if actual_set else 0.0
        recall = len(expected_set & actual_set) / len(expected_set) if expected_set else 0.0
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
        
        return {
            "tool_precision": precision,
            "tool_recall": recall,
            "tool_f1": f1
        }
    
    async def _evaluate_ragas(self, test_case: TestCase, output: Any) -> Dict[str, float]:
        if not test_case.ground_truth:
            return {}
        
        try:
            from .metrics import RagasMetrics
            ragas = RagasMetrics(["faithfulness", "answer_relevancy"])
            
            scores = await ragas.evaluate(
                question=test_case.input_data.get("query", str(test_case.input_data)),
                answer=str(output),
                ground_truth=test_case.ground_truth
            )
            
            return scores
        except Exception:
            return {}
    
    async def _evaluate_llm_judge(self, test_case: TestCase, output: Any) -> Dict[str, float]:
        # Check if LLM judge is disabled in expected_behavior
        if test_case.expected_behavior.get("skip_llm_evaluation", False):
            return {}
        
        # Check if LLM judge is disabled in metadata (fallback)
        if test_case.metadata.get("skip_llm_judge", False):
            return {}
        
        # Check if this is a tool-only evaluation
        if test_case.metadata.get("evaluation_type") == "tool_only":
            return {}
        
        if not test_case.expected_behavior:
            return {}
        
        try:
            from .llm_judge import LLMJudge
            judge = LLMJudge(langfuse_client=self.langfuse)
            
            # Extract the query and response
            query = ""
            response = ""
            
            if hasattr(output, "messages") and output.messages:
                # Get the original query from test case
                if test_case.input_data.get("messages"):
                    query_msg = test_case.input_data["messages"][0]
                    query = query_msg.content if hasattr(query_msg, "content") else str(query_msg)
                
                # Get the final response from the agent
                for msg in reversed(output.messages):
                    if hasattr(msg, "content") and not hasattr(msg, "tool_call_id"):
                        response = msg.content
                        break
            else:
                query = str(test_case.input_data)
                response = str(output)
            
            # Extract tool calls made during execution
            actual_tool_calls = self._extract_tool_calls(output)
            tool_names = [tc.get("name", "") for tc in actual_tool_calls] if actual_tool_calls else []
            
            # Add tool call information to expected behavior for judge
            enhanced_behavior = test_case.expected_behavior.copy()
            enhanced_behavior["actual_tools_used"] = tool_names
            enhanced_behavior["expected_tools"] = test_case.expected_tools
            
            # Use LLM judge for evaluation
            evaluation = await judge.evaluate_response_quality(
                query=query,
                response=response,
                expected_behavior=enhanced_behavior
            )
            
            result = {"llm_judge": evaluation.get("score", 0.5)}
            
            # Include reasoning if available
            if evaluation.get("reason"):
                result["llm_judge_reasoning"] = evaluation.get("reason")
            
            return result
            
        except Exception:
            # No fallback - requires Langfuse prompts for evaluation
            return {}
    
    def _extract_tool_calls(self, output: Any) -> List[Dict[str, Any]]:
        tool_calls = []
        tool_responses = {}
        
        if hasattr(output, "messages"):
            for message in output.messages:
                if hasattr(message, "tool_calls") and message.tool_calls:
                    for tc in message.tool_calls:
                        tool_call_data = {
                            "name": tc.get("name", "") if isinstance(tc, dict) else getattr(tc, "name", ""),
                            "args": tc.get("args", {}) if isinstance(tc, dict) else getattr(tc, "args", {}),
                            "id": tc.get("id", "") if isinstance(tc, dict) else getattr(tc, "id", "")
                        }
                        tool_calls.append(tool_call_data)
                
                elif hasattr(message, "tool_call_id") and hasattr(message, "content"):
                    tool_responses[message.tool_call_id] = message.content
        
        for tool_call in tool_calls:
            if tool_call.get("id") in tool_responses:
                tool_call["response"] = tool_responses[tool_call["id"]]
        
        if not tool_calls:
            if hasattr(output, "tool_calls"):
                return output.tool_calls
            elif isinstance(output, dict) and "tool_calls" in output:
                return output["tool_calls"]
        
        return tool_calls