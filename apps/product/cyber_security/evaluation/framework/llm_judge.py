"""LLM-as-Judge evaluation component using BaseAgentLLM pattern"""

from typing import Dict, Any, Optional
import json
import os
from abc import ABC, abstractmethod
from pydantic import BaseModel, Field
from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm.providers.provider import <PERSON><PERSON>rovider
from langfuse import Lang<PERSON>
from langchain_core.runnables import RunnableConfig

from evaluation.framework.utils import get_langfuse_prompt


class EvaluationResult(BaseModel):
    """Structured output for LLM evaluation"""
    score: float = Field(..., description="Evaluation score between 0.0 and 1.0", ge=0.0, le=1.0)
    reason: str = Field(..., description="Detailed reasoning for the evaluation score")


class LLMJudge(BaseAgentLLM, ABC):
    """Abstract LLM-as-Judge evaluator - base class for all domain-specific judges"""
    
    def __init__(
        self,
        name: str,
        description: str,
        prompt_name: str
    ):
        super().__init__(
            name=name,
            description=description,
            llm=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME", "azure_model_2"),
                temperature=0.0  # Zero temperature for consistent evaluations
            ),
            prompt_name=prompt_name,
        )
    
    @abstractmethod
    def get_domain(self) -> str:
        """Return the domain/context for this judge (e.g., 'cybersecurity', 'healthcare', 'finance')"""
        pass
    
    async def execute(self, evaluation_request: Dict[str, Any], config: Optional[RunnableConfig] = None) -> Dict[str, Any]:
        """Execute evaluation based on framework configuration"""
        from evaluation.framework.utils import load_agent_config
        framework_config = load_agent_config("framework")
        framework_prompts = framework_config.get("framework_prompts", [])
        evaluation_type = evaluation_request.get("type", "general_quality")
        prompt_config = next((p for p in framework_prompts if p["name"] == evaluation_type), None)
        
        if not prompt_config:
            evaluation_type = "general_quality"
            prompt_config = next((p for p in framework_prompts if p["name"] == evaluation_type), None)
        
        if not prompt_config:
            raise ValueError(f"No configuration found for evaluation type: {evaluation_type}")
        method_name = prompt_config.get("method", "evaluate_response_quality")
        expected_params = prompt_config.get("parameters", [])
        args = []
        for param in expected_params:
            if param in evaluation_request:
                args.append(evaluation_request[param])
            else:
                defaults = {
                    "query": "",
                    "response": "",
                    "domain": self.get_domain(),
                    "context": self.get_domain(),
                    "required_elements": [],
                    "expected_behavior": {}
                }
                args.append(defaults.get(param, ""))
        
        # Get the method and execute it
        method = getattr(self, method_name)
        return await method(*args)
    
    async def evaluate_with_prompt(self, prompt: str) -> Dict[str, Any]:
        """Evaluate using a custom prompt"""
        try:
            result = await self.llm.ainvoke(prompt)
            return self._parse_evaluation(result.content)
        except Exception as e:
            print(f"LLM Judge error: {e}")
            raise e
    
    async def evaluate_response_quality(
        self,
        query: str,
        response: str,
        expected_behavior: Dict[str, Any],
        trace_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Evaluate response quality using LLM-as-judge with structured output"""
        
        prompt = self._create_evaluation_prompt(query, response, expected_behavior)
        
        # Debug: Print what's being sent to LLM judge
        print(f"\n=== LLM JUDGE DEBUG ===")
        print(f"Expected behavior keys: {list(expected_behavior.keys())}")
        if "actual_tools_used" in expected_behavior:
            print(f"Actual tools used: {expected_behavior['actual_tools_used']}")
        if "expected_tools" in expected_behavior:
            print(f"Expected tools: {expected_behavior['expected_tools']}")
        print(f"Prompt preview: {prompt[:500]}...")
        
        try:
            # Try to use structured output if LLM supports it
            try:
                llm_with_structure = self.llm.with_structured_output(EvaluationResult)
                result = await llm_with_structure.ainvoke(prompt)
                evaluation = {"score": result.score, "reason": result.reason}
            except (AttributeError, Exception):
                # Use regular LLM call with structured parsing
                result = await self.llm.ainvoke(prompt)
                evaluation = self._parse_evaluation(result.content)
            
            # Record to Langfuse if trace_id provided
            if self.langfuse and trace_id:
                self.langfuse.score(
                    trace_id=trace_id,
                    name="llm_judge_quality",
                    value=evaluation["score"],
                    data_type="NUMERIC",
                    comment=evaluation.get("reason", "LLM judge evaluation")
                )
            
            return evaluation
            
        except Exception as e:
            print(f"LLM Judge error: {e}")
            raise e
    
    async def evaluate_technical_accuracy(
        self,
        response: str,
        domain: str,
        trace_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Evaluate technical accuracy of response for any domain"""
        
        # Get prompt from Langfuse - all prompts must be in Langfuse
        prompt = get_langfuse_prompt(
            f"{self.get_prompt_prefix()}/technical_accuracy",
            response=response,
            domain=domain
        )
        
        try:
            result = await self.llm.ainvoke(prompt)
            evaluation = self._parse_evaluation(result.content)
            
            if self.langfuse and trace_id:
                self.langfuse.score(
                    trace_id=trace_id,
                    name="technical_accuracy",
                    value=evaluation["score"],
                    data_type="NUMERIC",
                    comment=evaluation.get("reason", "Technical accuracy evaluation")
                )
            
            return evaluation
            
        except Exception as e:
            print(f"Technical evaluation error: {e}")
            raise e
    
    async def evaluate_completeness(
        self,
        query: str,
        response: str,
        required_elements: list,
        trace_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Evaluate if response covers all required elements"""
        
        # Get prompt from Langfuse - all prompts must be in Langfuse
        prompt = get_langfuse_prompt(
            f"{self.get_prompt_prefix()}/completeness",
            query=query,
            response=response,
            required_elements=required_elements
        )
        
        try:
            result = await self.llm.ainvoke(prompt)
            evaluation = self._parse_evaluation(result.content)
            
            if self.langfuse and trace_id:
                self.langfuse.score(
                    trace_id=trace_id,
                    name="completeness",
                    value=evaluation["score"],
                    data_type="NUMERIC",
                    comment=f"Covered: {evaluation.get('covered', [])}; Missing: {evaluation.get('missing', [])}"
                )
            
            return evaluation
            
        except Exception as e:
            print(f"Completeness evaluation error: {e}")
            raise e
    
    async def evaluate_harmfulness(
        self,
        response: str,
        context: str,
        trace_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Evaluate if response contains harmful or dangerous content for any domain"""
        
        # Get prompt from Langfuse - all prompts must be in Langfuse
        prompt = get_langfuse_prompt(
            f"{self.get_prompt_prefix()}/harmfulness",
            response=response,
            context=context
        )
        
        try:
            result = await self.llm.ainvoke(prompt)
            evaluation = self._parse_evaluation(result.content)
            
            if self.langfuse and trace_id:
                self.langfuse.score(
                    trace_id=trace_id,
                    name="harmfulness_check",
                    value=evaluation["score"],
                    data_type="BOOLEAN",
                    comment=evaluation.get("reason", "Harmfulness evaluation")
                )
            
            return evaluation
            
        except Exception as e:
            print(f"Harmfulness evaluation error: {e}")
            raise e
    
    def _create_evaluation_prompt(
        self,
        query: str,
        response: str,
        expected_behavior: Dict[str, Any]
    ) -> str:
        """Create evaluation prompt based on expected behavior"""
        
        # Framework is abstract - ALL prompts must come from agent-specific Langfuse
        return get_langfuse_prompt(
            f"{self.get_prompt_prefix()}/general_quality",
            query=query,
            response=response,
            expected_behavior=expected_behavior
        )
    
    @abstractmethod
    def get_prompt_prefix(self) -> str:
        """Get the agent-specific prompt prefix for Langfuse paths"""
        pass
    
    def _parse_evaluation(self, llm_response: str) -> Dict[str, Any]:
        """Parse LLM evaluation response - simplified version"""
        
        try:
            import re
            import json
            
            # Remove markdown code blocks if present
            cleaned_response = re.sub(r'```json\s*', '', llm_response)
            cleaned_response = re.sub(r'```\s*$', '', cleaned_response)
            
            # Try to extract JSON from response
            json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                
                try:
                    # Try direct JSON parsing first
                    parsed = json.loads(json_str)
                    return parsed
                except json.JSONDecodeError:
                    # Try to extract score and reason manually if JSON parsing fails
                    import re
                    score_match = re.search(r'"?score"?\s*[:=]\s*([0-9]*\.?[0-9]+)', json_str, re.IGNORECASE)
                    reason_match = re.search(r'"?reason"?\s*[:=]\s*"([^"]*)"', json_str, re.IGNORECASE | re.DOTALL)
                    
                    if score_match:
                        score = float(score_match.group(1))
                        reason = reason_match.group(1) if reason_match else "Evaluation completed"
                        return {"score": score, "reason": reason}
                    else:
                        # Try to find any number that could be a score
                        numbers = re.findall(r'\b([0-9]*\.?[0-9]+)\b', json_str)
                        if numbers:
                            score = float(numbers[0])
                            if score > 1.0:
                                score = score / 10.0
                            return {"score": min(1.0, max(0.0, score)), "reason": "Parsed from response"}
            
            # If no JSON and no extractable data, try to get any meaningful content
            if llm_response and len(llm_response.strip()) > 0:
                return {"score": 0.5, "reason": "Could not parse structured response from LLM"}
            else:
                return {"score": 0.0, "reason": "Empty response from LLM"}
            
        except Exception as e:
            return {"score": 0.0, "reason": f"Parse error: {str(e)}"}


class MultiCriteriaLLMJudge(LLMJudge):
    """Extended LLM judge with multiple evaluation criteria"""
    
    async def evaluate_all_criteria(
        self,
        query: str,
        response: str,
        expected_behavior: Dict[str, Any],
        trace_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Run multiple evaluation criteria and aggregate results"""
        
        evaluations = {}
        
        # Quality evaluation
        quality = await self.evaluate_response_quality(
            query, response, expected_behavior, trace_id
        )
        evaluations["quality"] = quality
        
        # Technical accuracy
        technical = await self.evaluate_technical_accuracy(
            response, self.get_domain(), trace_id
        )
        evaluations["technical_accuracy"] = technical
        
        # Harmfulness check
        safety = await self.evaluate_harmfulness(
            response, self.get_domain(), trace_id
        )
        evaluations["safety"] = safety
        
        # Calculate aggregate score
        scores = [
            evaluations["quality"]["score"],
            evaluations["technical_accuracy"]["score"],
            evaluations["safety"]["score"]
        ]
        
        aggregate_score = sum(scores) / len(scores)
        
        if self.langfuse and trace_id:
            self.langfuse.score(
                trace_id=trace_id,
                name="aggregate_llm_judge",
                value=aggregate_score,
                data_type="NUMERIC",
                comment="Aggregate of all LLM judge criteria"
            )
        
        return {
            "aggregate_score": aggregate_score,
            "evaluations": evaluations
        }