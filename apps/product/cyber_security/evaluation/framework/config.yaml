# Framework Evaluation Configuration
# This is the abstract base configuration for ALL evaluation agents
# Any domain-specific criteria should be in agent-specific configs

agent_name: framework

# RAGAS Metrics
ragas_metrics:
  - name: faithfulness
    enabled: true
    weight: 1.0
  - name: answer_relevancy
    enabled: true
    weight: 1.0

# Custom Metrics
custom_metrics: []

# Tool Metrics
tool_metrics:
  - name: tool_accuracy
    enabled: true
    weight: 1.0

# Performance Metrics
performance_metrics:
  - name: execution_time
    enabled: true
    weight: 0.3

# No framework-level evaluation methods - each agent defines its own
# All evaluation logic should be agent-specific and use agent-specific Langfuse prompts