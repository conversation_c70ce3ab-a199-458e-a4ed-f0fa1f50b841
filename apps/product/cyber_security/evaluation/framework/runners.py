"""Evaluation runner"""

import asyncio
from typing import List, Type, Optional
from datetime import datetime

from cyber_security.infra.init import infra
from langgraph.types import RunnableConfig

from .base import AgentEvaluator, TestCase, EvaluationResult
from .metrics import EvaluationSummary


class EvaluationRunner:
    
    def __init__(self, agent_class=None, state_class=None, trace_name=None):
        # This base runner shouldn't be used directly anymore
        # Each agent should have its own runner
        self.agent_class = agent_class
        self.state_class = state_class
        self.langfuse = infra.langfuse
        self.evaluator = None  # Must be set by subclass
        self.trace_name = trace_name or "generic_evaluation"  # Default trace name
    
    async def run(
        self,
        test_cases: List[TestCase],
        config: Optional[RunnableConfig] = None
    ) -> EvaluationSummary:
        semaphore = asyncio.Semaphore(3)
        
        async def run_test(test_case: TestCase) -> EvaluationResult:
            async with semaphore:
                return await self.evaluator.evaluate(
                    test_case=test_case,
                    state_class=self.state_class,
                    config=config
                )
        
        start_time = datetime.now()
        tasks = [run_test(tc) for tc in test_cases]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                final_results.append(
                    EvaluationResult(
                        test_case_id=test_cases[i].id,
                        status="failed",
                        scores={},
                        metrics={},
                        execution_time=0,
                        timestamp=datetime.now(),
                        error=str(result)
                    )
                )
            else:
                final_results.append(result)
        
        summary = EvaluationSummary.from_results(final_results)
        total_time = (datetime.now() - start_time).total_seconds()
        if self.langfuse:
            try:
                from langfuse.types import TraceContext
                
                # Create a unique trace ID
                trace_id = self.langfuse.create_trace_id()
                
                # Create trace context
                trace_context = TraceContext(trace_id=trace_id)
                
                def extract_message_content(msg):
                    if hasattr(msg, 'content'):
                        return msg.content
                    return str(msg)
                def format_tool_calls(tool_calls):
                    if not tool_calls:
                        return []
                    formatted = []
                    for tc in tool_calls:
                        if isinstance(tc, dict):
                            formatted.append({
                                "name": tc.get("name", "unknown"),
                                "args": tc.get("args", {}),
                                "id": tc.get("id", "")
                            })
                        elif hasattr(tc, 'name'):
                            formatted.append({
                                "name": tc.name,
                                "args": getattr(tc, 'args', {}),
                                "id": getattr(tc, 'id', '')
                            })
                    return formatted
                
                detailed_results = []
                for i, r in enumerate(final_results):
                    test_detail = {
                        "test_id": r.test_case_id,
                        "test_name": test_cases[i].name,
                        "status": str(r.status),
                        "score": r.overall_score,
                        "execution_time": r.execution_time,
                        "input": {
                            "query": extract_message_content(test_cases[i].input_data.get("messages", [{}])[0]) if test_cases[i].input_data.get("messages") else test_cases[i].input_data
                        },
                        "output": {
                            "response": extract_message_content(r.raw_output.messages[-1]) if hasattr(r.raw_output, 'messages') and r.raw_output.messages else str(r.raw_output),
                            "tool_calls": format_tool_calls(r.tool_calls) if r.tool_calls else []
                        },
                        "scores": {
                            "tool_precision": r.scores.get("tool_precision", 0),
                            "tool_recall": r.scores.get("tool_recall", 0),
                            "tool_f1": r.scores.get("tool_f1", 0),
                            "llm_judge": r.scores.get("llm_judge", 0),
                            "llm_judge_reasoning": r.scores.get("llm_judge_reasoning", ""),
                            "overall": r.scores.get("overall", 0)
                        },
                        "expected_tools": test_cases[i].expected_tools,
                        "actual_tools": [tc.get("name", "") for tc in format_tool_calls(r.tool_calls)] if r.tool_calls else []
                    }
                    detailed_results.append(test_detail)
                
                generation = self.langfuse.start_generation(
                    trace_context=trace_context,
                    name=self.trace_name,
                    input={
                        "test_count": len(test_cases),
                        "test_names": [tc.name for tc in test_cases],
                        "agent": self.evaluator.agent_class.__name__,
                        "test_cases": [
                            {
                                "id": tc.id,
                                "name": tc.name,
                                "query": extract_message_content(tc.input_data.get("messages", [{}])[0]) if tc.input_data.get("messages") else str(tc.input_data),
                                "expected_tools": tc.expected_tools,
                                "expected_behavior": tc.expected_behavior
                            }
                            for tc in test_cases
                        ]
                    },
                    output={
                        "summary": {
                            "pass_rate": summary.pass_rate,
                            "average_score": summary.average_score,
                            "passed": summary.passed_tests,
                            "failed": summary.failed_tests,
                            "total_time": total_time
                        },
                        "detailed_results": detailed_results
                    },
                    metadata={
                        "evaluation_run": True,
                        "timestamp": datetime.now().isoformat(),
                        "agent_class": self.evaluator.agent_class.__name__,
                        "state_class": self.state_class.__name__,
                        "test_count": len(test_cases),
                        "pass_rate": summary.pass_rate,
                        "average_score": summary.average_score,
                        "evaluation_framework": "cyber_security_evaluation"
                    }
                )
                
                generation.end()
                self.langfuse.create_score(
                    trace_id=trace_id,
                    name="pass_rate",
                    value=summary.pass_rate,
                    comment=f"{summary.passed_tests}/{summary.total_tests} tests passed",
                    data_type="NUMERIC"
                )
                
                self.langfuse.create_score(
                    trace_id=trace_id,
                    name="average_score", 
                    value=summary.average_score,
                    comment="Average score across all evaluation metrics",
                    data_type="NUMERIC",
                    metadata={
                        "metric_type": "aggregate",
                        "includes": ["tool_metrics", "llm_judge", "ragas"]
                    }
                )
                
                tool_scores = [r.scores.get("tool_f1", 0) for r in final_results if "tool_f1" in r.scores]
                if tool_scores:
                    avg_tool_score = sum(tool_scores) / len(tool_scores)
                    tool_details = []
                    for i, r in enumerate(final_results):
                        if "tool_f1" in r.scores:
                            tool_details.append({
                                "test_id": r.test_case_id,
                                "expected": test_cases[i].expected_tools,
                                "actual": [tc.get("name", "") for tc in (r.tool_calls or [])] if r.tool_calls else [],
                                "precision": r.scores.get("tool_precision", 0),
                                "recall": r.scores.get("tool_recall", 0),
                                "f1": r.scores.get("tool_f1", 0)
                            })
                    
                    self.langfuse.create_score(
                        trace_id=trace_id,
                        name="tool_accuracy",
                        value=avg_tool_score,
                        comment="Average tool usage accuracy (F1 score)",
                        data_type="NUMERIC",
                        metadata={"tool_details": tool_details}
                    )
                
                judge_scores = [r.scores.get("llm_judge", 0) for r in final_results if "llm_judge" in r.scores]
                if judge_scores:
                    avg_judge_score = sum(judge_scores) / len(judge_scores)
                    judge_details = []
                    for i, r in enumerate(final_results):
                        if "llm_judge" in r.scores:
                            detail = {
                                "test_id": r.test_case_id,
                                "test_name": test_cases[i].name,
                                "llm_judge_score": r.scores.get("llm_judge", 0),
                                "expected_behavior": test_cases[i].expected_behavior,
                                "query": extract_message_content(test_cases[i].input_data.get("messages", [{}])[0]) if test_cases[i].input_data.get("messages") else test_cases[i].input_data
                            }
                            # Add reasoning if available
                            if "llm_judge_reasoning" in r.scores:
                                detail["reasoning"] = r.scores.get("llm_judge_reasoning")
                            judge_details.append(detail)
                    
                    self.langfuse.create_score(
                        trace_id=trace_id,
                        name="llm_judge_evaluation",
                        value=avg_judge_score,
                        comment="LLM-as-Judge evaluation of agent responses",
                        data_type="NUMERIC",
                        metadata={
                            "evaluation_method": "llm_judge",
                            "average_score": avg_judge_score,
                            "test_evaluations": judge_details
                        }
                    )
                
                time_score = max(0, min(1, (30 - summary.average_execution_time) / 20))
                self.langfuse.create_score(
                    trace_id=trace_id,
                    name="performance",
                    value=time_score,
                    comment=f"Average execution time: {summary.average_execution_time:.1f}s",
                    data_type="NUMERIC",
                    metadata={
                        "average_time": summary.average_execution_time,
                        "total_time": total_time,
                        "individual_times": [r.execution_time for r in final_results]
                    }
                )
                
                self.langfuse.flush()
                
                print(f"\n✅ Evaluation trace created")
                print(f"   View in Langfuse: {self.langfuse.get_trace_url(trace_id=trace_id)}")
                
            except Exception as e:
                print(f"⚠️ Failed to create Langfuse trace: {e}")
        
        return summary