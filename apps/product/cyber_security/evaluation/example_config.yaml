# Example Configuration File for Evaluation Agents
# =================================================
# This file demonstrates all possible configuration options for creating
# evaluation agents. Copy this file to your agent directory and customize.
#
# File Location: evaluation/{agent_name}/config.yaml
# Example: evaluation/cyber_expert/config.yaml
#          evaluation/healthcare_agent/config.yaml
#          evaluation/finance_agent/config.yaml

# Agent Identification
# -------------------
# Unique identifier for this evaluation agent
# Must match the directory name where this config.yaml is located
agent_name: example_agent

# RAGAS Metrics Configuration
# ---------------------------
# Built-in metrics from the RAGAS evaluation library
# These provide standard RAG (Retrieval-Augmented Generation) evaluation
ragas_metrics:
  # Faithfulness: Measures how factually accurate the response is
  # Scale: 0.0 (completely unfaithful) to 1.0 (completely faithful)
  - name: faithfulness
    enabled: true        # Whether to run this metric
    weight: 1.0         # Importance weight for overall scoring

  # Answer Relevancy: How relevant the response is to the query
  # Scale: 0.0 (completely irrelevant) to 1.0 (highly relevant)
  - name: answer_relevancy
    enabled: true
    weight: 1.2         # Higher weight = more important

  # Context Precision: Quality of the retrieved context
  # Measures how well the context supports the answer
  - name: context_precision
    enabled: false      # Disabled if context retrieval not applicable
    weight: 0.8

  # Context Recall: Completeness of the retrieved context
  # Measures if all necessary context was retrieved
  - name: context_recall
    enabled: false      # Disabled if context retrieval not applicable
    weight: 0.8

# Custom Metrics Configuration
# ----------------------------
# Domain-specific LLM-based evaluations using Langfuse prompts
# These are the most important metrics for specialized evaluation
custom_metrics:
  # General Quality Judge - Foundation metric for all agents
  - name: general_quality_judge
    enabled: true
    weight: 1.5                                            # Higher weight for core metric
    prompt_name: "YourDomain/evaluation/example_agent/general_quality_judge"  # Langfuse prompt path
    
    # Criteria Mapping: Maps expected_behavior keys to evaluation criteria
    # Used when _create_evaluation_prompt() processes expected behavior
    criteria_mapping:
      # Key from expected_behavior -> Evaluation criterion description
      accuracy: "Response should be factually accurate and up-to-date"
      completeness: "Response should comprehensively address all aspects of the query"
      clarity: "Response should be clear, well-structured, and easy to understand"
      # Add domain-specific criteria below:
      # domain_specific_key: "Domain-specific evaluation criterion"

  # Technical Accuracy - Domain expertise evaluation
  - name: technical_accuracy
    enabled: true
    weight: 2.0                                            # Critical for technical domains
    prompt_name: "YourDomain/evaluation/example_agent/technical_accuracy"
    
    # Optional: Additional parameters for this metric
    parameters:
      domain_context: "your_domain"                        # e.g., "cybersecurity", "healthcare"
      expertise_level: "expert"                            # "beginner", "intermediate", "expert"

  # Domain-Specific Metric Example 1
  - name: regulatory_compliance
    enabled: true
    weight: 1.8
    prompt_name: "YourDomain/evaluation/example_agent/regulatory_compliance"
    parameters:
      regulations: ["GDPR", "HIPAA", "SOX"]               # List of relevant regulations
      compliance_level: "strict"

  # Domain-Specific Metric Example 2  
  - name: safety_assessment
    enabled: true
    weight: 2.5                                            # Highest weight for safety-critical domains
    prompt_name: "YourDomain/evaluation/example_agent/safety_assessment"
    parameters:
      risk_tolerance: "low"                                # "low", "medium", "high"
      safety_standards: ["ISO 26262", "IEC 61508"]

  # Content Quality Metric
  - name: content_appropriateness
    enabled: true
    weight: 1.0
    prompt_name: "YourDomain/evaluation/example_agent/content_appropriateness"

  # Optional: Disabled metric example
  - name: experimental_metric
    enabled: false                                         # Disabled for production
    weight: 0.5
    prompt_name: "YourDomain/evaluation/example_agent/experimental_metric"
    parameters:
      experimental_flag: true

# Tool Metrics Configuration
# --------------------------
# Evaluate agent tool usage and effectiveness
tool_metrics:
  # Tool Accuracy: How accurately the agent uses available tools
  - name: tool_accuracy
    enabled: true
    weight: 1.0
    parameters:
      check_tool_calls: true                               # Verify tool calls are correct
      check_tool_results: true                             # Verify tool results are used properly

  # Tool Appropriateness: Whether the right tools were selected
  - name: tool_appropriateness  
    enabled: true
    weight: 1.2
    prompt_name: "YourDomain/evaluation/example_agent/tool_appropriateness"  # Custom LLM evaluation
    parameters:
      available_tools: ["search", "calculator", "database"]  # List of available tools
      
  # Tool Efficiency: How efficiently tools were used
  - name: tool_efficiency
    enabled: false                                         # Optional advanced metric
    weight: 0.8
    parameters:
      max_tool_calls: 10                                   # Threshold for efficiency

# Performance Metrics Configuration
# ----------------------------------
# Measure execution characteristics and response quality
performance_metrics:
  # Execution Time: How long the agent takes to respond
  - name: execution_time
    enabled: true
    weight: 0.3                                            # Lower weight - performance vs quality tradeoff
    parameters:
      max_acceptable_time: 30.0                            # Seconds
      timeout_penalty: 0.1                                 # Score reduction for timeouts

  # Semantic Similarity: How similar response is to expected output
  - name: semantic_similarity
    enabled: true
    weight: 1.0
    parameters:
      similarity_threshold: 0.7                            # Minimum acceptable similarity
      embedding_model: "sentence-transformers/all-MiniLM-L6-v2"

  # Response Length Appropriateness
  - name: response_length
    enabled: false                                         # Optional - depends on use case
    weight: 0.2
    parameters:
      min_length: 50                                       # Minimum characters
      max_length: 2000                                     # Maximum characters
      optimal_range: [200, 800]                           # Optimal length range

# Advanced Configuration Options
# -------------------------------
# Global settings that affect all metrics

# Evaluation Settings
evaluation_settings:
  # Timeout for individual metric evaluations (seconds)
  metric_timeout: 60
  
  # Whether to continue evaluation if one metric fails
  fail_fast: false
  
  # Maximum retries for failed evaluations
  max_retries: 2
  
  # Temperature for LLM judge evaluations (0.0 = deterministic, 1.0 = creative)
  judge_temperature: 0.0

# Langfuse Integration Settings
langfuse_settings:
  # Environment/label for prompts (production, staging, development)
  prompt_label: "production"
  
  # Whether to trace evaluations to Langfuse
  enable_tracing: true
  
  # Langfuse project name (if different from default)
  # project_name: "custom_project"

# Scoring Configuration
scoring:
  # How to combine multiple metric scores
  aggregation_method: "weighted_average"                  # "weighted_average", "simple_average", "max", "min"
  
  # Score normalization (ensure all scores are 0-1)
  normalize_scores: true
  
  # Minimum score threshold for passing evaluation
  pass_threshold: 0.6
  
  # Whether to include individual metric scores in output
  include_individual_scores: true

# Domain-Specific Configuration
# -----------------------------
# Add your domain-specific settings here

# Example for cybersecurity domain:
# cybersecurity:
#   threat_databases: ["MITRE ATT&CK", "CVE", "NIST"]
#   frameworks: ["NIST CSF", "ISO 27001"]
#   threat_intelligence_sources: ["commercial", "open_source"]

# Example for healthcare domain:
# healthcare:
#   regulations: ["HIPAA", "HITECH", "FDA"]
#   medical_standards: ["HL7", "FHIR", "ICD-10"]
#   specialties: ["cardiology", "oncology", "radiology"]

# Example for finance domain:
# finance:
#   regulations: ["SOX", "GDPR", "PCI DSS"]
#   financial_standards: ["GAAP", "IFRS"]
#   risk_categories: ["market", "credit", "operational"]

# Notes and Best Practices
# ========================
#
# 1. Naming Conventions:
#    - Use snake_case for metric names
#    - Use descriptive names that indicate what is being measured
#    - Prefix domain-specific metrics with domain name if needed
#
# 2. Weight Guidelines:
#    - Core quality metrics: 1.0 - 2.0
#    - Safety/compliance metrics: 2.0 - 3.0 (highest)
#    - Performance metrics: 0.1 - 0.5 (lowest)
#    - Domain-specific metrics: 1.0 - 2.5
#
# 3. Langfuse Prompt Paths:
#    - Format: "Domain/evaluation/agent_name/metric_name"
#    - Example: "Healthcare/evaluation/medical_agent/drug_interaction_check"
#    - Be consistent with your domain naming
#
# 4. Enable/Disable Strategy:
#    - Start with core metrics enabled
#    - Add domain-specific metrics gradually
#    - Disable experimental metrics in production
#    - Consider computational cost vs. value trade-offs
#
# 5. Parameters:
#    - Use parameters to make metrics configurable
#    - Include reasonable defaults
#    - Document parameter meanings clearly
#
# 6. Testing:
#    - Test each metric individually before enabling all
#    - Validate Langfuse prompts render correctly
#    - Check weight balance gives reasonable overall scores
#    - Monitor metric performance and adjust as needed