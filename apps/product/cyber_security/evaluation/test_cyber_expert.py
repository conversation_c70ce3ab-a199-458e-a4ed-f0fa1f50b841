"""Test script for CyberExpert evaluation"""

from dotenv import load_dotenv
load_dotenv(override=True)

import asyncio
from cyber_expert import run_cyber_expert_evaluation, cyber_expert_test_cases


async def main():
    test_subset = cyber_expert_test_cases
    
    print(f"Running {len(test_subset)} CyberExpert tests...")
    summary = await run_cyber_expert_evaluation(test_subset)
    
    return summary


if __name__ == "__main__":
    asyncio.run(main())