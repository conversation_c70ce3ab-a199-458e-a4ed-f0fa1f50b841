"""Test script to run both evaluation frameworks sequentially (one after another)"""

from dotenv import load_dotenv
load_dotenv(override=True)

import asyncio
import time
from datetime import datetime


async def main():
    """Run both evaluation frameworks sequentially"""
    print("🚀 SEQUENTIAL TESTING OF CYBERSECURITY EVALUATION FRAMEWORKS")
    print(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    results = []
    total_start = time.time()
    
    # 1. Test CyberExpert Framework
    print("\n1️⃣ TESTING CYBEREXPERT EVALUATION FRAMEWORK")
    print("-" * 60)
    
    try:
        from cyber_expert import run_cyber_expert_evaluation, cyber_expert_test_cases
        
        print(f"Found {len(cyber_expert_test_cases)} CyberExpert test cases")
        
        start_time = time.time()
        cyber_summary = await run_cyber_expert_evaluation(cyber_expert_test_cases)
        end_time = time.time()
        
        cyber_result = {
            "framework": "CyberExpert",
            "pass_rate": cyber_summary.pass_rate,
            "average_score": cyber_summary.average_score,
            "passed_tests": cyber_summary.passed_tests,
            "total_tests": cyber_summary.total_tests,
            "execution_time": end_time - start_time,
            "success": True
        }
        results.append(cyber_result)
        
        print(f"✅ CyberExpert completed successfully")
        
    except Exception as e:
        print(f"❌ CyberExpert failed: {e}")
        results.append({
            "framework": "CyberExpert",
            "success": False,
            "error": str(e),
            "execution_time": 0
        })
    
    # 2. Test CymulateKnowledgeBase Framework
    print("\n2️⃣ TESTING CYMULATE KNOWLEDGE BASE EVALUATION FRAMEWORK") 
    print("-" * 60)
    
    try:
        from cymulate_knowledge_base import run_cymulate_knowledge_base_evaluation, cymulate_knowledge_base_test_cases
        
        print(f"Found {len(cymulate_knowledge_base_test_cases)} CymulateKnowledgeBase test cases")
        
        start_time = time.time()
        cymulate_summary = await run_cymulate_knowledge_base_evaluation(cymulate_knowledge_base_test_cases)
        end_time = time.time()
        
        cymulate_result = {
            "framework": "CymulateKnowledgeBase",
            "pass_rate": cymulate_summary.pass_rate,
            "average_score": cymulate_summary.average_score,
            "passed_tests": cymulate_summary.passed_tests,
            "total_tests": cymulate_summary.total_tests,
            "execution_time": end_time - start_time,
            "success": True
        }
        results.append(cymulate_result)
        
        print(f"✅ CymulateKnowledgeBase completed successfully")
        
    except Exception as e:
        print(f"❌ CymulateKnowledgeBase failed: {e}")
        results.append({
            "framework": "CymulateKnowledgeBase",
            "success": False,
            "error": str(e),
            "execution_time": 0
        })
    
    total_end = time.time()
    total_time = total_end - total_start
    
    # Print final comparison results
    print("\n📊 FINAL COMPARISON RESULTS")
    print("="*80)
    
    for result in results:
        framework = result["framework"]
        print(f"\n{framework}:")
        if result["success"]:
            print(f"   ✅ Status: PASSED")
            print(f"   📈 Pass Rate: {result['pass_rate']:.1%}")
            print(f"   🎯 Average Score: {result['average_score']:.2f}")
            print(f"   📝 Tests: {result['passed_tests']}/{result['total_tests']} passed")
            print(f"   ⏱️  Execution Time: {result['execution_time']:.1f}s")
        else:
            print(f"   ❌ Status: FAILED")
            print(f"   🚨 Error: {result['error']}")
            print(f"   ⏱️  Execution Time: {result['execution_time']:.1f}s")
    
    # Overall statistics
    successful = [r for r in results if r["success"]]
    failed = [r for r in results if not r["success"]]
    
    print(f"\n🎯 OVERALL SUMMARY:")
    print(f"   ⏱️  Total Time: {total_time:.1f}s")
    print(f"   ✅ Successful: {len(successful)}/2 frameworks")
    print(f"   ❌ Failed: {len(failed)}/2 frameworks")
    
    if successful:
        total_tests = sum(r["total_tests"] for r in successful)
        total_passed = sum(r["passed_tests"] for r in successful)
        avg_pass_rate = sum(r["pass_rate"] for r in successful) / len(successful)
        avg_score = sum(r["average_score"] for r in successful) / len(successful)
        
        print(f"   📊 Combined Pass Rate: {avg_pass_rate:.1%}")
        print(f"   📊 Combined Average Score: {avg_score:.2f}")
        print(f"   📊 Combined Tests: {total_passed}/{total_tests}")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    return results


if __name__ == "__main__":
    asyncio.run(main())