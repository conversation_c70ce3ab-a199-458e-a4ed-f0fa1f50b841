"""Test Cases for CymulateKnowledgeBase RAG Agent - Based on Specific Knowledge Q&A"""

from langchain_core.messages import HumanMessage
from evaluation.framework import TestCase

# STANDARDIZED RAG EXPECTED BEHAVIOR PATTERN
STANDARD_RAG_BEHAVIOR = {
    "uses_tool": None,  # True/False - Will be set per test
    "query_type": None,  # FACTUAL/SPECIFIC/PROCEDURAL/COMPARATIVE
    "provides_good_answer": True,  # Basic quality check
    "uses_retrieved_context": True,  # Uses knowledge base context
    "cites_sources": True,  # Includes source attribution
}

cymulate_knowledge_base_test_cases = [
    # Test Case 1: Advanced Scenarios Setup Methods
    TestCase(
        id="advanced_scenarios_setup",
        name="Advanced Scenarios Assessment Setup Methods",
        input_data={"messages": [HumanMessage(content="What are the two ways Advanced Scenarios assessments can be set up?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "FACTUAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["rag_quality_judge", "knowledge_relevance"]
        },
        ground_truth="They can be run in an Atomic manner to validate security solution protection or in a Real attack simulation manner by chaining executions to build real-world scenarios for red- and blue-team testing."
    ),

    # Test Case 2: MITRE ATT&CK Framework Mapping
    TestCase(
        id="mitre_framework_mapping",
        name="MITRE ATT&CK Framework in Advanced Scenarios",
        input_data={"messages": [HumanMessage(content="Which framework maps tactics and techniques in Advanced Scenarios?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "FACTUAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["context_utilization", "knowledge_relevance"]
        },
        ground_truth="The tactics and techniques are mapped to the MITRE ATT&CK Framework."
    ),

    # Test Case 3: BAS Templates Types
    TestCase(
        id="bas_templates_types",
        name="BAS Templates Page Template Types",
        input_data={"messages": [HumanMessage(content="What types of templates are available on the BAS Templates page?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "FACTUAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["answer_completeness", "source_attribution"]
        },
        ground_truth="Templates (ready-to-use, Cymulate-provided sets of scenarios), User Templates (custom assessments you design), and Smart Templates (dynamically updated collections based on a filter for the latest matching scenarios)."
    ),

    # Test Case 4: Smart Templates Updates
    TestCase(
        id="smart_templates_updates",
        name="Smart Templates Automatic Updates",
        input_data={"messages": [HumanMessage(content="How do Smart Templates keep assessments up to date?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "PROCEDURAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["rag_quality_judge", "context_utilization"]
        },
        ground_truth="Each time a Smart Template is launched, its scenario list is automatically matched to the latest threats that meet the filter criteria."
    ),

    # Test Case 5: IOC Automation
    TestCase(
        id="ioc_automation_purpose",
        name="IOC Definition and Automation Benefits",
        input_data={"messages": [HumanMessage(content="What are Indicators of Compromise (IOCs) and why does Cymulate automate their mitigation?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "FACTUAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["answer_completeness", "knowledge_relevance"]
        },
        ground_truth="IOCs (e.g., file hashes, IP addresses, domains) are artifacts indicating malicious activity. Automating their mitigation via integrations with EDR/XDR platforms reduces response time, increases consistency, and closes the detection-to-response loop."
    ),

    # Test Case 6: Supported Security Products
    TestCase(
        id="supported_security_products",
        name="IOC Mitigation Supported Security Products",
        input_data={"messages": [HumanMessage(content="Which security products does Cymulate support for automated IOC mitigation?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "FACTUAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["knowledge_relevance", "source_attribution"]
        },
        ground_truth="CrowdStrike Falcon, SentinelOne, Microsoft Defender for Endpoint, Palo Alto Cortex XDR, and Trend Micro Vision One."
    ),

    # Test Case 7: Assessment Reports Capabilities
    TestCase(
        id="assessment_reports_capabilities",
        name="Assessment Reports Page Capabilities",
        input_data={"messages": [HumanMessage(content="What can you do on the Assessment Reports page?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "PROCEDURAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["answer_completeness", "context_utilization"]
        },
        ground_truth="View module-specific reports via Overview, Last Campaign, or History; download Executive, CSV, or TXT reports; and view Performance and Benchmark graphs for selected modules and time ranges."
    ),

    # Test Case 8: Executive Global Report
    TestCase(
        id="executive_global_report",
        name="Executive Global Report Generation",
        input_data={"messages": [HumanMessage(content="How do you generate an executive global report covering all modules?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "PROCEDURAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["rag_quality_judge", "source_attribution"]
        },
        ground_truth="Click GENERAL EXECUTIVE REPORT and select Executive Global (for MSSP/Global accounts)."
    ),

    # Test Case 9: Potential Findings Definition
    TestCase(
        id="potential_findings_definition",
        name="Potential Findings in Scenario Overview",
        input_data={"messages": [HumanMessage(content="What are Potential Findings in a scenario's overview?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "FACTUAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["knowledge_relevance", "context_utilization"]
        },
        ground_truth="They're the findings that would be generated if the scenario is Not Prevented or Not Detected, each showing a title, description, prevention vs. detection type, and risk level."
    ),

    # Test Case 10: Advanced Scenarios Benefits
    TestCase(
        id="advanced_scenarios_benefits",
        name="Advanced Scenarios Module Key Benefits",
        input_data={"messages": [HumanMessage(content="Name two key benefits of the Advanced Scenarios module.")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "FACTUAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["answer_completeness", "knowledge_relevance"]
        },
        ground_truth="A finely tuned open-source executions repository for testing security solutions and simulated real behavior of malware and APT groups mapped to MITRE ATT&CK."
    ),

    # Test Case 11: RAG-ONLY - Pure RAG evaluation without LLM judge
    TestCase(
        id="rag_only_validation",
        name="RAG Performance Only - No LLM Judge",
        input_data={"messages": [HumanMessage(content="What framework does Cymulate use to map attack tactics and techniques?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "skip_llm_evaluation": True,
        },
        ground_truth="MITRE ATT&CK Framework is used to map tactics and techniques in Cymulate's Advanced Scenarios assessments."
    ),

    # Test Case 12: FALSE GROUND TRUTH TEST - Testing LLM Judge Detection of Wrong Ground Truth
    TestCase(
        id="false_ground_truth_test",
        name="LLM Judge False Ground Truth Detection Test",
        input_data={"messages": [HumanMessage(content="What are the main features of Cymulate's email security assessment?")]},
        expected_tools=["retrieve_cymulate_knowledge"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "FACTUAL",
            "provides_good_answer": True,
            "uses_retrieved_context": True,
            "cites_sources": True,
            "metrics_to_use": ["rag_quality_judge", "answer_completeness", "knowledge_relevance"]
        },
        # INTENTIONALLY FALSE/UNRELATED GROUND TRUTH - should cause low LLM judge score
        ground_truth="The capital of France is Paris, and pizza is a popular Italian food made with tomatoes and cheese."
    )
]