# CymulateKnowledgeBase RAG Agent Evaluation Configuration
agent_name: cymulate_knowledge_base

# Agent Configuration
agent_config:
  domain: "rag_cybersecurity"
  prompt_prefix: "CyberSecurity/evaluation/cymulate_knowledge_base"
  judge_manager_prompt: "CyberSecurity/evaluation/cymulate_knowledge_base/judge_manager"

# RAGAS Metrics - Core RAG evaluation metrics
ragas_metrics:
  # Core metrics for factual accuracy and relevance
  - name: faithfulness
    weight: 2.0  # Higher weight for factual accuracy in cybersecurity
    priority: high
  - name: answer_relevancy
    weight: 2.0  # Critical for RAG quality
    priority: high
  # Context evaluation metrics
  - name: context_precision
    weight: 1.5  # Important for retrieval quality
    priority: medium
  - name: context_recall
    weight: 1.5  # Ensures comprehensive retrieval
    priority: medium
  # Advanced 2025 metrics for better evaluation
  - name: answer_correctness
    weight: 2.5  # Combines factuality and semantic similarity
    priority: high
  - name: answer_similarity
    weight: 1.0  # Semantic comparison with ground truth
    priority: medium

# Custom Metrics - RAG-specific evaluation criteria
custom_metrics:
  - name: rag_quality_judge
    weight: 3.0
    prompt_name: "CyberSecurity/evaluation/cymulate_knowledge_base/rag_quality_judge"
  - name: knowledge_relevance
    weight: 2.0
    prompt_name: "CyberSecurity/evaluation/cymulate_knowledge_base/knowledge_relevance"
  - name: source_attribution
    weight: 1.5
    prompt_name: "CyberSecurity/evaluation/cymulate_knowledge_base/source_attribution"
  - name: context_utilization
    weight: 1.5
    prompt_name: "CyberSecurity/evaluation/cymulate_knowledge_base/context_utilization"
  - name: answer_completeness
    weight: 1.0
    prompt_name: "CyberSecurity/evaluation/cymulate_knowledge_base/answer_completeness"

# Tool Metrics - RAG retrieval evaluation
tool_metrics:
  - name: retrieval_accuracy
    enabled: true
    weight: 2.0

# Performance Metrics
performance_metrics:
  - name: execution_time
    enabled: true
    weight: 0.2
  - name: semantic_similarity
    enabled: true
    weight: 0.5
  - name: retrieval_latency
    enabled: true
    weight: 0.1

# 2025 Advanced RAG Evaluation Features
advanced_ragas_config:
  # Reference-free evaluation (2025 standard)
  reference_free_mode: true
  
  # Batch evaluation for efficiency
  batch_size: 5  # Process multiple tests together
  
  # LLM configuration for RAGAS
  llm_config:
    model: "gpt-4o"
    temperature: 0.1
    max_tokens: 1000

  context_evaluation:
    max_context_length: 8000
    chunk_overlap_analysis: true
    noise_sensitivity: true
  

  semantic_analysis:
    entity_extraction: true
    factual_consistency: true
    temporal_accuracy: true
  
  # Evaluation optimization
  parallel_evaluation: true
  caching_enabled: true
  

  quality_gates:
    min_faithfulness: 0.7
    min_answer_relevancy: 0.6
    max_hallucination_rate: 0.1

# Test-specific metric selection (2025 pattern)
metric_selection_strategy:
  # High-priority tests use comprehensive metrics
  comprehensive_suite: 
    - faithfulness
    - answer_relevancy  
    - answer_correctness
    - context_precision
    - context_recall
    - rag_quality_judge
  
  # Quick validation uses core metrics only
  quick_validation:
    - faithfulness
    - answer_relevancy
    - rag_quality_judge
  
  # Performance testing focuses on efficiency
  performance_focused:
    - execution_time
    - retrieval_latency
    - context_precision