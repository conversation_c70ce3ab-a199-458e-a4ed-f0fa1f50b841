"""CymulateKnowledgeBase RAG evaluation runner"""

import asyncio
from typing import List, Optional
from langgraph.types import RunnableConfig
from cyber_security.infra import init
from cyber_security.agents.cymulate_knowledge_base.agents.cymulate_knowledge_agent import CymulateKnowledgeBaseAgent
from cyber_security.workflow.state import CyberSecuritySupervisorState
from evaluation.framework.runners import EvaluationRunner
from evaluation.framework.base import TestCase, AgentEvaluator
from .evaluator import CymulateKnowledgeBaseEvaluator


class CymulateKnowledgeBaseRunner(EvaluationRunner):
    """Runner specifically configured for CymulateKnowledgeBase RAG evaluation"""
    
    def __init__(self):
        # Initialize with correct trace name for CymulateKnowledgeBase
        super().__init__(
            agent_class=CymulateKnowledgeBaseAgent,
            state_class=CyberSecuritySupervisorState,
            trace_name="cymulate_knowledge_base_evaluation"
        )
        # Use CymulateKnowledgeBase-specific evaluator
        self.evaluator = CymulateKnowledgeBaseEvaluator(CymulateKnowledgeBaseAgent)


async def run_cymulate_knowledge_base_evaluation(
    test_cases: List[TestCase],
    config: Optional[RunnableConfig] = None
) -> None:
    """Run CymulateKnowledgeBase RAG evaluation with custom configuration"""
    
    runner = CymulateKnowledgeBaseRunner()
    summary = await runner.run(test_cases, config)
    
    print("\n" + "="*60)
    print("CYMULATE KNOWLEDGE BASE RAG EVALUATION RESULTS")
    print("="*60)
    print(f"Pass Rate: {summary.pass_rate:.1%}")
    print(f"Average Score: {summary.average_score:.2f}")
    print(f"Tests Passed: {summary.passed_tests}/{summary.total_tests}")
    print(f"Average Execution Time: {summary.average_execution_time:.1f}s")
    
    if summary.failed_tests > 0:
        print(f"\n⚠️  {summary.failed_tests} test(s) failed")
    
    return summary


if __name__ == "__main__":
    from .test_cases import cymulate_knowledge_base_test_cases
    
    # Run evaluation
    asyncio.run(run_cymulate_knowledge_base_evaluation(cymulate_knowledge_base_test_cases))