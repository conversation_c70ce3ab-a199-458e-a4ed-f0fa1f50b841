"""CymulateKnowledgeBase-specific LLM judge implementation"""

from typing import Dict, Any, Optional, List
import os
from evaluation.framework.llm_judge import LLMJudge
from evaluation.framework.utils import get_langfuse_prompt
from langfuse import Langfuse
from corellm.providers.provider import ModelProvider


class CymulateKnowledgeBaseLLMJudge(LLMJudge):
    """LLM Judge specifically configured for CymulateKnowledgeBase RAG evaluation"""
    
    def __init__(self, langfuse_client: Optional[Langfuse] = None):
        super().__init__(
            name="cymulate_knowledge_base_judge",
            description="RAG evaluation judge for CymulateKnowledgeBase agent",
            prompt_name="CyberSecurity/evaluation/cymulate_knowledge_base/rag_quality_judge"
        )
        # Store langfuse client if provided
        if langfuse_client:
            self._langfuse_client = langfuse_client
    
    def get_domain(self) -> str:
        """Return the domain for this judge"""
        return "RAG cybersecurity knowledge base"
    
    def get_prompt_prefix(self) -> str:
        """Return the Langfuse prompt prefix for CymulateKnowledgeBase"""
        return "CyberSecurity/evaluation/cymulate_knowledge_base"
    
    async def evaluate_response_quality(
        self,
        query: str,
        response: str,
        expected_behavior: Dict[str, Any],
        retrieved_contexts: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Create RAG-specific evaluation prompt"""
        
        # All prompts must be in Langfuse - no hardcoded fallbacks
        return get_langfuse_prompt(
            f"{self.get_prompt_prefix()}/rag_quality_judge",
            query=query,
            response=response,
            expected_behavior=expected_behavior,
            retrieved_contexts=retrieved_contexts or [],
            domain=self.get_domain()
        )
    
    async def evaluate_knowledge_relevance(
        self,
        query: str,
        response: str,
        retrieved_contexts: List[str]
    ) -> Dict[str, Any]:
        """Evaluate how well the response uses relevant knowledge from retrieval"""
        
        return get_langfuse_prompt(
            f"{self.get_prompt_prefix()}/knowledge_relevance",
            query=query,
            response=response,
            retrieved_contexts=retrieved_contexts,
            domain=self.get_domain()
        )
    
    async def evaluate_source_attribution(
        self,
        response: str,
        retrieved_contexts: List[str]
    ) -> Dict[str, Any]:
        """Evaluate quality of source attribution in RAG response"""
        
        return get_langfuse_prompt(
            f"{self.get_prompt_prefix()}/source_attribution",
            response=response,
            retrieved_contexts=retrieved_contexts,
            domain=self.get_domain()
        )
    
    async def evaluate_context_utilization(
        self,
        query: str,
        response: str,
        retrieved_contexts: List[str]
    ) -> Dict[str, Any]:
        """Evaluate how well the response utilizes retrieved context"""
        
        return get_langfuse_prompt(
            f"{self.get_prompt_prefix()}/context_utilization",
            query=query,
            response=response,
            retrieved_contexts=retrieved_contexts,
            domain=self.get_domain()
        )
    
    async def evaluate_answer_completeness(
        self,
        query: str,
        response: str,
        ground_truth: Optional[str] = None
    ) -> Dict[str, Any]:
        """Evaluate completeness of the RAG response"""
        
        return get_langfuse_prompt(
            f"{self.get_prompt_prefix()}/answer_completeness",
            query=query,
            response=response,
            ground_truth=ground_truth or "",
            domain=self.get_domain()
        )