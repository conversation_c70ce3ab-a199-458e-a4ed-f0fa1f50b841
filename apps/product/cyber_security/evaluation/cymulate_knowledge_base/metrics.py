"""CymulateKnowledgeBase RAG-specific metrics implementation"""

from typing import List, Dict, Any
from evaluation.framework.metrics import RagasMetrics
from evaluation.framework.utils import load_agent_config, get_enabled_metrics
from .llm_judge import CymulateKnowledgeBaseLLMJudge


class CymulateKnowledgeBaseMetrics(RagasMetrics):
    """Metrics specifically configured for CymulateKnowledgeBase RAG evaluation"""
    
    def __init__(self):
        # Load configuration
        self.config = load_agent_config("cymulate_knowledge_base")
        # Initialize LLM judge for custom evaluations
        self.llm_judge = CymulateKnowledgeBaseLLMJudge()
        # Initialize with enhanced configuration
        super().__init__(
            metrics=self.get_ragas_metrics(),
            config=self.config
        )
    
    def get_ragas_metrics(self) -> List[str]:
        """CymulateKnowledgeBase-specific RAGAS metrics from config"""
        enabled_ragas = get_enabled_metrics(self.config, "ragas_metrics")
        return [metric["name"] for metric in enabled_ragas]
    
    async def evaluate_custom(self, test_case: Any, output: Any) -> Dict[str, float]:
        """CymulateKnowledgeBase RAG evaluation logic using config and Langfuse prompts"""
        scores = {}
        
        # Skip custom metrics if LLM evaluation is disabled in expected_behavior
        if hasattr(test_case, 'expected_behavior') and test_case.expected_behavior.get("skip_llm_evaluation", False):
            return scores
        
        # Skip custom metrics if explicitly disabled in test metadata
        if hasattr(test_case, 'metadata') and test_case.metadata.get("skip_custom_metrics", False):
            return scores
        
        # Extract response text from output
        response = ""
        if hasattr(output, "messages") and output.messages:
            for msg in reversed(output.messages):
                if hasattr(msg, "content") and not hasattr(msg, "tool_call_id"):
                    response = msg.content
                    break
        else:
            response = str(output)
        
        # Extract query from test case
        query = ""
        if hasattr(test_case, 'input_data') and test_case.input_data.get("messages"):
            query_msg = test_case.input_data["messages"][0]
            query = query_msg.content if hasattr(query_msg, "content") else str(query_msg)
        else:
            query = str(test_case)
        
        # Extract retrieved contexts from tool calls
        retrieved_contexts = []
        if hasattr(output, "messages") and output.messages:
            for msg in output.messages:
                if hasattr(msg, "tool_call_id") and hasattr(msg, "content"):
                    # This is a tool response containing retrieved knowledge
                    retrieved_contexts.append(msg.content)
        
        # Get custom metrics - either from test case specification or config
        if hasattr(test_case, 'expected_behavior') and test_case.expected_behavior.get("metrics_to_use"):
            # Use test-specific metrics
            requested_metrics = test_case.expected_behavior["metrics_to_use"]
            all_metrics = self.config.get("custom_metrics", [])
            custom_metrics = [m for m in all_metrics if m["name"] in requested_metrics]
        else:
            # Use default enabled metrics from config
            custom_metrics = get_enabled_metrics(self.config, "custom_metrics")
        
        # Run each custom metric evaluation using Langfuse prompts
        for metric in custom_metrics:
            metric_name = metric["name"]
            prompt_name = metric["prompt_name"]
            
            # Prepare context for RAG evaluation
            context = {
                "retrieved_contexts": retrieved_contexts,
                "expected_tools": getattr(test_case, 'expected_tools', []),
                "ground_truth": getattr(test_case, 'ground_truth', "")
            }
            
            try:
                # All metrics now use Langfuse prompts for RAG evaluation
                from evaluation.framework.utils import get_langfuse_prompt
                expected_behavior = getattr(test_case, 'expected_behavior', {})
                
                # Prepare parameters for Langfuse prompt based on what each RAG metric needs
                prompt_params = {
                    "query": query,
                    "response": response,
                    "expected_behavior": expected_behavior,
                    "context": context,
                    # RAG-specific parameters
                    "retrieved_contexts": retrieved_contexts,
                    "ground_truth": context.get("ground_truth", ""),
                    "expected_context_usage": expected_behavior.get("uses_retrieved_context", True),
                    "expected_source_citation": expected_behavior.get("cites_sources", True)
                }
                
                prompt = get_langfuse_prompt(prompt_name, **prompt_params)
                evaluation_result = await self.llm_judge.evaluate_with_prompt(prompt)
                
                # Add results with metric name prefix
                for key, value in evaluation_result.items():
                    scores[f"{metric_name}_{key}"] = value
                    
            except Exception as e:
                print(f"Error in {metric_name} evaluation: {str(e)}")
                scores[f"{metric_name}_error"] = 1.0
        
        return scores