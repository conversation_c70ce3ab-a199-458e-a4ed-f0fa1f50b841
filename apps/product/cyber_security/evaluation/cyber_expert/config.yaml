# CyberExpert Evaluation Configuration
agent_name: cyber_expert

# Agent Configuration
agent_config:
  domain: "cybersecurity"
  prompt_prefix: "CyberSecurity/evaluation/cyber_expert"
  judge_manager_prompt: "CyberSecurity/evaluation/cyber_expert/judge_manager"

# RAGAS Metrics - Enhanced evaluation metrics
ragas_metrics:
  # Core metrics for accuracy and relevance
  - name: faithfulness
    weight: 2.0  # Higher weight for cybersecurity accuracy
    priority: high
  - name: answer_relevancy
    weight: 2.0  # Critical for cybersecurity responses
    priority: high
  # Context evaluation metrics
  - name: context_precision
    weight: 1.0  # Important for threat intelligence
    priority: medium
  - name: context_recall
    weight: 1.0  # Comprehensive threat coverage
    priority: medium
  # Enhanced metrics
  - name: answer_correctness
    weight: 2.5  # Combines accuracy and semantics
    priority: high

# Custom Metrics - All available for test-specific selection
custom_metrics:
  - name: cyber_expert_quality_judge
    weight: 3.0
    prompt_name: "CyberSecurity/evaluation/cyber_expert/general_quality_judge"
  - name: threat_detection
    weight: 1.0
    prompt_name: "CyberSecurity/evaluation/cyber_expert/threat_detection"
  - name: cve_coverage
    weight: 1.0
    prompt_name: "CyberSecurity/evaluation/cyber_expert/cve_coverage"
  - name: mitre_coverage
    weight: 1.0
    prompt_name: "CyberSecurity/evaluation/cyber_expert/mitre_coverage"
  - name: ioc_quality
    weight: 1.0
    prompt_name: "CyberSecurity/evaluation/cyber_expert/ioc_quality"
    criteria_mapping:
      includes_ttps: "Response should include Tactics, Techniques, and Procedures (TTPs)"
      uses_mitre_framework: "Response should reference MITRE ATT&CK framework"
      includes_iocs: "Response should include Indicators of Compromise (IOCs)"
      includes_mitigation: "Response should include mitigation strategies"
      actionable_recommendations: "Response should provide actionable recommendations"
      technical_depth: "Response should have {value} technical depth"
      threat_coverage: "Response should cover relevant cybersecurity threats"
      security_best_practices: "Response should include security best practices"

# Tool Metrics - Increased weight since this is working well
tool_metrics:
  - name: tool_accuracy
    enabled: true
    weight: 2.0

# Performance Metrics - Reduced impact
performance_metrics:
  - name: execution_time
    enabled: true
    weight: 0.2
  - name: semantic_similarity
    enabled: false  # Disable for now
    weight: 0.5