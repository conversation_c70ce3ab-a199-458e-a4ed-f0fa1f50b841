"""CyberExpert-specific metrics implementation"""

from typing import List, Dict, Any
from evaluation.framework.metrics import RagasMetrics
from evaluation.framework.utils import load_agent_config, get_enabled_metrics
from .llm_judge import CyberExpertLLMJudge


class CyberExpertMetrics(RagasMetrics):
    """Metrics specifically configured for CyberExpert evaluation"""
    
    def __init__(self):
        # Load configuration
        self.config = load_agent_config("cyber_expert")
        # Initialize LLM judge for custom evaluations
        self.llm_judge = CyberExpertLLMJudge()
        # Initialize with enhanced configuration
        super().__init__(
            metrics=self.get_ragas_metrics(),
            config=self.config
        )
    
    def get_ragas_metrics(self) -> List[str]:
        """CyberExpert-specific RAGAS metrics from config"""
        enabled_ragas = get_enabled_metrics(self.config, "ragas_metrics")
        return [metric["name"] for metric in enabled_ragas]
    
    async def evaluate_custom(self, test_case: Any, output: Any) -> Dict[str, float]:
        """CyberExpert custom evaluation logic using config and Langfuse prompts"""
        scores = {}
        
        # Skip custom metrics if LLM evaluation is disabled in expected_behavior
        if hasattr(test_case, 'expected_behavior') and test_case.expected_behavior.get("skip_llm_evaluation", False):
            return scores
        
        # Skip LLM judge metrics for tool-only evaluations (significant speedup)
        if hasattr(test_case, 'metadata') and test_case.metadata.get("evaluation_type") == "tool_only":
            return scores
        
        # Skip custom metrics if explicitly disabled in test metadata
        if hasattr(test_case, 'metadata') and test_case.metadata.get("skip_custom_metrics", False):
            return scores
        
        # Extract response text from output
        response = ""
        if hasattr(output, "messages") and output.messages:
            for msg in reversed(output.messages):
                if hasattr(msg, "content") and not hasattr(msg, "tool_call_id"):
                    response = msg.content
                    break
        else:
            response = str(output)
        
        # Extract query from test case
        query = getattr(test_case, 'query', str(test_case))
        
        # Get custom metrics - either from test case specification or config
        if hasattr(test_case, 'expected_behavior') and test_case.expected_behavior.get("metrics_to_use"):
            # Use test-specific metrics
            requested_metrics = test_case.expected_behavior["metrics_to_use"]
            all_metrics = self.config.get("custom_metrics", [])
            custom_metrics = [m for m in all_metrics if m["name"] in requested_metrics]
        else:
            # Use default enabled metrics from config
            custom_metrics = get_enabled_metrics(self.config, "custom_metrics")
        
        # Run each custom metric evaluation using Langfuse prompts
        for metric in custom_metrics:
            metric_name = metric["name"]
            
            # Include cyber_expert_quality_judge in custom metrics (no longer handled separately)
                
            prompt_name = metric["prompt_name"]
            
            # Extract actual tool calls from output
            actual_tool_calls = []
            if hasattr(output, "messages") and output.messages:
                for msg in output.messages:
                    if hasattr(msg, "tool_calls") and msg.tool_calls:
                        for tool_call in msg.tool_calls:
                            if hasattr(tool_call, "name"):
                                actual_tool_calls.append(tool_call.name)
                            elif isinstance(tool_call, dict) and "name" in tool_call:
                                actual_tool_calls.append(tool_call["name"])
            
            # Prepare context for this metric
            context = {
                "expected_threats": test_case.metadata.get("expected_threats", []) if hasattr(test_case, 'metadata') else [],
                "expected_cves": test_case.metadata.get("expected_cves", []) if hasattr(test_case, 'metadata') else [],
                "tools_used": actual_tool_calls
            }
            
            try:
                # All metrics now use Langfuse prompts - no hardcoded evaluation logic
                from evaluation.framework.utils import get_langfuse_prompt
                # Pass all the required parameters that Langfuse prompts expect
                expected_behavior = getattr(test_case, 'expected_behavior', {})
                
                # Prepare parameters for Langfuse prompt based on what each metric needs
                prompt_params = {
                    "query": query,
                    "response": response,
                    "expected_behavior": expected_behavior,
                    "context": context,  # This contains expected_threats, expected_cves, tools_used
                    # Also pass individual parameters for convenience
                    "expected_threats": context.get("expected_threats", []),
                    "expected_cves": context.get("expected_cves", []),
                    "tools_used": context.get("tools_used", [])
                }
                
                prompt = get_langfuse_prompt(prompt_name, **prompt_params)
                evaluation_result = await self.llm_judge.evaluate_with_prompt(prompt)
                
                # Add results with metric name prefix
                for key, value in evaluation_result.items():
                    scores[f"{metric_name}_{key}"] = value
                    
                # Note: cyber_expert_quality_judge is now handled by the overridden _evaluate_llm_judge method
                # to avoid duplication and conflicts
                    
            except Exception as e:
                print(f"Error in {metric_name} evaluation: {str(e)}")
                scores[f"{metric_name}_error"] = 1.0
        
        return scores
    
