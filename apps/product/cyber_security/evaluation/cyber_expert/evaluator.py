"""CyberExpert-specific evaluator implementation"""

from typing import List, Dict, Any, Type, Optional
from datetime import datetime
from evaluation.framework.base import AgentEvaluator, EvaluationResult, TestCase, TestCaseStatus
from evaluation.framework.utils import load_agent_config, get_langfuse_prompt
from corelanggraph.agents.base_agent import BaseAgent
from langgraph.types import RunnableConfig
from .llm_judge import CyberExpertLLMJudge


class CyberExpertEvaluator(AgentEvaluator):
    """Evaluator specifically configured for CyberExpert agent"""
    
    def __init__(self, agent_class: Type[BaseAgent]):
        super().__init__(agent_class)
        self._llm_judge = None
        self._metrics = None
        # Load configuration for CyberExpert
        self.config = load_agent_config("cyber_expert")
    
    def get_metrics_instance(self):
        """Return CyberExpert-specific metrics instance"""
        if not self._metrics:
            from .metrics import CyberExpertMetrics
            self._metrics = CyberExpertMetrics()
        return self._metrics
    
    def get_llm_judge_prompt(self, query: str, response: str, expected_behavior: Dict[str, Any]) -> str:
        """Return CyberExpert-specific LLM judge prompt from Langfuse"""
        
        # Use CyberExpert-specific evaluation prompt from Langfuse
        try:
            # Get cyber_expert_quality_judge prompt from config.yaml
            cyber_expert_quality_config = next(
                (m for m in self.config.get("custom_metrics", []) if m["name"] == "cyber_expert_quality_judge"), 
                None
            )
            if cyber_expert_quality_config:
                prompt_name = cyber_expert_quality_config["prompt_name"]
                prompt = get_langfuse_prompt(
                    prompt_name,
                    query=query,
                    response=response,
                    expected_behavior=expected_behavior
                )
                return prompt
            else:
                raise ValueError("No cyber_expert_quality_judge found in config.yaml")
        except Exception as e:
            raise RuntimeError(f"Could not load Langfuse prompt for CyberExpert evaluation: {e}")
    
    def get_llm_judge(self):
        """Return CyberExpert-specific LLM judge instance"""
        if not self._llm_judge:
            self._llm_judge = CyberExpertLLMJudge()
        return self._llm_judge
    
    async def _evaluate_llm_judge(self, test_case: TestCase, output: Any) -> Dict[str, float]:
        """Override base method to use CyberExpert-specific LLM judge"""
        # Check if LLM judge is disabled in expected_behavior
        if test_case.expected_behavior.get("skip_llm_evaluation", False):
            return {}
        
        # Check if LLM judge is disabled in metadata (fallback)
        if test_case.metadata.get("skip_llm_judge", False):
            return {}
        
        # Check if this is a tool-only evaluation
        if test_case.metadata.get("evaluation_type") == "tool_only":
            return {}
        
        if not test_case.expected_behavior:
            return {}
        
        try:
            judge = self.get_llm_judge()
            
            # Extract the query and response
            query = ""
            response = ""
            
            if hasattr(output, "messages") and output.messages:
                # Get the original query from test case
                if test_case.input_data.get("messages"):
                    query_msg = test_case.input_data["messages"][0]
                    query = query_msg.content if hasattr(query_msg, "content") else str(query_msg)
                
                # Get the final response from the agent
                for msg in reversed(output.messages):
                    if hasattr(msg, "content") and not hasattr(msg, "tool_call_id"):
                        response = msg.content
                        break
            else:
                query = str(test_case.input_data)
                response = str(output)
            
            # Extract tool calls made during execution
            actual_tool_calls = self._extract_tool_calls(output)
            tool_names = [tc.get("name", "") for tc in actual_tool_calls] if actual_tool_calls else []
            
            # Add tool call information to expected behavior for judge
            enhanced_behavior = test_case.expected_behavior.copy()
            enhanced_behavior["actual_tools_used"] = tool_names
            enhanced_behavior["expected_tools"] = test_case.expected_tools
            
            # Get the cyber_expert_quality_judge prompt from config and use it
            cyber_expert_quality_config = next(
                (m for m in self.config.get("custom_metrics", []) if m["name"] == "cyber_expert_quality_judge"), 
                None
            )
            
            if cyber_expert_quality_config:
                prompt_name = cyber_expert_quality_config["prompt_name"]
                from evaluation.framework.utils import get_langfuse_prompt
                prompt = get_langfuse_prompt(
                    prompt_name,
                    query=query,
                    response=response,
                    expected_behavior=enhanced_behavior
                )
                evaluation = await judge.evaluate_with_prompt(prompt)
            else:
                # Use default evaluation method if config not found
                evaluation = await judge.evaluate_response_quality(
                    query=query,
                    response=response,
                    expected_behavior=enhanced_behavior
                )
            
            result = {"llm_judge": evaluation.get("score", 0.5)}
            
            # Include reasoning if available
            if evaluation.get("reason"):
                result["llm_judge_reasoning"] = evaluation.get("reason")
            
            return result
            
        except Exception as e:
            print(f"CyberExpert LLM judge error: {e}")
            # No fallback - requires Langfuse prompts for evaluation
            return {}
    
    async def evaluate(
        self,
        test_case: TestCase,
        state_class: Type,
        config: Optional[RunnableConfig] = None
    ) -> EvaluationResult:
        """Override to include CyberExpert custom metrics"""
        start_time = datetime.now()
        
        try:
            state = test_case.to_state(state_class)
            if config is None:
                config = RunnableConfig(configurable={})
            
            output = await self.agent.execute(state, config)
            
            scores = {}
            
            # Include base evaluations
            tool_scores = await self._evaluate_tools(test_case, output)
            scores.update(tool_scores)
            
            # Run evaluations in parallel for better performance
            import asyncio
            
            ragas_task = self._evaluate_ragas(test_case, output)
            metrics = self.get_metrics_instance()
            custom_task = metrics.evaluate_custom(test_case, output)
            
            # Wait for both to complete
            ragas_scores, custom_scores = await asyncio.gather(ragas_task, custom_task)
            
            scores.update(ragas_scores)
            scores.update(custom_scores)
            
            # Map any custom metric score to llm_judge (take the first score found)
            llm_judge_score = None
            llm_judge_reasoning = None
            
            for key, value in custom_scores.items():
                if key.endswith("_score") and llm_judge_score is None:
                    llm_judge_score = value
                elif key.endswith("_reason") and llm_judge_reasoning is None:
                    llm_judge_reasoning = value
            
            if llm_judge_score is not None:
                scores["llm_judge"] = llm_judge_score
            if llm_judge_reasoning is not None:
                scores["llm_judge_reasoning"] = llm_judge_reasoning
            
            # Calculate overall score (exclude non-numeric values like reasoning and metadata)
            numeric_scores = {k: v for k, v in scores.items() if isinstance(v, (int, float))}
            
            # Exclude metadata fields that shouldn't count toward score
            excluded_fields = ['total_iocs', 'needs_current_info', 'used_tool', 'tool_calls_made']
            score_fields = {k: v for k, v in numeric_scores.items() if not any(k.endswith(field) or k == field for field in excluded_fields)}
            
            # If no tools are expected, exclude tool metrics from overall score
            if not test_case.expected_tools:
                score_fields = {k: v for k, v in score_fields.items() 
                                if not k.startswith('tool_')}
            
            overall_score = sum(score_fields.values()) / len(score_fields) if score_fields else 0.0
            scores["overall"] = overall_score
            
            status = TestCaseStatus.PASSED if overall_score >= 0.7 else TestCaseStatus.FAILED
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return EvaluationResult(
                test_case_id=test_case.id,
                status=status,
                scores=scores,
                metrics={"execution_time": execution_time},
                execution_time=execution_time,
                timestamp=datetime.now(),
                raw_output=output,
                tool_calls=self._extract_tool_calls(output)
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            return EvaluationResult(
                test_case_id=test_case.id,
                status=TestCaseStatus.FAILED,
                scores={"overall": 0.0},
                metrics={"execution_time": execution_time},
                execution_time=execution_time,
                timestamp=datetime.now(),
                error=str(e)
            )