"""Simplified Test Cases for CyberExpert Agent - Consistent Pattern"""

from langchain_core.messages import HumanMessage
from evaluation.framework import TestCase

# STANDARDIZED EXPECTED BEHAVIOR PATTERN - All tests use same structure
STANDARD_BEHAVIOR_PATTERN = {
    "uses_tool": None,  # True/False - Will be set per test
    "query_type": None,  # FACTUAL/CURRENT/INVESTIGATIVE/STRATEGIC
    "provides_good_answer": True,  # Basic quality check
    "technical_accuracy": True,  # Is information correct
    "tool_appropriateness": True,  # Used tools correctly
}

cyber_expert_test_cases = [
    # Test Case 1: FACTUAL - No tool needed
    TestCase(
        id="factual_no_tool",
        name="Ransomware Definition - Factual Knowledge Test",
        input_data={"messages": [HumanMessage(content="What is ransomware and how does it work?")]},
        expected_tools=[],
        expected_behavior={
            "uses_tool": False,
            "query_type": "FACTUAL",
            "provides_good_answer": True,
            "technical_accuracy": True,
            "tool_appropriateness": True,
            "metrics_to_use": ["cyber_expert_quality_judge"]
        },
        ground_truth="Ransomware is malicious software that encrypts files and demands payment for decryption"
    ),

    # Test Case 2: CURRENT - Must use tool  
    TestCase(
        id="current_with_tool",
        name="Current APT Campaigns - Real-time Intelligence",
        input_data={"messages": [HumanMessage(content="What are the latest APT29 campaigns targeting financial institutions in 2024?")]},
        expected_tools=["advanced_cyber_search"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "CURRENT",
            "provides_good_answer": True,
            "technical_accuracy": True,
            "tool_appropriateness": True,
            "metrics_to_use": ["threat_detection"]
        }
    ),

    # Test Case 3: INVESTIGATIVE - Should use tool
    TestCase(
        id="investigative_selective_tool", 
        name="Specific CVE Investigation - CVE-2024-38063",
        input_data={"messages": [HumanMessage(content="Analyze CVE-2024-38063 and its exploitation in the wild")]},
        expected_tools=["advanced_cyber_search"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "INVESTIGATIVE",
            "provides_good_answer": True,
            "technical_accuracy": True,
            "tool_appropriateness": True,
            "metrics_to_use": ["cve_coverage"]
        }
    ),

    # Test Case 4: STRATEGIC - No tool needed
    TestCase(
        id="strategic_minimal_tool",
        name="NIST Compliance Assessment - Strategic Analysis", 
        input_data={"messages": [HumanMessage(content="How should we assess our organization's NIST Cybersecurity Framework compliance gaps?")]},
        expected_tools=[],
        expected_behavior={
            "uses_tool": False,
            "query_type": "STRATEGIC",
            "provides_good_answer": True,
            "technical_accuracy": True,
            "tool_appropriateness": True,
            "metrics_to_use": ["mitre_coverage", "cyber_expert_quality_judge"]
        },
        ground_truth="NIST CSF compliance assessment involves gap analysis, current state mapping, target state definition, and prioritized remediation planning"
    ),

    # Test Case 5: CURRENT - Complex query with tool
    TestCase(
        id="precision_speed_test",
        name="Multi-aspect Query - Precision & Filter Test",
        input_data={"messages": [HumanMessage(content="What are the current Lazarus Group techniques being used against cryptocurrency exchanges and what immediate detection rules should we implement?")]},
        expected_tools=["advanced_cyber_search"],
        expected_behavior={
            "uses_tool": True,
            "query_type": "CURRENT",
            "provides_good_answer": True,
            "technical_accuracy": True,
            "tool_appropriateness": True,
            "metrics_to_use": ["ioc_quality", "threat_detection"]
        }
    ),

    # Test Case 6: TOOL-ONLY - Only test tool usage, no LLM evaluation
    TestCase(
        id="tool_only_validation",
        name="Tool Usage Only - No LLM Judge",
        input_data={"messages": [HumanMessage(content="Get me the latest CVE-2024 vulnerabilities affecting Linux systems")]},
        expected_tools=["advanced_cyber_search"],
        expected_behavior={
            "uses_tool": True,
            "skip_llm_evaluation": True,
        },
        ground_truth="Should retrieve current CVE-2024 vulnerabilities affecting Linux systems using search tools"
    )
]