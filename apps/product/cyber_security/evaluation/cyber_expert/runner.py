"""CyberExpert evaluation runner"""

import asyncio
from typing import List, Optional
from langgraph.types import RunnableConfig
from cyber_security.infra import init
from cyber_security.agents.cyber_expert.agents.cyber_expert_agent import CyberExpertAgent
from cyber_security.workflow.state import CyberSecuritySupervisorState
from evaluation.framework.runners import EvaluationRunner
from evaluation.framework.base import TestCase, AgentEvaluator
from .evaluator import CyberExpertEvaluator


class CyberExpertRunner(EvaluationRunner):
    """Runner specifically configured for CyberExpert evaluation"""
    
    def __init__(self):
        # Initialize with correct trace name for CyberExpert
        super().__init__(
            agent_class=CyberExpertAgent,
            state_class=CyberSecuritySupervisorState,
            trace_name="cyber_expert_evaluation"
        )
        # Use CyberExpert-specific evaluator
        self.evaluator = CyberExpertEvaluator(CyberExpertAgent)


async def run_cyber_expert_evaluation(
    test_cases: List[TestCase],
    config: Optional[RunnableConfig] = None
) -> None:
    """Run CyberExpert evaluation with custom configuration"""
    
    runner = CyberExpertRunner()
    summary = await runner.run(test_cases, config)
    
    print("\n" + "="*60)
    print("CYBEREXPERT EVALUATION RESULTS")
    print("="*60)
    print(f"Pass Rate: {summary.pass_rate:.1%}")
    print(f"Average Score: {summary.average_score:.2f}")
    print(f"Tests Passed: {summary.passed_tests}/{summary.total_tests}")
    print(f"Average Execution Time: {summary.average_execution_time:.1f}s")
    
    if summary.failed_tests > 0:
        print(f"\n⚠️  {summary.failed_tests} test(s) failed")
    
    return summary


if __name__ == "__main__":
    from .test_cases import cyber_expert_test_cases
    
    # Run evaluation
    asyncio.run(run_cyber_expert_evaluation(cyber_expert_test_cases))