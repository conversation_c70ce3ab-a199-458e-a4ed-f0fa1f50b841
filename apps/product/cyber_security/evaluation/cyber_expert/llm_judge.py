"""CyberExpert-specific LLM Judge implementation"""

from typing import Dict, Any, Optional
import os
from evaluation.framework.llm_judge import LLMJudge
from evaluation.framework.utils import get_langfuse_prompt
from langfuse import Langfuse
from corellm.providers.provider import ModelProvider


class CyberExpertLLMJudge(LLMJudge):
    """LLM Judge specifically configured for evaluating CyberExpert responses"""
    
    def __init__(self):
        # Load config to get agent-specific settings
        from evaluation.framework.utils import load_agent_config
        config = load_agent_config("cyber_expert")
        agent_config = config.get("agent_config", {})
        
        super().__init__(
            name="cyber_expert_llm_judge",
            description="LLM Judge for evaluating CyberExpert agent responses with cybersecurity expertise.",
            prompt_name=agent_config.get("judge_manager_prompt", "CyberSecurity/evaluation/cyber_expert/judge_manager")
        )
        self.config = config
    
    def get_prompt_prefix(self) -> str:
        """Return CyberExpert-specific prompt prefix for Langfuse paths"""
        return self.config.get("agent_config", {}).get("prompt_prefix", "CyberSecurity/evaluation/cyber_expert")
    
    def get_domain(self) -> str:
        """Return the cybersecurity domain for this judge"""
        return self.config.get("agent_config", {}).get("domain", "cybersecurity")
    
    def _create_evaluation_prompt(
        self,
        query: str,
        response: str,
        expected_behavior: Dict[str, Any]
    ) -> str:
        """Create cybersecurity-specific evaluation prompt"""
        
        # All prompts must be in Langfuse - no hardcoded fallbacks
        return get_langfuse_prompt(
            f"{self.get_prompt_prefix()}/general_quality_judge",
            query=query,
            response=response,
            expected_behavior=expected_behavior
        )
    
    # REMOVED: All hardcoded evaluation methods
    # All evaluation should be configuration-driven through config.yaml
    
    # REMOVED: All hardcoded evaluation methods
    # All evaluation should be configuration-driven through config.yaml