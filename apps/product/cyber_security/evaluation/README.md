# CyberSecurity Evaluation System

A comprehensive, configurable evaluation system for cybersecurity agents that uses YAML configuration files and Langfuse prompts.

## Overview

This system provides:
- **YAML-based configuration** for all metrics (RAGAS, custom, tool, performance)
- **Langfuse integration** for centralized prompt management
- **LLM-based evaluators** for sophisticated cybersecurity assessment
- **Abstract base class pattern** for consistent agent evaluation structure
- **Modular design** that's easy to extend

## Architecture

### Abstract Base Class Pattern

The framework uses an abstract base class (`LLMJudge`) that all domain-specific evaluators inherit from:

```
LLMJudge (Abstract BaseAgentLLM + ABC)
├── get_prompt_prefix() → Must implement (agent-specific Langfuse paths)
├── get_domain() → Must implement (domain/context for evaluations)
├── execute() → Configuration-driven method dispatch
├── evaluate_technical_accuracy(response, domain) → No domain defaults
├── evaluate_completeness(query, response, elements) → Generic method
├── evaluate_harmfulness(response, context) → No context defaults
└── _create_evaluation_prompt() → Config-driven criteria mapping

CyberExpertLLMJudge extends LLMJudge
├── get_prompt_prefix() → "CyberSecurity/evaluation/cyber_expert"
├── get_domain() → "cybersecurity"
├── _create_evaluation_prompt() → Cybersecurity-specific criteria
└── Custom cyber-specific methods (threat intelligence, etc.)

HealthcareAgentLLMJudge extends LLMJudge  
├── get_prompt_prefix() → "Healthcare/evaluation"
├── get_domain() → "healthcare"
├── _create_evaluation_prompt() → Healthcare-specific criteria
└── Custom healthcare methods (HIPAA compliance, etc.)

FinanceAgentLLMJudge extends LLMJudge
├── get_prompt_prefix() → "Finance/evaluation"
├── get_domain() → "finance"
├── _create_evaluation_prompt() → Finance-specific criteria
└── Custom finance methods (regulatory compliance, etc.)
```

**Key Abstract Design Principles:**
- ✅ **Domain-agnostic framework**: No hardcoded domain defaults or prompts
- ✅ **Required abstractions**: Must implement `get_domain()` and `get_prompt_prefix()`
- ✅ **Configuration-driven**: All evaluation logic driven by YAML config
- ✅ **BaseAgentLLM inheritance**: Same pattern as real agents (CyberExpertAgent, AssessmentAgent)
- ✅ **Zero hardcoded conditionals**: All criteria mapping from config files
- ✅ **No framework prompts**: Framework is abstract - agents provide ALL prompts via Langfuse

**IMPORTANT**: The framework itself contains NO prompts and should never be instantiated directly. It serves as an abstract base class that enforces the pattern but provides no domain-specific functionality. Each agent (cyber_expert, healthcare, finance, etc.) must provide ALL its own prompts in Langfuse.

## Directory Structure

```
evaluation/
├── cyber_expert/               # CyberExpert agent evaluation
│   ├── config.yaml            # Agent-specific configuration
│   ├── metrics.py             # Metrics implementation
│   ├── evaluator.py           # Main evaluator class
│   ├── llm_judge.py          # LLM-based evaluations
│   └── ...
├── framework/                  # Abstract evaluation framework
│   ├── llm_judge.py           # Abstract LLM judge base class  
│   ├── metrics.py             # Base metrics classes
│   ├── base.py                # Base evaluator classes
│   └── utils.py               # Framework utilities
├── LANGFUSE_PROMPTS.md       # All prompts for Langfuse
└── README.md                 # This file
```

## How It Works

### 1. Configuration-Driven
All metrics are defined in `config.yaml`:

```yaml
# Agent identification
agent_name: cyber_expert

# RAGAS Metrics
ragas_metrics:
  - name: faithfulness
    enabled: true
    weight: 1.0

# Custom Metrics with Langfuse prompts
custom_metrics:
  - name: threat_detection
    enabled: true
    weight: 2.0
    prompt_name: "CyberSecurity/evaluation/threat_detection"
```

### 2. Langfuse Integration
Prompts are stored in Langfuse and referenced by name:
- `CyberSecurity/evaluation/threat_detection`
- `CyberSecurity/evaluation/cve_coverage`
- etc.

### 3. Automatic Loading
The metrics class automatically loads configuration:

```python
class CyberExpertMetrics(RagasMetrics):
    def __init__(self):
        self.config = load_agent_config("cyber_expert")  # Loads config.yaml
        super().__init__()
```

## Current Metrics

### RAGAS Metrics
- `faithfulness` - How factual is the response
- `answer_relevancy` - How relevant to the query
- `context_precision` - Quality of retrieved context
- `context_recall` - Completeness of context

### Custom Cybersecurity Metrics
- `threat_detection` - Threat identification capabilities
- `cve_coverage` - CVE identification and coverage
- `mitre_coverage` - MITRE ATT&CK framework usage
- `ioc_quality` - Quality of Indicators of Compromise
- `tool_appropriateness` - Appropriate tool usage

### Tool Metrics
- `tool_accuracy` - Accuracy of tool calls

### Performance Metrics
- `execution_time` - Response time
- `semantic_similarity` - Semantic similarity to expected output

## Adding a New Metric

### Step 1: Add to config.yaml

```yaml
custom_metrics:
  - name: my_new_metric
    enabled: true
    weight: 1.5
    prompt_name: "CyberSecurity/evaluation/my_new_metric"
```

### Step 2: Create Langfuse Prompt

Create a prompt in Langfuse with the name `CyberSecurity/evaluation/my_new_metric`:

```jinja2
You are an expert cybersecurity evaluator. Evaluate [specific aspect].

**Input:**
Query: {{query}}
Response: {{response}}
Context: {{context}}

**Evaluation Criteria:**
1. [Criterion 1]
2. [Criterion 2]
3. [Criterion 3]

**Instructions:**
Rate on a scale of 0-10 where:
- 0-3: Poor
- 4-6: Adequate
- 7-8: Good
- 9-10: Excellent

**Output Format:**
Score: [0-10]
Reasoning: [Detailed explanation]
```

### Step 3: Test the Metric

The metric will automatically be loaded and used:

```python
metrics = CyberExpertMetrics()
scores = await metrics.evaluate_custom(test_case, output)
# Will include: my_new_metric_score, my_new_metric_reasoning
```

### Step 4: (Optional) Add Custom Logic

If you need custom processing beyond the LLM judge, create a custom evaluator:

```python
# In evaluators.py
class MyNewMetricEvaluator(BaseLangfuseEvaluator):
    def __init__(self, langfuse_client):
        super().__init__(langfuse_client, "my_new_metric")
    
    async def evaluate(self, query: str, response: str, context: Dict[str, Any] = None):
        # Get LLM evaluation
        result = await super().evaluate(query, response, context)
        
        # Add custom logic
        custom_metrics = self._calculate_custom_metrics(response)
        result.update(custom_metrics)
        
        return result
    
    def _calculate_custom_metrics(self, response: str):
        # Custom calculation logic
        return {"custom_score": 0.8}
```

## Adding a New Agent

### Step 1: Create Agent Directory

```bash
mkdir evaluation/my_agent
```

### Step 2: Create config.yaml

```yaml
# evaluation/my_agent/config.yaml
agent_name: my_agent

ragas_metrics:
  - name: faithfulness
    enabled: true
    weight: 1.0

custom_metrics:
  - name: my_agent_specific_metric
    enabled: true
    weight: 1.0
    prompt_name: "MyAgent/evaluation/specific_metric"

tool_metrics:
  - name: tool_accuracy
    enabled: true
    weight: 1.0

performance_metrics:
  - name: execution_time
    enabled: true
    weight: 0.5
```

### Step 3: Create LLM Judge

```python
# evaluation/my_agent/llm_judge.py
from evaluation.framework.llm_judge import LLMJudge


class MyAgentLLMJudge(LLMJudge):
    """LLM Judge specifically configured for MyAgent responses"""
    
    def __init__(self):
        super().__init__(
            name="my_agent_llm_judge",
            description="LLM Judge for MyAgent domain-specific evaluation.",
            prompt_name="MyAgent/evaluation/judge_manager"
        )
    
    def get_prompt_prefix(self) -> str:
        """Return agent-specific prompt prefix for Langfuse paths"""
        return "MyAgent/evaluation"
    
    def get_domain(self) -> str:
        """Return the domain for this judge (REQUIRED - must implement abstract method)"""
        return "my_domain"  # e.g., "healthcare", "finance", "legal", etc.

**CRITICAL**: Notice the constructor pattern - exactly like real agents:
- Just `def __init__(self):` with no parameters
- Uses `ModelProvider().get_llm()` pattern
- Follows BaseAgentLLM inheritance
- No unnecessary langfuse_client parameter
    
    # Optional: Override evaluation methods for domain-specific behavior
    def _create_evaluation_prompt(
        self,
        query: str,
        response: str,
        expected_behavior: Dict[str, Any]
    ) -> str:
        """Create domain-specific evaluation prompt"""
        
        # Get prompt from Langfuse - all prompts must be in Langfuse
        try:
            from evaluation.framework.utils import get_langfuse_prompt
            prompt = get_langfuse_prompt(
                f"{self.get_prompt_prefix()}/general_quality",
                query=query,
                response=response,
                expected_behavior=expected_behavior
            )
            return prompt
        except Exception:
            pass  # Fall back to config-driven prompt
        
        # Use domain-specific criteria from config
        from evaluation.framework.utils import load_agent_config
        
        agent_config = load_agent_config("my_agent")
        custom_metrics = agent_config.get("custom_metrics", [])
        
        # Find general_quality_judge config
        quality_config = next((m for m in custom_metrics if m["name"] == "general_quality_judge"), None)
        criteria_mapping = quality_config.get("criteria_mapping", {}) if quality_config else {}
        
        # Build domain-specific criteria from config
        criteria = []
        for key, value in expected_behavior.items():
            if key in criteria_mapping:
                criterion_template = criteria_mapping[key]
                if "{value}" in criterion_template:
                    criteria.append(criterion_template.format(value=value))
                elif value:  # Only add if value is truthy
                    criteria.append(criterion_template)
            else:
                # Generic fallback
                criteria.append(f"{key}: {value}")
        
        return f"""
        Evaluate the following {self.get_domain()} response based on these criteria:
        
        Query: {query}
        
        Evaluation Criteria:
        {chr(10).join(f"- {c}" for c in criteria)}
        
        Response to Evaluate:
        {response}
        
        Provide a score from 0 to 1 based on {self.get_domain()} evaluation standards.
        
        Format your response as JSON:
        {{
            "score": 0.X,
            "reason": "Detailed {self.get_domain()}-focused explanation"
        }}
        """
```

### Step 4: Create metrics.py

```python
# evaluation/my_agent/metrics.py
from typing import List, Dict, Any
from evaluation.framework.metrics import RagasMetrics
from evaluation.framework.utils import load_agent_config, get_enabled_metrics, evaluate_with_llm_judge

class MyAgentMetrics(RagasMetrics):
    def __init__(self):
        self.config = load_agent_config("my_agent")
        super().__init__()
    
    def get_ragas_metrics(self) -> List[str]:
        enabled_ragas = get_enabled_metrics(self.config, "ragas_metrics")
        return [metric["name"] for metric in enabled_ragas]
    
    async def evaluate_custom(self, test_case: Any, output: Any) -> Dict[str, float]:
        scores = {}
        
        # Extract response
        response = str(output)
        query = str(test_case)
        
        # Get custom metrics from config
        custom_metrics = get_enabled_metrics(self.config, "custom_metrics")
        
        # Run each metric
        for metric in custom_metrics:
            try:
                result = await evaluate_with_llm_judge(
                    metric["prompt_name"], 
                    query, 
                    response, 
                    {}
                )
                for key, value in result.items():
                    scores[f"{metric['name']}_{key}"] = value
            except Exception as e:
                scores[f"{metric['name']}_error"] = 1.0
        
        return scores
```

### Step 5: Create Evaluator

```python
# evaluation/my_agent/evaluator.py
from typing import Type, Dict, Any
from evaluation.framework.base import AgentEvaluator
from evaluation.framework.utils import load_agent_config, get_langfuse_prompt
from corelanggraph.agents.base_agent import BaseAgent
from .llm_judge import MyAgentLLMJudge


class MyAgentEvaluator(AgentEvaluator):
    """Evaluator specifically configured for MyAgent"""
    
    def __init__(self, agent_class: Type[BaseAgent]):
        super().__init__(agent_class)
        self._llm_judge = None
        self._metrics = None
        self.config = load_agent_config("my_agent")
    
    def get_metrics_instance(self):
        if not self._metrics:
            from .metrics import MyAgentMetrics
            self._metrics = MyAgentMetrics()
        return self._metrics
    
    def get_llm_judge_prompt(self, query: str, response: str, expected_behavior: Dict[str, Any]) -> str:
        try:
            prompt = get_langfuse_prompt(
                "MyAgent/evaluation/general_quality_judge",
                query=query,
                response=response,
                expected_behavior=expected_behavior
            )
            return prompt
        except Exception as e:
            print(f"Could not load Langfuse prompt for general evaluation: {e}")
    
    def get_llm_judge(self):
        if not self._llm_judge:
            self._llm_judge = MyAgentLLMJudge()
        return self._llm_judge
```

### Step 6: Create Langfuse Prompts

Upload prompts to Langfuse with paths like:
- `MyAgent/evaluation/specific_metric`
- `MyAgent/evaluation/general_quality_judge`

### Step 7: Use the New Agent

```python
from evaluation.my_agent.metrics import MyAgentMetrics

metrics = MyAgentMetrics()
scores = await metrics.evaluate_custom(test_case, output)
```

## Configuration Options

### Metric Types

1. **RAGAS Metrics** (`ragas_metrics`)
   - Built-in evaluation metrics from the RAGAS library
   - Options: faithfulness, answer_relevancy, context_precision, context_recall

2. **Custom Metrics** (`custom_metrics`)
   - LLM-based evaluators using Langfuse prompts
   - Requires `prompt_name` pointing to Langfuse prompt

3. **Tool Metrics** (`tool_metrics`)
   - Evaluate tool usage accuracy and appropriateness
   - Currently: tool_accuracy

4. **Performance Metrics** (`performance_metrics`)
   - Measure execution time and quality
   - Options: execution_time, semantic_similarity

### Metric Properties

- `name`: Unique identifier for the metric
- `enabled`: Whether to run this metric (true/false)
- `weight`: Importance weight for overall scoring
- `prompt_name`: Langfuse prompt path (for custom metrics)
- `parameters`: Additional configuration (optional)

## Environment Variables

- `LANGFUSE_PROMPT_FOLDER`: Set automatically by the system
- `LANGFUSE_PROMPT_LABEL`: Defaults to "production"
- `MODEL_NAME`: LLM model for evaluations

## Usage Examples

### Basic Usage

```python
from evaluation.cyber_expert.metrics import CyberExpertMetrics

# Automatic configuration loading
metrics = CyberExpertMetrics()

# Evaluate a test case
scores = await metrics.evaluate_custom(test_case, agent_output)
print(scores)
# Output: {
#   'threat_detection_score': 0.8,
#   'threat_detection_reasoning': '...',
#   'cve_coverage_score': 0.7,
#   ...
# }
```

### Custom Evaluation

```python
from evaluation.framework.utils import evaluate_with_llm_judge

# Direct LLM evaluation
result = await evaluate_with_llm_judge(
    "CyberSecurity/evaluation/threat_detection",
    query="What are the latest APT threats?",
    response="APT29 is currently active...",
    context={"expected_threats": ["APT29"]}
)
print(result)  # {'score': 0.9, 'reasoning': '...'}
```

### Configuration Loading

```python
from evaluation.framework.utils import load_agent_config, get_enabled_metrics

# Load configuration
config = load_agent_config("cyber_expert")

# Get specific metric types
custom_metrics = get_enabled_metrics(config, "custom_metrics")
ragas_metrics = get_enabled_metrics(config, "ragas_metrics")
```

## Best Practices

### 1. Prompt Design
- Use clear evaluation criteria
- Include specific scoring guidelines
- Use Jinja2 templating for dynamic content
- Provide structured output format

### 2. Metric Configuration
- Use descriptive metric names
- Set appropriate weights based on importance
- Enable/disable metrics based on use case
- Group related metrics logically

### 3. Testing
- Test each metric individually
- Validate Langfuse prompt rendering
- Check configuration loading
- Verify scoring calculations

### 4. Maintenance
- Keep prompts up to date in Langfuse
- Version control configuration changes
- Monitor metric performance
- Update weights based on results

## Troubleshooting

### Common Issues

1. **Framework Instantiation Error**
   ```
   Error: Framework LLMJudge is abstract and should not be instantiated directly
   ```
   - ✅ Framework is abstract - use domain-specific judges (CyberExpertLLMJudge)
   - ✅ Framework has NO prompts - agents provide ALL prompts
   - ✅ Never instantiate LLMJudge directly

2. **Prompt Not Found**
   ```
   Error: No Langfuse prompt found: CyberSecurity/evaluation/my_metric
   ```
   - Check prompt name in config.yaml
   - Verify prompt exists in Langfuse
   - Check prompt label ("production")

3. **Configuration Not Loading**
   ```
   Error: Configuration file not found
   ```
   - Ensure config.yaml exists in agent directory
   - Check YAML syntax
   - Verify agent name matches directory

4. **Metric Not Running**
   - Check `enabled: true` in config
   - Verify metric type (ragas_metrics, custom_metrics, etc.)
   - Check for syntax errors in config.yaml

5. **Wrong Constructor Pattern**
   ```
   Error: TypeError: __init__() takes X positional arguments but Y were given
   ```
   - ✅ Use `def __init__(self):` pattern like real agents
   - ✅ Don't pass unnecessary parameters like langfuse_client
   - ✅ Follow exact BaseAgentLLM pattern

### Debug Mode

Enable debug logging to see what's happening:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Run evaluation
metrics = CyberExpertMetrics()
scores = await metrics.evaluate_custom(test_case, output)
```

## Contributing

When adding new metrics or agents:

1. Follow the existing naming conventions
2. Document your prompts in LANGFUSE_PROMPTS.md
3. Include example configurations
4. Test thoroughly with various inputs
5. Update this README with your additions

## Examples

See the `cyber_expert` implementation as a reference for:
- Complete configuration setup
- Metrics implementation
- LLM judge integration
- Evaluator class structure