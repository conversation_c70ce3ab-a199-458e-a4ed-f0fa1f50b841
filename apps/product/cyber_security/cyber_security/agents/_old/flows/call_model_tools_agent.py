import os
from datetime import datetime
from typing import List

from corelanggraph.agents import BaseAgentLLM
from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm.providers.provider import Model<PERSON>rovider
from ddtrace.llmobs.decorators import retrieval
from langchain_core.agents import AgentActionMessageLog
from langchain_core.messages import AIMessage, BaseMessage, trim_messages
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent
from logger import logger

from cyber_security.tools import tools
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class CymulateXspmAssistantAgents(BaseAgentLLM):
    def __init__(self):
        super().__init__(
            name="cymulate_xspm_assistant",
            description="Call the model and tools to generate a response.",
            llm=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME", "azure_model_2"), temperature=0.1
            ),
        )

    @execution_time
    @retrieval(name="call_model_tools_agent")
    async def execute(
        self, state: CyberSecurityState, config: RunnableConfig
    ) -> CyberSecurityState:
        """Call the model and tools to generate a response."""

        if "configurable" not in config or "session_id" not in config["configurable"]:
            raise ValueError(
                "Make sure that the config includes the following information: {'configurable': {'session_id': 'some_value'}}"
            )

        session_id = config["configurable"]["session_id"]
        logger.info(f"Fetching user history for session ID: {session_id}")

        self.write_stream(
            "update",
            {
                "event_data": state.event_data,
                "initial_response": state.initial_response,
                "message": ":loading-2: Preparing your answer...",
                "metadata": state.metadata,
            },
        )

        chat_history = self.get_history(state.messages)[:-1]
        input_metadata = self.get_metadata(state.input_metadata)

        results = await self.call_model(state, input_metadata, chat_history)

        response = results.get("output", "No response found.")
        state.messages.append(
            AIMessage(content=response, id=datetime.now().timestamp())
        )

        try:
            state.docs += self.process_intermediate_steps(results)
        except Exception as e:
            logger.warning(f"Error processing intermediate steps for docs: {e}")

        return state

    async def call_model(
        self,
        state: CyberSecurityState,
        input_metadata: List[BaseMessage],
        chat_history: List[BaseMessage],
    ) -> CyberSecurityState:
        prompt = self.system_prompt.format(
            input=state.user_input,
            input_metadata=input_metadata,
            chat_history=chat_history,
        )

        agent = create_react_agent(
            prompt=prompt,
            llm=self.llm,
            tools=tools,
            verbose=True,
        )
        return await agent.ainvoke(state)

    def process_links(self, links_list):
        """Extract valid document names and URLs from the links list."""
        if not isinstance(links_list, list):
            logger.warning(f"Unexpected format for links_list: {links_list}")
            return []

        docs = [
            {"name": link.get("name", "Unknown Name"), "url": link["url"]}
            for link in links_list
            if isinstance(link, dict) and "url" in link
        ]

        if not docs:
            logger.warning(f"No valid documents found in links list: {links_list}")
        return docs

    def process_step(self, step):
        """Process a single step to extract documents with names and URLs."""
        if not (
            isinstance(step, tuple)
            and len(step) > 1
            and isinstance(step[0], AgentActionMessageLog)
        ):
            logger.warning(f"Unexpected format or type in step: {step}")
            return []

        _, links_data = (
            step[1]
            if isinstance(step[1], tuple) and len(step[1]) == 2
            else (None, None)
        )
        return self.process_links(links_data) if links_data else []

    def process_intermediate_steps(self, results):
        """Process all intermediate steps and return extracted documents."""
        intermediate_steps = results.get("intermediate_steps")
        if not intermediate_steps:
            logger.info("No intermediate steps found in agent results.")
            return []

        docs = []
        for step in intermediate_steps:
            try:
                docs.extend(self.process_step(step))
            except Exception as e:
                logger.warning(f"Error processing step {step}: {e}")
        return docs

    def get_history(self, messages: List[BaseMessage]):
        history_messages = trim_messages(
            messages=messages,
            max_tokens=50000,
            strategy="last",
            token_counter=self.llm,
            include_system=True,
            allow_partial=True,
            # start_on=["human"],
            # end_on=["ai", "tool"],
        )

        return sorted(history_messages, key=lambda x: x.id)

    def get_metadata(self, input_metadata: List[BaseMessage]):
        metadata_messages = trim_messages(
            messages=input_metadata,
            max_tokens=50000,
            strategy="last",
            token_counter=self.llm,
            include_system=True,
            allow_partial=True,
        )

        return sorted(metadata_messages, key=lambda x: x.id)
