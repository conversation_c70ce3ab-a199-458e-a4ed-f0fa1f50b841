from datetime import datetime, timezone

from corelanggraph.agents import BaseAgent
from ddtrace.llmobs.decorators import retrieval
from logger import logger

from cyber_security.services.assessment_manager import AssessmentManager
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class SummaryAssessmentAdminAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="summary_assessment_admin_agent",
            description="Summarize the assessment (admin).",
        )

    @execution_time
    @retrieval(name="summary_assessment_admin_agent")
    async def execute(self, state: CyberSecurityState) -> CyberSecurityState:
        """Summarize the assessment (admin)."""
        assessment_manager = AssessmentManager(
            validate_user=state.should_validate_user()
        )

        doc_data = {
            "@timestamp": datetime.now(timezone.utc).isoformat(),
            "summary": "",
            "softwareEnv": state.event_data.software_env,
            "attackID": state.event_data.attack_id,
            "status": "In Progress",
        }

        try:
            assessment_id = state.assessments[0].get("attackID")

            if not state.assessments:
                doc_data["status"] = "Failed"
                logger.warning(f"Assessment {assessment_id} not found")
                raise ValueError(f"Assessment {assessment_id} not found")

            doc_data["summary"] = state.messages[-1].content
            doc_data["status"] = "Completed"
        except Exception as e:
            logger.error(
                f"Error summarizing assessment {state.event_data.attack_id}: {str(e)}"
            )
            doc_data["status"] = "Failed"
            doc_data["summary"] = str(e)

        await assessment_manager.upsert_document(state.event_data.attack_id, doc_data)

        return state
