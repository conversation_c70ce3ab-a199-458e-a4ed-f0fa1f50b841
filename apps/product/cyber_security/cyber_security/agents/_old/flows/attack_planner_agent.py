from datetime import datetime

from corelanggraph.agents import BaseAgent
from ddtrace.llmobs.decorators import retrieval
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig
from logger import logger

from cyber_security.infra.init import infra
from cyber_security.models.enums import ClientTypeEnum
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState

if (
    infra.get_secret(key="CLIENT_TYPE", default=ClientTypeEnum.Slack.value)
    == ClientTypeEnum.Websocket.value
):
    from cyber_security.services.attack_planner_manager import AttackPlannerManager


class AttackPlannerAgent(BaseAgent):
    def __init__(self) -> None:
        super().__init__(
            name="attack_planner_agent", description="Start the attack planner."
        )

    @execution_time
    @retrieval(name="attack_planner_agent")
    async def execute(
        self, state: CyberSecurityState, config: RunnableConfig
    ) -> CyberSecurityState:
        """Start the attack planner."""
        if not state.classification == "attack_planner":
            return state

        user_query = state.user_input

        if not state.user_cookies or not state.user_data:
            raise ValueError("No user cookies or user data provided.")

        try:
            attack_planner_manager: AttackPlannerManager = AttackPlannerManager(
                client_type=state.client_type,
            )

            attack_planner_answer, docs = await attack_planner_manager.start(
                message=user_query,
                cookie=state.user_cookies,
                user_data=state.user_data,
                event=state.event_data,
            )

            state.docs = docs
            state.messages.append(
                AIMessage(content=attack_planner_answer, id=datetime.now().timestamp())
            )

        except Exception as e:
            logger.error(f"Error retrieving dashboards data: {e}")
            state.error_message = "Something went wrong while processing your input. Please try again or choose a different method to continue."

        return state
