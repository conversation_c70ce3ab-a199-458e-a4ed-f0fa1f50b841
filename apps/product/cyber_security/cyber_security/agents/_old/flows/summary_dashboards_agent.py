import re
from datetime import datetime

from corelanggraph.agents import BaseAgent
from corelanggraph.app_builder.models.event import ClientTypeEnum
from ddtrace.llmobs.decorators import retrieval
from langchain_core.messages import AIMessage
from langchain_core.tools import tool
from logger import logger

from cyber_security.models.event_data import EventDataType
from cyber_security.models.user_data import UserData
from cyber_security.services.widgets_processor import WidgetProcessor
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class SummaryDashboardsAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="summary_dashboards_agent",
            description="Fetches dashboards data based on the user's query.",
        )

    @tool
    async def retrieve_dashboards_data(
        self,
        classification: str,
        query: str,
        user_cookies: str,
        client_type: ClientTypeEnum,
        event_data: EventDataType,
        user_data: UserData,
    ) -> str:
        """Retrieve dashboards data based on the user's cookies."""
        widget_processor = WidgetProcessor(client_type=client_type)

        widget_docs = await widget_processor.get_docs(
            query, event_data.software_env, user_data.client_ids, classification
        )
        widget_responses = await widget_processor.get_widgets_responses(
            event=event_data,
            widget_docs=widget_docs,
            question=query,
            cookie=user_cookies,
            endpoints=user_data.endpoints,
        )

        widgets_answer = await widget_processor.get_widgets_answers(
            widget_docs, widget_responses, query, classification
        )
        return widgets_answer

    @execution_time
    @retrieval(name="summary_dashboards_agent")
    async def execute(self, state: CyberSecurityState) -> CyberSecurityState:
        """Fetches dashboards data based on the user's query."""
        if (
            not state.user_profile
            or state.client_type != ClientTypeEnum.Websocket
            or state.classification != "dashboards"
        ):
            return state

        if not state.user_cookies or not state.should_validate_user():
            logger.error("No user cookies provided.")
            state.input_metadata.append(
                AIMessage(
                    content="No user cookies provided.", id=datetime.now().timestamp()
                )
            )
            return state

        if (
            not state.user_profile
            or state.client_type != ClientTypeEnum.Websocket
            or state.classification != "dashboards"
        ):
            return state

        if not state.user_cookies or not state.should_validate_user():
            logger.error("No user cookies provided.")
            state.input_metadata.append(
                AIMessage(
                    content="No user cookies provided.", id=datetime.now().timestamp()
                )
            )
            return state

        user_query = state.user_input

        try:
            answer = await self.retrieve_dashboards_data.ainvoke(
                {
                    "classification": state.classification,
                    "query": re.sub(
                        r"summarize", "", user_query, flags=re.IGNORECASE
                    ).strip(),
                    "user_cookies": state.user_cookies,
                    "client_type": state.client_type,
                    "event_data": state.event_data,
                    "user_data": state.user_data,
                }
            )

            if not answer:
                raise ValueError(
                    f"No data found for the user's query. Query: {user_query}"
                )

            state.messages.append(
                AIMessage(content=answer, id=datetime.now().timestamp())
            )
        except Exception as e:
            logger.error(e)
            state.error_message = "Something went wrong while processing your input. Please try again or choose a different method to continue."
        return state
