from datetime import datetime
from typing import Any

from corelanggraph.agents import BaseAgent
from ddtrace.llmobs.decorators import retrieval
from langchain_core.messages import AIMessage
from langchain_core.tools import tool

from cyber_security.services.assessment_manager import AssessmentManager
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class RetrieveAssessmentLogsAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="retrieve_assessment_logs_agent",
            description="Retrieves assessment logs based on MongoDB IDs.",
        )

    @tool
    async def retrieve_assessment_logs(
        self, validate_user: bool, mongo_ids: list[str]
    ) -> list[dict[str, Any]]:
        """Retrieve assessment logs based on MongoDB IDs."""
        assessment_manager = AssessmentManager(validate_user=validate_user)
        assessment_logs = await assessment_manager.get_assessments_logs(
            assessment_ids=mongo_ids
        )

        return assessment_logs

    @execution_time
    @retrieval(name="retrieve_assessment_logs_agent")
    async def execute(self, state: CyberSecurityState) -> CyberSecurityState:
        """Fetch assessment logs based on MongoDB IDs."""
        if not state.assessments:
            return state

        state.assessment_logs = await self.retrieve_assessment_logs.ainvoke(
            {
                "validate_user": state.should_validate_user(),
                "mongo_ids": state.mongo_ids,
            }
        )

        state.input_metadata.append(
            AIMessage(
                content=f"Assessments logs for {state.mongo_ids}: {state.assessment_logs}",
                id=datetime.now().timestamp(),
                additional_kwargs={"type": "assessment_logs"},
            )
        )
        return state
