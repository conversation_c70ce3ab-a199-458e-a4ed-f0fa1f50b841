from datetime import datetime
from typing import Any, List

from corelanggraph.agents import BaseAgent
from ddtrace.llmobs.decorators import retrieval
from langchain_core.messages import AIMessage
from langchain_core.tools import tool
from logger import logger

from cyber_security.services.document_processor import DocumentProcessor
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class RetrieveKnowledgeBaseAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="retrieve_knowledge_base_query_agent",
            description="Query the knowledge base for the given query.",
        )

    @tool
    async def query_knowledge_base(
        self, query: str
    ) -> tuple[List[str], List[dict[str, Any]]]:
        """
        Search the Cymulate Knowledge Base for documents related to a specified query.
        Returns a list of document contents and a list of references to the full documents.

        Args:
            query (str): The search term used to find relevant documents.

        Returns:
            tuple[List[str], List[dict[str, Any]]]: A tuple with:
                - List[str]: Document contents matching the query.
                - List[dict[str, Any]]: Metadata for each document, including:
                    - "name" (str): Document title.
                    - "url" (str): Document URL.

        Example:
            A query like "phishing simulation" might return:
            (
                ["Phishing simulation overview...", "Setup guide for simulations..."],
                [{"name": "Phishing Guide", "url": "https://cymulate.com/docs/phishing-guide"}]
            )
        """
        docs = await DocumentProcessor(type="docs360").get_docs(query)
        refs = [
            {"name": doc.metadata["name"], "url": doc.metadata["url"]}
            for doc in docs
            if "name" in doc.metadata and "url" in doc.metadata
        ]

        return [doc.page_content for doc in docs], refs

    @execution_time
    @retrieval(name="retrieve_knowledge_base_query_agent")
    async def execute(self, state: CyberSecurityState) -> CyberSecurityState:
        """Query the knowledge base for the given query."""
        user_query = state.user_input

        try:
            docs = await self.query_knowledge_base.ainvoke(
                {"client_type": state.client_type, "query": user_query}
            )

            message = AIMessage(
                content=f"Knowledge base result for '{user_query}': {docs}",
                id=datetime.now().timestamp(),
                additional_kwargs={
                    "type": "knowledge_base",
                    "message_type": "knowledge_base",
                    "document_names": [
                        doc.metadata.get("name")
                        for doc in docs
                        if doc.metadata.get("name")
                    ],
                },
            )

            state.input_metadata.append(message)
            state.docs = list(
                set(state.docs)
                | {name for name in message.additional_kwargs["document_names"]}
            )
        except Exception as e:
            logger.error(e)

        return state
