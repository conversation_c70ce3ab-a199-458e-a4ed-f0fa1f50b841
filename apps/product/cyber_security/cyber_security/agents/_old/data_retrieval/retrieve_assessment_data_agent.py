from datetime import datetime
from typing import Any, List, Optional

from corelanggraph.agents import BaseAgent
from ddtrace.llmobs.decorators import retrieval
from langchain_core.messages import AIMessage
from langchain_core.tools import tool
from logger import logger

from cyber_security.models.user_data import UserData
from cyber_security.services.assessment_manager import AssessmentManager
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class RetrieveAssessmentDataAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="retrieve_assessment_data_agent",
            description="Retrieves assessment data based on MongoDB IDs.",
        )

    @tool
    async def retrieve_assessment_data(
        self,
        validate_user: bool,
        mongo_ids: List[str],
        user_data: Optional[UserData] = None,
    ) -> tuple[list[dict[str, Any]], int, list[str]]:
        """Retrieve assessment data based on MongoDB IDs."""
        assessment_manager = AssessmentManager(validate_user=validate_user)
        assessments = await assessment_manager.get_assessments_data(
            mongo_ids=mongo_ids, user_data=user_data
        )
        assessments_count = len(
            await assessment_manager.get_assessments_data(
                mongo_ids=mongo_ids, user_data=user_data, ignore_validation=True
            )
        )
        assessment_ids = [assessment["attackID"] for assessment in assessments]

        return assessments, assessments_count, assessment_ids

    @execution_time
    @retrieval(name="retrieve_assessment_data_agent")
    async def execute(self, state: CyberSecurityState) -> CyberSecurityState:
        """Fetches assessment data based on MongoDB IDs."""
        if not state.mongo_ids:
            return state

        try:
            assessments, assessments_count, assessment_ids = (
                await self.retrieve_assessment_data.ainvoke(
                    {
                        "validate_user": state.should_validate_user(),
                        "mongo_ids": state.mongo_ids,
                        "user_data": state.user_data,
                    }
                )
            )

            if not assessments and assessments_count:
                state.assessments = []
                state.error_message = "Access Denied: You do not have permission to view this assessment. It is possible that the assessment has been deleted or you do not have the necessary permissions. Please ensure you have the correct permissions or contact your administrator for further assistance."
            elif assessments_count:
                state.assessments = (state.assessments or []) + assessments

                docs = [
                    {"assessmentID": assessmentID, "type": "assessment"}
                    for assessmentID in assessment_ids
                ]
                state.docs = state.docs + docs

                state.input_metadata.append(
                    AIMessage(
                        content=f"Assessments data for {state.mongo_ids}: {state.assessments}",
                        id=datetime.now().timestamp(),
                        additional_kwargs={"type": "assessment"},
                    )
                )
            else:
                state.input_metadata.append(
                    AIMessage(
                        content=f"No assessments found for {state.mongo_ids}",
                        id=datetime.now().timestamp(),
                        additional_kwargs={"type": "assessment"},
                    )
                )
        except Exception as e:
            logger.error(e)

        return state
