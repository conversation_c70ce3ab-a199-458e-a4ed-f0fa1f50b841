import asyncio
import json

from corelanggraph.agents import BaseAgent
from ddtrace.llmobs.decorators import retrieval
from elasticsearch import ApiError
from langchain_core.messages import BaseMessage, messages_from_dict
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from logger import logger

from cyber_security.infra.init import infra
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class RetrieveHistoryAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="retrieve_history_agent", description="Retrieves the user's history."
        )

    @staticmethod
    @tool
    async def retrieve_user_history(session_id: str) -> list[BaseMessage]:
        """Retrieve user history based on session ID."""
        try:
            result = await asyncio.to_thread(
                lambda: infra.elasticsearch_client.search(
                    index=infra.get_secret(
                        key="INDEX_CHAT_HISTORY", default="cy-ai-docs-chat-history"
                    ),
                    query={"term": {"session_id": {"value": session_id}}},
                    sort=[{"created_at": {"order": "asc"}}],
                    size=100,
                )
            )

            if result and len(result["hits"]["hits"]) > 0:
                items = [
                    json.loads(document["_source"]["history"])
                    for document in result["hits"]["hits"]
                ]
            else:
                items = []

            messages = messages_from_dict(items)

            for message in messages:
                message.id = message.additional_kwargs.get("id", message.id)

            return messages
        except ApiError as err:
            logger.error(f"Error connecting to Elasticsearch: {err}")
            raise err

    @execution_time
    @retrieval(name="retrieve_history_agent")
    async def execute(
        self, state: CyberSecurityState, config: RunnableConfig
    ) -> CyberSecurityState:
        """Fetches the user's history."""

        if "configurable" not in config or "session_id" not in config["configurable"]:
            raise ValueError(
                "Make sure that the config includes the following information: {'configurable': {'session_id': 'some_value'}}"
            )

        session_id = config["configurable"]["session_id"]
        logger.info(f"Fetching user history for session ID: {session_id}")

        try:
            messages: list[BaseMessage] = await self.retrieve_user_history.ainvoke(
                {"session_id": session_id}
            )
            state.messages = messages + state.messages

        except Exception as e:
            logger.error(f"Error retrieving dashboards data: {e}")

        return state
