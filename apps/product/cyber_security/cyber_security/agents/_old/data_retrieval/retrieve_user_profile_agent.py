import json
from datetime import datetime

from corelanggraph.agents import BaseAgent
from corelanggraph.app_builder.models.event import ClientTypeEnum
from cymredis.redis_client import RedisClient
from ddtrace.llmobs.decorators import retrieval
from langchain_core.messages import AIMessage
from langchain_core.tools import tool
from logger import logger

from cyber_security.infra.init import infra
from cyber_security.models.metadata_generator import <PERSON>ada<PERSON>, MetadataGenerator
from cyber_security.models.user_data import (
    Endpoints,
    Permissions,
    UserData,
    UserProfile,
)
from cyber_security.services.profile_manager import ProfileManager
from cyber_security.utils.glbl import UnauthorizedError, execution_time
from cyber_security.utils.user import merge_client_ids
from cyber_security.workflow.state import CyberSecurityState


class RetrieveUserProfileAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="retrieve_user_profile_agent",
            description="Fetches user data based on the user's cookies and caches it in Redis.",
        )

    @tool
    async def retrieve_user_profile(
        self, user_cookies: str, user_data: UserData
    ) -> tuple[UserData, dict]:
        """Retrieves user profile based on cookies."""

        try:
            endpoints = Endpoints(**infra.get_secret("endpoints"))
        except Exception:
            endpoints = user_data.endpoints

        user_data.endpoints = endpoints
        user_profile = await ProfileManager.get_profile_data(
            user_cookies, user_data.endpoints
        )
        user_data.client_ids = merge_client_ids(user_data, user_profile)

        if not hasattr(user_data, "permissions"):
            user_data.permissions = Permissions(env=["default"], modules=[])

        user_data.permissions.env = user_data.permissions.env or []

        if user_profile:
            user_data.permissions.env.extend(
                env
                for env in user_profile.get("envPermissions", [])
                if env not in user_data.permissions.env
            )

            default_env_id = "62cc302329b3b8cfe95d7f08"
            if (
                "default" in user_data.permissions.env
                and default_env_id not in user_data.permissions.env
            ):
                user_data.permissions.env.append(default_env_id)

            if "bas2" not in user_data.permissions.modules:
                user_data.permissions.modules.append("bas2")

        validated_modules = list(
            dict.fromkeys(
                module.lower().replace("aptwrapper", "apt")
                for module in user_data.permissions.modules
            )
        )

        user_data.permissions = Permissions.model_validate(
            {"env": user_data.permissions.env, "modules": validated_modules}
        )

        return user_data, user_profile

    @execution_time
    @retrieval(name="retrieve_user_profile_agent")
    async def execute(self, state: CyberSecurityState) -> CyberSecurityState:
        """Fetches user data based on the user's cookies and caches it in Redis."""
        if state.user_profile or not state.should_validate_user():
            return state

        if not state.user_cookies:
            logger.error("No user cookies provided.")
            state.input_metadata.append(
                AIMessage(
                    content="No user cookies provided.", id=datetime.now().timestamp()
                )
            )
            return state

        try:
            redis_client = RedisClient(
                redis_uri=infra.get_secret("websocket", secret_type="redis")
            )
            await redis_client.connect()

            user_id = state.user_data.id
            if not user_id:
                logger.error("No user ID found in user data.")
                state.input_metadata.append(
                    AIMessage(
                        content="No user ID found in user data.",
                        id=datetime.now().timestamp(),
                    )
                )
                return state

            cache_key = f"chatbot_user_{user_id}_{state.user_data.env}"

            cached_data = await redis_client.client.get(cache_key)
            if cached_data:
                logger.info(
                    f"User data retrieved from Redis cache for key: {cache_key}"
                )
                cached_data = json.loads(cached_data)
                state.user_data = UserData.model_validate(cached_data.get("user_data"))
                state.user_profile = UserProfile.model_validate(
                    cached_data.get("user_profile")
                )
            else:
                try:
                    user_data, user_profile = await self.retrieve_user_profile.ainvoke(
                        {
                            "user_cookies": state.user_cookies,
                            "user_data": state.user_data,
                        }
                    )
                except UnauthorizedError as e:
                    logger.error(f"Unauthorized error: {e}")
                    state.error_message = "Unauthorized error. Please try again later."
                    return state

                if user_data:
                    await redis_client.client.set(
                        cache_key,
                        json.dumps(
                            {
                                "user_data": user_data.model_dump(),
                                "user_profile": user_profile,
                            }
                        ),
                        ex=3600,
                        nx=True,
                    )
                    state.user_profile = UserProfile.model_validate(user_profile)
                    state.user_data = UserData.model_validate(user_data)
                    logger.info(f"User data cached in Redis with key: {cache_key}")

            _metadata: Metadata = MetadataGenerator.generate_metadata(
                event=state.event_data,
                event_type=ClientTypeEnum.Websocket,
                user_profile=state.user_profile,
                user_data=state.user_data,
            )

            event_data = state.event_data
            state.metadata = _metadata
            event_data.metadata = state.metadata
            state.event_data = event_data
            state.input_metadata.append(
                AIMessage(
                    content=f"User Cookies: {state.user_cookies}",
                    id=datetime.now().timestamp(),
                    additional_kwargs={"type": "user_cookies"},
                )
            )
            state.input_metadata.append(
                AIMessage(
                    content=f"Endpoints: {state.user_data.endpoints.model_dump()}",
                    id=datetime.now().timestamp(),
                    additional_kwargs={"type": "endpoints"},
                )
            )
            state.input_metadata.append(
                AIMessage(
                    content=f"User Info: {state.user_data.model_dump()}",
                    id=datetime.now().timestamp(),
                    additional_kwargs={"type": "User Info"},
                )
            )

        except Exception as e:
            logger.error(f"Error retrieving user data: {e}")
            state.error_message = "Error retrieving user data. Please try again later."

        return state
