from datetime import datetime
from typing import Any

from corelanggraph.agents import BaseAgent
from ddtrace.llmobs.decorators import retrieval
from langchain_core.messages import AIMessage
from langchain_core.tools import tool

from cyber_security.services import AssessmentManager
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class RetrieveAgentMetricsAgent(BaseAgent):
    def __init__(self) -> None:
        super().__init__(
            name="retrieve_agent_metrics_agent",
            description="Retrieves agent metrics based on the assessments.",
        )

    @tool
    async def retrieve_agent_metrics(
        self, validate_user: bool, assessments: list[dict[str, Any]]
    ) -> list[dict[str, Any]]:
        """Retrieve agent metrics based on assessments."""

        assessment_manager = AssessmentManager(validate_user=validate_user)
        agent_metrics = await assessment_manager.get_agent_metrics_data(assessments)

        return agent_metrics

    @execution_time
    @retrieval(name="retrieve_agent_metrics_agent")
    async def execute(self, state: CyberSecurityState) -> CyberSecurityState:
        """Fetches agent metrics based on the assessments."""
        if not state.assessments:
            return state

        agent_metrics = await self.retrieve_agent_metrics.ainvoke(
            {
                "validate_user": state.should_validate_user(),
                "assessments": state.assessments,
            }
        )

        state.agent_metrics = (state.agent_metrics or []) + agent_metrics

        state.input_metadata.append(
            AIMessage(
                content=f"Agent Metrics: {state.agent_metrics}",
                id=datetime.now().timestamp(),
                additional_kwargs={"type": "agent_metrics"},
            )
        )

        return state
