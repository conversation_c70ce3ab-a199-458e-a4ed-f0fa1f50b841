"""Advanced comprehensive web search engine for cybersecurity intelligence."""

import asyncio
import hashlib
import re
from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import quote_plus, urljoin, urlparse

import aiohttp
from bs4 import BeautifulSoup
from langchain_core.tools import tool
from logger import logger

# Import professional cybersecurity analytics engine
from ..utils import CybersecurityIntelligenceAnalytics


@tool
async def advanced_cyber_search(query: str, search_depth: str = "comprehensive") -> Dict[str, Any]:
    """
    Advanced cybersecurity research engine that mimics comprehensive Task tool methodology.
    Performs multi-source intelligence gathering with cross-referencing and validation.

    Args:
        query: Any cybersecurity research query (vulnerabilities, threats, techniques, tools, etc.)
        search_depth: "basic", "comprehensive", or "deep" (default: comprehensive)

    Returns:
        Professional cybersecurity intelligence with cross-referenced findings and actionable insights
    """
    try:
        # Initialize comprehensive research engine
        research_engine = CyberSecurityResearchEngine()

        # Perform Task-tool-like comprehensive research
        results = await research_engine.conduct_research(query, search_depth)

        return {
            "query": query,
            "search_depth": search_depth,
            "research_methodology": results.get("methodology", ""),
            "findings": results.get("findings", []),
            "cross_referenced_data": results.get("cross_references", []),
            "specific_identifiers": results.get("identifiers", []),
            "technical_details": results.get("technical_details", {}),
            "total_sources": results.get("total_sources", 0),
            "search_strategies": results.get("strategies", []),
            "professional_analysis": results.get("analysis", {}),
            "key_discoveries": results.get("discoveries", []),
            "threat_intelligence": results.get("threat_intel", {}),
            "confidence_level": results.get("confidence", "Medium"),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Advanced cyber research failed: {str(e)}")
        return {
            "query": query,
            "error": f"Research engine error: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }


class CyberSecurityResearchEngine:
    """High-end cybersecurity search engine with multi-source intelligence gathering."""

    def __init__(self):
        self.session = None
        self.intelligence_analytics = CybersecurityIntelligenceAnalytics()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        # Cybersecurity-focused search sources
        self.sources = {
            "news": [
                {"name": "Google News", "url": "https://news.google.com/rss/search?q={query}&hl=en-US&gl=US&ceid=US:en"},
                {"name": "Security Week", "url": "https://www.securityweek.com/search/?q={query}"},
                {"name": "Threat Post", "url": "https://threatpost.com/?s={query}"},
                {"name": "Bleeping Computer", "url": "https://www.bleepingcomputer.com/search/?q={query}"},
                {"name": "The Hacker News", "url": "https://thehackernews.com/search?q={query}"}
            ],
            "databases": [
                {"name": "CVE Details", "url": "https://www.cvedetails.com/google-search-results.php?q={query}"},
                {"name": "CVE Search", "url": "https://cve.mitre.org/cgi-bin/cvekey.cgi?keyword={query}"},
                {"name": "Vulnerability DB", "url": "https://vuldb.com/?search={query}"},
                {"name": "Exploit DB", "url": "https://www.exploit-db.com/search?q={query}"},
                {"name": "NVD Search", "url": "https://nvd.nist.gov/vuln/search/results?form_type=Basic&results_type=overview&query={query}"}
            ],
            "threat_intel": [
                {"name": "MITRE ATT&CK", "url": "https://attack.mitre.org/search/?q={query}"},
                {"name": "AlienVault OTX", "url": "https://otx.alienvault.com/api/v1/search/pulses?q={query}&limit=10"},
                {"name": "VirusTotal", "url": "https://www.virustotal.com/gui/search/{query}"}
            ],
            "technical": [
                {"name": "GitHub Security", "url": "https://api.github.com/search/repositories?q={query}+security&sort=updated"},
                {"name": "Security Stack Exchange", "url": "https://security.stackexchange.com/search?q={query}"},
                {"name": "Reddit NetSec", "url": "https://www.reddit.com/r/netsec/search.json?q={query}&limit=10&sort=new"},
                {"name": "CVE Trends", "url": "https://cvetrends.com/search?q={query}"},
                {"name": "Packet Storm Security", "url": "https://packetstormsecurity.com/search/?q={query}"}
            ]
        }

    async def conduct_research(self, query: str, depth: str = "comprehensive") -> Dict[str, Any]:
        """Conduct comprehensive cybersecurity research using Task-tool methodology."""
        async with aiohttp.ClientSession(headers=self.headers, timeout=aiohttp.ClientTimeout(total=45)) as session:
            self.session = session

            # Step 1: Research Planning (like Task tool)
            research_plan = self._create_research_plan(query)
            
            # Check for ambiguous query that needs clarification
            if research_plan.get("type") == "ambiguous_query":
                return {
                    "query": query,
                    "query_analysis": "AMBIGUOUS QUERY DETECTED",
                    "clarification_needed": research_plan.get("clarification_needed"),
                    "ambiguous_term": research_plan.get("ambiguous_term"),
                    "suggestion": f"Please be more specific in your query. {research_plan.get('clarification_needed')}",
                    "confidence_level": "Low - Query Requires Clarification"
                }

            # Step 2: Multi-Strategy Search Execution
            search_results = await self._execute_research_strategies(query, research_plan, depth)

            # Step 3: Cross-Reference and Validate Findings
            validated_findings = await self._cross_reference_findings(search_results, query)

            # Step 4: Extract Specific Identifiers (CVEs, techniques, etc.)
            specific_identifiers = self._extract_specific_identifiers(validated_findings)

            # Step 5: Generate Professional Intelligence Report
            intelligence_report = self._generate_intelligence_report(validated_findings, query, research_plan)

            return {
                "methodology": f"Comprehensive {len(research_plan.get('strategies', []))} strategy research approach",
                "findings": validated_findings,
                "cross_references": self._generate_cross_references(validated_findings),
                "identifiers": specific_identifiers,
                "technical_details": intelligence_report.get("technical_details", {}),
                "total_sources": len([r for r in search_results if r.get("results")]),
                "strategies": research_plan.get("strategies", []),
                "analysis": intelligence_report.get("analysis", {}),
                "discoveries": intelligence_report.get("key_discoveries", []),
                "threat_intel": intelligence_report.get("threat_intelligence", {}),
                "confidence": intelligence_report.get("confidence_level", "Medium")
            }

    def _analyze_query(self, query: str) -> Dict[str, Any]:
        """Analyze query to determine optimal search strategy."""
        query_lower = query.lower()
        
        # Ambiguity detection is now handled by the LLM via system prompt

        # Detect query type with enhanced specificity
        if re.search(r'cve-\d{4}-\d+', query_lower):
            return {"type": "cve_lookup", "focus": "databases", "categories": ["databases", "news"]}
        elif "win32k" in query_lower and any(term in query_lower for term in ["vulnerability", "cve", "elevation", "privilege"]):
            return {"type": "win32k_research", "focus": "comprehensive", "categories": ["databases", "news", "threat_intel"]}
        elif "macos" in query_lower or "mac os" in query_lower:
            return {"type": "macos_specific", "focus": "comprehensive", "categories": ["databases", "news", "threat_intel"]}
        elif any(term in query_lower for term in ["apt", "threat actor", "campaign"]):
            return {"type": "threat_actor", "focus": "threat_intel", "categories": ["threat_intel", "news"]}
        elif any(term in query_lower for term in ["malware", "ransomware", "trojan"]):
            return {"type": "malware", "focus": "all", "categories": ["threat_intel", "news", "databases"]}
        elif any(term in query_lower for term in ["exploit", "poc", "proof of concept"]):
            return {"type": "exploit", "focus": "technical", "categories": ["technical", "databases"]}
        elif any(term in query_lower for term in ["latest", "recent", "new"]):
            return {"type": "current_threat", "focus": "news", "categories": ["news", "threat_intel"]}
        else:
            return {"type": "general", "focus": "all", "categories": list(self.sources.keys())}
    
    def _has_specific_context(self, query_lower: str, ambiguous_term: str) -> bool:
        """Check if ambiguous term has specific context that makes it clear."""
        context_indicators = {
            "mac": ["macos", "mac os", "apple", "safari", "darwin", "address"],
            "windows": ["microsoft", "win32", "kernel", "registry", "gui"],
            "linux": ["ubuntu", "debian", "kernel", "systemd", "embedded"],
            "mobile": ["ios", "android", "device", "smartphone"],
            "browser": ["chrome", "firefox", "safari", "webkit"],
            "database": ["mysql", "postgres", "sqlite", "software"],
            "server": ["apache", "nginx", "web", "hardware"]
        }
        
        if ambiguous_term in context_indicators:
            return any(indicator in query_lower for indicator in context_indicators[ambiguous_term])
        
        return False

    def _detect_ambiguous_terms(self, query_lower: str) -> Optional[Dict[str, Any]]:
        """Disambiguation is now handled by the LLM via system prompt."""
        # No longer needed - the system prompt handles ambiguity detection
        return None
    

    
    def _has_sufficient_context(self, term: str, query: str, meanings: List[str]) -> bool:
        """Use pure reasoning patterns to check if query has sufficient context."""
        
        # NO hardcoded lists - only reasoning patterns
        
        # Reasoning pattern 1: Query length and complexity
        # Longer, more complex queries usually have more context
        words = query.split()
        if len(words) >= 4 and len(query) >= 20:
            return True
            
        # Reasoning pattern 2: Presence of technical patterns
        # Version numbers, technical acronyms, file extensions indicate specificity
        if re.search(r'\d+\.\d+|v\d+|\.[a-z]{2,4}|CVE-\d{4}-\d+', query, re.IGNORECASE):
            return True
            
        # Reasoning pattern 3: Capitalized technical terms
        # Proper nouns and acronyms suggest specific context
        capitalized_words = [word for word in words if word[0].isupper() and len(word) > 2]
        if len(capitalized_words) >= 2:
            return True
            
        # Reasoning pattern 4: Query contains qualifying adjectives
        # Use pattern matching for time-related or specificity words
        if re.search(r'\b(latest|recent|new|specific|particular|certain)\b', query.lower()):
            return True
            
        # Reasoning pattern 5: Multiple technical-sounding nouns suggest specificity
        # Count words that aren't common function words
        function_word_pattern = r'\b(what|where|when|how|why|were|was|are|is|the|and|or|but|a|an|in|on|at|to|for|of|with)\b'
        non_function_words = [word for word in words if len(word) > 3 and not re.match(function_word_pattern, word.lower())]
        if len(non_function_words) >= 3:
            return True
            
        return False
    
    def _generate_dynamic_clarification(self, term: str, meanings: List[str]) -> str:
        """Generate a helpful clarification prompt based on the term and its meanings."""
        
        # Generate clarification dynamically based on the meanings
        numbered_meanings = [f"{i+1}. **{meaning}**" for i, meaning in enumerate(meanings)]
        
        return (
            f"Your query about '{term}' could refer to multiple cybersecurity contexts:\n\n"
            + "\n".join(numbered_meanings) + "\n\n"
            "Could you please specify which context you're interested in so I can provide more targeted results?"
        )

    async def _search_source(self, source: Dict[str, str], query: str) -> Dict[str, Any]:
        """Search a specific source and return structured results."""
        try:
            url = source["url"].format(query=quote_plus(query))
            parsed_url = urlparse(url)
            hostname = parsed_url.hostname if parsed_url.hostname else ""

            # Handle different source types with secure URL parsing
            # RSS/Feed detection based on path or known RSS domains
            if parsed_url.path and ("rss" in parsed_url.path.lower() or "feed" in parsed_url.path.lower()):
                return await self._search_rss(source, url, query)
            elif hostname.lower() == "api.github.com":
                return await self._search_github_api(source, url, query)
            elif (hostname == "reddit.com" or hostname.endswith(".reddit.com")) and ".json" in url:
                return await self._search_reddit_api(source, url, query)
            elif hostname == "otx.alienvault.com":
                return await self._search_otx_api(source, url, query)
            else:
                return await self._search_html(source, url, query)

        except Exception as e:
            logger.debug(f"Search failed for {source['name']}: {str(e)}")
            return {"source": source["name"], "results": [], "error": str(e)}
    
    async def _fetch_cve_description(self, cve_id: str) -> str:
        """Fetch actual CVE content from official databases - NO FALLBACKS."""
        sources_to_try = [
            {
                "name": "NVD",
                "url": f"https://nvd.nist.gov/vuln/detail/{cve_id}",
                "selectors": [
                    'p[data-testid="vuln-description"]',
                    '.vuln-description p',
                    '#vulnDetailTableView p', 
                    '.vulnerability-summary p',
                    '.description-container p',
                    'div[class*="description"] p'
                ]
            },
            {
                "name": "MITRE",
                "url": f"https://cve.mitre.org/cgi-bin/cvename.cgi?name={cve_id}",
                "method": "table_parse"
            },
            {
                "name": "CVEdetails",
                "url": f"https://www.cvedetails.com/cve/{cve_id}/",
                "selectors": [
                    '.cvedetailssummary',
                    '.summary p',
                    'div[class*="summary"]',
                    'td[class*="cvesummarylong"]'
                ]
            }
        ]
        
        for source in sources_to_try:
            try:
                async with self.session.get(source["url"]) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        
                        if source["name"] == "MITRE":
                            # Special MITRE table parsing
                            desc_cell = soup.find('td', string=re.compile(r'Description', re.IGNORECASE))
                            if desc_cell:
                                next_td = desc_cell.find_next_sibling('td')
                                if next_td:
                                    full_desc = next_td.get_text(strip=True)
                                    logger.info(f"✅ Fetched {cve_id} description from MITRE: {len(full_desc)} chars")
                                    return full_desc
                            
                            # Alternative MITRE parsing
                            table = soup.find('table')
                            if table:
                                all_text = table.get_text()
                                # Extract description section
                                desc_match = re.search(rf'Description\s*([^<]+(?:vulnerability|allows|enables|could|can|may)[^<]+)', all_text, re.IGNORECASE | re.DOTALL)
                                if desc_match:
                                    desc = desc_match.group(1).strip()
                                    logger.info(f"✅ Extracted {cve_id} from MITRE table: {len(desc)} chars")
                                    return desc
                        else:
                            # Try all selectors for other sources
                            for selector in source["selectors"]:
                                desc_elem = soup.select_one(selector)
                                if desc_elem:
                                    full_desc = desc_elem.get_text(strip=True)
                                    if len(full_desc) > 20:  # Must have substantial content
                                        logger.info(f"✅ Fetched {cve_id} from {source['name']} using {selector}: {len(full_desc)} chars")
                                        return full_desc
                            
                            # If selectors fail, search for any description content
                            desc_patterns = [
                                rf'{cve_id}[:\s]*([^<]+(?:vulnerability|allows|enables|could|can|may|exploit|flaw|weakness)[^<]+)',
                                r'(?:Description|Summary)[:\s]*([^<]+(?:vulnerability|allows|enables|could|can|may)[^<]+)',
                            ]
                            
                            page_text = soup.get_text()
                            for pattern in desc_patterns:
                                match = re.search(pattern, page_text, re.IGNORECASE | re.DOTALL)
                                if match:
                                    desc = match.group(1).strip()
                                    if len(desc) > 50:
                                        logger.info(f"✅ Pattern-extracted {cve_id} from {source['name']}: {len(desc)} chars")
                                        return desc
                    else:
                        logger.warning(f"❌ HTTP {response.status} for {source['name']}: {source['url']}")
                        
            except Exception as e:
                logger.warning(f"❌ Failed to fetch from {source['name']}: {e}")
                continue
        
        # Only if ALL sources fail
        logger.error(f"❌ FAILED to fetch {cve_id} from ALL sources - returning error")
        return f"ERROR: Unable to fetch {cve_id} description from any CVE database source"

    async def _search_rss(self, source: Dict[str, str], url: str, query: str) -> Dict[str, Any]:
        """Search RSS/XML feeds."""
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    content = await response.text()
                    soup = BeautifulSoup(content, 'xml')

                    items = soup.find_all('item')[:10]
                    results = []

                    for item in items:
                        title = item.find('title')
                        link = item.find('link')
                        description = item.find('description')
                        pub_date = item.find('pubDate')

                        if title and link:
                            results.append({
                                "title": title.get_text(strip=True),
                                "url": link.get_text(strip=True),
                                "snippet": description.get_text(strip=True)[:300] if description else "",
                                "date": pub_date.get_text(strip=True) if pub_date else "",
                                "relevance": self._calculate_relevance(title.get_text(), query)
                            })

                    return {"source": source["name"], "results": results, "type": "rss"}

        except Exception as e:
            return {"source": source["name"], "results": [], "error": str(e)}

    async def _search_html(self, source: Dict[str, str], url: str, query: str) -> Dict[str, Any]:
        """Search HTML pages and extract results with CVE-specific parsing."""
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    results = []

                    # CVE-specific parsing for CVE databases
                    if "cve" in source["name"].lower():
                        results = self._parse_cve_results(soup, query, source["name"])
                    else:
                        # Generic HTML parsing for other sources
                        results = self._parse_generic_html(soup, query, url)

                    return {"source": source["name"], "results": results, "type": "html"}

        except Exception as e:
            return {"source": source["name"], "results": [], "error": str(e)}

    def _parse_cve_results(self, soup: BeautifulSoup, query: str, source_name: str) -> List[Dict[str, Any]]:
        """Parse CVE database results to extract CVE numbers and actual descriptions."""
        results = []

        # Look for CVE patterns in text
        text_content = soup.get_text()
        cve_matches = re.findall(r'CVE-\d{4}-\d+', text_content)

        # Try to find CVE table rows or entries with descriptions
        cve_elements = soup.find_all(['tr', 'div', 'li', 'td', 'p'], string=re.compile(r'CVE-\d{4}-\d+'))

        for element in cve_elements[:10]:
            cve_text = element.get_text(strip=True)
            cve_match = re.search(r'CVE-\d{4}-\d+', cve_text)

            if cve_match:
                cve_id = cve_match.group()
                
                # Extract actual vulnerability description
                description = self._extract_cve_description(element, cve_id, soup)

                # Try to find associated link
                link_elem = element.find('a') or element.find_parent().find('a') if element.find_parent() else None
                link = link_elem.get('href', '') if link_elem else f"https://cve.mitre.org/cgi-bin/cvename.cgi?name={cve_id}"

                results.append({
                    "title": f"{cve_id}: {self._get_cve_title(description)}",
                    "url": link,
                    "snippet": description,
                    "cve_id": cve_id,
                    "relevance": self._calculate_relevance(cve_text, query),
                    "type": "cve"
                })

        # If no specific CVE entries found, add any CVE IDs found in text with extracted descriptions
        for cve_id in set(cve_matches[:5]):
            if not any(r.get("cve_id") == cve_id for r in results):
                # Try to extract description from surrounding context
                description = self._extract_cve_description_from_context(text_content, cve_id)
                
                results.append({
                    "title": f"{cve_id}: {self._get_cve_title(description)}",
                    "url": f"https://cve.mitre.org/cgi-bin/cvename.cgi?name={cve_id}",
                    "snippet": description,
                    "cve_id": cve_id,
                    "relevance": 0.8,
                    "type": "cve"
                })

        return results
    
    def _extract_cve_description(self, element, cve_id: str, soup: BeautifulSoup) -> str:
        """Extract detailed CVE description from various database formats."""
        description = ""
        
        # Method 1: Look for description in next sibling elements (more comprehensive)
        next_elem = element.find_next_sibling()
        if next_elem:
            desc_text = next_elem.get_text(strip=True)
            if len(desc_text) > 50 and not re.search(r'CVE-\d{4}-\d+', desc_text):
                description = desc_text[:500]  # Increased from 300 to 500 chars
        
        # Method 2: Look for description in parent's text (more detailed)
        if not description:
            parent = element.find_parent(['div', 'td', 'li', 'tr'])
            if parent:
                parent_text = parent.get_text(strip=True)
                # Remove the CVE ID and extract remaining text
                desc_parts = parent_text.split(cve_id, 1)
                if len(desc_parts) > 1:
                    desc_candidate = desc_parts[1].strip()
                    if len(desc_candidate) > 30:
                        description = desc_candidate[:500]  # More detailed
        
        # Method 3: Look for extended description patterns
        if not description:
            # Search for more comprehensive description patterns
            context_text = element.get_text() + " " + (element.find_parent().get_text() if element.find_parent() else "")
            
            # Cybersecurity-specific technical patterns
            desc_patterns = [
                r'(?:buffer overflow|stack overflow|heap overflow|integer overflow)[^.]+\.',
                r'(?:remote code execution|RCE|arbitrary code execution)[^.]+\.',
                r'(?:elevation of privilege|privilege escalation|EoP|LPE)[^.]+\.',
                r'(?:use after free|UAF|double free|memory corruption)[^.]+\.',
                r'(?:SQL injection|SQLi|code injection|command injection)[^.]+\.',
                r'(?:cross-site scripting|XSS|cross-site request forgery|CSRF)[^.]+\.',
                r'(?:denial of service|DoS|DDoS|service disruption)[^.]+\.',
                r'(?:information disclosure|data leak|sensitive information)[^.]+\.',
                r'(?:authentication bypass|authorization bypass|access control)[^.]+\.',
                r'(?:directory traversal|path traversal|file inclusion)[^.]+\.',
                r'(?:race condition|time-of-check|TOCTOU)[^.]+\.',
                r'(?:format string|format specifier|string formatting)[^.]+\.',
                r'(?:kernel|driver|system service|Windows service)[^.]+(?:vulnerability|flaw)[^.]+\.',
                r'(?:Win32k|kernel32|ntdll|user32|gdi32)[^.]+(?:vulnerability|exploit)[^.]+\.'
            ]
            
            for pattern in desc_patterns:
                matches = re.findall(pattern, context_text, re.IGNORECASE | re.DOTALL)
                if matches:
                    # Take the longest match for more detail
                    best_match = max(matches, key=len)
                    description = best_match[:500]
                    break
        
        # Method 4: Look for detailed technical information
        if not description:
            # Search for detailed technical context around CVE
            extended_context = ""
            for elem in [element, element.find_parent(), element.find_next_sibling()]:
                if elem:
                    extended_context += " " + elem.get_text()
            
            # Extract technical details about the vulnerability
            tech_patterns = [
                r'(?:affects?|impacts?)[^.]+(?:\.[^.]+)*\.',
                r'(?:CVSS|score|severity)[^.]+\.',
                r'(?:Windows|Linux|macOS|iOS|Android)[^.]+(?:version|system)[^.]+\.',
                r'(?:component|module|service|kernel)[^.]+(?:vulnerability|flaw)[^.]+\.'
            ]
            
            for pattern in tech_patterns:
                matches = re.findall(pattern, extended_context, re.IGNORECASE)
                if matches:
                    description = max(matches, key=len)[:500]
                    break
        
        # NO FALLBACK - Description must be fetched from actual CVE URL
        if not description:
            description = f"CVE description extraction incomplete - will be fetched from official CVE database URL"
        
        return description.strip()
    
    def _extract_cve_description_from_context(self, text: str, cve_id: str) -> str:
        """Extract detailed CVE description from surrounding text context."""
        # Find the CVE ID in text
        cve_index = text.find(cve_id)
        if cve_index == -1:
            return "Security vulnerability (detailed description not available)"
        
        # Extract larger context around CVE for more detail
        start = max(0, cve_index - 500)  # Increased context window
        end = min(len(text), cve_index + 1000)  # Larger forward context
        context = text[start:end]
        
        # Cybersecurity-specific technical patterns only
        desc_patterns = [
            r'(?:buffer overflow|stack overflow|heap overflow|integer overflow)[^.]+\.',
            r'(?:remote code execution|RCE|arbitrary code execution)[^.]+\.',
            r'(?:elevation of privilege|privilege escalation|EoP|LPE)[^.]+\.',
            r'(?:use after free|UAF|double free|memory corruption)[^.]+\.',
            r'(?:SQL injection|SQLi|code injection|command injection)[^.]+\.',
            r'(?:cross-site scripting|XSS|cross-site request forgery|CSRF)[^.]+\.',
            r'(?:denial of service|DoS|DDoS|service disruption)[^.]+\.',
            r'(?:information disclosure|data leak|sensitive information)[^.]+\.',
            r'(?:authentication bypass|authorization bypass|access control)[^.]+\.',
            r'(?:directory traversal|path traversal|file inclusion)[^.]+\.',
            r'(?:race condition|time-of-check|TOCTOU)[^.]+\.',
            r'(?:format string|format specifier|string formatting)[^.]+\.',
            r'(?:kernel|driver|system service|Windows service)[^.]+(?:vulnerability|flaw)[^.]+\.',
            r'(?:Win32k|kernel32|ntdll|user32|gdi32)[^.]+(?:vulnerability|exploit)[^.]+\.'
        ]
        
        for pattern in desc_patterns:
            matches = re.findall(pattern, context, re.IGNORECASE | re.DOTALL)
            if matches:
                # Take the longest, most detailed match
                best_match = max(matches, key=len)
                return best_match[:500]  # More detailed output
        
        # Extract multiple sentences after CVE ID for comprehensive description
        after_cve = context[context.find(cve_id) + len(cve_id):].strip()
        
        # Try to get 2-3 sentences for more detail
        sentences = re.findall(r'[^.!?]+[.!?]', after_cve)
        if sentences:
            detailed_desc = " ".join(sentences[:3])  # Up to 3 sentences
            return detailed_desc[:500] if len(detailed_desc) <= 500 else detailed_desc[:500] + "..."
        
        # NO FALLBACK - Must fetch actual content from CVE URL
        return f"CVE {cve_id} - Unable to extract description from current context, fetching from official CVE database"
    
    def _get_cve_title(self, description: str) -> str:
        """Generate a concise title from CVE description."""
        # Extract key vulnerability type
        if "elevation of privilege" in description.lower():
            return "Elevation of Privilege Vulnerability"
        elif "remote code execution" in description.lower():
            return "Remote Code Execution Vulnerability"
        elif "denial of service" in description.lower():
            return "Denial of Service Vulnerability"
        elif "information disclosure" in description.lower():
            return "Information Disclosure Vulnerability"
        elif "buffer overflow" in description.lower():
            return "Buffer Overflow Vulnerability"
        elif "sql injection" in description.lower():
            return "SQL Injection Vulnerability"
        elif "cross-site scripting" in description.lower() or "xss" in description.lower():
            return "Cross-Site Scripting (XSS) Vulnerability"
        else:
            # Extract first few words as title
            words = description.split()[:5]
            return " ".join(words) + "..."

    def _parse_generic_html(self, soup: BeautifulSoup, query: str, base_url: str) -> List[Dict[str, Any]]:
        """Parse generic HTML results."""
        results = []

        # Try multiple selectors for different sites
        selectors = [
            'div[class*="result"]',
            'div[class*="search"]',
            'article',
            'div[class*="post"]',
            'li[class*="result"]'
        ]

        for selector in selectors:
            elements = soup.select(selector)[:10]
            if elements:
                break

        for element in elements:
            title_elem = element.find(['h2', 'h3', 'h4', 'a'])
            link_elem = element.find('a', href=True)
            snippet_elem = element.find(['p', 'div', 'span'])

            if title_elem and link_elem:
                title = title_elem.get_text(strip=True)
                link = urljoin(base_url, link_elem['href'])
                snippet = snippet_elem.get_text(strip=True)[:300] if snippet_elem else ""

                if title and len(title) > 10:  # Filter out short/irrelevant titles
                    results.append({
                        "title": title,
                        "url": link,
                        "snippet": snippet,
                        "relevance": self._calculate_relevance(title + " " + snippet, query)
                    })

        return results

    async def _search_github_api(self, source: Dict[str, str], url: str, query: str) -> Dict[str, Any]:
        """Search GitHub API for security repositories."""
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []

                    for repo in data.get('items', [])[:10]:
                        results.append({
                            "title": f"GitHub: {repo.get('full_name', '')}",
                            "url": repo.get('html_url', ''),
                            "snippet": repo.get('description', '')[:300],
                            "stars": repo.get('stargazers_count', 0),
                            "language": repo.get('language', ''),
                            "updated": repo.get('updated_at', ''),
                            "relevance": self._calculate_relevance(repo.get('name', '') + " " + repo.get('description', ''), query)
                        })

                    return {"source": source["name"], "results": results, "type": "github"}

        except Exception as e:
            return {"source": source["name"], "results": [], "error": str(e)}

    async def _search_reddit_api(self, source: Dict[str, str], url: str, query: str) -> Dict[str, Any]:
        """Search Reddit API for security discussions."""
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []

                    for post in data.get('data', {}).get('children', [])[:10]:
                        post_data = post.get('data', {})
                        results.append({
                            "title": post_data.get('title', ''),
                            "url": f"https://reddit.com{post_data.get('permalink', '')}",
                            "snippet": post_data.get('selftext', '')[:300],
                            "score": post_data.get('score', 0),
                            "comments": post_data.get('num_comments', 0),
                            "subreddit": post_data.get('subreddit', ''),
                            "relevance": self._calculate_relevance(post_data.get('title', ''), query)
                        })

                    return {"source": source["name"], "results": results, "type": "reddit"}

        except Exception as e:
            return {"source": source["name"], "results": [], "error": str(e)}

    async def _search_otx_api(self, source: Dict[str, str], url: str, query: str) -> Dict[str, Any]:
        """Search AlienVault OTX API for threat intelligence."""
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    results = []

                    for pulse in data.get('results', [])[:10]:
                        results.append({
                            "title": f"Threat Intel: {pulse.get('name', '')}",
                            "url": f"https://otx.alienvault.com/pulse/{pulse.get('id', '')}",
                            "snippet": pulse.get('description', '')[:300],
                            "author": pulse.get('author_name', ''),
                            "created": pulse.get('created', ''),
                            "tags": pulse.get('tags', []),
                            "relevance": self._calculate_relevance(pulse.get('name', '') + " " + pulse.get('description', ''), query)
                        })

                    return {"source": source["name"], "results": results, "type": "threat_intel"}

        except Exception as e:
            return {"source": source["name"], "results": [], "error": str(e)}

    def _calculate_relevance(self, text: str, query: str) -> float:
        """Calculate relevance score between text and query."""
        if not text or not query:
            return 0.0

        text_lower = text.lower()
        query_terms = query.lower().split()

        score = 0.0
        for term in query_terms:
            if term in text_lower:
                # Exact match
                score += 1.0
                # Bonus for multiple occurrences
                score += text_lower.count(term) * 0.1

        # Normalize by number of query terms
        return min(score / len(query_terms), 1.0) if query_terms else 0.0

    def _process_results(self, raw_results: List, query: str) -> List[Dict[str, Any]]:
        """Process and deduplicate search results."""
        all_results = []
        seen_urls = set()

        for result_set in raw_results:
            if isinstance(result_set, dict) and "results" in result_set:
                for result in result_set["results"]:
                    url = result.get("url", "")
                    if url and url not in seen_urls:
                        seen_urls.add(url)
                        result["source_name"] = result_set.get("source", "Unknown")
                        result["result_type"] = result_set.get("type", "unknown")
                        all_results.append(result)

        # Sort by relevance score
        all_results.sort(key=lambda x: x.get("relevance", 0), reverse=True)

        return all_results[:20]  # Top 20 results

    def _generate_analysis(self, results: List[Dict[str, Any]], query: str, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """Generate professional cybersecurity analysis of results."""
        if not results:
            return {"summary": "No results found for the specified query."}

        # Analyze result types and sources
        source_distribution = {}
        type_distribution = {}

        for result in results:
            source = result.get("source_name", "Unknown")
            result_type = result.get("result_type", "unknown")

            source_distribution[source] = source_distribution.get(source, 0) + 1
            type_distribution[result_type] = type_distribution.get(result_type, 0) + 1

        return {
            "query_type": strategy.get("type", "general"),
            "total_results": len(results),
            "source_coverage": list(source_distribution.keys()),
            "content_types": list(type_distribution.keys()),
            "search_quality": "High" if len(results) > 10 else "Medium" if len(results) > 5 else "Low",
            "intelligence_confidence": "High" if len(source_distribution) >= 5 else "Medium"
        }

    def _extract_key_findings(self, results: List[Dict[str, Any]]) -> List[str]:
        """Extract key findings from search results."""
        findings = []

        # Analyze top results for key information
        top_results = results[:5]

        for result in top_results:
            title = result.get("title", "")
            snippet = result.get("snippet", "")

            # Extract CVE numbers
            cve_matches = re.findall(r'CVE-\d{4}-\d+', title + " " + snippet, re.IGNORECASE)
            if cve_matches:
                findings.append(f"Identified CVE: {', '.join(set(cve_matches))}")

            # Extract severity keywords
            severity_keywords = ["critical", "high", "severe", "dangerous", "exploit", "zero-day"]
            for keyword in severity_keywords:
                if keyword in (title + " " + snippet).lower():
                    findings.append(f"Severity indicator: {keyword.title()} mentioned in results")
                    break

        return findings[:5]  # Top 5 findings

    def _extract_threat_intelligence(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract threat intelligence indicators from results."""
        intel = {
            "indicators": [],
            "threat_actors": [],
            "techniques": [],
            "tools": []
        }

        for result in results:
            content = (result.get("title", "") + " " + result.get("snippet", "")).lower()

            # Extract threat actor names (common APT groups)
            apt_groups = ["apt1", "apt28", "apt29", "lazarus", "carbanak", "fin7", "cozy bear", "fancy bear"]
            for group in apt_groups:
                if group in content:
                    intel["threat_actors"].append(group.upper())

            # Extract MITRE ATT&CK techniques
            technique_matches = re.findall(r'T\d{4}(?:\.\d{3})?', content.upper())
            intel["techniques"].extend(technique_matches)

        # Deduplicate
        for key in intel:
            intel[key] = list(set(intel[key]))

        return intel


    def _create_research_plan(self, query: str) -> Dict[str, Any]:
        """Create comprehensive research plan with ambiguity detection."""
        # First analyze query for ambiguity
        query_analysis = self._analyze_query(query)
        
        # If ambiguous, return early with clarification request
        if query_analysis.get("type") == "ambiguous_query":
            return query_analysis
        
        query_lower = query.lower()
        
        # Determine research strategies based on query analysis
        strategies = []
        search_terms = []

        # Primary strategy based on query type
        if any(term in query_lower for term in ["cve", "vulnerability", "exploit"]):
            strategies.extend([
                "vulnerability_database_search",
                "security_advisory_research", 
                "exploit_database_analysis",
                "patch_information_gathering"
            ])
            search_terms.extend([query, f"{query} CVE", f"{query} vulnerability", f"{query} exploit"])

        elif any(term in query_lower for term in ["threat", "actor", "apt", "campaign"]):
            strategies.extend([
                "threat_intelligence_research",
                "attribution_analysis",
                "campaign_tracking",
                "ioc_collection"
            ])
            search_terms.extend([query, f"{query} threat actor", f"{query} APT", f"{query} campaign"])

        elif any(term in query_lower for term in ["latest", "recent", "new", "current"]):
            strategies.extend([
                "current_threat_monitoring",
                "breaking_news_analysis",
                "advisory_tracking",
                "timeline_research"
            ])
            search_terms.extend([query, f"{query} 2025", f"{query} recent", f"{query} latest"])
        
        elif query_analysis.get("type") == "macos_specific":
            strategies.extend([
                "macos_vulnerability_research",
                "apple_security_advisory_analysis",
                "macos_specific_threat_analysis"
            ])
            search_terms.extend([query, f"{query} macOS", f"{query} Apple", f"{query} Darwin", f"{query} iOS"])

        else:
            # General comprehensive research
            strategies.extend([
                "multi_source_investigation",
                "cross_reference_analysis",
                "technical_documentation_review",
                "community_intelligence_gathering"
            ])
            search_terms.extend([query, f"{query} security", f"{query} analysis", f"{query} technical"])

        return {
            "type": query_analysis.get("type", "general"),
            "query": query,
            "strategies": strategies,
            "search_terms": search_terms,
            "priority_sources": self._determine_priority_sources(query_lower),
            "expected_findings": self._predict_expected_findings(query_lower)
        }

    def _determine_priority_sources(self, query_lower: str) -> List[str]:
        """Determine priority sources based on query type."""
        if "cve" in query_lower or "vulnerability" in query_lower:
            return ["databases", "news", "technical"]
        elif "threat" in query_lower or "apt" in query_lower:
            return ["threat_intel", "news", "technical"]
        elif "latest" in query_lower or "recent" in query_lower:
            return ["news", "threat_intel", "databases"]
        else:
            return ["databases", "news", "threat_intel", "technical"]

    def _predict_expected_findings(self, query_lower: str) -> List[str]:
        """Predict what types of findings we expect to discover."""
        findings = []

        if any(term in query_lower for term in ["cve", "vulnerability"]):
            findings.extend(["CVE numbers", "CVSS scores", "affected systems", "patch information"])
        if any(term in query_lower for term in ["threat", "actor", "apt"]):
            findings.extend(["threat actor names", "TTPs", "IOCs", "campaign details"])
        if any(term in query_lower for term in ["malware", "ransomware"]):
            findings.extend(["malware families", "signatures", "behavioral analysis"])
        if any(term in query_lower for term in ["technique", "attack"]):
            findings.extend(["MITRE ATT&CK techniques", "attack vectors", "detection methods"])

        return findings

    async def _execute_research_strategies(self, query: str, research_plan: Dict[str, Any], depth: str) -> List[Dict[str, Any]]:
        """Execute multiple research strategies in parallel."""
        search_tasks = []

        # Execute searches for each search term across priority sources
        for search_term in research_plan.get("search_terms", [query])[:4]:  # Limit terms
            for source_category in research_plan.get("priority_sources", ["databases", "news"]):
                if source_category in self.sources:
                    sources = self.sources[source_category]
                    num_sources = 3 if depth == "comprehensive" else 2 if depth == "basic" else 5

                    for source in sources[:num_sources]:
                        search_tasks.append(self._search_source(source, search_term))

        # Execute all searches in parallel
        results = await asyncio.gather(*search_tasks, return_exceptions=True)

        # Filter out exceptions and return valid results
        return [r for r in results if isinstance(r, dict) and not isinstance(r, Exception)]

    async def _cross_reference_findings(self, search_results: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """Cross-reference findings across multiple sources for validation."""
        all_findings = []

        # Collect all results
        for result_set in search_results:
            if "results" in result_set:
                for finding in result_set["results"]:
                    finding["source_category"] = result_set.get("type", "unknown")
                    finding["source_name"] = result_set.get("source", "Unknown")
                    all_findings.append(finding)

        # Cross-reference and validate
        validated_findings = []
        seen_content = set()
        cve_descriptions = {}  # Cache for CVE descriptions

        for finding in all_findings:
            content_hash = hashlib.md5(
                (finding.get("title", "") + finding.get("snippet", ""))[:200].encode()
            ).hexdigest()

            if content_hash not in seen_content:
                seen_content.add(content_hash)

                # If this is a CVE finding, fetch the actual description
                if finding.get("type") == "cve" and finding.get("cve_id"):
                    cve_id = finding["cve_id"]
                    if cve_id not in cve_descriptions:
                        cve_descriptions[cve_id] = await self._fetch_cve_description(cve_id)
                    
                    # Update the finding with actual CVE description
                    finding["snippet"] = cve_descriptions[cve_id]
                    finding["title"] = f"{cve_id}: {self._get_cve_title(cve_descriptions[cve_id])}"

                # Add professional intelligence analytics validation
                intelligence_validation = self._compute_intelligence_validation(finding, query, all_findings)
                finding["intelligence_analytics"] = intelligence_validation
                finding["validation_score"] = intelligence_validation["ensemble_confidence_score"]
                finding["confidence_level"] = intelligence_validation["confidence_category"]
                finding["cross_referenced"] = finding["validation_score"] > 0.7

                validated_findings.append(finding)

        # Sort by validation score and relevance
        return sorted(validated_findings,
                     key=lambda x: (x.get("validation_score", 0), x.get("relevance", 0)),
                     reverse=True)[:20]

    def _calculate_validation_score(self, finding: Dict[str, Any], query: str) -> float:
        """Calculate validation score for cross-referencing."""
        score = 0.0

        # Source credibility
        source_scores = {
            "vulnerability": 0.9,
            "cve": 0.9,
            "rss": 0.7,
            "threat_intel": 0.8,
            "html": 0.6,
            "github": 0.7,
            "reddit": 0.5
        }
        score += source_scores.get(finding.get("source_category", "html"), 0.5)

        # Content relevance
        content = (finding.get("title", "") + " " + finding.get("snippet", "")).lower()
        query_terms = query.lower().split()

        for term in query_terms:
            if term in content:
                score += 0.1

        # Specific indicators (CVE numbers, etc.)
        if re.search(r'cve-\d{4}-\d+', content):
            score += 0.2
        if re.search(r't\d{4}', content):  # MITRE techniques
            score += 0.2

        return min(score, 1.0)

    def _extract_specific_identifiers(self, findings: List[Dict[str, Any]]) -> Dict[str, List[str]]:
        """Extract specific identifiers like CVEs, MITRE techniques, etc."""
        identifiers = {
            "cve_numbers": [],
            "mitre_techniques": [],
            "threat_actors": [],
            "malware_families": [],
            "domains": [],
            "ip_addresses": [],
            "file_hashes": []
        }

        for finding in findings:
            content = finding.get("title", "") + " " + finding.get("snippet", "")

            # Extract CVE numbers
            cves = re.findall(r'CVE-\d{4}-\d+', content, re.IGNORECASE)
            identifiers["cve_numbers"].extend(cves)

            # Extract MITRE techniques
            techniques = re.findall(r'T\d{4}(?:\.\d{3})?', content)
            identifiers["mitre_techniques"].extend(techniques)

            # Extract threat actors (common APT groups)
            apt_groups = ["apt1", "apt28", "apt29", "lazarus", "carbanak", "fin7", "cozy bear", "fancy bear"]
            for group in apt_groups:
                if group.lower() in content.lower():
                    identifiers["threat_actors"].append(group.upper())

            # Extract domains
            domains = re.findall(r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', content)
            identifiers["domains"].extend([d for d in domains if "." in d and len(d) > 4])

            # Extract IP addresses
            ips = re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', content)
            identifiers["ip_addresses"].extend(ips)

        # Deduplicate and return top findings
        for key in identifiers:
            identifiers[key] = list(set(identifiers[key]))[:10]

        return identifiers

    def _generate_cross_references(self, findings: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Generate cross-references between findings."""
        cross_refs = []

        # Group findings by common identifiers
        cve_groups = {}
        for finding in findings:
            content = finding.get("title", "") + " " + finding.get("snippet", "")
            cves = re.findall(r'CVE-\d{4}-\d+', content, re.IGNORECASE)

            for cve in cves:
                if cve not in cve_groups:
                    cve_groups[cve] = []
                cve_groups[cve].append(finding)

        # Create cross-references for CVEs mentioned in multiple sources
        for cve, related_findings in cve_groups.items():
            if len(related_findings) > 1:
                sources = [f.get("source_name", "Unknown") for f in related_findings]
                cross_refs.append({
                    "identifier": cve,
                    "type": "CVE",
                    "cross_referenced_sources": sources,
                    "confidence": "High" if len(sources) >= 3 else "Medium",
                    "findings_count": len(related_findings)
                })

        return cross_refs

    def _generate_intelligence_report(self, findings: List[Dict[str, Any]], query: str, research_plan: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive intelligence report like Task tool output."""

        # Analyze findings quality and coverage
        total_findings = len(findings)
        high_confidence = len([f for f in findings if f.get("validation_score", 0) > 0.8])
        source_diversity = len(set(f.get("source_name", "") for f in findings))

        # Key discoveries
        discoveries = []
        cve_count = len(set(re.findall(r'CVE-\d{4}-\d+', " ".join([f.get("snippet", "") for f in findings]))))
        if cve_count > 0:
            discoveries.append(f"Identified {cve_count} specific CVE number(s)")

        technique_count = len(set(re.findall(r'T\d{4}', " ".join([f.get("snippet", "") for f in findings]))))
        if technique_count > 0:
            discoveries.append(f"Found {technique_count} MITRE ATT&CK technique(s)")

        # Confidence assessment
        if high_confidence >= 5 and source_diversity >= 4:
            confidence = "High"
        elif high_confidence >= 3 and source_diversity >= 3:
            confidence = "Medium"
        else:
            confidence = "Low"

        return {
            "analysis": {
                "total_findings": total_findings,
                "high_confidence_findings": high_confidence,
                "source_diversity": source_diversity,
                "research_strategies_used": len(research_plan.get("strategies", [])),
                "coverage_assessment": "Comprehensive" if total_findings >= 15 else "Moderate"
            },
            "key_discoveries": discoveries,
            "threat_intelligence": self._extract_threat_intelligence(findings),
            "technical_details": {
                "search_depth": "comprehensive",
                "validation_method": "cross_source_verification",
                "research_scope": research_plan.get("strategies", [])
            },
            "confidence_level": confidence
        }
    
    def _compute_intelligence_validation(self, finding: Dict[str, Any], query: str, all_findings: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Compute comprehensive intelligence validation using professional analytics engine."""
        try:
            # Extract metadata for validation
            metadata = {
                'title': finding.get('title', ''),
                'ssl_valid': finding.get('url', '').startswith('https'),
                'last_updated': finding.get('date', ''),
                'source': finding.get('source_name', ''),
                'update_count': 1,  # Default
                'relevance': finding.get('relevance', 0.5)
            }
            
            # Compute component scores using professional analytics
            source_score = self.intelligence_analytics.compute_source_credibility_score(
                finding.get('url', ''), metadata
            )
            
            relevance_score = self.intelligence_analytics.analyze_content_relevance(
                finding.get('title', '') + ' ' + finding.get('snippet', ''),
                query,
                metadata
            )
            
            temporal_score = self.intelligence_analytics.assess_temporal_validity(
                finding.get('date', ''), metadata
            )
            
            cross_ref_score = self.intelligence_analytics.validate_cross_references(
                all_findings, query
            )
            
            # Compute ensemble confidence
            ensemble_score, confidence_category = self.intelligence_analytics.compute_ensemble_confidence(
                source_score, relevance_score, temporal_score, cross_ref_score
            )
            
            return {
                'component_scores': {
                    'source_credibility': source_score,
                    'content_relevance': relevance_score,
                    'temporal_validity': temporal_score,
                    'cross_reference_validation': cross_ref_score
                },
                'ensemble_confidence_score': ensemble_score,
                'confidence_category': confidence_category,
                'analytics_method': 'Professional Cybersecurity Intelligence Analytics',
                'validation_timestamp': datetime.now().isoformat(),
                'formulas_used': {
                    'source_credibility': 'Domain reputation + SSL bonus - freshness penalty + security site bonus',
                    'content_relevance': 'TF-IDF similarity (60%) + domain expertise (40%)',
                    'temporal_validity': 'Recency score (50%) + update frequency (20%) + freshness category (30%)',
                    'cross_reference': 'Multi-source consistency with Jaccard similarity + authority weighting',
                    'ensemble': 'Weighted average: Source(30%) + Relevance(35%) + Temporal(20%) + Cross-ref(15%)'
                }
            }
            
        except Exception as e:
            logger.error(f"Intelligence validation computation failed: {e}")
            return {
                'component_scores': {
                    'source_credibility': 0.5,
                    'content_relevance': 0.5,
                    'temporal_validity': 0.5,
                    'cross_reference_validation': 0.5
                },
                'ensemble_confidence_score': 0.5,
                'confidence_category': 'Medium Confidence',
                'analytics_method': 'Fallback Default Scoring',
                'validation_error': str(e)
            }