import os

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm.providers.provider import <PERSON><PERSON>rovider
from langchain_core.runnables import RunnableConfig
from langgraph.graph.state import CompiledStateGraph
from langgraph.prebuilt import create_react_agent

# Import advanced comprehensive search engine - ONLY TOOL NEEDED
from cyber_security.agents.cyber_expert.tools import (
    advanced_cyber_search,
)
from cyber_security.workflow.configuration import Configuration
from cyber_security.workflow.state import CyberSecuritySupervisorState


class CyberExpertAgent(BaseAgentLLM):
    def __init__(self):
        super().__init__(
            name="cyber_expert_agent",
            description="Cybersecurity expert agent with comprehensive knowledge and current threat intelligence.",
            llm=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME"), temperature=0.1
            ),
            prompt_name="CyberSecurity/cyber_agent/manager",
        )

    async def execute(
        self, state: CyberSecuritySupervisorState, config: RunnableConfig
    ):

        prompt = self.system_prompt
        prompt.extend(state.messages)

        tools = [
            advanced_cyber_search,  # Comprehensive cybersecurity intelligence engine
        ]

        react_agent: CompiledStateGraph = create_react_agent(
            model=self.llm,
            tools=tools,
            config_schema=Configuration,
        )

        response = await react_agent.ainvoke(
            {
                "messages": prompt.format_messages(),
            }
        )

        state.messages = response["messages"]
        return state
