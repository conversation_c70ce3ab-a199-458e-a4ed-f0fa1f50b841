import os
from typing import Union

from corelanggraph import EnhancedStateGraph
from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.graph import END, START
from langgraph.graph.state import CompiledStateGraph

from cyber_security.workflow.configuration import Configuration

from .state import <PERSON>ber<PERSON>xpertState


def add_nodes(workflow_builder: EnhancedStateGraph):
    from cyber_security.agents.cyber_expert.agents.cyber_expert_agent import (
        CyberExpertAgent,
    )

    workflow_builder.add_node("cyber_expert_agent", CyberExpertAgent)


def add_edges(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_edge(START, "cyber_expert_agent")
    workflow_builder.add_edge("cyber_expert_agent", END)


def base_workflow(checkpointer: Union[BaseCheckpointSaver, None]) -> CompiledStateGraph:
    workflow_builder = EnhancedStateGraph(CyberExpertState, config_schema=Configuration)
    add_nodes(workflow_builder)
    add_edges(workflow_builder)
    return workflow_builder.compile(
        checkpointer=checkpointer, name="cyber_expert_agent"
    )


if os.getenv("LANGGRAPH_UI_BUNDLER") == "true":
    from cyber_security.infra import init

compiled_workflow = base_workflow(checkpointer=None)
