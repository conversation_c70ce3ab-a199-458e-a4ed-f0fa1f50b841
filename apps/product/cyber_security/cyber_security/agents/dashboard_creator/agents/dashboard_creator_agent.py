import os
from datetime import timedelta

from corelanggraph.agents.base_mcp_agent import BaseMCPAgent
from corellm.providers.provider import Model<PERSON><PERSON>ider
from langchain_core.runnables import RunnableConfig
from langchain_mcp_adapters.sessions import StreamableHttpConnection
from langgraph.graph.state import CompiledStateGraph
from langgraph.prebuilt import create_react_agent

from cyber_security.agents.dashboard_creator.workflow.configuration import Configuration
from cyber_security.agents.dashboard_creator.workflow.state import (
    DashboardCreatorState,
    DashboardResult,
)


class DashboardCreatorAgent(BaseMCPAgent):
    def __init__(self):
        super().__init__(
            name="dashboard_ creator_agent",
            description="Dashboard creator agent.",
            llm=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME", "azure_model_2"), temperature=0.1
            ),
            prompt_name="CyberSecurity/dashboard_creator/manager",
        )

    def mcp_session(
        self, config: RunnableConfig
    ) -> dict[str, StreamableHttpConnection]:
        user_token = config.get("configurable").get("user_token")
        return {
            "mcp_server": {
                "transport": "streamable_http",
                "url": "http://localhost:7252/msbas/api/mcp",
                "headers": {
                    "Authorization": f"Bearer {user_token}",
                },
                "timeout": timedelta(seconds=10),
                "terminate_on_close": True,
            }
        }

    async def execute(
        self, state: DashboardCreatorState, config: RunnableConfig
    ) -> DashboardCreatorState:

        prompt = self.system_prompt
        prompt.extend(state.messages)

        react_agent: CompiledStateGraph = create_react_agent(
            model=self.llm,
            tools=[],
            config_schema=Configuration,
            response_format=DashboardResult,
        )
        response = await react_agent.ainvoke(
            {
                "messages": prompt.format_messages(),
            }
        )
        state.messages = response["messages"]
        structured_response: DashboardResult = response["structured_response"]
        if structured_response.dashboard_data:
            state.created_dashboard = structured_response.dashboard_data

        return state
