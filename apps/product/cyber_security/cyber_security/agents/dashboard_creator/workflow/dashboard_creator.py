import os
from typing import Union

from corelanggraph import EnhancedStateGraph
from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.graph import END, START
from langgraph.graph.state import CompiledStateGraph

from cyber_security.agents.dashboard_creator.agents.dashboard_creator_agent import (
    DashboardCreatorAgent,
)
from cyber_security.workflow.configuration import Configuration

from .state import DashboardCreatorState


def add_nodes(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_node("dashboard_creator_agent", DashboardCreatorAgent)


def add_edges(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_edge(START, "dashboard_creator_agent")
    workflow_builder.add_edge("dashboard_creator_agent", END)


def base_workflow(checkpointer: Union[BaseCheckpointSaver, None]) -> CompiledStateGraph:
    workflow_builder = EnhancedStateGraph(
        DashboardCreatorState, config_schema=Configuration
    )
    add_nodes(workflow_builder)
    add_edges(workflow_builder)
    return workflow_builder.compile(checkpointer=checkpointer, name="dashboard_creator")


if os.getenv("LANGGRAPH_UI_BUNDLER") == "true":
    from cyber_security.infra import init

compiled_workflow = base_workflow(checkpointer=None)
