"""Define the shared values."""

from __future__ import annotations

from dataclasses import dataclass
from typing import Optional

from langchain_core.messages import AnyMessage
from langgraph.graph import add_messages
from pydantic import BaseModel, Field
from typing_extensions import Annotated


class Widget(BaseModel):
    name: str = Field(description="The name of the widget.")
    description: str = Field(description="The description of the widget.")
    type: str = Field(description="The type of the widget.")
    measurement: str = Field(description="The measurement of the widget.")
    dimensions: Optional[dict[str, list[str]]] = Field(
        default=None, description="The dimensions of the widget."
    )
    filters: Optional[dict[str, list[str]]] = Field(
        default=None, description="The filters of the widget."
    )


class Dashboard(BaseModel):
    dashboard_name: str = Field(description="The name of the dashboard.")
    widgets: list[Widget] = Field(description="The widgets of the dashboard.")


class DashboardResult(BaseModel):
    dashboard_data: Optional[Dashboard] = Field(
        default=None, description="The data of the dashboard."
    )
    ai_asked_for_modification: bool = Field(
        default=False, description="Whether the AI asked for modification."
    )
    ai_asked_for_approval: bool = Field(
        default=False, description="Whether the AI asked for approval."
    )


@dataclass(kw_only=True)
class DashboardCreatorState:
    """Main graph state."""

    messages: Annotated[list[AnyMessage], add_messages]
    """The messages in the conversation."""

    created_dashboard: Optional[Dashboard] = Field(
        default=None, description="The created dashboard."
    )


__all__ = [
    "State",
]
