import os
from typing import Union

from cyber_security.workflow.configuration import Configuration
from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.graph import END, START
from langgraph.graph.state import CompiledStateGraph

from corelanggraph import EnhancedStateGraph
from .state import AssessmentState
from ..agents import AssessmentAgent


def add_nodes(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_node("assessment_agent", AssessmentAgent)


def add_edges(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_edge(START, "assessment_agent")
    workflow_builder.add_edge("assessment_agent", END)


def base_workflow(checkpointer: Union[BaseCheckpointSaver, None]) -> CompiledStateGraph:
    workflow_builder = EnhancedStateGraph(AssessmentState, config_schema=Configuration)
    add_nodes(workflow_builder)
    add_edges(workflow_builder)
    return workflow_builder.compile(checkpointer=checkpointer, name="assessment_agent")


if os.getenv("LANGGRAPH_UI_BUNDLER") == "true":
    pass

compiled_workflow = base_workflow(checkpointer=None)
