import os

from corelanggraph.agents.base_mcp_agent import BaseMCPAgent
from corellm.providers.provider import Model<PERSON>rovider
from langchain_core.runnables import RunnableConfig
from langgraph.graph.state import CompiledStateGraph
from langgraph.prebuilt import create_react_agent
from pydantic import BaseModel

from cyber_security.agents.assessment.workflow.state import AssessmentState
from cyber_security.workflow.configuration import Configuration


class OutputMessage(BaseModel):
    answer: str
    thought: str


class AssessmentAgent(BaseMCPAgent):
    def __init__(self):
        super().__init__(
            name="assessment_agent",
            description="Assessment agent.",
            llm=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME", "azure_model_2"), temperature=0.1
            ),
            prompt_name="CyberSecurity/assessments/manager",
        )

    async def execute(self, state: AssessmentState) -> AssessmentState:
        self.write_stream(
            "update",
            "Searching for relevant information in bas platform...",
        )
        config = Configuration.from_context()
        prompt = self.system_prompt
        prompt.extend(state.messages)
        tools = await self.get_tools(
            self.mcp_session(
                url=os.getenv("BAS_MCP_SERVER_URL"),
                user_token=config.user_token,
            )
        )

        react_agent: CompiledStateGraph = create_react_agent(
            model=self.llm,
            tools=tools,
            system_prompt=self.system_prompt,
            config_schema=Configuration,
            response_format=OutputMessage,
        )
        response = await react_agent.ainvoke(
            {
                "messages": prompt.format_messages(),
            }
        )

        state.messages = response["messages"]
        return state
