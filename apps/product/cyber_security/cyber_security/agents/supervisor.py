import os

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from cyber_security.agents.utilities import GuardrailsAgent
from langchain_openai.chat_models.base import OpenAIRefusalError
from langchain_core.messages import AIMessage
from corellm.providers.provider import Model<PERSON>rovider
from langgraph.pregel import Pregel
from langgraph_supervisor import create_supervisor

from cyber_security.workflow.configuration import Configuration
from cyber_security.workflow.state import (
    CyberSecuritySupervisorResponse,
    CyberSecuritySupervisorState,
)


class CyberSecuritySupervisorAgent(BaseAgentLLM):
    model_name: str
    temperature: float
    available_agents: list[str]

    def __init__(
        self,
        available_agents: list[Pregel],
    ):
        super().__init__(
            name="supervisor_agent",
            description="Supervisor agent",
            llm=ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME")),
            prompt_name="CyberSecurity/supervisor_agent",
        )
        self.available_agents = available_agents

    def get_supervisor_agent(self):
        try:
            prompt_content = self.system_prompt.format_messages()[0].content
        except Exception as e:
            print(f"Langfuse error: {e}")
            # Fallback prompt when Lang<PERSON> is not available
            prompt_content = """You are a cybersecurity supervisor agent that routes requests to specialized agents.
            
            CRITICAL: For template creation requests, you MUST route to template_generator_agent.
            
            Available agents:
            - assessment_agent: For assessment data and results
            - cymulate_knowledge_base_agent: For platform documentation
            - template_generator_agent: For creating security assessment templates
            - cyber_expert_agent: For threat intelligence and security advice
            
            When user asks to "create template", "assessment template", "security template", or similar:
            Route to template_generator_agent immediately.
            
            Do not generate your own response for template creation - always delegate to the appropriate agent."""
        
        print(f"Available agents: {[agent.name if hasattr(agent, 'name') else str(agent) for agent in self.available_agents]}")
        
        supervisor = create_supervisor(
            prompt=prompt_content,
            config_schema=Configuration,
            agents=self.available_agents,
            tools=[],
            model=self.llm,
            response_format=CyberSecuritySupervisorResponse,
        )
        print(f"Supervisor created with {len(self.available_agents)} agents")
        
        return supervisor.compile()

    async def execute(self, state: CyberSecuritySupervisorState):
        try:
            return await self.get_supervisor_agent().ainvoke(state)
        except OpenAIRefusalError as e:
            from logger import logger
            logger.warning(f"⚠️ OpenAI refused request: {str(e)}")
            
            state.messages.append(
                AIMessage(content="I cannot assist with that request due to safety policies. Please ask about cybersecurity defense, best practices, or educational topics.")
            )
            return state
        except Exception as e:
            from logger import logger
            logger.error(f"❌ Supervisor error: {str(e)}")
            
            state.messages.append(
                AIMessage(content="I encountered an error processing your request. Please try rephrasing your question.")
            )
            return state
