import os
from typing import Union

from corelanggraph import EnhancedStateGraph
from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.graph import END, START
from langgraph.graph.state import CompiledStateGraph

from cyber_security.agents.findings.agents.findings_agent import FindingsAgent
from cyber_security.workflow.configuration import Configuration
from cyber_security.workflow.state import CyberSecurityState


def add_nodes(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_node("findings_agent", FindingsAgent)


def add_edges(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_edge(START, "findings_agent")
    workflow_builder.add_edge("findings_agent", END)


def base_workflow(checkpointer: Union[BaseCheckpointSaver, None]) -> CompiledStateGraph:
    workflow_builder = EnhancedStateGraph(
        CyberSecurityState, config_schema=Configuration
    )
    add_nodes(workflow_builder)
    add_edges(workflow_builder)
    return workflow_builder.compile(checkpointer=checkpointer, name="findings_agent")


if os.getenv("LANGGRAPH_UI_BUNDLER") == "true":
    pass

compiled_workflow = base_workflow(checkpointer=None)
