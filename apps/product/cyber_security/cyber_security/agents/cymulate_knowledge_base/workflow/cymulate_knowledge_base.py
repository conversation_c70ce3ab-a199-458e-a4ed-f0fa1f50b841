import os
from typing import Union

from corelanggraph import EnhancedStateGraph
from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.graph import END, START
from langgraph.graph.state import CompiledStateGraph

from cyber_security.agents.cymulate_knowledge_base.agents.cymulate_knowledge_agent import (
    CymulateKnowledgeBaseAgent,
)
from cyber_security.workflow.state import (
    CyberSecuritySupervisorState,
)
from cyber_security.workflow.configuration import Configuration


def add_nodes(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_node(
        "cymulate_knowledge_base_agent", CymulateKnowledgeBaseAgent
    )


def add_edges(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_edge(START, "cymulate_knowledge_base_agent")
    workflow_builder.add_edge("cymulate_knowledge_base_agent", END)


def base_workflow(checkpointer: Union[BaseCheckpointSaver, None]) -> CompiledStateGraph:
    workflow_builder = EnhancedStateGraph(
        CyberSecuritySupervisorState, config_schema=Configuration
    )
    add_nodes(workflow_builder)
    add_edges(workflow_builder)
    return workflow_builder.compile(
        checkpointer=checkpointer, name="cymulate_knowledge_base_agent"
    )


if os.getenv("LANGGRAPH_UI_BUNDLER") == "true":
    from cyber_security.infra import init

compiled_workflow = base_workflow(checkpointer=None)
