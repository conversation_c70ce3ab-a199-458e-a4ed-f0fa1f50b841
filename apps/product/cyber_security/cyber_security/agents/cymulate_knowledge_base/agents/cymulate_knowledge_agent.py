import os

from corelanggraph.agents.base_mcp_agent import BaseMCPAgent
from corellm.providers.provider import Model<PERSON>rovider
from langchain_core.tools import tool
from langgraph.graph.state import CompiledStateGraph
from langgraph.prebuilt import create_react_agent

from cyber_security.workflow.state import (
    CyberSecuritySupervisorState,
)
from cyber_security.workflow.configuration import (
    Configuration,
)


class CymulateKnowledgeBaseAgent(BaseMCPAgent):
    def __init__(self):
        super().__init__(
            name="cymulate_knowledge_agent",
            description="Cymulate knowledge agent.",
            llm=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME", "azure_model_2"), temperature=0.1
            ),
            prompt_name="CyberSecurity/knowledgebase/manager",
        )

    async def execute(
        self, state: CyberSecuritySupervisorState
    ) -> CyberSecuritySupervisorState:

        @tool
        def retrieve_cymulate_knowledge(question: str) -> str:
            """Retrieve knowledge from Cymulate knowledge base.

            Args:
                question: The cybersecurity question to search for in the knowledge base

            Returns:
                Relevant information from the Cymulate knowledge base
            """
            try:
                from langchain_aws import AmazonKnowledgeBasesRetriever

                knowledge_base_id = os.getenv("KNOWLEDGE_BASE_ID")
                if not knowledge_base_id:
                    return "Error: KNOWLEDGE_BASE_ID environment variable is not set"

                retriever = AmazonKnowledgeBasesRetriever(
                    retrieval_config={
                        "vectorSearchConfiguration": {"numberOfResults": 4}
                    },
                    knowledge_base_id=knowledge_base_id,
                )

                results = retriever.invoke(question)
                if not results:
                    return "No relevant information found in the knowledge base"

                # Format the results as a string with basic information
                formatted_results = []
                for i, result in enumerate(results, 1):
                    content = ""

                    if hasattr(result, "page_content"):
                        content = result.page_content
                    else:
                        content = str(result)

                    # Simple formatting - just include the content
                    formatted_results.append(f"{i}. {content}")

                # Add a note about source availability
                final_result = "\n\n---\n\n".join(formatted_results)
                final_result += "\n\n📖 **Note:** This information is from the Cymulate Knowledge Base. For complete documentation and latest updates, please refer to the official Cymulate documentation portal."

                return final_result

            except Exception as e:
                return f"Error retrieving knowledge: {str(e)}"

        configuration = Configuration.from_context()

        prompt = self.system_prompt
        prompt.extend(state.messages)

        tools = [retrieve_cymulate_knowledge]

        react_agent: CompiledStateGraph = create_react_agent(
            model=self.llm,
            tools=tools,
            response_format=CyberSecuritySupervisorState,
            config_schema=Configuration,
        )
        response = await react_agent.ainvoke(
            {
                "messages": prompt.format_messages(),
            }
        )
        state.messages = response["messages"]
        try:
            state.documentation_links = response["structured_response"][
                "documentation_links"
            ]
        except Exception as e:
            print(e)
        return state
