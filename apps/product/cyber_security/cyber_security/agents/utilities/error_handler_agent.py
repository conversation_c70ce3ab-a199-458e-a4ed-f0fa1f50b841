from datetime import datetime

from corelanggraph.agents import BaseAgent
from ddtrace.llmobs.decorators import retrieval
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig

from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class ErrorHandlerAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="error_handler_agent",
            description="Handles errors and sends error messages to the user.",
        )

    @execution_time
    @retrieval(name="error_handler_agent")
    async def execute(
        self, state: CyberSecurityState, config: RunnableConfig
    ) -> CyberSecurityState:
        """Handles errors and sends error messages to the user."""
        if state.error_message:
            state.messages.append(
                AIMessage(
                    content=f"Error: {state.error_message}",
                    is_error=True,
                    id=datetime.now().timestamp(),
                )
            )

            self.write_stream(
                "error",
                {
                    "event_data": state.event_data.model_dump(),
                    "metadata": state.metadata,
                    "error_message": state.error_message,
                    "override": False,
                },
            )

        return state
