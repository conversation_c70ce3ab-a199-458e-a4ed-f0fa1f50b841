import yaml
import os
import json
from typing import List
from corelanggraph.agents import BaseAgent
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langchain_aws import ChatBedrock
from langgraph.graph.message import RemoveMessage
from logger import logger

from cyber_security.workflow.state import CyberSecuritySupervisorState


class OutputGuardrailsAgent(BaseAgent):
    """Agent that validates AI responses before sending to users"""
    
    def __init__(self):
        super().__init__(
            name="output_guardrails_agent",
            description="Validate AI responses before sending to users"
        )
        
        # Load config and initialize LLM
        self.config = self._load_config()
        self.llm = self._initialize_llm()
        self.output_check_prompt = self._load_output_check_prompt()
    
    def _load_config(self) -> dict:
        """Load guardrails config file"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), "..", "..", "guardrails", "config.yml")
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except Exception as e:
            logger.warning(f"⚠️ Failed to load guardrails config: {e}")
            return {}
    
    def _initialize_llm(self) -> ChatBedrock:
        """Initialize Claude model from config"""
        try:
            model_config = self.config.get('models', [{}])[0]
            model_name = model_config.get('model', 'us.anthropic.claude-3-haiku-20240307-v1:0')
            return ChatBedrock(model_id=model_name, region_name='us-east-1')
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize LLM: {e}")
            return None
    
    def _load_output_check_prompt(self) -> str:
        """Load output check prompt from config"""
        prompts = self.config.get('prompts', [])
        for prompt in prompts:
            if prompt.get('task') == 'self_check_output':
                return prompt.get('content', '')
        return 'Check if this AI response is appropriate: "{{ bot_response }}"'
    
    async def _check_output(self, content) -> dict:
        """Use LLM to check if AI output is appropriate"""
        if not self.llm:
            return {"allow": True, "reason": "LLM not available"}
        
        # Handle both string and list content
        if isinstance(content, list):
            content_text = " ".join(str(item) for item in content)
        else:
            content_text = str(content)
        
        try:
            prompt = self.output_check_prompt.replace('{{ bot_response }}', content_text)
            response = await self.llm.ainvoke(prompt)
            
            # Handle both JSON and text responses
            try:
                return json.loads(response.content)
            except json.JSONDecodeError:
                # If not JSON, parse text response
                content_lower = response.content.lower()
                if "yes" in content_lower or "allow" in content_lower:
                    return {"allow": True, "reason": "Output approved"}
                else:
                    return {"allow": False, "reason": "Output blocked by policy"}
        except Exception as e:
            logger.warning(f"⚠️ Output check failed: {e}")
            return {"allow": True, "reason": "Output check error"}

    def _get_last_ai_message(self, messages: List[BaseMessage]) -> AIMessage:
        """Get the last AI message from the conversation"""
        for message in reversed(messages):
            if isinstance(message, AIMessage):
                return message
        return None

    async def execute(self, state: CyberSecuritySupervisorState, config: RunnableConfig):
        """Validate the last AI response before sending to user"""
        try:
            logger.info("🛡️ Applying output guardrails to AI response")

            # Only process if there are messages
            if not state.messages:
                logger.info("✅ No messages to validate")
                return {}

            # Get the last AI message
            last_ai_message = self._get_last_ai_message(state.messages)
            if not last_ai_message:
                logger.info("✅ No AI message to validate")
                return {}

            # Skip if this message was already processed by output guardrails
            # Check both metadata attribute and additional_kwargs for the flag
            already_checked = False
            if hasattr(last_ai_message, 'additional_kwargs') and last_ai_message.additional_kwargs:
                already_checked = last_ai_message.additional_kwargs.get('output_guardrails_checked', False)

            if already_checked:
                logger.info("✅ Message already checked by output guardrails")
                return {}

            # Check the AI response
            validation_result = await self._check_output(last_ai_message.content)

            if not validation_result.get('allow', True):
                # Create a safe response to replace the unsafe one
                safe_response = AIMessage(
                    content="I apologize, but I cannot provide that response. Please ask about cybersecurity defense, best practices, or educational topics.",
                    additional_kwargs={'output_guardrails_checked': True, 'original_blocked': True},
                    id=getattr(last_ai_message, 'id', None)
                )

                logger.warning(f"⚠️ Blocked AI response: {validation_result.get('reason', 'No reason provided')}")

                # Remove the unsafe message and add the safe one
                messages_to_update = []
                if hasattr(last_ai_message, 'id') and last_ai_message.id:
                    # Remove the unsafe message by ID
                    messages_to_update.append(RemoveMessage(id=last_ai_message.id))

                # Add the safe response
                messages_to_update.append(safe_response)

                return {
                    "messages": messages_to_update,
                    "output_blocked_by_guardrails": True
                }
            else:
                # Mark the message as checked by adding to additional_kwargs
                # Preserve existing additional_kwargs and add our flag
                existing_kwargs = getattr(last_ai_message, 'additional_kwargs', {}) or {}
                updated_kwargs = {**existing_kwargs, 'output_guardrails_checked': True}

                # Create checked message preserving original attributes
                checked_message = AIMessage(
                    content=last_ai_message.content,
                    additional_kwargs=updated_kwargs,
                    id=getattr(last_ai_message, 'id', None)
                )

                # If the message has an ID, remove the old one and add the updated one
                messages_to_update = []
                if hasattr(last_ai_message, 'id') and last_ai_message.id:
                    # Remove the old message and add the updated one
                    messages_to_update.append(RemoveMessage(id=last_ai_message.id))
                    messages_to_update.append(checked_message)
                else:
                    # If no ID, just add the checked message (it will append)
                    messages_to_update.append(checked_message)

                logger.info("✅ AI response passed output guardrails")

                return {
                    "messages": messages_to_update
                }

        except Exception as e:
            logger.error(f"❌ Output guardrails failed: {e}")
            return {}
