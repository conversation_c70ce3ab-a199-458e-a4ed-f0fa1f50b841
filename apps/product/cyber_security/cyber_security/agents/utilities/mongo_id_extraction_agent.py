from datetime import datetime

from corelanggraph.agents import BaseAgent
from ddtrace.llmobs.decorators import retrieval
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig

from cyber_security.tools.extractors import extract_mongo_ids_from_question
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class MongoIdExtractionAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="mongo_id_extraction_agent",
            description="Extract MongoDB IDs from the user's message.",
        )

    @execution_time
    @retrieval(name="mongo_id_extraction_agent")
    async def execute(
        self, state: CyberSecurityState, config: RunnableConfig
    ) -> CyberSecurityState:
        """Extract MongoDB IDs from the user's message."""

        user_question = state.user_input
        state.mongo_ids = state.mongo_ids + extract_mongo_ids_from_question.invoke(
            user_question
        )

        if state.mongo_ids:
            state.input_metadata.append(
                AIMessage(
                    content=f"Extracted MongoDB IDs: {state.mongo_ids}",
                    id=datetime.now().timestamp(),
                )
            )

            self.write_stream(
                "update",
                {
                    "event_data": state.event_data,
                    "metadata": state.metadata,
                    "initial_response": state.initial_response,
                    "message": f":loading-2: Found {len(state.mongo_ids)} assessments...",
                    "data": {"mongo_ids": state.mongo_ids},
                },
            )

        return state
