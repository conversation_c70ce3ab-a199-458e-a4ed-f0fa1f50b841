from typing import List
from corelanggraph.agents import BaseAgent
from langchain_core.messages import BaseMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph.message import RemoveMessage
from logger import logger

from cyber_security.workflow.state import CyberSecuritySupervisorState


class OutputValidatorAgent(BaseAgent):
    """Agent that processes output validation results and handles message replacement"""
    
    def __init__(self):
        super().__init__(
            name="output_validator_agent",
            description="Process output validation results and handle message replacement"
        )

    def _get_last_ai_message(self, messages: List[BaseMessage]) -> AIMessage:
        """Get the last AI message from the conversation"""
        for message in reversed(messages):
            if isinstance(message, AIMessage):
                return message
        return None

    async def execute(self, state: CyberSecuritySupervisorState, config: RunnableConfig):
        """Process output validation result and handle message replacement if needed"""
        try:
            logger.info("🔍 Processing output validation result")
            
            # Check if we have a validation result
            validation = state.output_validation
            if not validation:
                logger.info("✅ No output validation to process")
                return {}
            
            # If validation passed, just mark the message as checked
            if validation.get('allow', True):
                logger.info("✅ Output validation passed")
                
                # Get the last AI message and mark it as checked
                last_ai_message = self._get_last_ai_message(state.messages)
                if last_ai_message:
                    # Create updated message with guardrails flag
                    existing_kwargs = getattr(last_ai_message, 'additional_kwargs', {}) or {}
                    updated_kwargs = {**existing_kwargs, 'output_guardrails_checked': True}
                    
                    checked_message = AIMessage(
                        content=last_ai_message.content,
                        additional_kwargs=updated_kwargs,
                        id=getattr(last_ai_message, 'id', None)
                    )
                    
                    # Replace the message
                    messages_to_update = []
                    if hasattr(last_ai_message, 'id') and last_ai_message.id:
                        messages_to_update.append(RemoveMessage(id=last_ai_message.id))
                    messages_to_update.append(checked_message)
                    
                    return {"messages": messages_to_update}
                
                return {}
            
            # If validation failed, replace with safe message
            else:
                logger.warning(f"⚠️ Output validation failed: {validation.get('reason', 'Unknown reason')}")
                
                # Create safe response
                safe_response = AIMessage(
                    content="I apologize, but I cannot provide that response. Please ask about cybersecurity defense, best practices, or educational topics.",
                    additional_kwargs={'output_guardrails_checked': True, 'original_blocked': True}
                )
                
                # Get the last AI message to replace
                last_ai_message = self._get_last_ai_message(state.messages)
                messages_to_update = []
                
                if last_ai_message and hasattr(last_ai_message, 'id') and last_ai_message.id:
                    # Remove the unsafe message by ID
                    messages_to_update.append(RemoveMessage(id=last_ai_message.id))
                
                # Add the safe response
                messages_to_update.append(safe_response)
                
                return {
                    "messages": messages_to_update,
                    "output_blocked_by_guardrails": True
                }
            
        except Exception as e:
            logger.error(f"❌ Output validation processing failed: {e}")
            return {}
