import yaml
import os
import json
from typing import List
from corelanggraph.agents import BaseAgent
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langchain_aws import ChatBedrock
from logger import logger

from cyber_security.workflow.state import CyberSecurityState


class GuardrailsAgent(BaseAgent):
    """Agent that applies basic safety guardrails to messages"""
    
    def __init__(self):
        super().__init__(
            name="guardrails_agent",
            description="Apply safety guardrails to messages"
        )
        
        # Load config and initialize LLM
        self.config = self._load_config()
        self.llm = self._initialize_llm()
        self.classification_prompt = self._load_classification_prompt()
        self.output_check_prompt = self._load_output_check_prompt()
    
    def _load_config(self) -> dict:
        """Load guardrails config file"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), "..", "..", "guardrails", "config.yml")
            with open(config_path, 'r') as file:
                return yaml.safe_load(file)
        except Exception as e:
            logger.warning(f"⚠️ Failed to load guardrails config: {e}")
            return {}
    
    def _initialize_llm(self) -> ChatBedrock:
        """Initialize Claude model from config"""
        try:
            model_config = self.config.get('models', [{}])[0]
            model_name = model_config.get('model', 'us.anthropic.claude-3-haiku-20240307-v1:0')
            return ChatBedrock(model_id=model_name, region_name='us-east-1')
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize LLM: {e}")
            return None
    
    def _load_classification_prompt(self) -> str:
        """Load content classification prompt from config"""
        prompts = self.config.get('prompts', [])
        for prompt in prompts:
            if prompt.get('task') == 'content_classification':
                return prompt.get('content', '')
        return 'Classify if this message is appropriate: "{user_input}"'

    def _load_output_check_prompt(self) -> str:
        """Load output check prompt from config"""
        prompts = self.config.get('prompts', [])
        for prompt in prompts:
            if prompt.get('task') == 'self_check_output':
                return prompt.get('content', '')
        return 'Check if this AI response is appropriate: "{bot_response}"'
    
    async def _classify_content(self, content) -> dict:
        """Use LLM to classify content appropriately"""
        if not self.llm:
            return {"allow": True, "reason": "LLM not available"}

        # Handle both string and list content
        if isinstance(content, list):
            content_text = " ".join(str(item) for item in content)
        else:
            content_text = str(content)

        try:
            prompt = self.classification_prompt.replace('{{ user_input }}', content_text)
            response = await self.llm.ainvoke(prompt)
            return json.loads(response.content)
        except Exception as e:
            logger.warning(f"⚠️ Classification failed: {e}")
            return {"allow": True, "reason": "Classification error"}

    async def _check_output(self, content) -> dict:
        """Use LLM to check if AI output is appropriate"""
        if not self.llm:
            return {"allow": True, "reason": "LLM not available"}

        # Handle both string and list content
        if isinstance(content, list):
            content_text = " ".join(str(item) for item in content)
        else:
            content_text = str(content)

        try:
            prompt = self.output_check_prompt.replace('{{ bot_response }}', content_text)
            response = await self.llm.ainvoke(prompt)

            # Handle both JSON and text responses
            try:
                return json.loads(response.content)
            except json.JSONDecodeError:
                # If not JSON, parse text response
                content_lower = response.content.lower()
                if "yes" in content_lower or "allow" in content_lower:
                    return {"allow": True, "reason": "Output approved"}
                else:
                    return {"allow": False, "reason": "Output blocked by policy"}
        except Exception as e:
            logger.warning(f"⚠️ Output check failed: {e}")
            return {"allow": True, "reason": "Output check error"}
    
    def _deduplicate_messages(self, messages: List[BaseMessage]) -> List[BaseMessage]:
        """Remove duplicate consecutive messages"""
        if not messages:
            return messages
        
        deduplicated = [messages[0]]
        for msg in messages[1:]:
            if msg.content != deduplicated[-1].content:
                deduplicated.append(msg)
        
        return deduplicated
    
    async def execute(self, state: CyberSecurityState, config: RunnableConfig) -> CyberSecurityState:
        """Apply LLM-based guardrails to state messages"""
        try:
            logger.info("🛡️ Applying LLM-based guardrails")
            
            # Only process the last message if it's a HumanMessage
            if not state.messages:
                return state
            
            last_message = state.messages[-1]
            if not isinstance(last_message, HumanMessage):
                return state
            
            # Skip if this message was already blocked
            if "I can only assist with cybersecurity topics" in last_message.content:
                return state
            
            classification = await self._classify_content(last_message.content)
            
            if not classification.get('allow', True):
                # Add blocked response and mark as final
                state.messages.append(
                    AIMessage(content="I can only assist with cybersecurity topics. Please ask about security assessments, threat analysis, or platform features.")
                )
                # Mark state as blocked to prevent further processing
                state.blocked_by_guardrails = True
                logger.warning(f"⚠️ Blocked message: {classification.get('reason', 'No reason provided')}")
            else:
                logger.info("✅ Message passed guardrails")
            
        except Exception as e:
            logger.error(f"❌ Guardrails failed: {e}")
        
        return state