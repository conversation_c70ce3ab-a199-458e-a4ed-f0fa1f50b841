import os
from datetime import datetime
from typing import List

from corelanggraph.agents import BaseAgentLLM
from corelanggraph.app_builder.models.files import EventFile
from corellm.providers.provider import ModelProvider
from ddtrace.llmobs.decorators import retrieval
from langchain_core.documents import Document
from langchain_core.messages import HumanMessage, trim_messages
from langchain_core.tools import tool
from logger import logger
from pydantic import ValidationError

from cyber_security.loaders.loader_factory import LoaderFactory
from cyber_security.utils import execution_time, extract_mongo_ids
from cyber_security.workflow.state import CyberSecurityState

loader_factory = LoaderFactory()


class FileProcessingAgent(BaseAgentLLM):
    def __init__(self):
        super().__init__(
            name="file_processing_agent",
            description="Processes the files and updates the state with unique MongoDB IDs and trimmed messages.",
            llm=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME", "azure_model_2")
            ),
        )

    @tool
    async def file_processing(
        self, files: list[EventFile], user_input: str
    ) -> List[Document]:
        """Processes the files based on the user input and returns the documents.

        Args:
            files (list["EventFiles"]): The event files.
            user_input (str): The user input

        Returns:
            List[Document]: The documents generated by processing the files."""

        documents = []
        for event_file in files:
            try:
                loader = loader_factory.create_loader(event_file, user_input)
                if loader:
                    documents.extend(loader.load())
            except ValidationError as e:
                logger.error(f"Validation error for file {event_file.name}: {e}")

        return documents

    @execution_time
    @retrieval(name="file_processing_agent")
    async def execute(self, state: CyberSecurityState) -> CyberSecurityState:
        """Processes the files and updates the state with unique MongoDB IDs and trimmed messages."""

        if not state.files:
            return state

        documents: List[Document] = await self.file_processing.ainvoke(
            {"files": state.files, "user_input": state.user_input}
        )

        logger.info(f"Documents: {documents}")

        all_mongo_ids = {
            mongo_id
            for doc in documents
            for mongo_id in extract_mongo_ids(doc.page_content)
        }
        state.mongo_ids = set(state.mongo_ids).union(all_mongo_ids)

        messages = [
            HumanMessage(
                content=doc.page_content,
                id=datetime.now().timestamp(),
                additional_kwargs={"type": "attachment", **doc.metadata},
                usage_metadata={"type": "attachment", **doc.metadata},
            )
            for doc in documents
        ]

        state.input_metadata += trim_messages(
            messages=messages,
            max_tokens=50000,
            strategy="last",
            token_counter=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME", "azure_model_2")
            ),
            include_system=True,
            allow_partial=True,
        )

        state.files = []
        return state
