from typing import Any
from langchain_core.messages import AIMessage
from langchain_openai.chat_models.base import OpenAIRefusalError
from langchain_core.runnables import RunnableConfig
from logger import logger

from cyber_security.workflow.state import CyberSecurityState


async def handle_openai_refusal(func, state: CyberSecurityState, config: RunnableConfig) -> CyberSecurityState:
    """Wrapper to handle OpenAI refusal errors gracefully"""
    try:
        return await func(state, config)
    except OpenAIRefusalError as e:
        logger.warning(f"⚠️ OpenAI refused request: {str(e)}")
        
        # Add safe response message
        state.messages.append(
            AIMessage(content="I cannot assist with that request due to safety policies. Please ask about cybersecurity defense, best practices, or educational topics.")
        )
        return state
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        
        # Add generic error message
        state.messages.append(
            AIMessage(content="I encountered an error processing your request. Please try rephrasing your question.")
        )
        return state