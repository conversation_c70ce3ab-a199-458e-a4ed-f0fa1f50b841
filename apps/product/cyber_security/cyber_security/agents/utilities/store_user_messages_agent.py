import asyncio
import json
from datetime import datetime
from time import time
from typing import List

from corelanggraph.agents import BaseAgent
from corelanggraph.app_builder.models.event import ClientTypeEnum
from ddtrace.llmobs.decorators import retrieval
from elasticsearch.exceptions import ApiError
from elasticsearch.helpers import bulk
from langchain_core.messages import BaseMessage, message_to_dict
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool
from logger import logger

from cyber_security.infra.init import infra
from cyber_security.models.event_data import EventDataType
from cyber_security.models.metadata_generator import Metadata
from cyber_security.utils.glbl import execution_time
from cyber_security.workflow.state import CyberSecurityState


class StoreUserMessagesAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="store_user_messages_agent",
            description="Store user messages in the database.",
        )

    @tool
    async def store_user_messages(
        self, session_id: str, messages: list[BaseMessage]
    ) -> None:
        """Store user messages based on session ID in bulk."""
        try:
            await asyncio.to_thread(
                infra.elasticsearch_client.delete_by_query,
                index=infra.get_secret(
                    key="INDEX_CHAT_HISTORY", default="cy-ai-docs-chat-history"
                ),
                body={"query": {"term": {"session_id": {"value": session_id}}}},
                refresh=True,
            )

            actions = [
                {
                    "_index": infra.get_secret(
                        key="INDEX_CHAT_HISTORY", default="cy-ai-docs-chat-history"
                    ),
                    "_op_type": "index",
                    "_source": {
                        "session_id": session_id,
                        "created_at": round(time() * 1000),
                        "history": json.dumps(
                            message_to_dict(message), ensure_ascii=True
                        ),
                    },
                }
                for message in messages
            ]

            success_count, failures = bulk(
                client=infra.elasticsearch_client,
                actions=actions,
                chunk_size=5000,
                raise_on_error=False,
            )

            if failures:
                for failure in failures:
                    logger.error(f"Failed to index document: {failure}")

            logger.info(
                f"Indexed {success_count} documents with {len(failures)} failures for session {session_id}."
            )

            await asyncio.to_thread(
                infra.elasticsearch_client.indices.refresh,
                index=infra.get_secret(
                    key="INDEX_CHAT_HISTORY", default="cy-ai-docs-chat-history"
                ),
            )

        except ApiError as err:
            logger.error(f"Error connecting to Elasticsearch: {err}")
            raise
        except Exception as e:
            logger.error(f"An unexpected error occurred: {e}")
            raise

    @tool
    async def store_user_metrics(
        self,
        session_id: str,
        messages: list[BaseMessage],
        input_metadata: List[BaseMessage],
        user_input: str,
        metadata: Metadata,
        event_data: EventDataType,
        client_type: ClientTypeEnum,
        classification: str,
    ) -> None:
        """Store user metrics based on session ID."""
        answer = messages[-1].content
        index: str = infra.get_secret(
            key="INDEX_CHAT_HISTORY_METRICS", default="cy-ai-docs-chat-history-metrics"
        )

        document = {
            "session_id": session_id,
            "question_id": event_data.question_id or metadata.question_id,
            "listener": event_data.listener,
            "question": user_input,
            "answer": answer,
            "client_type": client_type,
            "classification": classification,
            "input_metadata": [message_to_dict(message) for message in input_metadata],
            "@timestamp": datetime.now().isoformat(),
            "metadata": metadata.model_dump(),
            "event": event_data.model_dump(),
        }

        await asyncio.to_thread(
            infra.elasticsearch_client.index, index=index, document=document
        )

    @execution_time
    @retrieval(name="store_user_messages_agent")
    async def execute(
        self, state: CyberSecurityState, config: RunnableConfig
    ) -> CyberSecurityState:
        """Store user messages in the database."""

        if "configurable" not in config or "session_id" not in config["configurable"]:
            raise ValueError(
                "Make sure that the config includes the following information: {'configurable': {'session_id': 'some_value'}}"
            )

        try:
            session_id = config["configurable"]["session_id"]

            excluded_types = {
                "metadata",
                "knowledge_base",
                "image",
                "attachment",
                "assessment",
                "assessment_logs",
            }
            filtered_messages = [
                msg
                for msg in state.messages
                if msg.additional_kwargs.get("type") not in excluded_types
            ]

            asyncio.create_task(
                self.store_user_messages.ainvoke(
                    {"session_id": session_id, "messages": filtered_messages}
                )
            )

            asyncio.create_task(
                self.store_user_metrics.ainvoke(
                    {
                        "session_id": session_id,
                        "messages": filtered_messages,
                        "input_metadata": state.input_metadata,
                        "user_input": state.user_input,
                        "metadata": state.metadata,
                        "event_data": state.event_data,
                        "client_type": state.client_type,
                        "classification": state.classification,
                    }
                )
            )
        except Exception as e:
            logger.error(e)

        return state
