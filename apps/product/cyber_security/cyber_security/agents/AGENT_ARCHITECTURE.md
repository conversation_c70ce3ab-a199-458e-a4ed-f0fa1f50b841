# Cyber Security Agent Architecture

## Overview

The Cyber Security system has been refactored into specialized agents, each with distinct responsibilities and capabilities. This modular approach ensures better separation of concerns, improved maintainability, and enhanced scalability.

## Agent Structure

### 1. **Assessment Agent** (Reouven)

- **Purpose**: Data retrieval and assessment analysis
- **Responsibilities**:
  - Retrieve assessment data using BAS tools
  - Analyze assessment metadata and execution status
  - Interpret Cymulate scores and metrics
  - Provide high-level assessment summaries
- **Tools**: `retrieve_bas2_assessment`, `retrieve_bas2_assessment_scenarios`, `retrieve_bas2_assessment_findings`
- **Output**: Structured assessment data and execution metrics

### 2. **Finding Agent** (<PERSON>)

- **Purpose**: Security finding analysis and risk assessment
- **Responsibilities**:
  - Analyze detailed security findings and vulnerabilities
  - Prioritize findings by risk level and impact
  - Map findings to MITRE ATT&CK framework
  - Evaluate security control effectiveness
- **Input**: Assessment data from Assessment Agent
- **Output**: Prioritized findings analysis with risk assessment

### 3. **Cyber Agent** (<PERSON><PERSON>)

- **Purpose**: Strategic cybersecurity context and threat intelligence
- **Responsibilities**:
  - Provide strategic cybersecurity insights
  - Analyze current threat landscape
  - Assess organizational security maturity
  - Recommend security framework alignments
- **Input**: Assessment results and findings summary
- **Output**: Strategic recommendations and threat context

### 4. **KB Agent** (Arik)

- **Purpose**: Platform knowledge and documentation
- **Responsibilities**:
  - Search Cymulate Knowledge Base
  - Provide platform feature explanations
  - Offer troubleshooting support
  - Share best practices and configuration guidance
- **Tools**: `query_knowledge_base`, `query_api_routes_from_swagger`
- **Output**: Platform guidance and documentation insights

### 5. **Template Generator Agent** (DONE)

- **Purpose**: Assessment template creation and configuration
- **Responsibilities**:
  - Create customized assessment templates
  - Generate attack scenarios based on requirements
  - Configure assessment parameters
- **Status**: Already implemented
- **Output**: Assessment templates and scenarios

### 6. **Dashboard Generator Agent**

- **Purpose**: Visual dashboards and reporting
- **Responsibilities**:
  - Create executive-level security dashboards
  - Generate technical security reports
  - Visualize security metrics and trends
  - Design stakeholder-specific presentations
- **Input**: Processed data from all other agents
- **Output**: Visual dashboards and reports

## Agent Interaction Flow

```
User Query
    ↓
Assessment Agent → Retrieves assessment data
    ↓
Finding Agent → Analyzes findings and vulnerabilities
    ↓
Cyber Agent → Provides strategic context
    ↓
KB Agent → Adds platform knowledge (when needed)
    ↓
Dashboard Generator Agent → Creates visualizations
    ↓
Combined Response to User
```

## Data Flow Architecture

### Primary Data Flow

1. **Assessment Agent** retrieves raw assessment data
2. **Finding Agent** processes findings for detailed analysis
3. **Cyber Agent** adds strategic context and threat intelligence
4. **KB Agent** provides platform-specific guidance
5. **Dashboard Generator Agent** creates visual representations

### Cross-Agent Dependencies

- Finding Agent depends on Assessment Agent for raw data
- Cyber Agent uses both Assessment and Finding Agent outputs
- Dashboard Generator Agent synthesizes all agent outputs
- KB Agent operates independently but enhances all other agents

## Specialization Benefits

### 🎯 **Focused Expertise**

- Each agent specializes in a specific domain
- Deeper knowledge and better performance in specialized areas
- Easier maintenance and updates

### 🔄 **Modular Architecture**

- Agents can be developed and updated independently
- Easy to add new agents or modify existing ones
- Better testing and debugging capabilities

### 📊 **Scalability**

- Agents can be scaled independently based on demand
- Parallel processing capabilities
- Resource optimization

### 🛡️ **Separation of Concerns**

- Clear boundaries between different types of analysis
- Reduced complexity in individual components
- Better error handling and debugging

## Implementation Status

| Agent                     | Status      | Owner   | MCP Integration |
| ------------------------- | ----------- | ------- | --------------- |
| Assessment Agent          | ✅ Ready    | Reouven | ✅              |
| Finding Agent             | ✅ Ready    | Yuri    | ✅              |
| Template Generator Agent  | ✅ Complete | -       | ⚠️ Optional     |
| Dashboard Generator Agent | ✅ Ready    | -       | ⚠️ Optional     |
| Cyber Agent               | ✅ Ready    | Kevyn   | ❌              |
| KB Agent                  | ✅ Ready    | Arik    | ❌              |

## Integration Points

### Tool Integration

- **BAS Tools**: Used by Assessment Agent for data retrieval
- **Knowledge Base Tools**: Used by KB Agent for documentation
- **API Tools**: Used by KB Agent for API documentation

### Data Exchange

- Agents communicate through structured data formats
- Clear input/output specifications for each agent
- Standardized error handling and validation

## Next Steps

1. **Implement Individual Agents**: Create concrete implementations for each agent
2. **Integration Testing**: Test inter-agent communication and data flow
3. **Performance Optimization**: Optimize agent performance and resource usage
4. **Documentation**: Create detailed implementation guides for each agent
5. **Monitoring**: Implement monitoring and logging for agent performance
