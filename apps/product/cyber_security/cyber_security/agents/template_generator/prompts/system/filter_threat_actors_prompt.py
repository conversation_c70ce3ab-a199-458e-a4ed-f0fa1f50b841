from langchain_core.prompts import PromptTemplate

filter_threat_actors_prompt = PromptTemplate(
    template=(
        """
    ### **Role: Filter Threat Actors By user_query**
    Based on the given threat context, return a JSON object with the following structure:
    {output_schema}
    Context (threat_context): {threat_context}
    Input (user_query): {user_query}
    """
    ),
    input_variables=["threat_context", "user_query", "output_schema"]
)
