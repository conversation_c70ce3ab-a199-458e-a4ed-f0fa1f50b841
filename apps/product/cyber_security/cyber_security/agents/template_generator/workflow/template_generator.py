import os
from typing import Union

from langgraph.checkpoint.base import Base<PERSON>heckpointSaver
from langgraph.graph.state import CompiledStateGraph
from ....workflow.configuration import Configuration
from langgraph.graph import StateGraph, END, START

from ..graph.agent_state import AssessmentGeneratorAgentState
from ..graph.graph_state import determine_next_step, determine_start_next_step
from ..models.agent_enum import NextStepEnum
from ..agents import ClassifierAgent, TemplateBuilderAgent, ThreatQueryAgent
from ..agents.free_text_agent import FreeTextAgent
from ..agents.pre_requirements_agent import PreRequirementsAgent


def base_workflow(checkpointer: Union[BaseCheckpointSaver, None]) -> CompiledStateGraph:
    workflow_builder = StateGraph(AssessmentGeneratorAgentState, config_schema=Configuration)
    
    # Add nodes
    workflow_builder.add_node(NextStepEnum.FREE_TEXT_AGENT.value, FreeTextAgent())
    workflow_builder.add_node(NextStepEnum.PRE_REQUIREMENTS_AGENT.value, PreRequirementsAgent())
    workflow_builder.add_node(NextStepEnum.CLASSIFIER_AGENT.value, ClassifierAgent())
    workflow_builder.add_node(NextStepEnum.THREAT_QUERY_AGENT.value, ThreatQueryAgent())
    workflow_builder.add_node(NextStepEnum.TEMPLATE_BUILDER_AGENT.value, TemplateBuilderAgent())
    
    # Add edges
    workflow_builder.add_conditional_edges(
        START,
        determine_start_next_step,
        {
            NextStepEnum.PRE_REQUIREMENTS_AGENT.value: NextStepEnum.PRE_REQUIREMENTS_AGENT.value,
            NextStepEnum.FREE_TEXT_AGENT.value: NextStepEnum.FREE_TEXT_AGENT.value,
            END: END,
        },
    )
    
    workflow_builder.add_conditional_edges(
        NextStepEnum.PRE_REQUIREMENTS_AGENT.value,
        determine_next_step,
        {
            NextStepEnum.TEMPLATE_BUILDER_AGENT.value: NextStepEnum.TEMPLATE_BUILDER_AGENT.value,
            NextStepEnum.CLASSIFIER_AGENT.value: NextStepEnum.CLASSIFIER_AGENT.value,
            NextStepEnum.PRE_REQUIREMENTS_AGENT.value: NextStepEnum.PRE_REQUIREMENTS_AGENT.value,
            END: END,
            NextStepEnum.THREAT_QUERY_AGENT.value: NextStepEnum.THREAT_QUERY_AGENT.value,
        },
    )
    
    workflow_builder.add_conditional_edges(
        NextStepEnum.FREE_TEXT_AGENT.value,
        determine_next_step,
        {
            NextStepEnum.TEMPLATE_BUILDER_AGENT.value: NextStepEnum.TEMPLATE_BUILDER_AGENT.value,
            END: END,
        },
    )
    
    workflow_builder.add_conditional_edges(
        NextStepEnum.CLASSIFIER_AGENT.value,
        determine_next_step,
        {
            NextStepEnum.THREAT_QUERY_AGENT.value: NextStepEnum.THREAT_QUERY_AGENT.value,
            NextStepEnum.TEMPLATE_BUILDER_AGENT.value: NextStepEnum.TEMPLATE_BUILDER_AGENT.value,
        },
    )
    
    workflow_builder.add_conditional_edges(
        NextStepEnum.THREAT_QUERY_AGENT.value,
        determine_next_step,
        {
            NextStepEnum.TEMPLATE_BUILDER_AGENT.value: NextStepEnum.TEMPLATE_BUILDER_AGENT.value,
            END: END,
        },
    )
    
    workflow_builder.add_conditional_edges(
        NextStepEnum.TEMPLATE_BUILDER_AGENT.value,
        determine_next_step,
        {
            NextStepEnum.PRE_REQUIREMENTS_AGENT.value: NextStepEnum.PRE_REQUIREMENTS_AGENT.value,
            END: END,
        },
    )
    
    return workflow_builder.compile(checkpointer=checkpointer, name="template_generator_agent")


if os.getenv("LANGGRAPH_UI_BUNDLER") == "true":
    pass

compiled_workflow = base_workflow(checkpointer=None)