import os
from typing import List

from corellm.providers.provider import <PERSON><PERSON>rovider
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from logger import logger

from ..graph.agent_state import AssessmentGeneratorAgentState
from ..models.agent_enum import NextStepEnum
from ..models.compliance_mapper import ComplianceMapper
from ..models.pre_requirements import (
    PreRequirementsAgentEnum,
    PreRequirementsSchema,
    output_schema,
    pre_requirements_output_parser,
)
from ....models.user_data import UserProfile

# Simple LLM instance without AgentExecutor
llm = ModelProvider().get_llm(
    model_name=os.getenv("MODEL_NAME", "azure_model_2"),
    temperature=0.75
)

async def PreRequirementsAgent(state: AssessmentGeneratorAgentState, config: RunnableConfig) -> AssessmentGeneratorAgentState:
    """ Minimal agent for pre-requirements workflow without AgentExecutor. """
    if "configurable" not in config or "session_id" not in config["configurable"]:
        config["configurable"] = {"session_id": "default_session"}

    # Simple prompt without complex agent framework
    prompt = f"""
    You are a cybersecurity assessment expert. Analyze the user input and extract pre-requirements.
    
    User Input: {state.user_input}
    
    Extract and structure the following information:
    - Organization details
    - Security controls
    - Platforms (AWS, Azure, GCP, etc.)
    - Operating systems
    - Compliance frameworks
    - APT groups of concern
    
    Output format: {output_schema}
    """

    try:
        response = await llm.ainvoke(prompt)
        parsed_output = PreRequirementsSchema(**pre_requirements_output_parser.parse(response.content))
        # Log specific fields instead of entire object to avoid serialization issues
        logger.info(f"Pre-requirements parsed: success={parsed_output.success}, "
                   f"organization={getattr(parsed_output, 'organization_name', 'N/A')}, "
                   f"apt_groups_count={len(getattr(parsed_output, 'apt_groups', []))}")

        if not parsed_output.success:
            state.next_step = NextStepEnum(PreRequirementsAgentEnum.END)
        else:
            if parsed_output.compliance_frameworks:
                compliance_frameworks_mapper = ComplianceMapper(compliance_frameworks=parsed_output.compliance_frameworks)
                additional_security_control = compliance_frameworks_mapper.map_to_controls()
                if additional_security_control:
                    parsed_output.security_controls = list(set((parsed_output.security_controls or []) + additional_security_control))

            state.next_step = NextStepEnum(PreRequirementsAgentEnum.THREAT_QUERY_AGENT)
            state.pre_requirements = parsed_output
            
            if parsed_output.compliance_frameworks:
                state.pre_requirements.apt_groups = []

    except Exception as e:
        logger.error(f"Error in PreRequirementsAgent: {e}")
        state.next_step = NextStepEnum(PreRequirementsAgentEnum.END)

    return state