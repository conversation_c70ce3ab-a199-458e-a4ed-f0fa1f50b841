import os

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm.providers.provider import <PERSON><PERSON>rovider
from langchain_core.runnables import RunnableConfig

from ..graph.agent_state import (
    AssessmentGeneratorAgentState,
)
from ..models.agent_enum import NextStepEnum
from ..models.pre_requirements import (
    PreRequirementsAgentEnum,
)
from ..services.scenario_free_text_service import (
    generate_template_title,
    similar_scenarios,
)
from ..services.utils import (
    extract_text_from_html,
    extract_url_if_single_line,
    fetch_url,
)


class FreeTextAgent(BaseAgentLLM):
    def __init__(self):
        super().__init__(
            name="free_text_agent",
            description="Free text agent",
            llm=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME", "azure_model_2"), temperature=0.1
            ),
            prompt_name="CyberSecurity/template_generator/free_text"
        )

    async def execute(
        self, state: AssessmentGeneratorAgentState, config: RunnableConfig
    ) -> AssessmentGeneratorAgentState:
        """The agent for the free text workflow."""
        # Make session_id optional for development
        if "configurable" not in config:
            config["configurable"] = {}
        if "session_id" not in config["configurable"]:
            config["configurable"]["session_id"] = "dev_session"

        url = extract_url_if_single_line(state.user_input)
        if url:
            response = await fetch_url(url)
            article = extract_text_from_html("free_text", response.text)
        else:
            article = state.user_input

        results = await similar_scenarios(state.event_data, article)

        if not results.success:
            state.success = results.success
            state.error_message = results.error
            state.next_step = NextStepEnum(PreRequirementsAgentEnum.END.value)
        else:
            state.success = results.success
            state.scenarios = results.results
            state.template_name = await generate_template_title(article)
            state.next_step = NextStepEnum(
                PreRequirementsAgentEnum.TEMPLATE_BUILDER_AGENT.value
            )

            self.write_stream(
                type="update",
                data={
                    "event_data": state.event_data,
                    "message": f":loading-2: Finalizing... (template name: {state.template_name})",
                },
            )

        return state
