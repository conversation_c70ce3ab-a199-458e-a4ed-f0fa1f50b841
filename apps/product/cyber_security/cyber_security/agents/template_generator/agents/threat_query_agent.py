import os

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm.providers.provider import Model<PERSON>rovider
from langchain_core.runnables import RunnableConfig
from logger import logger

from ..graph.agent_state import (
    AssessmentGeneratorAgentState,
)
from ..models.agent_enum import NextStepEnum
from ..tools.threat_query_tool import (
    threat_query_tool,
)


class ThreatQueryAgent(BaseAgentLLM):
    def __init__(self):
        super().__init__(
            name="threat_query_agent",
            description="Threat query agent",
            llm=ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME", "azure_model_2")),
            prompt_name="CyberSecurity/template_generator/threat_query"
        )

    async def execute(self, state: AssessmentGeneratorAgentState, config: RunnableConfig) -> AssessmentGeneratorAgentState:
        """ The agent for the threat query workflow. """
        # Make session_id optional for development
        if "configurable" not in config:
            config["configurable"] = {}
        if "session_id" not in config["configurable"]:
            config["configurable"]["session_id"] = "dev_session"

        try:
            if state.pre_requirements and not getattr(state.pre_requirements, 'apt_groups', None):
                main_apt_groups, apt_groups = await threat_query_tool.ainvoke({
                    "pre_requirements": state.pre_requirements
                })

                # Ensure data is properly converted to lists
                state.pre_requirements.main_apt_groups = list(main_apt_groups) if main_apt_groups else []
                state.pre_requirements.apt_groups = list(apt_groups) if apt_groups else []
        except Exception as e:
            logger.error(f"Error retrieving APT groups: {e}")
            # Create minimal pre_requirements if None
            if not state.pre_requirements:
                from ..models.pre_requirements import PreRequirementsSchema
                state.pre_requirements = PreRequirementsSchema(team_size=30)  # MEDIUM team size

        state.next_step = NextStepEnum.TEMPLATE_BUILDER_AGENT
        return state
