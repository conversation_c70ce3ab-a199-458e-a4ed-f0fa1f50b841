import os
from typing import List

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm.providers.provider import Model<PERSON>rovider
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from logger import logger

from ..graph.agent_state import (
    AssessmentGeneratorAgentState,
)
from ..llm.agent import create_agent
from ..models.agent_enum import NextStepEnum
from ..models.compliance_mapper import (
    ComplianceMapper,
)
from ..models.pre_requirements import (
    PreRequirementsAgentEnum,
    PreRequirementsSchema,
    output_schema,
    pre_requirements_output_parser,
)
from ..prompts.system.pre_requirements_prompt import (
    pre_requirements_prompt,
)
from ..tools import pre_requirements_tools
from ....models.user_data import UserProfile


class PreRequirementsAgent(BaseAgentLLM):
    def __init__(self):
        super().__init__(
            name="pre_requirements_agent",
            description="Pre requirements agent",
            llm=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME", "azure_model_2"),
                temperature=0.75
            ),
            prompt_name="CyberSecurity/template_generator/pre_requirements"
        )
        self.agent = create_agent(
            name="pre_requirements",
            tools=pre_requirements_tools,
            full_prompt=pre_requirements_prompt,
            model=self.llm
        )

    async def execute(self, state: AssessmentGeneratorAgentState, config: RunnableConfig) -> AssessmentGeneratorAgentState:
        """ The agent for the pre-requirements workflow. """
        # Make session_id optional for development
        if "configurable" not in config:
            config["configurable"] = {}
        if "session_id" not in config["configurable"]:
            config["configurable"]["session_id"] = "dev_session"

        # Check if we're waiting for user response (ignore supervisor transfer messages)
        if len(state.messages) > 0:
            # Check if the last message is from AI asking a question (not supervisor transfer)
            last_msg = state.messages[-1]
            if (hasattr(last_msg, 'content') and isinstance(last_msg.content, str) and 
                not any(msg.name == 'supervisor' for msg in state.messages if hasattr(msg, 'name')) and
                (not state.user_input or not state.user_input.strip())):
                # We're waiting for user response, end workflow to wait for input
                state.next_step = NextStepEnum.END.value
                return state

        # Convert messages to proper LangChain format, filtering out supervisor transfer messages
        chat_history = []
        for msg in state.messages:
            # Skip supervisor transfer messages that contain tool_calls
            if (hasattr(msg, 'name') and msg.name == 'supervisor') or \
               (hasattr(msg, 'tool_calls') and msg.tool_calls) or \
               (hasattr(msg, 'name') and 'transfer_to_template_generator_agent' in str(msg)):
                continue
                
            if hasattr(msg, 'content'):
                if isinstance(msg.content, str):
                    chat_history.append(msg)
                elif isinstance(msg.content, list) and len(msg.content) > 0:
                    # Convert new format to string
                    text_content = ""
                    for item in msg.content:
                        if isinstance(item, dict) and item.get('type') == 'text':
                            text_content += item.get('text', '')
                    if text_content:
                        if hasattr(msg, 'type') and msg.type == 'human':
                            chat_history.append(HumanMessage(content=text_content))
                        else:
                            chat_history.append(AIMessage(content=text_content))
        
        # Add current user input if not already in chat_history
        if state.user_input and state.user_input.strip():
            # Check if user input is already in chat_history
            user_input_exists = any(
                isinstance(msg, HumanMessage) and msg.content == state.user_input 
                for msg in chat_history
            )
            if not user_input_exists:
                chat_history.append(HumanMessage(content=state.user_input))
        
        try:
            response = await self.agent.ainvoke({
                "input_text": state.user_input or "Create a security assessment template",
                "chat_history": chat_history,
                "output_schema": output_schema,
            })
            
            parsed_output: PreRequirementsSchema = PreRequirementsSchema(**pre_requirements_output_parser.parse(response.get('output')))
            logger.info(f"PreRequirementsAgent parsed_output: success={parsed_output.success}, next_step={parsed_output.next_step}")
            
            if not parsed_output.success:
                # Agent is asking a question - return the question and end workflow to wait for response
                state.messages = [AIMessage(content=parsed_output.user_query)]
                state.next_step = NextStepEnum.END.value
                logger.info(f"Asking question and ending workflow to wait for response: {parsed_output.user_query}")
            else:
                # Agent has collected all requirements - process and move to next step
                if parsed_output.compliance_frameworks:
                    compliance_frameworks_mapper = ComplianceMapper(compliance_frameworks=parsed_output.compliance_frameworks)
                    additional_security_control = compliance_frameworks_mapper.map_to_controls()

                    if additional_security_control:
                        parsed_output.security_controls = list(set(parsed_output.security_controls or [] + additional_security_control))

                state.next_step = NextStepEnum(PreRequirementsAgentEnum.THREAT_QUERY_AGENT.value)
                state.pre_requirements = parsed_output

                if parsed_output.compliance_frameworks:
                    state.pre_requirements.apt_groups = []

                logger.info(f"Requirements collected, moving to next step: {state.next_step}")
                
        except Exception as e:
            logger.error(f"PreRequirementsAgent LLM error: {e}")
            # Ask the initial question when LLM fails
            initial_question = """Hi! I'm here to help you create a tailored attack simulation to uncover and validate your organization's exposure.
To get started, could you let me know what you'd like to focus on? Would you prefer to:
1. Test your cloud infrastructure for potential detection or prevention gaps.
2. Understand your exposure across security layers like EDR, email gateway, web gateway, IPS, WAF, and SIEM.
3. Ensure compliance with regulatory frameworks such as ISO 27001, PCI-DSS, HIPAA, NERC CIP?
Understanding your priorities will help us design the most relevant simulation for your environment."""
            
            state.messages = [AIMessage(content=initial_question)]
            state.next_step = NextStepEnum.END.value
            return state

        return state


    def create_user_profile_message(self, user_profile: UserProfile, chat_history: List[BaseMessage]) -> List[BaseMessage]:
        if not user_profile or any(message.id == "user_profile_message" for message in chat_history):
            return chat_history

        ai_details = user_profile.client_id.aiDetails.model_dump() if user_profile.client_id and user_profile.client_id.aiDetails else {}
        organization = user_profile.client_id.name if user_profile.client_id else None

        countries = ", ".join(ai_details.get("countries") or [])
        industries = ", ".join(ai_details.get("industries") or [])

        metadata_content = []
        if organization:
            metadata_content.append(f"Organization: {organization}")
        if countries:
            metadata_content.append(f"Countries: {countries}")
        if industries:
            metadata_content.append(f"Industries: {industries}")

        if metadata_content:
            content = "User Profile:\n" + "\n".join(metadata_content)
            user_profile_message = HumanMessage(content=content, id="user_profile_message")
            chat_history = [user_profile_message] + chat_history

        return chat_history