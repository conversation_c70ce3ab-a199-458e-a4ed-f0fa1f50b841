import os

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm.providers.provider import <PERSON><PERSON>rovider
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.runnables import RunnableConfig
from logger import logger

from ..graph.agent_state import (
    AssessmentGeneratorAgentState,
)
from ..models.agent_enum import NextStepEnum
from ..models.classifier import ClassifierSchema
from ..prompts.system import classifier_prompt

classifier_output_parser = JsonOutputParser(pydantic_object=ClassifierSchema)


class ClassifierAgent(BaseAgentLLM):
    def __init__(self):
        super().__init__(
            name="classifier_agent",
            description="Classifier agent",
            llm=ModelProvider().get_llm(
                model_name=os.getenv("MODEL_NAME", "azure_model_2"),
                temperature=0.1
            ),
            prompt_name="CyberSecurity/template_generator/classifier_prompt"
        )

    async def execute(self, state: AssessmentGeneratorAgentState, config: RunnableConfig) -> AssessmentGeneratorAgentState:
        """The agent for the classifier workflow."""
        # Make session_id optional for development
        if "configurable" not in config:
            config["configurable"] = {}
        if "session_id" not in config["configurable"]:
            config["configurable"]["session_id"] = "dev_session"

        formatted_prompt = classifier_prompt.format(
            output_schema=classifier_output_parser.get_format_instructions(),
            pre_requirements=state.pre_requirements,
        )

        response = await self.llm.ainvoke(formatted_prompt)

        response_content = response.content
        parsed_output: ClassifierSchema = ClassifierSchema(**classifier_output_parser.parse(response_content))
        logger.info(parsed_output)
        state.next_step = NextStepEnum(parsed_output.next_step.value)

        return state

