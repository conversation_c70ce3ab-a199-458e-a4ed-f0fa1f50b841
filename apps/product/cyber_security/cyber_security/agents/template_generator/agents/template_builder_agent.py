import json
import os

from corelanggraph.agents.base_agent_llm import BaseAgentLLM
from corellm.providers.provider import Model<PERSON>rovider
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig
from logger import logger

from ..graph.agent_state import (
    AssessmentGeneratorAgentState,
)
from ..models.agent_enum import NextStepEnum
from ..tools.bas2_scenarios_retrieve_tool import (
    analyze_scenarios,
    bas2_scenarios_retrieve_tool,
)


class TemplateBuilderAgent(BaseAgentLLM):
    def __init__(self):
        super().__init__(
            name="template_builder_agent",
            description="Template builder agent",
            llm=ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME", "azure_model_2")),
            prompt_name="CyberSecurity/template_generator/template_builder"
        )

    async def execute(self, state: AssessmentGeneratorAgentState, config: RunnableConfig) -> AssessmentGeneratorAgentState:
        """ The agent for the template builder workflow. """
        # Make session_id optional for development
        if "configurable" not in config:
            config["configurable"] = {}
        if "session_id" not in config["configurable"]:
            config["configurable"]["session_id"] = "dev_session"

        if state.classification == "template_generator_free_text":
            template_name, summary, description, scenarios =  await analyze_scenarios(
                scenarios=[json.loads(scenario.model_dump_json()) for scenario in state.scenarios],
                template_name=state.template_name
            )
        else:
            logger.info(state.pre_requirements)
            # Handle None values
            if not state.pre_requirements:
                from ..models.pre_requirements import PreRequirementsSchema
                state.pre_requirements = PreRequirementsSchema(team_size=30)  # MEDIUM team size
            if not state.metadata:
                state.metadata = {}
                
            template_name, summary, description, scenarios = await bas2_scenarios_retrieve_tool.ainvoke({
                "pre_requirements": state.pre_requirements,
                "metadata": state.metadata,
            }, config)

            if not scenarios:
                logger.warning("No scenarios found for the given pre-requirements.")
                state.messages += [AIMessage(content="No scenarios found. Please refine your query and try again.")]
                state.next_step = NextStepEnum.PRE_REQUIREMENTS_AGENT
                return state

        scenario_ids = list({scenario.get("id") for scenario in scenarios})
        security_controls = self.extract_distinct_values([scenario for scenario in scenarios], "securityControls")
        platforms = self.extract_distinct_values([scenario for scenario in scenarios], "platforms")
        modules = self.extract_distinct_values([scenario for scenario in scenarios], "basTags", "value", filter_key="type", filter_value="module")

        state.docs = [{
            "name": template_name,
            "description": description,
            "type": "template_generator",
            "scenarios": scenario_ids,
            "securityControls": security_controls,
            "platforms": platforms,
            "modules": modules
        }]

        state.messages = state.messages + [
            AIMessage(content=summary),
        ]

        state.is_final = True
        state.next_step = NextStepEnum.END

        return state

    def extract_distinct_values(self, data, key, nested_key=None, filter_key=None, filter_value=None):
        extracted = set()
        for item in data:
            values = item.get(key, [])
            if nested_key:
                if filter_key and filter_value:
                    values = [
                        v.get(nested_key) for v in values
                        if v.get(filter_key) == filter_value
                    ]
                else:
                    values = [v.get(nested_key) for v in values if nested_key in v]
            elif filter_key and filter_value:
                values = [v for v in values if v.get(filter_key) == filter_value]
            extracted.update(values)
        return list(extracted)
