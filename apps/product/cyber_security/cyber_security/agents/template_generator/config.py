import configparser

config = configparser.ConfigParser()
config.read('config.cfg')

CYMULATE_API_KEY = config['API_KEYS']['CYMULATE_API_KEY']
OPENAI_API_KEY = config['API_KEYS']['OPENAI_API_KEY']
PREFFERED_GPT_MODEL = config['DEFAULTS']['PREFFERED_GPT_MODEL']
DATABASE_PATH = config['DEFAULTS']['DATABASE_PATH']
INFORMATION_ASSISTANT = config['ASSISTANTS']['INFORMATION_ASSISTANT']
REQUEST_ASSISTANT = config['ASSISTANTS']['REQUEST_ASSISTANT']
QUERY_ASSISTANT = config['ASSISTANTS']['QUERY_ASSISTANT']
