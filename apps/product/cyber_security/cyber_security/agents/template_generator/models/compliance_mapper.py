from typing import List

from pydantic import BaseModel

from cyber_security.agents.template_generator.models.general_enum import (
    COMPLIANCE_DATA,
    CONTROL_MAPPING,
    ComplianceFrameworkEnum,
    SecurityControlsEnum,
)


class ComplianceMapper(BaseModel):
    compliance_frameworks: List[ComplianceFrameworkEnum]

    def map_to_controls(self) -> List[SecurityControlsEnum]:
        """
        Map selected compliance_frameworks to a list of unique SecurityControlsEnum.
        """
        controls = set()
        for framework in self.compliance_frameworks:
            raw_controls = COMPLIANCE_DATA.get(framework, [])
            for control in raw_controls:
                mapped_control = CONTROL_MAPPING.get(control.strip().upper())
                if mapped_control:
                    controls.add(mapped_control)
        return list(controls)
