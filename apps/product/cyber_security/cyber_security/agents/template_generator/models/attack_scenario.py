from datetime import datetime
from typing import List, Optional

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    constr,
    field_validator,
    model_validator,
)
from pydantic_core.core_schema import ValidationInfo

from cyber_security.agents.template_generator.models.general_enum import (
    PlatformEnum,
    SecurityControlsEnum,
)


class CoreTag(BaseModel):
    type: str
    value: str

    @field_validator('type')
    def validate_type(cls, v):
        valid_types = { 'attack-type', 'classification', 'db', 'extension', 'module', 'purple-team', 'research', 'threat-type', 'user-tags', 'web-policy' }
        if v not in valid_types:
            raise ValueError(f"Invalid type: {v}. Must be one of {valid_types}.")
        return v

class TemplateMigrationData(BaseModel):
    payload: str
    executionMethod: str
    stager: str
    isPacked: bool

class AttackScenarioSchema(BaseModel):
    model_config = ConfigDict(
        validate_default=True,
        populate_by_name=True,
        extra="forbid",
    )

    id: str = Field(alias="_id")
    aptGroups: List[str]
    coreTags: List["CoreTag"]
    createdAt: datetime
    createdBy: Optional[str]
    customQueryKey: List[str]
    cves: List[str]
    description: str
    iocs: List[str]
    isElevated: bool
    isShared: bool
    name: str
    platforms: List[str]
    public: bool
    risk: int
    securityControls: List[str]
    software: List[str]
    tactics: List[str]
    techniques: List[str]
    templateMigrationData: TemplateMigrationData
    visible: bool
    actionsCount: int
    attack_type: List[str] = Field(alias="attack-type")
    module: List[str]
    avg_run_time: int
    status: str

    @field_validator('risk')
    def validate_risk(cls, v):
        if not (1 <= v <= 5):
            raise ValueError("Risk must be between 1 and 5.")
        return v

    @field_validator('createdBy', mode='before')
    def preprocess_created_by(cls, v):
        return None if v == "null" else v

    @field_validator('platforms')
    def validate_platforms(cls, v):
        valid_platforms = {'windows', 'linux', 'macos'}
        invalid_platforms = set(v) - valid_platforms
        if invalid_platforms:
            raise ValueError(f"Invalid platforms: {', '.join(invalid_platforms)}. Must be one of {valid_platforms}.")
        return v

    @field_validator('status')
    def validate_status(cls, v):
        valid_statuses = {'not prevented', 'prevented', 'unknown'}
        if v not in valid_statuses:
            raise ValueError(f"Invalid status: {v}. Must be one of {valid_statuses}.")
        return v

    @model_validator(mode='before')
    def preprocess_data(cls, values):
        # Example preprocessing: Ensure 'createdBy' is None if it's the string "null"
        if values.get('createdBy') == "null":
            values['createdBy'] = None
        return values

    @model_validator(mode='after')
    def postprocess_data(cls, values):
        risk = values.get('risk')
        if risk is not None and not (1 <= risk <= 5):
            raise ValueError("Risk must be between 1 and 5.")
        return values

class Scenario(BaseModel):
    name: str
    description: constr(max_length=1_000)
    platforms: Optional[List[PlatformEnum]] = None

    @staticmethod
    def _coerce_enum_list(v, enum_cls):
        if v is None:
            return v
        if not isinstance(v, list):
            raise TypeError("expected a list")
        cleaned = []
        for item in v:
            if isinstance(item, str):
                match = next(
                    (e for e in enum_cls if e.value.lower() == item.lower()), None
                )
                if match:
                    cleaned.append(match)
            else:
                cleaned.append(item)
        return cleaned or None

    @field_validator("platforms", mode="before")
    def _valid_platforms(cls, v, info: ValidationInfo):
        return cls._coerce_enum_list(v, PlatformEnum)

class FilteredScenarioResult(BaseModel):
    group: str
    name: str
    securityControls: List[SecurityControlsEnum] = Field(
        default_factory=list,
        description="Security controls applicable to the scenario",
    )
    platforms: List[PlatformEnum] = Field(
        default_factory=list,
        description="Platforms applicable to the scenario",
    )
    basTags: List[CoreTag] = Field(
        default_factory=list,
        description="Tags associated with the scenario",
    )
    id: str

class FilteredScenarioResults(BaseModel):
    scenarios: List[FilteredScenarioResult]

class ArticleResponse(BaseModel):
    success: bool
    scenario: Optional[Scenario] = None
    embedding_texts: Optional[List[str]] = Field(
        default=None, description="≤ 20 single‑line summaries"
    )
    error: Optional[str] = None

class FreeTextResponse(ArticleResponse):
    results: List[FilteredScenarioResult] = Field(
        default_factory=list,
        description="≤ 20 single‑line summaries",
    )