from enum import Enum


class TeamSize(Enum):
    SMALL = 15 # 15 hours
    MEDIUM = 30 # 30 hours
    LARGE = 60 # 60 hours
    ENTERPRISE = 100 # 100 hours

    @property
    def available_minutes(self) -> int: # return hours in minutes
        return self.value * 60

    @staticmethod
    def from_string(size: str) -> "TeamSize":
        size_map = {
            "small": TeamSize.SMALL,
            "medium": TeamSize.MEDIUM,
            "large": TeamSize.LARGE,
            "enterprise": TeamSize.ENTERPRISE
        }
        return size_map.get(size.lower(), TeamSize.SMALL)

class CadenceEnum(Enum):
    """Enumeration of possible cadence values."""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    #YEARLY = "yearly"

class PlatformEnum(Enum):
    """
    Enumeration for different platforms.
    """
    ALIBABA_CLOUD = "alibaba_cloud"
    APPLICATION_SERVERS = "application_servers"
    AWS = "aws"
    AZURE = "azure"
    CENTOS = "centos"
    CLOUD = "cloud"
    CONTAINERS = "containers"
    DEBIAN = "debian"
    DIGITAL_OCEAN = "digital_ocean"
    DOCKER = "docker"
    GCP = "gcp"
    IAAS = "iaas"
    JAVA = "java"
    KUBERNETES = "kubernetes"
    LDAP = "ldap"
    UBUNTU = "ubuntu"
    LINUX = "linux"
    MAC = "mac"
    MICROSOFT_EXCHANGE = "microsoft_exchange"
    MONGODB = "mongodb"
    MSSQL = "mssql"
    MYSQL = "mysql"
    NOSQL = "nosql"
    ORACLE = "oracle"
    PHP = "php"
    POSTGRES_SQL = "postgres_sql"
    REDHAT = "redhat"
    SQL = "sql"
    WEB_BROWSERS = "web_browsers"
    WINDOWS = "windows"


class OSEnum(Enum):
    """Enumeration of possible operating systems."""
    WINDOWS = "windows"
    LINUX = "linux"
    MACOS = "mac"

class SectorsEnum(Enum):
    UNKNOWN = "Unknown"
    TRANSPORTATION_SHIPPING = "Transportation & Shipping"
    AUTOMOTIVE = "Automotive"
    RETAIL = "Retail"
    BANKING_FINANCIAL_WEALTH_MANAGEMENT = "Banking/Financial/Wealth Management"
    CONSTRUCTION = "Construction"
    VARIOUS = "Various"
    GOVERNMENT = "Government"
    OUTSOURCING_HOSTING = "Outsourcing & Hosting"
    WHOLESALE = "Wholesale"
    BUSINESS_SERVICES = "Business Services"
    PROCESS_MANUFACTURING = "Process Manufacturing"
    SOFTWARE = "Software"
    TELECOM = "Telecom"
    EDUCATION = "Education"
    INDUSTRIAL = "Industrial"
    HEALTHCARE = "Healthcare"
    AVIATION = "Aviation"
    MANUFACTURING = "Manufacturing"
    TECHNOLOGY_IT = "Technology/IT"
    CONSUMER_PRODUCTS = "Consumer Products"
    MEDIA_COMMUNICATIONS = "Media & Communications"
    ENERGY_OIL_GAS = "Energy/Oil & Gas"
    INSURANCE = "Insurance"
    PHARMA = "Pharma"
    UTILITIES = "Utilities"
    SERVICES = "Services"
    ACCOUNTING_LEGAL = "Accounting & Legal"
    DISCRETE_MANUFACTURING = "Discrete Manufacturing"
    FOOD_BEVERAGES = "Food/Beverages"
    PERSONAL_SERVICES = "Personal Services"


class ComplianceFrameworkEnum(Enum):
    ISO = "ISO"
    PCI = "PCI"
    COOP = "COOP"
    HIPAA = "Hipaa"
    SOC2 = "SOC2"
    DORA = "DORA"

class SecurityControlsEnum(Enum):
    ANTIVIRUS = "Antivirus"
    CLOUD_INFRASTRUCTURE_DETECTION = "Cloud Infrastructure Detection"
    CLOUD_WORKLOAD_RUNTIME_SECURITY = "Cloud Workload Runtime Security"
    #CONTENT_DATA_LOSS_PREVENTION = "Content Data Loss Prevention"
    EMAIL_GATEWAY = "Email Gateway"
    ENDPOINT_DETECTION_AND_RESPONSE = "Endpoint Detection and Response"
    INTRUSION_PREVENTION_DETECTION_SYSTEM = "Intrusion Prevention / Detection System"
    KUBERNETES_RUNTIME_SECURITY = "Kubernetes Runtime Security"
    SIEM = "SIEM"
    SOAR = "SOAR"
    WEB_APPLICATION_FIREWALL = "Web Application Firewall"
    WEB_GATEWAY = "Web Gateway"

COMPLIANCE_DATA = {
    ComplianceFrameworkEnum.ISO: [
        "EDR", "Firewall", "Web Gateway", "Email Gateway", "SIEM", "SOAR", "IPS-IDS"
    ],
    ComplianceFrameworkEnum.PCI: [
        "EDR", "Firewall", "WG", "EG", "HostIPS", "NetworkPS", "WAF", "SOAR", "SIEM"
    ],
    ComplianceFrameworkEnum.COOP: [
        "Unified Endpoint Management", "WAF", "SIEM", "EDR", "Network IPS"
    ],
    ComplianceFrameworkEnum.HIPAA: [
        "SIEM", "SOAR", "EG", "WG", "Firewall", "IPS-IDS", "EDR"
    ],
    ComplianceFrameworkEnum.SOC2: [
        "AV", "EDR", "Firewall", "IPS-IDS", "WAF", "SIEM", "SOAR", "Unified Endpoint Management", "Network IPS"
    ],
    ComplianceFrameworkEnum.DORA: [
        "SIEM", "SOAR"
    ],
}

CONTROL_MAPPING = {
    "EDR": SecurityControlsEnum.ENDPOINT_DETECTION_AND_RESPONSE,
    "FIREWALL": SecurityControlsEnum.WEB_APPLICATION_FIREWALL,
    "WEB GATEWAY": SecurityControlsEnum.WEB_GATEWAY,
    "EMAIL GATEWAY": SecurityControlsEnum.EMAIL_GATEWAY,
    "SIEM": SecurityControlsEnum.SIEM,
    "SOAR": SecurityControlsEnum.SOAR,
    "IPS-IDS": SecurityControlsEnum.INTRUSION_PREVENTION_DETECTION_SYSTEM,
    "HOSTIPS": SecurityControlsEnum.INTRUSION_PREVENTION_DETECTION_SYSTEM,
    "NETWORKPS": SecurityControlsEnum.INTRUSION_PREVENTION_DETECTION_SYSTEM,
    "WAF": SecurityControlsEnum.WEB_APPLICATION_FIREWALL,
    "AV": SecurityControlsEnum.ANTIVIRUS,
    "UNIFIED ENDPOINT MANAGEMENT": SecurityControlsEnum.ENDPOINT_DETECTION_AND_RESPONSE,
    "NETWORK IPS": SecurityControlsEnum.INTRUSION_PREVENTION_DETECTION_SYSTEM,
    #"DLP": None,
    #"VULNERABILITY MANAGEMENT": SecurityControlsEnum.CLOUD_INFRASTRUCTURE_DETECTION,
    #"VM": SecurityControlsEnum.CLOUD_WORKLOAD_RUNTIME_SECURITY,
}
