from enum import Enum
from typing import List, Optional

from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel, Field

from cyber_security.agents.template_generator.models.general_enum import (
    CadenceEnum,
    ComplianceFrameworkEnum,
    OSEnum,
    PlatformEnum,
    SectorsEnum,
    SecurityControlsEnum,
    TeamSize,
)


class PreRequirementsAgentEnum(Enum):
    """Enumeration of possible agent states."""
    THREAT_QUERY_AGENT = "threat_query_agent"
    CLASSIFIER_AGENT = "classifier_agent"
    TEMPLATE_BUILDER_AGENT = "template_builder_agent"
    END = "__end__"

class PreRequirementsSchema(BaseModel):
    """Schema representing the state and results of a classification process."""

    success: bool = Field(
        default=False,
        description="True if all necessary details are available; False otherwise."
    )

    next_step: PreRequirementsAgentEnum = Field(
        default=PreRequirementsAgentEnum.END,
        description="The next action based on available information."
    )

    text_query: str = Field(
        default="",
        description="Once all necessary details are collected, summarize the assessment in a structured and actionable format for similarity matching.",
    )

    user_query: str = Field(
        default="",
        description="If the user cannot provide certain details, infer data where possible and clearly indicate assumptions in the summary.",
    )

    organization_name: Optional[str] = Field(
        default=None,
        description="User's organization name for personalization."
    )

    countries: Optional[List[str]] = Field(
        default_factory=list,
        description="Primary operating countries in ISO 3166-1 alpha-2 format."
    )

    sectors: Optional[List[SectorsEnum]] = Field(
        default_factory=list,
        description="The sectors / Industries targeted by the organization.",
    )

    apt_groups: Optional[List[str]] = Field(
        default_factory=list,
        description="Concerned Advanced Persistent Threat (APT) groups. use this field for apt_groups if provided by the user."
    )

    main_apt_groups: Optional[List[str]] = Field(
        default_factory=list,
        description="Concerned Advanced Persistent Threat (APT) groups. Dont use this field for apt_groups!"
    )

    security_controls: Optional[List[SecurityControlsEnum]] = Field(
        default_factory=list,
        description="Security controls with in the organization."
    )

    compliance_frameworks: Optional[List[ComplianceFrameworkEnum]] = Field(
        default_factory=list,
        description="Compliance frameworks followed by the organization."
    )

    os: Optional[List[OSEnum]] = Field(
        default_factory=list,
        description="Operating systems used by the organization."
    )

    platforms: Optional[List[PlatformEnum]] = Field(
        default_factory=list,
        description="Platforms used by the organization. related to cloud providers, operating system, web application"
    )

    team_size: Optional[TeamSize] = Field(
        default=None,
        description="The size of the organization's security team."
    )

    cadence: Optional[CadenceEnum] = Field(
        default=CadenceEnum.WEEKLY,
        description="The frequency of simulations run by the organization."
    )

    assessment_summary: Optional[str] = Field(
        default=None,
        description="The summary of the assessment."
    )

    template_name: Optional[str] = Field(
        default=None,
        description="The name of the template."
    )

pre_requirements_output_parser = JsonOutputParser(pydantic_object=PreRequirementsSchema)
output_schema = pre_requirements_output_parser.get_format_instructions()
