from typing import List

from pydantic import BaseModel, Field


class FilterThreatActorsSchema(BaseModel):
    """
    Schema representing a threat actor in the list of threat actors.
    """

    id: str = Field(
        default="",
        description="The ID of the threat actor.",
    )

    enrichment: List[str]

    name: str = <PERSON>(
        default="",
        description="The name of the threat actor.",
    )

    description: str = Field(
        default="",
        description="The description of the threat actor.",
    )


class FilterThreatActorsListSchema(BaseModel):
    actors: list[FilterThreatActorsSchema]