from enum import Enum

from pydantic import BaseModel, Field


class ClassifierAgentEnum(Enum):
    """Enumeration of possible agent states."""
    THREAT_QUERY_AGENT = 'threat_query_agent'
    TEMPLATE_BUILDER_AGENT = 'template_builder_agent'


class ClassifierSchema(BaseModel):
    """The schema for the classifier agent."""

    next_step: ClassifierAgentEnum = Field(
        default=ClassifierAgentEnum.THREAT_QUERY_AGENT,
        description="Specifies the next agent to execute in the workflow.",
    )
