import os

from corellm.providers.provider import ModelProvider
from langchain_community.tools import WikipediaQueryRun
from langchain_community.utilities import WikipediaAPIWrapper
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.tools import tool


@tool
def wiki_tool(company: str) -> str:
    """
    The tool for querying the Wikipedia based on the company (return suggestion for country and industry).

    Args:
        company: The company name.

    Returns:
        str: The Wikipedia response.
    """

    llm = ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME", "azure_model_2"), temperature=0.1)
    wikipedia = WikipediaQueryRun(api_wrapper=WikipediaAPIWrapper())

    wiki_response = wikipedia.run(company)
    if wiki_response != "No good Wikipedia Search Result was found":
        llm_response = llm.invoke([
            SystemMessage(content=f"I found some information about {company} on Wikipedia. try to extract country and industry.\n--------\nHere's a summary:"),
            HumanMessage(content=wiki_response)
        ])
        return llm_response.content
    else:
        return "No good Wikipedia Search Result was found"
