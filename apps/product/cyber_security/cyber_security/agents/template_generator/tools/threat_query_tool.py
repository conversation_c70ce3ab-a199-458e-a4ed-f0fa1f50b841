import os
from typing import Any

from corellm.providers.provider import ModelProvider
from langchain_core.embeddings import Embeddings
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.tools import tool
from langchain_elasticsearch import ElasticsearchStore
from logger import logger

from ..models.filter_threat_actors import (
    FilterThreatActorsListSchema,
)
from ..models.pre_requirements import (
    PreRequirementsSchema,
)
from ....infra.init import infra

embeddings: Embeddings = ModelProvider().get_embeddings(model_name=os.getenv("EMBEDDING_MODEL_NAME", "azure_text_embedding_3_small"))
threat_actor_store: ElasticsearchStore = ElasticsearchStore(
    es_connection=infra.elasticsearch_client,
    index_name=infra.get_secret("THREAT_ACTORS_INDEX", "cy-ai-threat-actors"),
    embedding=embeddings,
)

filter_threat_actors_output_parser = JsonOutputParser(pydantic_object=FilterThreatActorsListSchema)

@tool
async def threat_query_tool(pre_requirements: PreRequirementsSchema) -> tuple[list[str], list[str]]:
    """
    The tool for querying the threat actors based on the pre-requirements.

    Args:
        pre_requirements: The pre-requirements schema.

    Returns:
        List[str]: The list of APT groups.
    """
    targeted_sectors = [sector.value for sector in pre_requirements.sectors] if pre_requirements.sectors else []
    filters = {
        "bool": {
            "should": [
                {
                    "terms": {
                        "metadata.campaign_list.targeted_countries.keyword": pre_requirements.countries or []
                    }
                },
                {
                    "terms": {
                        "metadata.targeted_sectors.keyword": targeted_sectors
                    }
                },
                {
                    "terms": {
                        "metadata.name.keyword": pre_requirements.apt_groups or []
                    }
                },
                {
                    "terms": {
                        "metadata.synonyms.keyword": pre_requirements.apt_groups or []
                    }
                }
            ],
            "minimum_should_match": 1
        }
    }

    docs = await threat_actor_store.asimilarity_search(
        query=pre_requirements.text_query,
        vector_field="vector",
        k=15,
        score_threshold= 0.85,
        filter=filters,
        fetch_k=10000,
    )

    main_apt_groups = set()
    apt_groups = set()

    for doc in docs:
        name = doc.metadata.get("name", "")
        synonyms = doc.metadata.get("synonyms", [])
        apt_groups.update([name] + synonyms)
        main_apt_groups.add(name)

    apt_groups = sorted(list(apt_groups))

    logger.info(main_apt_groups)

    return list(main_apt_groups), apt_groups