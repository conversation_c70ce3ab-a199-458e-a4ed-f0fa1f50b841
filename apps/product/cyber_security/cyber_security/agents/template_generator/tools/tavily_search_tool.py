from langchain_tavily import TavilySearch

from cyber_security.infra.init import infra

tavily_search_tool = TavilySearch(
    tavily_api_key=infra.get_secret("TAVILY_APY_KEY"),
    max_results=5,
    include_images=False,
    include_answer=True,
    name="Internet_Cyber_Information_Search",
    description="A tool for querying <PERSON><PERSON>'s database to retrieve cyber threat intelligence, such as lists industries and countries.",
)
