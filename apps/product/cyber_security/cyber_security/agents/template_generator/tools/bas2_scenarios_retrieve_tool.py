import os
from collections import Counter
from typing import Any, List, Optional

from corellm.providers.provider import Model<PERSON>rovider
from langchain_core.documents import Document
from langchain_core.tools import tool
from langchain_elasticsearch import ElasticsearchStore
from logger import logger

from ..models.general_enum import TeamSize
from ..models.pre_requirements import (
    PreRequirementsSchema,
)
from ..services.scenario_service import (
    fetch_scenarios,
)
from ..services.selection_service import (
    select_diverse_scenarios,
)
from ..services.utils import (
    get_available_minutes,
)
from ....infra.init import infra
from ....models.metadata_generator import Metadata

bas2_scenarios_store: ElasticsearchStore = ElasticsearchStore(
    es_connection=infra.elasticsearch_client,
    index_name=os.getenv("BAS2_SCENARIOS_INDEX", "cy-ai-bas2-scenarios"),
    embedding=ModelProvider().get_embeddings(model_name=os.getenv("EMBEDDING_MODEL_NAME", "azure_text_embedding_3_small")),
)

llm = ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME", "azure_model_2"), temperature=0.1)


@tool
async def bas2_scenarios_retrieve_tool(pre_requirements: PreRequirementsSchema, metadata: Metadata) -> \
        list[Any] | tuple[Any, Any, Any, list[Document]]:
    """
    Tool to retrieve scenarios based on pre-requirements, ensuring at least the desired number of scenarios are collected
    by relaxing filters incrementally if necessary, and analyzing the scenarios using LLM.


    Args:
        pre_requirements (PreRequirementsSchema): The pre-requirements.
        metadata (Metadata): The metadata.

    Returns:
        List[Document]: The list of scenarios.
    """

    platforms = list({
        *extract_values(pre_requirements.os, "value"),
        *extract_values(pre_requirements.platforms, "value")
    })

    security_controls = extract_values(pre_requirements.security_controls, "value")
    apt_groups = list(set(pre_requirements.apt_groups or []))


    scenarios, used_time, unique_key_counts = await retrieve_scenarios(
        platforms=platforms,
        security_controls=security_controls,
        apt_groups=apt_groups,
        client_id=metadata.client_id,
        team_size=pre_requirements.team_size
    )

    if not scenarios:
        logger.warning("No scenarios found for the given pre-requirements.")
        return "", "", "", []

    logger.info(f"Final scenarios count: {len(scenarios)}")

    return await analyze_scenarios(scenarios, pre_requirements, used_time, unique_key_counts)


async def retrieve_scenarios(
    platforms: List[str],
    security_controls: List[str],
    apt_groups: List[str],
    client_id: str,
    team_size: TeamSize
) -> tuple[Any, Any, Counter[Any]]:
    """
    Retrieve scenarios by iterating over combinations of platform and security controls,
    then filter out duplicates using existing_ids set.
    """

    logger.info("Fetching scenarios...")
    scenarios = await fetch_scenarios(
        platforms,
        security_controls,
        apt_groups,
        client_id
    )

    logger.info("Selecting scenarios...")
    selected, used_time = await select_diverse_scenarios(scenarios, team_size)

    unique_key_counts = Counter(item['unique_key'] for item in selected)
    logger.info(f"Unique keys count: {unique_key_counts}")
    logger.info("Scenario selection complete")
    logger.info("\n=== Scenario Selection Summary ===")
    logger.info(f"Organization Size: {team_size} (available: {get_available_minutes(team_size)} minutes)")
    logger.info(f"Scenarios Selected: {len(selected)}")
    logger.info(f"Total Time Used: {used_time} minutes\n")

    for s in selected:
        s["id"] = s.get("_id")
        logger.info(s)

    return selected, used_time, unique_key_counts

async def analyze_scenarios(scenarios: List[Any], pre_requirements: PreRequirementsSchema = None, used_time: int = None, unique_key_counts = None, template_name=None) -> tuple[
    Any, Any, Any, list[Document]]:
    """Analyze scenarios using LLM for detailed insights."""
    summary = ""
    description = ""

    try:
        summary = (await llm.ainvoke(
            f"""
            You are a Cymulate Breach and Attack scenario template expert tasked with analyzing the provided template.
            Your analysis should address the following:
            security controls in scope, industry, targeted APT groups, operating systems, cloud provider, web application, attack scenarios, simulation volume, cadence, platforms, and compliance framework.
            
            ##Requirements**:
            - Summary: Provide a brief summary of the assessment template.
            
            ##Example:
            - **Total Time**: 60 minutes
            - **Security Controls in Scope**: EDR, email gateway, IPS
            - **Industry**: Energy
            - **Targeted APT Groups**: Dragonfly, Sandworm Team, APT28, and others targeting the energy sector - ** take main_apt_groups and ignore apt_groups **
            - **Operating Systems**: Windows and Linux
            - **Cloud Provider**: Azure
            - **Web Application**: Hosted on Azure with Kubernetes infrastructure
            - **Attack Scenarios**: 150 scenarios distributed across the MITRE ATT&CK framework, focusing on Kubernetes-specific and compliance-relevant TTPs
            - **Simulation Volume**: Medium, suited for a team of 15 analysts
            - **Cadence**: Weekly assessments
            - **Platforms**: Azure, Kubernetes, Windows, Linux
            - **Compliance Framework**: NERC CIP
            
            Summary
            This template provides a comprehensive simulation tailored for the energy sector, emphasizing critical areas like Kubernetes infrastructure and compliance-relevant techniques. It evaluates security controls such as EDR, email gateways, and IPS, simulating real-world threats from APT groups targeting the industry. The scenarios focus on TTPs aligned with the MITRE ATT&CK framework, ensuring effective security validation against advanced attacks. With a medium simulation volume and weekly cadence, it is well-suited for teams looking to maintain continuous improvement in their security posture while aligning with NERC CIP compliance requirements.
            
            Grouping: {unique_key_counts}
            Total Time: {used_time} minutes
            Scenarios Count: {len(scenarios)}
            Configurations: {pre_requirements}
            Scenarios Data (Template): {[doc.get("name") for doc in scenarios]}
            """
        ))

        if not template_name:
            template_name = (await llm.ainvoke(
                f"""
                You are a Cymulate Breach and Attack simulation expert tasked with naming an assessment template. 
                This template will be used to generate assessments and must have a concise and professional name that reflects its purpose and scope.
            
                IMPORTANT: Respond in plain text only, without any special formatting such as bold, italics, bullet points, or headers, with no additional formatting (e.g., no Markdown or styling)
            
                ##Naming Guidelines:
                - The name should clearly indicate the focus of the assessment, such as the industry, APT groups, platforms, or compliance frameworks.
                - It should be short, precise, and meaningful for stakeholders to understand the template's purpose at a glance.
                - Avoid overly generic names like Security Assessment Template and focus on unique characteristics.
    
                ##Example Template Names:
                - Energy Sector Threat Mitigation
                - Healthcare Compliance and Security
                - Cloud Infrastructure Breach Simulation
                - Financial Industry Advanced APT Scenarios
                - Manufacturing MITRE ATT&CK Alignment
                --- 
                
                ##Input Data:
                 {summary}
    
                --- 
                Based on the above input, generate a suitable name for this assessment template that stakeholders can easily identify and use to generate assessments.
                """)).content

        description = (await llm.ainvoke(f"""
            You are a Cymulate Breach and Attack simulation expert tasked with summarizing the provided assessment template.
            Generate template description.
            IMPORTANT: Respond in plain text only, without any special formatting such as bold, italics, bullet points, or headers, with no additional formatting (e.g., no Markdown or styling)
            
            ##Summary:
            {template_name}, {summary}
        """))

        logger.info(f"LLM Analysis Response: {summary.content}")
    except Exception as e:
        logger.error(f"Error during scenario analysis: {e}")

    return template_name, summary.content, description.content, scenarios


def extract_values(items: Optional[List[Any]], attribute_name: str) -> List[str]:
    """Extract values from a list of objects based on a specified attribute."""
    if not items:
        return []

    return list({
        getattr(item, attribute_name)() if callable(getattr(item, attribute_name)) else getattr(item, attribute_name)
        for item in items
    })