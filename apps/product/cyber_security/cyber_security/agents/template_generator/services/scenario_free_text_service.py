from __future__ import annotations

import asyncio
import json
import os
from functools import partial
from typing import Any, Sequence, Union

from corellm import ModelProvider
from langchain_elasticsearch import ElasticsearchStore
from logger import logger

from ..chains.article_chain import (
    build_article_chain,
)
from ..chains.article_title_chain import (
    build_article_title_chain,
)
from ..chains.filter_chain import (
    build_filter_chain,
)
from ..models.attack_scenario import (
    ArticleResponse,
    FilteredScenarioResult,
    FreeTextResponse,
)
from ....infra.init import infra

__all__ = [
    "similar_scenarios",
    "generate_template_title",
]


MAX_CONCURRENCY: int = 5
SIMILARITY_TOP_K: int = 50
SIMILARITY_SCORE_THRESHOLD: float = 0.99

_article_chain = build_article_chain()
_article_name_chain = build_article_title_chain()
_filter_chain = build_filter_chain()
_es_store = ElasticsearchStore(
    es_connection=infra.elasticsearch_client,
    # TODO: change the index
    index_name=os.getenv("BAS2_SCENARIOS_INDEX", "cy-ai-bas2-scenarios"),
    embedding=ModelProvider().get_embeddings(
        model_name=os.getenv("EMBEDDING_MODEL_NAME", "azure_text_embedding_3_small")
    ),
)


async def _run_in_executor(func, *args, **kwargs):
    loop = asyncio.get_running_loop()
    return await loop.run_in_executor(None, partial(func, *args, **kwargs))


async def _extract_article(raw_text: str) -> Union[ArticleResponse, FreeTextResponse]:
    return await _article_chain.ainvoke({"article_text": raw_text})


async def _filter_scenarios(
    embedding_text: str, scenarios: list[dict[str, Any]]
) -> list[FilteredScenarioResult]:
    response = await _filter_chain.ainvoke(
        {"embedding_text": embedding_text, "json": json.dumps(scenarios)}
    )

    logger.info(
        f"[Template Generator Free Text] Filtered {len(response.scenarios)} scenarios, [Embedding Text] {embedding_text}"
    )
    return response.scenarios


async def _similar_docs(summary: str):
    return await _run_in_executor(
        _es_store.similarity_search,
        query=summary,
        vector_field="vector",
        k=SIMILARITY_TOP_K,
        score_threshold=SIMILARITY_SCORE_THRESHOLD,
    )


async def _process_summary(summary: str) -> list[FilteredScenarioResult]:
    docs = await _similar_docs(summary)
    if not docs:
        return []

    scenarios = [
        {
            "id": d.metadata["_id"],
            "name": d.metadata["name"],
            "platforms": d.metadata["platforms"],
            "securityControls": d.metadata["securityControls"],
            "basTags": d.metadata["basTags"],
            "cves": d.metadata["cves"],
            "software": d.metadata["software"],
        }
        for d in docs
    ]

    filtered_scenarios = await _filter_scenarios(summary, scenarios)

    # TODO: send update using stream writer
    return filtered_scenarios


async def similar_scenarios(
    article_text: str,
    *,
    max_concurrency: int = MAX_CONCURRENCY,
) -> Union[ArticleResponse, FreeTextResponse]:
    extracted = await _extract_article(article_text)
    if not extracted.success:
        return extracted

    semaphore = asyncio.Semaphore(max_concurrency)

    async def _guard(summary: str):
        async with semaphore:
            return await _process_summary(summary)

    logger.info(
        f"[Template Generator Free Text] Extracted {len(extracted.embedding_texts)} summaries"
    )

    # TODO: send update using stream writer

    # await event_handler.send_update(
    #     event_data=event,
    #     message=f":loading-2: Thinking.. ({len(extracted.embedding_texts)} summaries)",
    # )

    tasks = (_guard(s) for s in extracted.embedding_texts)
    results: Sequence[Union[list[FilteredScenarioResult], Exception]] = (
        await asyncio.gather(*tasks, return_exceptions=True)
    )

    unique: dict[str, FilteredScenarioResult] = {}
    for item in results:
        if isinstance(item, Exception):
            logger.error(f"[Template Generator Free Text] Error: {item}")
            continue
        for scenario in item:
            unique[scenario.id] = scenario

    # TODO: send update using stream writer
    # await event_handler.send_update(
    #     event_data=event,
    #     message=f":loading-2: Thinking.. (found {len(unique)} scenarios)",
    # )

    extracted_with_results = FreeTextResponse(**extracted.model_dump())
    extracted_with_results.results = list(unique.values())
    return extracted_with_results


async def generate_template_title(article: str):
    response = await _article_name_chain.ainvoke({"article_text": article})

    return response.content
