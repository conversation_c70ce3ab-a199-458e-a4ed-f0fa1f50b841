from bson import ObjectId


async def build_pipeline(
    conditions: dict,
    client_id: str
) -> list:
    match_stage = {"$match": conditions}

    return [
        match_stage,
        {
            "$lookup": {
                "from": "bas2_tags",
                "let": {"tagIds": "$basTags"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {"$in": ["$_id", "$$tagIds"]}
                        }
                    },
                    {
                        "$project": {
                            "value": 1,
                            "type": 1,
                            "_id": 0
                        }
                    }
                ],
                "as": "basTags"
            }
        },
        {"$unwind": {"path": "$actions", "preserveNullAndEmptyArrays": True}},
        {"$unwind": {"path": "$actions.findings", "preserveNullAndEmptyArrays": True}},
        {
            "$addFields": {
                "actionID": "$actions.actionID"
            }
        },
        {
            "$lookup": {
                "from": "globalAssessmentResults",
                "let": {
                    "findingResource": "$actions.findings.resource"
                },
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$clientID", client_id]},
                                    {"$eq": ["$resource", "$$findingResource"]}
                                ]
                            }
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "status": 1,
                            "date": 1
                        }
                    },
                    {"$sort": {"lastUpdated": -1}},
                    {"$limit": 1}
                ],
                "as": "assessmentResults"
            }
        },
        {
            "$addFields": {
                "hasNoPreReq": {
                    "$or": [
                        {"$ne": ["$actions.actionID", ObjectId("656cc827b6f71db408afc774")]},
                        {"$eq": ["$actions.config.name", "execute-reverse-shell"]},
                        {
                            "$or": [
                                {"$eq": [{"$ifNull": ["$actions.config.value.dependencies", None]}, None]},
                                {"$eq": [{"$size": {"$ifNull": ["$actions.config.value.dependencies", []]}}, 0]}
                            ]
                        }
                    ]
                }
            }
        },
        {
            "$addFields": {
                "baseStatus": {
                    "$ifNull": [{"$arrayElemAt": ["$assessmentResults.status", 0]}, "Not Tested"]
                }
            }
        },
        {
            "$addFields": {
                "status": {
                    "$cond": {
                        "if": "$hasNoPreReq",
                        "then": {"$concat": ["$baseStatus", " - No Pre Req"]},
                        "else": "$baseStatus"
                    }
                }
            }
        },
        {
            "$addFields": {
                "convertedFindingNameID": {
                    "$cond": {
                        "if": {"$ne": ["$actions.findings.findingNameID", None]},
                        "then": {"$toObjectId": "$actions.findings.findingNameID"},
                        "else": None,
                    }
                }
            }
        },
        {
            "$lookup": {
                "from": "findingsNames",
                "localField": "convertedFindingNameID",
                "foreignField": "_id",
                "as": "findingDetails"
            }
        },
        {
            "$addFields": {
                "findingName": {"$arrayElemAt": ["$findingDetails.name", 0]}
            }
        },
        {"$match": {"findingName": {"$exists": True}}},
        {
            "$lookup": {
                "from": "bas2_scenario_durations",
                "let": {"findingName": "$findingName", "actionID": "$actionID"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$eq": ["$findingName", "$$findingName"]},
                                    {"$eq": ["$actionID", "$$actionID"]}
                                ]
                            }
                        }
                    },
                    {"$project": {"_id": 0, "base_time": 1, "extra_time": 1, "actionName": 1, "visible": 1}},
                ],
                "as": "durationDetails"
            }
        },
        {"$match": {"durationDetails.visible": True}},
        {"$addFields": {"durationDetails": {"$arrayElemAt": ["$durationDetails", 0]}}},
        {"$addFields": {
            "base_time": {"$ifNull": ["$durationDetails.base_time", 20]},
            "extra_time": {"$ifNull": ["$durationDetails.extra_time", 3]},
            "actionName": {"$ifNull": ["$durationDetails.actionName", "$actionID"]},
        }},
        {
            "$project": {
                "_id": 1,
                "name": 1,
                "scenarioName": "$name",
                "status": 1,
                "basTags": 1,
                "platforms": 1,
                "securityControls": 1,
                "findingName": 1,
                "base_time": 1,
                "extra_time": 1,
                "actionName": 1,
                "findingNameID": "$findingNameIDString",
            }
        },
    ]