import asyncio
from collections import defaultdict

from cyber_security.agents.template_generator.services.utils import (
    get_available_minutes,
    get_status_priority,
)


def remove_duplicates(records):
    unique_records = []
    seen_ids = set()
    for record in records:
        if record['_id'] not in seen_ids:
            unique_records.append(record)
            seen_ids.add(record['_id'])
    return unique_records

def group_scenarios_by_key(scenarios: list) -> dict:
    groups = defaultdict(list)
    for scenario in scenarios:
        groups[scenario["unique_key"]].append(scenario)
    return groups


def sort_groups_by_priority(groups: dict) -> dict:
    return {
        key: sorted(scenarios, key=lambda s: get_status_priority(s["status"]))
        for key, scenarios in groups.items()
    }


def select_primary_scenarios(groups, remaining_time, mitigation_count, selected):
    """
    For each group, pop the first candidate and deduct its full cost (base_time + extra_time)
    from the remaining time. If sufficient time exists, add the candidate to selected.
    """
    for key, group in groups.items():
        if remaining_time <= 0 or not group:
            continue

        scenario = group.pop(0)
        total_cost = scenario.get("base_time", 0) + scenario.get("extra_time", 0)

        if remaining_time >= total_cost:
            selected[key].append(scenario)
            mitigation_count[key] += 1
            remaining_time -= total_cost

    return remaining_time


async def fill_with_additional_scenarios(groups, remaining_time, selected, mitigation_count, max_occurrences):
    """
    Round‑robin over groups to add additional candidates. For each candidate, only the extra_time
    is deducted from remaining_time. Candidates are added only if remaining_time can cover their extra_time
    and if the group has not reached max_occurrences.
    """
    group_names = list(groups.keys())
    selection_made = True

    while selection_made and remaining_time >= 0:
        selection_made = False
        for group_name in group_names:
            if not groups[group_name]:
                continue
            if mitigation_count[group_name] >= max_occurrences:
                continue

            candidate = groups[group_name][0]
            extra_time = candidate.get("extra_time", 0)
            if remaining_time >= extra_time:
                candidate = groups[group_name].pop(0)
                selected[group_name].append(candidate)
                mitigation_count[group_name] += 1
                remaining_time -= extra_time
                selection_made = True
                await asyncio.sleep(0)
                if remaining_time < 0:
                    break
        if not selection_made:
            break
    return remaining_time


def flatten_selected(selected: dict) -> list:
    all_selected = [s for group in selected.values() for s in group]
    return sorted(all_selected, key=lambda s: get_status_priority(s["status"]))


async def select_diverse_scenarios(scenarios, org_size, max_occurrences=99999):
    available_minutes = get_available_minutes(org_size)
    remaining_time = available_minutes

    mitigation_count = defaultdict(int)
    selected = defaultdict(list)

    groups = group_scenarios_by_key(scenarios)
    sorted_groups = sort_groups_by_priority(groups)

    remaining_time = select_primary_scenarios(sorted_groups, remaining_time, mitigation_count, selected)

    remaining_time = await fill_with_additional_scenarios(sorted_groups, remaining_time, selected, mitigation_count, max_occurrences)

    final_selection = flatten_selected(selected)

    used_time = available_minutes - remaining_time

    return remove_duplicates(final_selection), used_time
