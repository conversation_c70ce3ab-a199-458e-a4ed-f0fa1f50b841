from cyber_security.agents.template_generator.models.general_enum import PlatformEnum

STATUS_PRIORITY = {
    "Not Tested - No Pre Req": 1,
    "Not Prevented - No Pre Req": 2,
    "Not Tested": 3,
    "Not Prevented": 4,
    "Prevented - No Pre Req": 5,
    "Prevented": 6
}


EXCLUDED_CLOUD_PLATFORMS = [
    PlatformEnum.ALIBABA_CLOUD.value,
    PlatformEnum.AWS.value,
    PlatformEnum.AZURE.value,
    PlatformEnum.DIGITAL_OCEAN.value,
    PlatformEnum.GCP.value,
]

EXCLUDED_KUBERNETES_PLATFORMS = [
    PlatformEnum.KUBERNETES.value,
    PlatformEnum.DOCKER.value,
    PlatformEnum.CONTAINERS.value
]
