import asyncio
import time
from typing import Any, Dict, List, Optional

from logger import logger
from mongo import CymulateMongoClient
from pymongo.database import Database
from pymongo.errors import PyMongoError

from cyber_security.agents.template_generator.models.general_enum import (
    SecurityControlsEnum,
)
from cyber_security.agents.template_generator.services.constants import (
    EXCLUDED_CLOUD_PLATFORMS,
    EXCLUDED_KUBERNETES_PLATFORMS,
)
from cyber_security.agents.template_generator.services.pipeline_builder import (
    build_pipeline,
)

MAX_QUERY_TIME_MS = 300000
FALLBACK_SCENARIO_COUNT = 15


def process_documents(documents: List[Dict]) -> List[Dict]:
    return [
        {
            **doc,
            "_id": str(doc["_id"]),
            "unique_key": f"{doc.get('findingName') or 'UnknownFinding'}_{str(doc.get('actionName'))}",
        }
        for doc in documents
    ]


async def execute_scenario_query(
    collection: Any, pipeline: List[Dict], max_time_ms: int = MAX_QUERY_TIME_MS
) -> List[Dict]:
    start_time = time.monotonic()
    try:
        result = collection.aggregate(
            pipeline=pipeline, allowDiskUse=True, maxTimeMS=max_time_ms
        ).to_list(length=None)
        duration = time.monotonic() - start_time
        logger.debug(
            f"Executed scenario query in {duration:.2f} seconds, found {len(result)} documents."
        )
        return result
    except PyMongoError as e:
        duration = time.monotonic() - start_time
        logger.error(
            f"Error executing scenario query after {duration:.2f} seconds: {e}",
            exc_info=True,
        )
        return []


async def _fetch_scenario_batch(
    db: Database,
    collection: Any,
    platforms: List[str],
    controls: List[str],
    apt_group_ids: List[str],
    client_id: str,
    apply_fallback: bool = False,
    ignore_apt_groups: bool = False,
) -> List[Dict]:
    logger.debug(
        f"Fetching scenario batch for controls: {controls}, fallback: {apply_fallback}"
    )

    conditions = await build_match_conditions(
        platforms, controls, apt_group_ids, ignore_apt_groups=ignore_apt_groups
    )
    total_scenarios = await count_scenarios(db, conditions=conditions)

    logger.info(f"Found {total_scenarios} scenarios with conditions: {conditions}")

    if apply_fallback and total_scenarios <= FALLBACK_SCENARIO_COUNT:
        logger.info(
            f"Applying fallback: ignoring APT groups for controls {controls} due to count <= {FALLBACK_SCENARIO_COUNT}."
        )
        conditions = await build_match_conditions(
            platforms, controls, apt_group_ids, ignore_apt_groups=True
        )
        logger.info(f"Using fallback conditions: {conditions}")

    pipeline = await build_pipeline(conditions=conditions, client_id=client_id)
    documents = await execute_scenario_query(collection, pipeline)
    processed_docs = process_documents(documents)

    logger.debug(
        f"Finished processing batch for controls: {controls}, processed {len(processed_docs)} documents."
    )

    return processed_docs


async def fetch_scenarios(
    platforms: List[str],
    security_controls: List[str],
    apt_groups: List[str],
    client_id: str,
) -> List[Dict]:
    logger.info(f"🔍 fetch_scenarios called with: platforms={platforms}, security_controls={security_controls}, apt_groups={apt_groups}, client_id={client_id}")
    
    if (
        not isinstance(platforms, list)
        or not isinstance(security_controls, list)
        or not isinstance(apt_groups, list)
    ):
        logger.error("Invalid input types for fetch_scenarios.")
        return []

    logger.info("📊 Getting MongoDB database instance...")
    db = CymulateMongoClient.get_instance().get_db()
    logger.info(f"✅ Got database: {db.name if hasattr(db, 'name') else 'unknown'}")
    
    try:
        logger.info(f"🔍 Fetching APT group IDs for: {apt_groups}")
        apt_group_ids = await get_apt_group_ids_by_name(db, apt_groups)
        logger.info(f"✅ Found APT group IDs: {apt_group_ids}")

        logger.info(f"🔄 Separating WAF and other controls from: {security_controls}")
        waf_controls = [
            c
            for c in security_controls
            if c == SecurityControlsEnum.WEB_APPLICATION_FIREWALL.value
        ]
        other_controls = [
            c
            for c in security_controls
            if c != SecurityControlsEnum.WEB_APPLICATION_FIREWALL.value
        ]
        logger.info(f"✅ WAF controls: {waf_controls}, Other controls: {other_controls}")

        logger.info("📊 Getting bas2_scenarios collection...")
        collection = db.get_collection("bas2_scenarios")
        logger.info(f"✅ Got collection: {collection.name if hasattr(collection, 'name') else 'bas2_scenarios'}")
        
        tasks = []

        if waf_controls:
            logger.info("Scheduling WAF scenario fetch.")
            tasks.append(
                _fetch_scenario_batch(
                    db,
                    collection,
                    platforms,
                    waf_controls,
                    apt_group_ids,
                    client_id,
                    apply_fallback=False,
                    ignore_apt_groups=True,
                )
            )

        if other_controls:
            logger.info("Scheduling non-WAF scenario fetch.")
            tasks.append(
                _fetch_scenario_batch(
                    db,
                    collection,
                    platforms,
                    other_controls,
                    apt_group_ids,
                    client_id,
                    apply_fallback=True,
                )
            )

        if not tasks:
            logger.info("⚠️ No controls specified, returning empty list.")
            return []

        logger.info(f"🚀 Running {len(tasks)} scenario fetch tasks concurrently...")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        logger.info(f"✅ Completed {len(results)} concurrent tasks")

        scenarios = []
        logger.info(f"🔄 Processing {len(results)} results...")
        for i, result in enumerate(results):
            logger.info(f"🔍 Processing result {i+1}: type={type(result)}")
            if isinstance(result, Exception):
                logger.error(f"💥 Error during concurrent scenario fetch task {i+1}", exc_info=result)
            elif isinstance(result, list):
                logger.info(f"✅ Task {i+1} returned {len(result)} items")
                valid_items = [
                    item for item in result if isinstance(item, dict) and "_id" in item
                ]
                invalid_items = len(result) - len(valid_items)

                if invalid_items > 0:
                    logger.warning(
                        f"⚠️ Task {i+1}: Skipped {invalid_items} invalid scenario items missing _id."
                    )

                scenarios.extend(valid_items)
                logger.info(f"✅ Task {i+1}: Added {len(valid_items)} valid scenarios")
            else:
                logger.warning(
                    f"⚠️ Task {i+1}: Unexpected result type from asyncio.gather: {type(result)}"
                )

        logger.info(f"📊 Fetched {len(scenarios)} scenarios before deduplication.")

        logger.info("🔄 Deduplicating scenarios...")
        deduped = {str(s["_id"]): s for s in scenarios}
        final_scenarios = list(deduped.values())

        logger.info(f"✅ Returning {len(final_scenarios)} unique scenarios.")
        return final_scenarios

    except Exception as e:
        logger.exception(f"💥 Unhandled exception in fetch_scenarios: {type(e).__name__}: {str(e)}")
        return []


async def get_apt_group_ids_by_name(
    db: Database, apt_groups: Optional[List[str]] = None
) -> List[Any]:
    """Fetch APT group ObjectIds by name or alias."""
    logger.info(f"🔍 get_apt_group_ids_by_name called with: {apt_groups}")
    
    if not apt_groups:
        logger.info("ℹ️ No APT groups provided, returning empty list")
        return []

    logger.info(f"📊 Accessing _mitreGroups collection...")
    try:
        collection = db.get_collection("_mitreGroups")
        logger.info(f"✅ Got collection: {collection.name if hasattr(collection, 'name') else '_mitreGroups'}")
        
        query = {
            "$or": [{"aliases": {"$in": apt_groups}}, {"name": {"$in": apt_groups}}]
        }
        logger.info(f"🔍 Executing query: {query}")
        
        ids = collection.distinct("_id", query)
        logger.info(f"✅ Found {len(ids)} APT group IDs: {ids}")
        return ids

    except PyMongoError as e:
        logger.error(
            f"💥 PyMongoError fetching APT group IDs for {apt_groups}: {e}", exc_info=True
        )
        return []
    except Exception as e:
        logger.error(
            f"💥 Unexpected error fetching APT group IDs for {apt_groups}: {type(e).__name__}: {str(e)}", exc_info=True
        )
        return []


def build_security_controls_condition(
    security_controls: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """Build condition for security controls with special handling for EDR."""
    if not security_controls:
        return {}

    controls = security_controls.copy()
    if SecurityControlsEnum.ENDPOINT_DETECTION_AND_RESPONSE.value in controls:
        if SecurityControlsEnum.ANTIVIRUS.value not in controls:
            controls.append(SecurityControlsEnum.ANTIVIRUS.value)
            logger.debug("Added ANTIVIRUS control due to EDR presence.")

    return {"securityControls": {"$in": controls}}


def build_apt_groups_condition(
    apt_group_ids: Optional[List[Any]] = None, ignore_apt_groups: bool = False
) -> Dict[str, Any]:
    """Build condition for APT groups."""
    if ignore_apt_groups or not apt_group_ids:
        return {}
    return {"aptGroups": {"$in": apt_group_ids}}


async def build_filter_condition(
    security_controls: Optional[List[str]] = None,
    apt_group_ids: Optional[List[Any]] = None,
    ignore_apt_groups: bool = False,
) -> list:
    conditions = []

    security_condition = build_security_controls_condition(security_controls)
    if security_condition:
        conditions.append(security_condition)

    apt_condition = build_apt_groups_condition(apt_group_ids, ignore_apt_groups)
    if apt_condition:
        conditions.append(apt_condition)

    return conditions


def build_excluded_platforms_query_filter(platforms: List[str]) -> Dict[str, Any]:
    exclusion = set()

    if not any(platform in platforms for platform in EXCLUDED_CLOUD_PLATFORMS):
        exclusion.update(EXCLUDED_CLOUD_PLATFORMS)

    if not any(platform in platforms for platform in EXCLUDED_KUBERNETES_PLATFORMS):
        exclusion.update(EXCLUDED_KUBERNETES_PLATFORMS)

    return {"platforms": {"$nin": list(exclusion)}} if exclusion else {}


def build_platform_condition(platforms: Optional[List[str]] = None) -> Dict[str, Any]:
    if not platforms:
        return {}

    platform_filter: Dict[str, Any] = {"$in": platforms}
    excluded_filter = build_excluded_platforms_query_filter(platforms)

    if excluded_nin := excluded_filter.get("platforms", {}).get("$nin"):
        platform_filter["$nin"] = excluded_nin

    return {"platforms": platform_filter}


async def build_match_conditions(
    platforms: List[str],
    security_controls: List[str],
    apt_group_ids: List[Any],
    ignore_apt_groups: bool = False,
) -> dict:
    base_match = {"public": True, "visible": True}

    filter_conditions = await build_filter_condition(
        security_controls, apt_group_ids, ignore_apt_groups
    )

    platform_cond = build_platform_condition(platforms)
    if platform_cond:
        filter_conditions.append(platform_cond)

    if filter_conditions:
        return {"$and": [base_match, *filter_conditions]}
    else:
        return base_match


async def count_scenarios(db: Database, conditions: Dict[str, Any]) -> int:
    logger.debug(f"Counting scenarios with conditions: {conditions}")
    start_time = time.monotonic()
    try:
        count = db.get_collection("bas2_scenarios").count_documents(conditions)
        duration = time.monotonic() - start_time
        logger.debug(f"Counted {count} scenarios in {duration:.2f} seconds.")
        return count
    except PyMongoError as e:
        duration = time.monotonic() - start_time
        logger.error(
            f"Error counting scenarios after {duration:.2f} seconds: {e}", exc_info=True
        )
        return 0
