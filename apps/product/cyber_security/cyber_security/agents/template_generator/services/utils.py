import asyncio
import logging
import re
from typing import Optional, Union

import httpx
from bs4 import BeautifulSoup

from cyber_security.agents.template_generator.models.general_enum import TeamSize
from cyber_security.agents.template_generator.services.constants import STATUS_PRIORITY


def extract_text_from_html(resource, html_content):
    if resource == "dynamic-dashboards":
        return html_content
    soup = BeautifulSoup(html_content, "html.parser")
    return soup.get_text(separator=" ", strip=True)


def get_available_minutes(team_size: TeamSize = TeamSize.MEDIUM) -> int:
    return team_size.available_minutes


def get_status_priority(status: str = "Not Tested") -> int:
    return STATUS_PRIORITY.get(status, 100)


def extract_url_if_single_line(text: str) -> Optional[str]:
    if "\n" in text:
        return None

    url_pattern = re.compile(r"https?://[^\s/$.?#].[^\s]*", re.IGNORECASE)

    match = url_pattern.search(text)
    if match:
        return match.group(0)

    return None


async def fetch_url(
    url: str,
    retries: int = 2,
    timeout: Union[int, httpx.Timeout] = 5,
    raise_on_failure: bool = True,
) -> Optional[httpx.Response]:
    """
    Async fetch of a URL with retries using httpx.

    Returns:
        httpx.Response if successful, otherwise None or raises on failure.
    """
    for attempt in range(retries + 1):
        try:
            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.get(url)
                response.raise_for_status()
                return response
        except httpx.HTTPError as e:
            logging.warning(f"Attempt {attempt + 1} failed for {url}: {e}")
            if attempt == retries:
                if raise_on_failure:
                    raise RuntimeError(
                        f"HTTPX: Request to {url} failed after {retries} retries"
                    ) from e
                return None
            await asyncio.sleep(2**attempt)
