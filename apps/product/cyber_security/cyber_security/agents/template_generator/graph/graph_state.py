from langgraph.graph import <PERSON><PERSON>

from cyber_security.agents.template_generator.graph.agent_state import (
    AssessmentGeneratorAgentState,
)
from cyber_security.agents.template_generator.models.agent_enum import NextStepEnum


def determine_next_step(state: AssessmentGeneratorAgentState) -> str:
    """
    Determines the next step for guided flows.
    It reads an enum member from state.next_step and returns its string value
    for LangGraph routing, or END.
    """
    from logger import logger
    next_step_determined = determine_start_next_step(state)
    logger.info(f"determine_next_step: {next_step_determined}")
    logger.info(f"State state.next_step: {str(state.next_step)}")
    # Handle both enum and string values
    if hasattr(state.next_step, 'value'):
        next_node_name_str = str(state.next_step.value)
    else:
        next_node_name_str = str(state.next_step)

    logger.info(f"state.next_step: {next_node_name_str}, messages count: {len(state.messages)}")
    logger.info(f"State user_input: {state.user_input}")
    logger.info(f"State messages: {state.messages}")

    if next_node_name_str == END or next_node_name_str == "__end__":
        return END

    return next_node_name_str


def determine_start_next_step(state: AssessmentGeneratorAgentState) -> str:
    """
    Determines the starting point based on classification.
    Returns a string node name or END.
    """
    from logger import logger
    from langchain_core.messages import HumanMessage

    classification = state.classification
    logger.info(f"determine_start_next_step called with classification: {classification}")

    # Extract user input from messages if user_input is empty
    if not state.user_input and state.messages:
        for msg in state.messages:
            if isinstance(msg, HumanMessage):
                if isinstance(msg.content, str):
                    state.user_input = msg.content
                elif isinstance(msg.content, list) and len(msg.content) > 0:
                    # Extract text from list format
                    text_content = ""
                    for item in msg.content:
                        if isinstance(item, dict) and item.get('type') == 'text':
                            text_content += item.get('text', '')
                    if text_content:
                        state.user_input = text_content
                logger.info(f"Extracted user_input from messages: {state.user_input}")
                break

    logger.info(f"State user_input: {state.user_input}")

    if classification == "template_generator_free_text":
        logger.info("Starting FREE_TEXT_AGENT")
        return NextStepEnum.FREE_TEXT_AGENT.value
    elif classification == "template_generator":
        logger.info("Starting PRE_REQUIREMENTS_AGENT")
        return NextStepEnum.PRE_REQUIREMENTS_AGENT.value
    else:
        # Default to PRE_REQUIREMENTS_AGENT if no specific classification
        logger.info("Defaulting to PRE_REQUIREMENTS_AGENT")
        return NextStepEnum.PRE_REQUIREMENTS_AGENT.value
