from typing import Annotated, List, Optional, Union, Any

from pydantic import Field

from ..models.agent_enum import NextStepEnum
from ..models.attack_scenario import (
    FilteredScenarioResult,
)
from ..models.classifier import (
    ClassifierAgentEnum,
)
from ..models.pre_requirements import (
    PreRequirementsAgentEnum,
    PreRequirementsSchema,
)
from ....workflow.state import TemplateGeneratorState


class AssessmentGeneratorAgentState(TemplateGeneratorState):
    classification: str = Field(
        default="template_generator",
        description="Classification of the user's question"
    )

    scenarios: List[FilteredScenarioResult] = Field(
        default_factory=list,
        description="≤ 20 single‑line summaries",
    )

    template_name: Annotated[
        Optional[str], Field(max_length=100, description="The name of the template.")
    ] = None

    pre_requirements: Any = Field(
        default=None,
        description="The pre-requirements of the agent.",
    )

    next_step: Any = Field(
        default=NextStepEnum.CLASSIFIER_AGENT,
        description="The next step of the agent.",
    )
