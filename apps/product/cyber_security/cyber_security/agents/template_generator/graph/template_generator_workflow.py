from json import J<PERSON><PERSON>ecode<PERSON>rror

from corelanggraph.utils.enhanced_state_graph import EnhancedStateGraph
from langchain_core.exceptions import OutputParserException
from langgraph.graph import END, START
from langgraph.types import RetryPolicy
from pydantic import ValidationError

from ..agents import (
    ClassifierAgent,
    TemplateBuilderAgent,
    ThreatQueryAgent,
)
from ..agents.free_text_agent import (
    FreeTextAgent,
)
from ..agents.pre_requirements_agent import (
    PreRequirementsAgent,
)
from .agent_state import (
    AssessmentGeneratorAgentState,
)
from .graph_state import (
    determine_next_step,
    determine_start_next_step,
)
from ..models.agent_enum import NextStepEnum

retry_policy = RetryPolicy(
    max_attempts=15,
    initial_interval=0.5,
    backoff_factor=1.0,
    max_interval=15.0,
    jitter=True,
    retry_on=lambda exc: isinstance(
        exc, (OutputParserException, JSONDecodeError, ValidationError)
    ),
)

template_generator_workflow = EnhancedStateGraph(AssessmentGeneratorAgentState)

# Add nodes to the graph
template_generator_workflow.add_node(
    NextStepEnum.FREE_TEXT_AGENT.value, FreeTextAgent, retry=retry_policy
)

template_generator_workflow.add_node(
    NextStepEnum.PRE_REQUIREMENTS_AGENT.value, PreRequirementsAgent, retry=retry_policy
)

template_generator_workflow.add_node(
    NextStepEnum.CLASSIFIER_AGENT.value, ClassifierAgent, retry=retry_policy
)

template_generator_workflow.add_node(
    NextStepEnum.THREAT_QUERY_AGENT.value, ThreatQueryAgent, retry=retry_policy
)

template_generator_workflow.add_node(
    NextStepEnum.TEMPLATE_BUILDER_AGENT.value, TemplateBuilderAgent, retry=retry_policy
)


template_generator_workflow.add_conditional_edges(
    START,
    determine_start_next_step,
    {
        NextStepEnum.PRE_REQUIREMENTS_AGENT.value: NextStepEnum.PRE_REQUIREMENTS_AGENT.value,
        NextStepEnum.FREE_TEXT_AGENT.value: NextStepEnum.FREE_TEXT_AGENT.value,
        END: END,
    },
)

template_generator_workflow.add_conditional_edges(
    NextStepEnum.PRE_REQUIREMENTS_AGENT.value,
    determine_next_step,
    {
        NextStepEnum.TEMPLATE_BUILDER_AGENT.value: NextStepEnum.TEMPLATE_BUILDER_AGENT.value,
        NextStepEnum.CLASSIFIER_AGENT.value: NextStepEnum.CLASSIFIER_AGENT.value,
        END: END,
        NextStepEnum.THREAT_QUERY_AGENT.value: NextStepEnum.THREAT_QUERY_AGENT.value,
    },
)

template_generator_workflow.add_conditional_edges(
    NextStepEnum.FREE_TEXT_AGENT.value,
    determine_next_step,
    {
        NextStepEnum.TEMPLATE_BUILDER_AGENT.value: NextStepEnum.TEMPLATE_BUILDER_AGENT.value,
        END: END,
    },
)

template_generator_workflow.add_conditional_edges(
    NextStepEnum.CLASSIFIER_AGENT.value,
    determine_next_step,
    {
        NextStepEnum.THREAT_QUERY_AGENT.value: NextStepEnum.THREAT_QUERY_AGENT.value,
        NextStepEnum.TEMPLATE_BUILDER_AGENT.value: NextStepEnum.TEMPLATE_BUILDER_AGENT.value,
    },
)

template_generator_workflow.add_edge(
    NextStepEnum.THREAT_QUERY_AGENT.value, NextStepEnum.TEMPLATE_BUILDER_AGENT.value
)

template_generator_workflow.add_conditional_edges(
    NextStepEnum.TEMPLATE_BUILDER_AGENT.value,
    determine_next_step,
    {
        NextStepEnum.PRE_REQUIREMENTS_AGENT.value: NextStepEnum.PRE_REQUIREMENTS_AGENT.value,
        END: END,
    },
)

template_generator_graph = template_generator_workflow.compile(
    name="template_generator_agent"
)
