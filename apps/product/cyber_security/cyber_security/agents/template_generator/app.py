import asyncio
from typing import Any, List

from colorama import Fore, Style
from graph.template_generator_workflow import template_generator_graph
from IPython.display import Image
from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.runnables.graph import MermaidDrawMethod
from langgraph.graph.state import CompiledStateGraph
from logger import logger

from cyber_security.agents.template_generator.graph.agent_state import (
    AssessmentGeneratorAgentState,
)


def print_stream(stream):
    for s in stream:
        message = s["messages"][-1]
        if isinstance(message, tuple):
            print(message)
        else:
            message.pretty_print()


def save_image(img_object: Image) -> None:
    with open("./output_image.png", "wb") as file:
        file.write(img_object.data)


def render_graph(graph: "CompiledStateGraph") -> None:
    try:
        img_object = Image(
            graph.get_graph().draw_mermaid_png(
                draw_method=MermaidDrawMethod.API,
            )
        )
        save_image(img_object)
    except Exception as e:
        print(f"Failed to render PNG: {e}")


def extract_final_response(final_state: AssessmentGeneratorAgentState) -> str:
    final_response = final_state.get("messages", [])
    if final_response:
        return final_response[-1].content
    else:
        logger.info("No messages found in final state.")
        return ""


@logger.catch
async def process_input(user_input: str, messages: List[Any]):
    try:
        if not messages:
            render_graph(template_generator_graph)

        inputs = {
            "input": user_input,
            "messages": messages + [HumanMessage(content=user_input)],
            "input_metadata": [],
        }

        logger.debug(f"Inputs to graph: {inputs}")

        response: AssessmentGeneratorAgentState = await template_generator_graph.ainvoke(inputs, config={
            "configurable": {
                "session_id": "d654f72b-c4de-4716-897a-72dc922b0b23",
                "thread_id": "1",
            },
        })

        logger.debug(f"Response from graph: {response}")

        final_response: str = extract_final_response(response)
        messages.append(HumanMessage(content=user_input))
        messages.append(AIMessage(content=final_response))

        print("\n\n")
        print(Fore.LIGHTBLACK_EX + "-" * 50 + Style.RESET_ALL)
        print(Style.BRIGHT + Fore.WHITE + "User input:" + Style.RESET_ALL, Fore.WHITE + messages[0].content)
        print(Style.BRIGHT + Fore.LIGHTCYAN_EX + "Final response:" + Style.RESET_ALL, Fore.LIGHTBLUE_EX + final_response)
        print(Fore.LIGHTBLACK_EX + "-" * 50 + Style.RESET_ALL)
        print("\n\n")
        if response.get('is_final'):
            print("Final state reached. Clearing messages.")
            messages.clear()
            return final_response

        return final_response

    except Exception as e:
        logger.error("An exception occurred during input processing.", exc_info=True)
        raise


@logger.catch
def main():
    messages = [HumanMessage(content="My organization is elbit systems, industry is aerospace and defense, country is is IL")]
    print("Interactive Chat Mode: Type your input below. Type 'exit' to quit.")

    # Send "hi" to process_input on the first load
    try:
        asyncio.run(process_input("hi", messages))
    except Exception as e:
        logger.error(f"An error occurred during the initial processing: {e}", exc_info=True)
        return

    # Interactive chat loop
    while True:
        user_input = input("You: ")
        if user_input.lower() == "exit":
            print("Exiting chat. Goodbye!")
            break
        if not user_input:
            print("Please enter a valid input.")
            continue
        try:
            asyncio.run(process_input(user_input, messages))
        except Exception as e:
            logger.error(f"An error occurred: {e}", exc_info=True)
            break



if __name__ == "__main__":
    main()
