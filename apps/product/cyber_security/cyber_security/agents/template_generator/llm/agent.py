from typing import List

from langchain.agents import AgentExecutor, create_openai_functions_agent
from langchain_core.messages import SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, PromptTemplate
from langchain_core.tools import BaseTool


def create_agent(name: str, tools: List[BaseTool] = None, system_prompt: SystemMessage = None, full_prompt: PromptTemplate = None, model=None) -> AgentExecutor:
    if full_prompt is None:
        full_prompt = ChatPromptTemplate.from_messages(
            [
                ("system", system_prompt),
                ("placeholder", "{chat_history}"),
                ("placeholder", "{input_metadata}"),
                ("human", "{input}"),
                ("placeholder", "{agent_scratchpad}"),
            ]
        )

    if not system_prompt and not full_prompt:
        raise ValueError("Either system_prompt or prompt must be provided.")

    # if tools:
    #     agent = create_tool_calling_agent(llm=model, tools=tools, prompt=full_prompt)
    # else:
    #     agent = OpenAIMultiFunctionsAgent(llm=model, prompt=full_prompt, tools=[])

    agent = create_openai_functions_agent(llm=model, prompt=full_prompt, tools=tools)

    return AgentExecutor(
        name=name,
        agent=agent,
        tools=tools or [],
        verbose=True,
        max_iterations=60,
        early_stopping_method="generate",
        #max_execution_time=1,
        return_intermediate_steps=True
    )
