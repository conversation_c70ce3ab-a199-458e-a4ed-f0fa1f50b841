"""
LLM chain that keeps only scenarios semantically relevant to the
given embedding text (“summary”).  Returns a FilteredScenarioResults pydantic
model.
"""
import os

from corellm.providers.provider import ModelProvider
from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate

from cyber_security.agents.template_generator.models.attack_scenario import (
    FilteredScenarioResults,
)
from cyber_security.agents.template_generator.prompts.system.filter_template import (
    SYSTEM_TMPL_FILTER,
)


def build_filter_chain():
    """Factory – import once and reuse."""
    base_parser = PydanticOutputParser(
        pydantic_object=FilteredScenarioResults
    )
    llm = ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME", "azure_model_2"), temperature=0.1)
    parser = OutputFixingParser.from_llm(
        parser=base_parser,
        llm=llm,

    )

    prompt = (
        ChatPromptTemplate.from_messages(
            [("system", SYSTEM_TMPL_FILTER), ("user", "{json}")]
        ).partial(format_instructions=parser.get_format_instructions())
    )

    llm = llm.bind(
        response_format={"type": "json_object"}
    )

    return prompt | llm | parser
