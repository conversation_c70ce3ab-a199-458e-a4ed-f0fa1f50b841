import os

from corellm.providers.provider import Model<PERSON>rovider
from langchain_core.prompts import Chat<PERSON>romptTemplate

from cyber_security.agents.template_generator.prompts.system.build_template_name_free_text import (
    SYSTEM_TMPL_NAME_BUILD,
)


def build_article_title_chain():
    prompt = (
        ChatPromptTemplate.from_messages(
            [("system", SYSTEM_TMPL_NAME_BUILD), ("user", "{article_text}")]
        )
    )
    llm = ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME", "azure_model_2"), temperature=0.1)
    return prompt | llm
