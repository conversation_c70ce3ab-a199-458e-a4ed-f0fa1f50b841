import os

from corellm.providers.provider import Model<PERSON>rovider
from langchain.output_parsers import OutputFixingParser
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate

from cyber_security.agents.template_generator.models.attack_scenario import (
    ArticleResponse,
)
from cyber_security.agents.template_generator.prompts.system.build_template_free_text import (
    SYSTEM_TMPL_BUILD,
)


def build_article_chain():
    base_parser = PydanticOutputParser(pydantic_object=ArticleResponse)
    llm = ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME", "azure_model_2"), temperature=0.1)
    parser = OutputFixingParser.from_llm(
        parser=base_parser,
        llm=llm,
    )

    prompt = (
        ChatPromptTemplate.from_messages(
            [("system", SYSTEM_TMPL_BUILD), ("user", "{article_text}")]
        ).partial(format_instructions=parser.get_format_instructions())
    )   
    llm = llm.bind(response_format={"type": "json_object"})
    return prompt | llm | parser
