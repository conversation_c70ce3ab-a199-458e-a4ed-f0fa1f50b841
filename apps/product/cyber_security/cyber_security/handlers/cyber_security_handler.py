import uuid
from dataclasses import asdict
from datetime import datetime
from typing import List, Optional, override
from langgraph.checkpoint.mongodb import Async<PERSON>ongoDBSaver
from pymongo import AsyncMongoClient

from cyber_security.workflow.configuration import (
    Configuration,
)
from corelanggraph.app_builder.models.event import ClientTypeEnum
from corelanggraph.handler.run_handler import <PERSON>Handler
from corelanggraph.handler.stream_handler import <PERSON>Handler
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from langfuse import get_client
from langraph_redis_checkpointer.redis_checkpoint import get_redis_checkpointer
from logger import logger
from pydantic import BaseModel
from secretmanager.factory import SecretManagerFactory

from cyber_security.models.event_data import EventDataType
from cyber_security.models.files import EventFile
from cyber_security.models.metadata_generator import Metadata, MetadataGenerator
from cyber_security.models.user_data import UserData
from cyber_security.workflow.cyber_security import base_workflow
from cyber_security.workflow.state import CyberSecuritySupervisorState


class CyberSecurityInput(BaseModel):

    user_input: str
    event_data: "EventDataType"
    client_type: ClientTypeEnum
    files: List["EventFile"]
    session_id: str
    user_id: Optional[str] = None
    cookie: Optional[str] = ""
    user_data: Optional["UserData"] = None
    metadata: Metadata = None
    initial_response: Optional[dict] = None


class CyberSecurityHandlerBase:
    """Base class for shared functionality between run and stream handlers"""

    def __init__(self):
        secret = SecretManagerFactory.create().get_secret()
        self.redis_url = secret.redis.default
        self.ttl = 8 * 60 * 60 * 1000

    def get_workflow(self, checkpointer):
        """Common workflow creation method"""
        return base_workflow(checkpointer=checkpointer)

    @staticmethod
    def _clean_session_string(text: str) -> str:
        """Utility method to clean session strings by removing dashes and underscores"""
        return text.replace("-", "").replace("_", "")

    def _generate_metadata(
        self, event: "EventDataType", user_data: "UserData", client_type: ClientTypeEnum
    ) -> Metadata:
        return MetadataGenerator.generate_metadata(
            event=event, event_type=client_type, user_data=user_data
        )

    def concat_state(
        self, input: CyberSecurityInput, state: CyberSecuritySupervisorState
    ) -> CyberSecuritySupervisorState:
        state = CyberSecuritySupervisorState(**state)
        state.messages.append(
            HumanMessage(content=input.user_input, id=datetime.now().timestamp())
        )
        return state

    def init_state(self, input: CyberSecurityInput) -> CyberSecuritySupervisorState:
        initial_messages = [
            HumanMessage(content=input.user_input, id=datetime.now().timestamp())
        ]
        return CyberSecuritySupervisorState(
            messages=initial_messages,
        )

    async def get_checkpointer(self):
        logger.info("🔄 Initializing Redis checkpointer")
        checkpointer = await get_redis_checkpointer(self.ttl, self.redis_url)
        logger.info("✅ Redis checkpointer initialized successfully")
        return checkpointer

    async def get_mongo_checkpointer(self):
        logger.info("🔄 Initializing Redis checkpointer")
        client = AsyncMongoClient("mongodb://localhost:27017/")
        checkpointer = AsyncMongoDBSaver(client, db_name="uli_db")
        logger.info("✅ Redis checkpointer initialized successfully")
        return checkpointer, client


class CyberSecurityHandlerRun(
    CyberSecurityHandlerBase,
    RunHandler[CyberSecuritySupervisorState, CyberSecurityInput],
):

    def __init__(self):
        super().__init__()

    def get_config(self, input: CyberSecurityInput) -> RunnableConfig:
        messagesId = str(uuid.uuid4())

        config = Configuration(
            cookie_string=input.cookie,
            user_data=input.user_data,
            messagesId=messagesId,
        )
        session_id = self._clean_session_string(input.session_id)
        return {
            "run_id": get_client().get_current_trace_id(),
            "callbacks": [self.get_langfuse_handler()],
            "configurable": {
                "thread_id": session_id,
                "user_id": input.user_id,
                "session_id": session_id,
                **asdict(config),
            },
        }


class CyberSecurityHandlerStream(
    CyberSecurityHandlerBase,
    StreamHandler[CyberSecuritySupervisorState, CyberSecurityInput],
):

    def __init__(self):
        super().__init__()

    def get_stream_mode(self) -> List[str]:
        return [
            "values",
            "messages",
            "custom",
        ]

    def get_config(self, input: CyberSecurityInput) -> RunnableConfig:
        session_id = self._clean_session_string(input.session_id)
        messagesId = str(uuid.uuid4())
        config = Configuration(
            cookie_string=input.cookie,
            user_data=input.user_data,
            messagesId=messagesId,
        )
        return {
            "run_id": get_client().get_current_trace_id(),
            "callbacks": [self.get_langfuse_handler()],
            "configurable": {
                "thread_id": session_id,
                "user_id": input.user_id,
                "session_id": session_id,
                **asdict(config),
            },
        }
