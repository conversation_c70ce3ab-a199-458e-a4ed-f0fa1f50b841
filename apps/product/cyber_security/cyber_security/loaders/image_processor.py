import base64
import os
from io import BytesIO

import httpx
from corellm.providers.provider import ModelProvider
from langchain_core.messages import HumanMessage
from logger import logger
from PIL import ExifTags, Image


class ImageProcessor:
    def __init__(self, max_width: int = 800, max_height: int = 800, quality: int = 85):
        self.max_width = max_width
        self.max_height = max_height
        self.quality = quality

    @staticmethod
    def fetch_image(image_url: str) -> BytesIO:
        """Fetch the image from a URL."""
        try:
            response = httpx.get(image_url)
            response.raise_for_status()
            return BytesIO(response.content)
        except Exception as e:
            print(f"Error fetching the image: {e}")
            raise

    def process_image(self, image_data: BytesIO, target_size_bytes: int = None) -> BytesIO:
        """Resize and compress the image."""
        try:
            image = Image.open(image_data)
            image = self.correct_orientation(image)

            if image.mode not in ("RGB", "L"):
                image = image.convert("RGB")

            image.thumbnail((self.max_width, self.max_height), Image.Resampling.LANCZOS)

            buffer = BytesIO()
            if target_size_bytes:
                quality = self.quality
                while True:
                    buffer.seek(0)
                    image.save(buffer, format="JPEG", optimize=True, quality=quality)
                    if buffer.tell() <= target_size_bytes or quality <= 10:
                        break
                    quality -= 5  # Gradually reduce quality to lower size
            else:
                # Compress with default quality if no target size is provided
                image.save(buffer, format="JPEG", optimize=True, quality=self.quality)

            buffer.seek(0)
            return buffer
        except Exception as e:
            print(f"Error processing the image: {e}")
            raise

    @staticmethod
    def correct_orientation(image: Image.Image) -> Image.Image:
        """Correct image orientation using EXIF metadata."""
        try:
            if hasattr(image, "_getexif"):
                exif = image._getexif()
                if exif:
                    orientation_tag = [tag for tag, name in ExifTags.TAGS.items() if name == "Orientation"]
                    if orientation_tag:
                        orientation = exif.get(orientation_tag[0])
                        if orientation == 3:
                            return image.rotate(180, expand=True)
                        elif orientation == 6:
                            return image.rotate(270, expand=True)
                        elif orientation == 8:
                            return image.rotate(90, expand=True)
            return image
        except Exception as e:
            print(f"Error correcting orientation: {e}")
            return image

    @staticmethod
    def encode_image_to_base64(image_data: BytesIO) -> str:
        """Encode image data to a Base64 string."""
        try:
            return base64.b64encode(image_data.read()).decode("utf-8")
        except Exception as e:
            print(f"Error encoding the image to base64: {e}")
            raise

class ImageGPTProcessor:
    def __init__(self):
        self.image_processor = ImageProcessor()
        self.llm = ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME",'azure_model_2'))

    def compress_and_encode_image(self, image_url: str, target_size_bytes: int = None) -> str:
        """Fetch, process, and encode the image."""
        try:
            raw_image = self.image_processor.fetch_image(image_url)
            processed_image = self.image_processor.process_image(raw_image, target_size_bytes)
            return self.image_processor.encode_image_to_base64(processed_image)
        except Exception as e:
            print(f"Error compressing and encoding image: {e}")
            return ""

    def generate_image_prompt(self, user_input: str, image_url: str = None, target_size_bytes: int = None, encoded_image: str = None) -> str:
        """Generate a prompt based on the image."""
        logger.info(f"Generating image prompt")

        if not encoded_image:
            encoded_image = self.compress_and_encode_image(image_url, target_size_bytes)

        message = HumanMessage(
            content=[
                {"type": "text", "text": f"User Input: {user_input}"},
                {"type": "text", "text": "Based on the provided user input, analyze the image and describe its content."},
                {"type": "text", "text": "Additionally, extract all visible text from the image."},
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{encoded_image}"}},
            ]
        )

        try:
            response = self.llm.invoke([message])
            logger.info(response.content)
            return response.content
        except Exception as e:
            print(f"Error invoking the model: {e}")

