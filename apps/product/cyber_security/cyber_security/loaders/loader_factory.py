from typing import TYPE_CHECKING, Optional

from logger import logger

from cyber_security.loaders.archive_loader import ArchiveLoader
from cyber_security.loaders.document_loaders import (
    DocLoader,
    PDFLoader,
    TextOrJSONLoader,
)
from cyber_security.loaders.image_loader import ImageFileLoader
from cyber_security.models.enums import FileCategory
from cyber_security.models.files import EventFile

if TYPE_CHECKING:
    from cyber_security.loaders.base_loader import BaseFileLoader


class LoaderFactory:
    def create_loader(self, event_file: EventFile, user_input: str) -> Optional["BaseFileLoader"]:
        """
        Create and return the appropriate loader based on the file category of `event_file`.
        """
        if event_file.category == FileCategory.DOCUMENT:
            if event_file.type == "pdf":
                return PDFLoader(event_file)
            elif event_file.type in {"doc", "docx"}:
                return DocLoader(event_file)
            else:
                logger.warning(f"No specific loader for document type: {event_file.type}")
                return None
        elif event_file.category == FileCategory.LOG:
            return TextOrJSONLoader(event_file)
        elif event_file.category == FileCategory.ARCHIVE:
            return ArchiveLoader(self, event_file, user_input)
        elif event_file.category == FileCategory.IMAGE:
            return ImageFileLoader(event_file, user_input)
        else:
            logger.warning(f"No loader found for category: {event_file.category}")
            return None
