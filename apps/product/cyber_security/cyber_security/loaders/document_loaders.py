import json
import tempfile
from typing import List

from langchain_community.document_loaders import Docx2txt<PERSON>oader, PyPDFLoader, TextLoader
from langchain_core.documents import Document

from cyber_security.loaders.base_loader import BaseFileLoader
from cyber_security.models.files import EventFile
from cyber_security.utils.glbl import sort_by_level, truncate_documents


class PDFLoader(BaseFileLoader):
    def __init__(self, event_file: EventFile):
        super().__init__(event_file)

    def load(self) -> List[Document]:
        loader = PyPDFLoader(self.event_file.file_path)
        documents = loader.load()
        return truncate_documents(documents)

class DocLoader(BaseFileLoader):
    def __init__(self, event_file: EventFile):
        super().__init__(event_file)

    def load(self) -> List[Document]:
        loader = Docx2txtLoader(self.event_file.file_path)
        documents = loader.load()
        return truncate_documents(documents)

class TextOrJSONLoader(BaseFileLoader):
    def __init__(self, event_file: EventFile):
        super().__init__(event_file)

    def load(self) -> List[Document]:
        file_path = self._prepare_file_for_loading()
        text_loader = TextLoader(file_path)
        documents = text_loader.load_and_split()

        if not self._is_json_file() and documents and documents[0].page_content.startswith("["):
            documents = sort_by_level(documents)

        return truncate_documents(documents)

    def _is_json_file(self) -> bool:
        """Check if the event file is JSON based on its type or mimetype."""
        return self.event_file.type == "json" or self.event_file.mimetype == "application/json"

    def _prepare_file_for_loading(self) -> str:
        """
        Prepare the file for loading. If JSON, beautify it and save to a temporary file;
        otherwise, return the original file path.
        """
        if self._is_json_file():
            with open(self.event_file.file_path, 'r') as f:
                data = json.load(f)
            json_string = json.dumps(data, indent=4, ensure_ascii=False)

            with tempfile.NamedTemporaryFile(delete=False, suffix=".txt", mode="w") as temp_file:
                temp_file.write(json_string)
                return temp_file.name
        else:
            return self.event_file.file_path
