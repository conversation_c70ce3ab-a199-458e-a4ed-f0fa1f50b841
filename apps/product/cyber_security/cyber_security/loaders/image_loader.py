from io import By<PERSON><PERSON>
from typing import List

from langchain_core.documents import Document

from cyber_security.loaders.base_loader import BaseFileLoader
from cyber_security.loaders.image_processor import ImageGPTProcessor, ImageProcessor
from cyber_security.models.files import EventFile


class ImageFileLoader(BaseFileLoader):
    def __init__(self, event_file: EventFile, user_input: str):
        super().__init__(event_file)
        self.image_processor = ImageProcessor()
        self.llm_image_processor = ImageGPTProcessor()
        self.user_input = user_input
        self.image_compression_size = 10_000

    def load(self) -> List[Document]:
        processed_image = self.image_processor.process_image(BytesIO(self.event_file.content), self.image_compression_size)
        image_base64 = self.image_processor.encode_image_to_base64(processed_image)
        llm_response = self.llm_image_processor.generate_image_prompt(user_input=self.user_input, encoded_image=image_base64)

        return [
            Document(
                page_content=f"Image described by the LLM model: {llm_response}",
                metadata={
                    "file_name": self.event_file.name,
                    "file_type": self.event_file.type,
                    "file_category": self.event_file.category,
                    "type": "image"
                }
            )
        ]
