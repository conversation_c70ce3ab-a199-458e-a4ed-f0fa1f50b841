import os
import tempfile
import zipfile
from datetime import datetime
from typing import TYPE_CHECKING, List

import rarfile
from langchain_core.documents import Document
from logger import logger

from cyber_security.loaders.base_loader import BaseFileLoader
from cyber_security.models import config_vars
from cyber_security.models.files import EventFile

if TYPE_CHECKING:
    from cyber_security.loaders.loader_factory import LoaderFactory


class ArchiveLoader(BaseFileLoader):
    def __init__(self, loader_factory: "LoaderFactory", event_file: EventFile, user_input: str):
        super().__init__(event_file)
        self.loader_factory = loader_factory
        self.user_input = user_input

    def load(self) -> List[Document]:
        documents = []
        metadata_summary = []

        with tempfile.TemporaryDirectory() as temp_dir:
            extracted = False
            try:
                # Handle ZIP archives
                if self.event_file.name.endswith('.zip'):
                    with zipfile.ZipFile(self.event_file.file_path, 'r') as archive:
                        archive.extractall(temp_dir, pwd=config_vars.ARCHIVE_PASSWORD.encode())
                        extracted = True
                # Handle RAR archives
                elif self.event_file.name.endswith('.rar'):
                    with rarfile.RarFile(self.event_file.file_path, 'r') as archive:
                        archive.extractall(temp_dir, pwd=config_vars.ARCHIVE_PASSWORD.encode())
                        extracted = True
            except Exception as e:
                logger.warning(f"Failed to extract {self.event_file.name} with password: {e}")

            if not extracted:
                logger.error(f"Skipping archive {self.event_file.name} due to extraction failure.")
                return documents

            for root, _, files in os.walk(temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_extension = os.path.splitext(file)[1][1:].lower()
                    file_category = EventFile.get_category(file_extension)

                    file_size = os.path.getsize(file_path)
                    creation_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    last_modified_time = datetime.fromtimestamp(os.path.getmtime(file_path))

                    # Append metadata to summary list
                    metadata_summary.append({
                        "file_name": file,
                        "size": file_size,
                        "creation_time": creation_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "last_modified_time": last_modified_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "file_category": file_category,
                    })

                    try:
                        with open(file_path, 'rb') as f:
                            file_content = f.read()
                    except Exception as e:
                        logger.warning(f"Failed to read content from {file_path}: {e}")
                        file_content = b""

                    try:
                        event_file_extracted = EventFile(
                            name=file,
                            type=file_extension,
                            size=file_size,
                            mimetype="",
                            category=file_category,
                            file_path=file_path,
                            content=file_content
                        )
                    except Exception as e:
                        logger.warning(f"Failed to create EventFile for extracted file: {e}")
                        continue

                    loader = self.loader_factory.create_loader(event_file_extracted, self.user_input)
                    if loader:
                        documents.extend(loader.load())
                    else:
                        logger.warning(f"No loader found for extracted file: {file_path}")

            metadata_content = "\n".join([
                f"File Name: {meta['file_name']}, Size: {meta['size']} bytes, "
                f"Creation Time: {meta['creation_time']}, Last Modified Time: {meta['last_modified_time']}, "
                f"Category: {meta['file_category']}"
                for meta in metadata_summary
            ])

            metadata_document = Document(
                page_content=metadata_content,
                metadata={"description": "Summary of all files in the archive"}
            )

            documents.append(metadata_document)

        return documents
