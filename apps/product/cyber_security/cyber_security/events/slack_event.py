
from enum import Enum
from typing import Any, Coroutine, Dict, List, Tuple

from corelanggraph.app_builder.event_type.slack_event import Slack<PERSON><PERSON>
from corelanggraph.app_builder.models.event import ClientTypeEnum
from logger import logger
from ratelimit import limits
from slack_client import Slack<PERSON>lient

from cyber_security.handlers.cyber_security_handler import (
    CyberSecurityHandlerStream,
    CyberSecurityInput,
)
from cyber_security.infra.init import infra
from cyber_security.models.event_data import SlackEvent as SlackEventData
from cyber_security.models.files import EventFile


class Event(Enum):
    APP_MENTION="app_mention"
    MESSAGE="message"

class SlackCyberEvent(SlackEvent):
    MAX_FILE_SIZE_MB = 20
    MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
    MAX_TOTAL_FILES = 20
    MAX_CONCURRENT_DOWNLOADS = 15
    def __init__(self):
        handler = CyberSecurityHandlerStream()
        slack_client = SlackClient(
            bot_token=infra.get_instance().get_secret("SLACK_BOT_TOKEN"),
            app_token=infra.get_instance().get_secret("SLACK_APP_TOKEN")
        )
        super().__init__(handler,slack_client,[Event.APP_MENTION.value,Event.MESSAGE.value])


    async def send_error_response(self, message: dict, error: str) -> None:
        pass

    async def parse_message_output(self,message:Any)->Tuple[str,Any]:
        pass
    

    
    async def parse_message(self, event_type: str, message: Any, path: str) -> Coroutine[Any, Any, Any]:
        match path:
            case Event.APP_MENTION.value:
                return await self.parse_message_app_mention(message)
            case Event.MESSAGE.value:
                return await self.parse_message_message(message)
            case _:
                raise ValueError(f"Invalid event type: {path}")

    @staticmethod
    def normalize_response(response, event=None):
        if isinstance(response, str):
            return response.strip()
        return response
    

    @limits(calls=60, period=30)
    async def process_event(self, event_data: "SlackEventData", files: List["EventFile"]):
        user_id = event_data.user
        thread_ts = event_data.thread_ts
        user_info_response = await self.slack_client.get_user_info(user_id)
        event_data.username = user_info_response['user']['real_name']

        channel_info_response = await self.slack_client.get_channel_info(channel=event_data.channel)
        logger.info(f"Retrieved channel info: {channel_info_response}")
        if 'channel' in channel_info_response and 'name' in channel_info_response['channel']:
            event_data.channel_name = channel_info_response['channel']['name']
        else:
            if channel_info_response['channel'].get('is_im'):
                event_data.channel_name = "direct_message"
            else:
                logger.error(f"Channel name not found in response: {channel_info_response}")
                event_data.channel_name = "unknown_channel"

        event_data.listener = ClientTypeEnum.Slack

        initial_response: dict = await self.slack_client.post_message(event_data.channel, ":loading-2: Working on your question...", thread_ts)

        try:
            response, docs, assessment_logs = await self.handle_event(CyberSecurityInput({
                "event_data":event_data,
                "files":files,
                "user_input":event_data.text,
                "initial_response":initial_response,
                "session_id":thread_ts,
                "user_id":user_id,
            }))

            normalized_response = self.normalize_response(response, event_data)
            await self.delete_message(event_data.channel, initial_response['ts'])
            await self.send_response(normalized_response, docs, assessment_logs, event_data, thread_ts)
        except Exception as e:
            logger.error(f"Error in processing the question: {e}")
            error_user_id = "UHQMSKU1J"
            error_message = (
                "Something went wrong while processing your input. Please try again or choose a different method to continue"
            )

            await self.delete_message(event_data.channel, initial_response.get('ts'))
            await self.post_message(event_data.channel, error_message, thread_ts)
    async def parse_message_app_mention(self, body: dict) -> Coroutine[Any, Any, Any]:
        event:dict = body['event']
        if event.get('channel_type') == 'im':
            return

        files: List["EventFile"] = await self._extract_and_download_files(event)
        event_data: SlackEventData = SlackEventData.model_validate(body['event'])
        await self.process_event(event_data, files)

    async def parse_message_message(self, body:dict) -> Coroutine[Any, Any, Any]:
        event:dict = body['event']
        if '<@U06DK6L4KFZ>' in event.get('text', ''):
            return

        if (
                (event.get('channel_type') == 'im' or
                    (event.get('channel') in {"C02N7M3GBHN", "C0798ES163T"}
                    and 'thread_ts' not in event
                    and 'bot_id' not in event)) and
                event.get('subtype') != 'message_changed'
        ):
            files: List["EventFile"] = await self._extract_and_download_files(event)
            event_data: SlackEventData = SlackEventData.model_validate(event)

            await self.process_event(event_data, files)
        
    async def post_message(self, channel, text, thread_ts=None) -> dict:
        return await self.slack_client.post_message(channel, text, thread_ts)

    async def update_message(self, channel, ts, text) -> dict:
        return await self.slack_client.update_message(channel, ts, text)

    async def delete_message(self, channel, ts) -> dict:
        return await self.slack_client.delete_message(channel, ts)
    
    async def send_response(
            self,
            response: str,
            docs: List[dict],
            assessment_logs: List[Dict[str, Any]],
            event_data: "SlackEventData",
            thread_ts: str = None
    ) -> dict:

        if "UG2TFQMC3" not in response and not len(assessment_logs):
            formatted_links = []
            for doc in docs:
                url = None
                name = None

                metadata:dict = doc.get('metadata', None)
                if metadata:
                    url = metadata.get('url')
                    name = metadata.get('name')
                else:
                    url = doc.get('url')
                    name = doc.get('name')

                if not url or not name:
                    continue

                logger.debug('Retrieved document passage from: %s', name)
                formatted_links.append(f"<{url}|{name}>")

            if formatted_links:
                response += '\n' + '\n'.join(formatted_links) + '\n'

        return await self.post_message(event_data.channel, f"{response} \n\n*<@{event_data.user}>*", thread_ts)

    