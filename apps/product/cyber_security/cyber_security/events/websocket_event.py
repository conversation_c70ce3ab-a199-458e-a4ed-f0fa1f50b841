import json
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Coroutine, Dict, List, Set, Tuple

from corelanggraph.app_builder.core.cookie_processor import CookieProcessor
from corelanggraph.app_builder.event_type.websocket_event import WebSocketEvent
from corelanggraph.app_builder.models.event import ClientTypeEnum
from corelanggraph.handler import (
    LangGraphStreamEventProcessor,
    LangGraphStreamProcessor,
)

from logger import logger
from websocket import WebsocketClient
from websocket.websocket_client import WebsocketConfig

from cyber_security.handlers.cyber_security_handler import (
    CyberSecurityHandlerStream,
    CyberSecurityInput,
)
from cyber_security.infra.init import infra
from cyber_security.models.event_data import (
    WebsocketEvent as WebsocketEventData,
)
from cyber_security.models.metadata_generator import Metadata, MetadataGenerator
from cyber_security.models.user_data import UserData
from cyber_security.utils import is_valid_uuid


class Channel(Enum):
    CHATBOT_DISTRIBUTOR = "cyber-distributor"


class WebsocketCyberEvent(WebSocketEvent):

    def __init__(self):
        handler = CyberSecurityHandlerStream()
        websocket_client = WebsocketClient.get_instance(
            WebsocketConfig(
                server_url=infra.get_instance().get_secret("WEBSOCKET_URL"),
                jwt_secret=infra.get_instance().get_secret("JWT_SECRET"),
                namespace=None,
                token_refresh_period=50,
                max_queue_size=1000,
            )
        )
        super().__init__(
            handler,
            websocket_client,
            [
                Channel.CHATBOT_DISTRIBUTOR.value,
            ],
        )
        if handler.type == "stream_events":
            self.langgraph_stream_processor = LangGraphStreamEventProcessor()
        else:
            self.langgraph_stream_processor = LangGraphStreamProcessor()

    async def emit_event(self, result: Any, config: dict) -> None:
        """Enhanced streaming handler that sends updates and final responses"""
        try:
            user_data = config.get("configurable", {}).get("user_data")
            session_id = config.get("configurable", {}).get("session_id", "default")
            messages_id = config.get("configurable", {}).get("messagesId")

            if not user_data:
                raise ValueError("User data not found in config")

            messages = list(self.langgraph_stream_processor._process_event(result))
            # with open("result.json", "a") as f:
            #     json.dump(result, f, indent=4, default=str)

            for content in messages:

                response_payload = {
                    "clientID": user_data.client_id,
                    "userID": user_data.id,
                    "messagesId": messages_id,
                    "sessionId": session_id,
                    **content,
                }

                await self.websocket_client.send_message(
                    Channel.CHATBOT_DISTRIBUTOR,
                    response_payload,
                )

        except Exception as e:
            logger.error(f"Error in streaming handler: {e}")
            # Send error response
            error_payload = {
                "type": "error",
                "error": str(e),
                "clientID": user_data.client_id if user_data else None,
                "userID": user_data.id if user_data else None,
                "messagesId": messages_id,
                "sessionId": session_id,
                "timestamp": datetime.now().isoformat(),
            }
            await self.websocket_client.send_message(
                Channel.CHATBOT_DISTRIBUTOR, error_payload
            )

    def _log_raw_result(self, result: Any, session_id: str) -> None:
        """Log raw result structure for debugging purposes"""
        try:
            if isinstance(result, tuple) and len(result) >= 2:
                agent_path, event_type = result[0], result[1]
                logger.debug(
                    f"📊 Session {session_id}: Agent path: {agent_path}, Event type: {event_type}"
                )

                # Log if this looks like a completion
                if not agent_path or agent_path == ():
                    logger.info(
                        f"🏁 Session {session_id}: Detected workflow completion (empty agent path)"
                    )
                elif (
                    isinstance(agent_path, tuple)
                    and len(agent_path) == 1
                    and "supervisor" in str(agent_path[0]).lower()
                ):
                    logger.info(
                        f"🎯 Session {session_id}: Supervisor response detected"
                    )
        except Exception as e:
            logger.debug(f"Failed to log raw result: {e}")

    async def send_error_response(self, message: dict, error: str) -> None:
        await self.websocket_client.send_message(
            Channel.CHATBOT_DISTRIBUTOR, {"error": error}
        )

    async def parse_message_output(self, message: Any) -> Tuple[str, Any]:
        return message, message

    async def parse_message(
        self, event_type: str, message: Any, path: str
    ) -> Coroutine[Any, Any, Any]:
        match path:
            case Channel.CHATBOT_DISTRIBUTOR.value:
                return await self.parse_message_distributor(message)
            case _:
                raise ValueError(f"Invalid event type: {path}")

    def prepare_data(
        self, event: dict, user_data: dict
    ) -> tuple[WebsocketEventData, UserData, Metadata]:
        session_id = user_data.get("_id") + "_" + self.set_session_id(event)
        user_data["env"] = "production"
        event.update(
            {
                "sessionID": session_id,
                "text": event.get("question"),
                "thread_ts": datetime.now().isoformat(),
                "channel": "websocket",
                "listener": ClientTypeEnum.Websocket,
                "softwareEnv": user_data.get("env"),
            }
        )

        user_data: UserData = UserData.model_validate(user_data)
        _metadata: Metadata = MetadataGenerator.generate_metadata(
            event=WebsocketEventData(**event),
            event_type=ClientTypeEnum.Websocket,
            user_data=user_data,
        )

        event.update({"metadata": _metadata})
        event_data: WebsocketEventData = WebsocketEventData.model_validate(event)

        return event_data, user_data, _metadata

    def set_session_id(self, event: dict):
        if event.get("sessionID"):
            return event.get("sessionID")
        return str(uuid.uuid4())

    async def parse_message_distributor(
        self, message: dict
    ) -> Coroutine[Any, Any, Any]:
        message["questionID"] = str(uuid.uuid4())
        try:
            cookie_str = message.get("cookie")
            cookie_processor = CookieProcessor(cookie_str)
            user_data = cookie_processor.decoded_token
            logger.info(f"Received message: {message} from user: {user_data}")
            event_data, user_data, metadata = self.prepare_data(message, user_data)

            # await self.chatbot.rate_limiter.init()
            # await self.chatbot.rate_limiter.is_allowed(user_id=user_data.id, requests=30, duration=60)
            # await self.emit_event("update_message", "processing", "Working on your question...", event_data, metadata=metadata)

            input_data = CyberSecurityInput(
                client_type=ClientTypeEnum.Websocket,
                user_input=event_data.text,
                event_data=event_data,
                files=[],
                metadata=metadata,
                session_id=event_data.session_id,
                user_id=user_data.id,
                cookie=cookie_str,
                user_data=user_data,
            )
            return input_data
        except Exception as e:
            logger.error(f"Error parsing message: {e}")
            await self.websocket_client.send_message(
                Channel.CHATBOT_DISTRIBUTOR, {"error": str(e)}
            )
            raise e
            # await self.send_error_response(message, str(e))
