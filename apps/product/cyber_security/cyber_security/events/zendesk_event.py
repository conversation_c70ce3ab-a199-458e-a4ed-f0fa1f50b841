import asyncio
import uuid
from typing import Any, List, Optional

from corelanggraph.app_builder.event_type.zendesk_event import ZendeskEvent
from corelanggraph.app_builder.models.event import ClientTypeEnum
from corelanggraph.app_builder.models.files import EventFile
from logger import logger
from ratelimit import RateLimitException, limits
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)
from zendesk.zendesk_client import ZendeskClient
from zenpy.lib.api_objects import Ticket

from cyber_security.handlers.cyber_security_handler import (
    CyberSecurityHandlerStream,
    CyberSecurityInput,
)
from cyber_security.infra.init import infra


class ZendeskCyberEvent(ZendeskEvent):

    def __init__(self):
        handler = CyberSecurityHandlerStream()
        zendesk_client = ZendeskClient(
            email=infra.get_instance().get_secret("ZENDESK_EMAIL"),
            token=infra.get_instance().get_secret("ZENDESK_TOKEN"),
            subdomain=infra.get_instance().get_secret("ZENDESK_SUBDOMAIN")
        )
        interval = int(infra.get_instance().get_secret("ZENDESK_INTERVAL",600))
        super().__init__(handler,zendesk_client,interval)
        


    @staticmethod
    def _prepare_data(ticket: Ticket, title: str, description: str, full_name: str, ticket_id: int, zendesk_user_id: int) -> dict:
        sessionID = str(uuid.uuid4())

        event_metadata: dict = {
            "username": full_name,
            "question": f"!!zendesk!!: {title} \n {description}",
            "sessionID": sessionID
        }

        event = {
            "listener": ClientTypeEnum.Zendesk,
            "questionID": str(uuid.uuid4()),
            "text": event_metadata.get('question'),
            "username": event_metadata.get('username'),
            "sessionID": sessionID,
            "metadata": {},
            "channel": ClientTypeEnum.Zendesk,
            "thread_ts": sessionID,
            "ticket_id": ticket_id,
            "assignee_id": zendesk_user_id
        }

        return {**event, **ticket.__dict__}
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4),
        retry=retry_if_exception_type((asyncio.TimeoutError, Exception))
    )
    async def _ask_question_ticket(self, ticket_id: int) -> Optional[str]:
        tickets: list[Ticket] = self.zendesk_client.zenpy_client.tickets
        ticket: Ticket = await asyncio.to_thread(tickets, id=ticket_id)
        title = ticket.subject or "No title"
        description = ticket.description or "No description"
        full_name = ticket.requester.name or "No name"
        event = self._prepare_data(ticket, title, description, full_name, ticket_id, ticket_id)

        files: List[EventFile] = []
        event_data: ZendeskEvent = self.handle_event(event, files)

        logger.info(f"Ticket ID: {ticket_id}\nTitle: {title}")
        await self.handle_event(
            CyberSecurityInput(
                event_data=event_data,
                files=files,
                user_data=None,
                metadata=None,
                cookie=None
            )
         )

    @limits(calls=4, period=60)
    async def _add_private_note_to_task(self, task_id: int, response: str) -> None:
        if not response or "An error occurred" in response:
            return

        note = f"{self.zendesk_client.ai_tag} \n {response}"
        await asyncio.to_thread(self.zendesk_client.add_private_note_to_ticket, task_id, note)


    async def parse_message(self,event_type:str,task_id:int,path:str)->Any:
        if task_id in self.processed_task_ids:
            logger.info(f"Task {task_id} already processed. Skipping.")
            return

        try:
            await self._ask_question_ticket(task_id)
        except RateLimitException:
            logger.warning(f"Rate limit hit for task {task_id}. Re-queuing task.")
            await self._add_task_to_queue(task_id)
        except Exception as e:
            logger.error(f"Error processing task {task_id}: {e}")
        finally:
            self.processed_task_ids.add(task_id)
