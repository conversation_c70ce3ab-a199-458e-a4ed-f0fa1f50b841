import base64
import json
import os
import threading

from elasticsearch import Elasticsearch


# Décorateur debounce pour méthodes de classe/instance
def debounce(wait):
    def decorator(fn):
        timer = None
        lock = threading.Lock()
        result = [None]

        def debounced(*args, **kwargs):
            nonlocal timer

            def call_it():
                with lock:
                    timer = None
                    result[0] = fn(*args, **kwargs)

            with lock:
                if timer is not None:
                    timer.cancel()
                timer = threading.Timer(wait, call_it)
                timer.start()
            # Optionnel: retourne la dernière valeur connue
            return result[0]

        return debounced

    return decorator


class InfraSingleton:
    _instance = None

    def __init__(self):
        if InfraSingleton._instance is not None:
            raise Exception("This class is a singleton!")
        import os

        try:
            from logger import logger

            logger.info("🚀 Starting infrastructure initialization...")

            from corelanggraph.langfuse import LangfuseClient
            from corellm import ModelProvider
            from mongo import CymulateMongoClient
            from secretmanager import SecretManagerFactory

            logger.info("🔐 Initializing Langfuse client...")
            self.langfuse = LangfuseClient(
                secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
                public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
                host=os.getenv("LANGFUSE_HOST"),
            ).client

            os.environ["LANGFUSE_TRACING_ENVIRONMENT"] = os.getenv("ENV", "local")
            os.environ["LANGFUSE_PROMPT_FOLDER"] = "CyberSecurity"

            logger.info("🔐 Loading secrets...")
            self.secret = SecretManagerFactory.create().get_secret()

            logger.info("📊 Loading MongoDB databases...")
            CymulateMongoClient._load_all_dbs()

            logger.info("🤖 Initializing model provider...")
            self.model_provider = ModelProvider(
                list(map(lambda x: x.model_dump(), self.secret.chatbot_ai.models))
            )

            logger.info("🔑 Setting up AWS credentials...")
            os.environ["CUST_AWS_ACCESS_KEY_ID"] = self.secret.s3.accessKeyId
            os.environ["CUST_AWS_SECRET_ACCESS_KEY"] = self.secret.s3.secretAccessKey
            os.environ["CUST_AWS_SECRET_ACCESS_KEY_ENCRYPTED"] = "True"

            logger.info("🔍 Initializing Elasticsearch client...")
            self.elasticsearch_client = self.init_elasticsearch()

            logger.success("✅ Infrastructure initialization completed successfully!")
        except Exception as e:
            from logger import logger

            logger.error(f"❌ Infrastructure initialization failed: {e}")
            logger.exception("Full error details:")
            # Set minimal defaults for development
            self.langfuse = None
            self.secret = None
            self.model_provider = None
            self.elasticsearch_client = None

        InfraSingleton._instance = self

    def init_elasticsearch(self) -> Elasticsearch:
        from logger import logger

        try:
            logger.info("🔍 Setting up Elasticsearch connection...")
            elastic = self.secret.elastic
            elastic_api_keys = elastic.apiKey
            raw_api_key = f"{elastic_api_keys.id}:{elastic_api_keys.api_key}"
            elastic_api_key = base64.b64encode(raw_api_key.encode("utf-8")).decode(
                "utf-8"
            )

            if not elastic.cloudId:
                raise ValueError(
                    "[Elasticsearch] Please provide both cloudId and apiKey"
                )

            client = Elasticsearch(
                cloud_id=elastic.cloudId,
                api_key=elastic_api_key,
                request_timeout=200,
                http_compress=True,
                retry_on_timeout=True,
                max_retries=20,
            )

            # Test the connection
            info = client.info()
            logger.success(
                f"✅ Elasticsearch connected successfully: {info['cluster_name']}"
            )
            return client
        except Exception as e:
            logger.error(f"❌ Failed to initialize Elasticsearch: {e}")
            raise

    def get_secret(self, key: str, default=None, secret_type="chatbot_ai"):
        env_key = key
        value = os.getenv(env_key)

        if value is None:
            env_key = f"{secret_type.lower()}.{key.lower()}"
            value = os.getenv(env_key) or os.getenv(env_key.upper())

        if value is not None:
            try:
                return json.loads(value)
            except Exception as e:
                return value

        secret = getattr(self.secret, secret_type)
        if secret is None:
            return default

        if isinstance(secret, dict):
            return secret.get(key, default)
        else:
            return getattr(secret, key, default)

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls()
        return cls._instance


infra = InfraSingleton.get_instance()
