from typing import Any, List

from langchain_core.tools import tool

from cyber_security.services.document_processor import DocumentProcessor


@tool
async def query_knowledge_base(query: str) -> tuple[List[str], List[dict[str, Any]]]:
    """
    Search the Cymulate Knowledge Base for documents related to a specified query.
    Returns a list of document contents and a list of references to the full documents.

    Args:
        query (str): The search term used to find relevant documents.

    Returns:
        tuple[List[str], List[dict[str, Any]]]: A tuple with:
            - List[str]: Document contents matching the query.
            - List[dict[str, Any]]: Metadata for each document, including:
                - "name" (str): Document title.
                - "url" (str): Document URL.

    Example:
        A query like "phishing simulation" might return:
        (
            ["Phishing simulation overview...", "Setup guide for simulations..."],
            [{"name": "Phishing Guide", "url": "https://cymulate.com/docs/phishing-guide"}]
        )
    """
    docs = await DocumentProcessor(type="docs360").get_docs(query)
    refs = [{"name": doc.metadata["name"], "url": doc.metadata["url"]} for doc in docs if "name" in doc.metadata and "url" in doc.metadata]

    return [doc.page_content for doc in docs], refs

@tool
async def query_api_routes_from_swagger(query: str, api_server: str) -> tuple[List[str], List[dict[str, Any]]]:
    """
    Search the Cymulate API documentation for routes related to a specified query.
    Requires the base URL of the Cymulate API server.
    Returns a list of API routes and a list of references to the full API documentation.

    Args:
        query (str): A keyword or phrase to search for within the Cymulate API documentation, used to filter relevant API routes.
        api_server (str): The base URL of the Cymulate API server.

    Returns:
        tuple[List[str], List[dict[str, Any]]]: A tuple with:
    """

    docs = await DocumentProcessor(type="swagger_api").get_docs(query)
    refs = [{"name": "Cymulate API Documentation", "url": api_server.rstrip('/').replace("/v1", "") + "/docs/"}] if api_server else []

    return [doc.page_content for doc in docs], refs
