from typing import Optional

from langchain_core.tools import tool

from cyber_security.models.user_data import Endpoints
from cyber_security.utils.glbl import execute_tool_request


@tool
async def retrieve_bas2_assessment_scenarios(
        assessment_id: str,
        endpoints: Endpoints,
        user_cookies: str,
        skip: int = 0,
        limit: int = 25,
        search: Optional[str] = None,
        sort_key: Optional[str] = None,
        sort_ascending: Optional[bool] = None,
        status: Optional[str] = None
):
    """Retrieve BAS 2 assessment scenarios based on the assessment ID. Returns scenario data, pagination, and success flag.

    Args:
        assessment_id (str): Assessment ID (MongoDB ID).
        endpoints (Endpoints): System endpoint URLs.
        user_cookies (str): User's session cookies.
        skip (int): Number of records to skip. Default is 0.
        limit (int): Max records to return. Default is 25.
        search (Optional[str]): Keyword filter.
        sort_key (Optional[str]): Field for sorting. Default is "status".
        sort_ascending (Optional[bool]): Sort order. Default is False.
        status (Optional[str]): Filter by status: "failed", "notTested", "notPrevented", or "prevented".

    Returns:
        dict: Contains scenario data (`items`), pagination (`skip`, `limit`, `total`), and success flag.
    """

    valid_status_options = {"failed", "notTested", "notPrevented", "prevented"}
    if status and status not in valid_status_options:
        raise ValueError(f"Invalid status: {status}. Must be one of {valid_status_options}.")

    url = f"{endpoints.bas_server}/api/assessment-scenarios/report/{assessment_id}"

    default_sort_key = "status"
    default_sort_ascending = False

    json_body = {
        "pagination": {"skip": skip, "limit": limit},
        "filters": [],
        "sort": [
            {
                "key": sort_key if sort_key else default_sort_key,
                "ascending": sort_ascending if sort_ascending is not None else default_sort_ascending
            }
        ]
    }

    if search:
        json_body["filters"].append({"key": "searchTerm", "selected": [search]})

    if status:
        json_body["filters"].append({"key": "status", "selected": [status]})

    return await execute_tool_request(url=url, user_cookies=user_cookies, body=json_body, endpoints=endpoints)


@tool
async def retrieve_bas2_assessment_findings(
    assessment_id: str,
    endpoints: Endpoints,
    user_cookies: str,
    skip: int,
    limit: int,
    search: Optional[str] = None
):
    """Retrieve BAS 2 assessment findings based on the assessment ID. Returns findings data, pagination, and success flag.

    Args:
        assessment_id (str): Assessment ID (MongoDB ID).
        endpoints (Endpoints): System endpoint URLs.
        user_cookies (str): User's session cookies.
        skip (int): Number of records to skip. Default is 0.
        limit (int): Max records to return. Default is 25.
        search (Optional[str]): Keyword filter.

    Returns:
        dict: Contains findings data (`items`), pagination (`skip`, `limit`, `total`), and success flag.
    """

    url = f"{endpoints.finding_server}/api/search/assessment/{assessment_id}"

    json_body = {
        "filters": {},
        "skip": 0,
        "sort": {"key": "date", "value": 1},
        "columns": [
            "testCase",
            "scenarioName",
            "findingName",
            "date",
            "status",
            "detection",
            "prevStatus",
            "techniques",
            "tactics",
            "affectedAsset",
            "securityControls",
            "risk",
            "module",
            "type",
            "os",
            "env",
            "asset",
            "agentUserID",
            "latest",
            "tags",
            "bas2SecurityControls"
        ]
    }

    if search:
        json_body["searchTerm"] = search

    return await execute_tool_request(url=url, user_cookies=user_cookies, body=json_body, endpoints=endpoints)


@tool
async def retrieve_bas2_assessment(assessment_id: str, endpoints: Endpoints, user_cookies: str):
    """Retrieve BAS 2 assessment data based on the assessment ID. Returns assessment data and success flag.

    Args:
        assessment_id (str): Assessment ID (MongoDB ID).
        endpoints (Endpoints): System endpoint URLs.
        user_cookies (str): User's session cookies.

    Returns:
        dict: Contains assessment data and success flag.
    """

    url = f"{endpoints.bas_server}/api/assessment/{assessment_id}"

    return await execute_tool_request(url=url, user_cookies=user_cookies, method="GET", endpoints=endpoints)
