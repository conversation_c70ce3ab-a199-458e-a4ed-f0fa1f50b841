import re
from typing import List

from langchain.agents import tool
from langchain_core.messages import BaseMessage

from cyber_security.loaders.image_processor import ImageGPTProcessor
from cyber_security.models.enums import CLASSIFICATION_PATTERNS, ClassificationType
from cyber_security.utils import extract_mongo_ids


@tool
def extract_mongo_ids_from_question(question: str) -> List[str]:
    """Extract MongoDB IDs from the question."""
    return extract_mongo_ids(question)

@tool
def read_image_from_bytesio(user_input: str) -> str:
    """Processes an image from a BytesIO source and generates a prompt for the LLM model.

    This function reads an image from a given source URL or BytesIO, compresses it to the desired size,
    and generates a prompt for the LLM to analyze or process based on the user's input.

    Args:
        user_input (str): Instructions or context provided by the user for processing the image.

    Returns:
        str: The result generated by the LLM model after processing the image and user input.
    """
    image_url = "https://cymulate.com/uploaded-files/2024/07/table.png.webp"
    processor = ImageGPTProcessor()
    results: str = processor.generate_image_prompt(user_input,image_url, target_size_bytes=1_000)  # Compress to ~50 KB

    return results


@tool
def query_classifier(question: str) -> str:
    """
    Classify the user's question based on its content.

    Args:
        question: User's question

    Returns:
        The classification category as a string.
    """
    question = question.strip()
    for pattern, query_type in CLASSIFICATION_PATTERNS.items():
        if re.match(pattern, question, re.IGNORECASE):
            return query_type.value
    return ClassificationType.GENERAL.value


def clean_message_by_pattern(content: str) -> str:
    original_content = content.strip()

    for pattern, _ in CLASSIFICATION_PATTERNS.items():
        compiled_pattern = re.compile(pattern, re.IGNORECASE)
        match = compiled_pattern.match(original_content)
        if match:
            original_content = compiled_pattern.sub('', original_content).strip()

    return original_content if original_content else content.strip()


def clean_messages_by_pattern(messages: List[BaseMessage]) -> List[BaseMessage]:
    for message in messages:
        message.content = clean_message_by_pattern(message.content)

    return messages
