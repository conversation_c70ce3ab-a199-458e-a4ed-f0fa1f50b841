import os
from typing import Op<PERSON>, Dict, Any
from nemoguardrails import LLMRails, RailsConfig
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from logger import logger


class GuardrailsWrapper:
    """Wrapper for NeMo Guardrails integration"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or os.path.join(
            os.path.dirname(__file__), "config.yml"
        )
        self._rails = None
    
    @property
    def rails(self) -> LLMRails:
        """Lazy load rails configuration"""
        if self._rails is None:
            try:
                config = RailsConfig.from_path(os.path.dirname(self.config_path))
                self._rails = LLMRails(config)
                logger.info("✅ Guardrails initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize guardrails: {e}")
                raise
        return self._rails
    
    async def check_input(self, message: str) -> Dict[str, Any]:
        """Check if input message is safe"""
        try:
            response = await self.rails.generate_async(
                messages=[{"role": "user", "content": message}]
            )
            
            is_safe = not response.get("is_blocked", False)
            return {
                "is_safe": is_safe,
                "message": message if is_safe else "Input blocked by guardrails",
                "reason": response.get("block_reason", "")
            }
        except Exception as e:
            logger.error(f"❌ Guardrails input check failed: {e}")
            return {"is_safe": True, "message": message, "reason": ""}
    
    async def check_output(self, message: str) -> Dict[str, Any]:
        """Check if output message is safe"""
        try:
            response = await self.rails.generate_async(
                messages=[
                    {"role": "user", "content": "Check this response"},
                    {"role": "assistant", "content": message}
                ]
            )
            
            is_safe = not response.get("is_blocked", False)
            return {
                "is_safe": is_safe,
                "message": message if is_safe else "Response blocked by guardrails",
                "reason": response.get("block_reason", "")
            }
        except Exception as e:
            logger.error(f"❌ Guardrails output check failed: {e}")
            return {"is_safe": True, "message": message, "reason": ""}


# Global instance
guardrails = GuardrailsWrapper()