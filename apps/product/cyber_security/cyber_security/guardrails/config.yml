models:
  - type: main
    engine: bedrock
    model: us.anthropic.claude-3-haiku-20240307-v1:0



rails:
  input:
    flows:
      - self check input
  output:
    flows:
      - self check output

prompts:
  - task: content_classification
    content: |
      You are a content filter for a cybersecurity AI assistant.
      
      ALLOW if the message is about:
      - Cybersecurity defense, best practices, education
      - Security assessments, threat analysis
      - Platform features and documentation
      - Legitimate security research and learning
      
      BLOCK if the message is about:
      - Creating malware, exploits, or attack tools
      - Illegal hacking activities
      - Off-topic subjects (cooking, recipes, general topics)
      - Harmful or malicious content
      
      Message: "{{ user_input }}"
      
      Respond with JSON: {{"allow": true/false, "reason": "brief explanation"}}

  - task: self_check_input
    content: |
      Your task is to check if the user message below complies with the company policy for AI assistants.
      
      Company policy for the AI assistant:
      - Should not answer questions about creating malware, hacking tools, or illegal activities
      - Should not provide information that could be used for malicious purposes
      - Should focus on cybersecurity education and defense
      - Should not generate harmful code or exploits
      
      User message: "{{ user_input }}"
      
      Question: Should the assistant respond to this message?
      Answer:

  - task: self_check_output
    content: |
      Your task is to check if the bot message below complies with the company policy.
      
      Company policy for the AI assistant:
      - Should not provide malware creation instructions
      - Should not include actual exploit code
      - Should focus on defensive cybersecurity measures
      - Should not encourage illegal activities
      
      Bot message: "{{ bot_response }}"
      
      Question: Should this message be sent to the user?
      Answer: