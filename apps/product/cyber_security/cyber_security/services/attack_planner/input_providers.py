backward_input_providers = {
    'Remote_Host_Address': 'Generic Port Scanner',
    'Local_User_Name': "Get Current User",
    'Domain_User_Name': "Get Current User",
    'Target_IP_Address_Range': "Get Local IP Network Info",
    'Target_Host_Address': 'Execute an IP Scan Using Advanced IP Scanner',
    'Targets_List': 'Execute an IP Scan Using Advanced IP Scanner',
    'LSASS_Dump_Path': 'Dumping LSASS.exe Memory with ProcDump',
    'SYSTEM_Dump_Path': "Dump SAM Registry Hives (System and Sam)",
    'SECURITY_Dump_Path': 'Registry Dump of LSA Secrets',
    'SAM_Dump_Path': "Dump SAM Registry Hives (System and Sam)",
    'Domain_Name': 'Get current domain-name details',
    'FQDN': 'Get current domain-name details',
    'Domain_Controller_Addr': "Find the Domain Controller of a Specified Domain or Forest",
    'Domain_SID': 'Find Domain Security Identifier Using PowerView',
    'Sharphound_Groups_JSON_Path': 'SharpHound: Extract All Domain Information',
    'Sharphound_LocalAdmins_JSON_Path': 'SharpHound: Enumerate Domain Users with Local Admin Privileges',
    'Exfil_File_Path': "Data Staged: Local Data Staging - Save Data to File (Windows)",
    'Hash_File_To_Crack': 'PWDump',
    'Remote_File_Path': 'Remote/Local SMB Share Enumeration',
    'Credentials_File_Path': 'Windows Credential Editor: Dump Credentials',
    'UNC_File_Path': 'Remote/Local SMB Share Enumeration',
    'RC4_Hashes_File_Path': "Kerberoasting RC4 with Rubeus",
    'ASREP_Hashes_File_Path': 'AS-REP Roasting with Rubeus',
    'Kerberoast_Hash_File_Path': "Dump SPN User's Hash with Kerberoasting",
}

backward_combinations = {
    ('Domain_User_Name', 'Domain_User_Password', 'Domain_Name'): "Online Credential Theft Using Mimikatz",
    ('Domain_User_Name', 'Domain_User_Password'): "Online Credential Theft Using Mimikatz",
    ('Local_User_Name', 'Local_User_Password', 'Domain_Name'): "Online Credential Theft Using Mimikatz",
    ('Local_User_Name', 'Local_User_Password'): "Online Credential Theft Using Mimikatz",
    ('Database_Server_Username', 'Database_Server_Password'): "Dump Database Credentials Using LaZagne",
    ('SAM_Dump_Path', 'SYSTEM_Dump_Path'): "Dump SAM Registry Hives (System and Sam)",
    ('SYSTEM_Dump_Path', 'SECURITY_Dump_Path', 'NTDS_Dump_Path'): "Copy NTDS.dit, SECURITY, and SYSTEM Hives on a Domain Controller",
    ('SYSTEM_Dump_Path', 'SECURITY_Dump_Path'): "Registry Dump of LSA Secrets",
    ('rc4_hash', 'Domain_User_Name'): "Rubeus: Generate RC4 Hash for Password",
    ('Domain_Name', 'NTLM_Hash', "Domain_User_Name"): 'Online Credential Theft Using Mimikatz',
    ('FQDN', 'NTLM_Hash', "Domain_User_Name"): 'Online Credential Theft Using Mimikatz',
    ('Sharphound_Groups_JSON_Path', 'Sharphound_Comps_JSON_Path', 'Sharphound_Users_JSON_Path'): 'SharpHound: Extract All Domain Information',
    ('Sharphound_Groups_JSON_Path', "Sharphound_LocalAdmins_JSON_Path"): 'SharpHound: Enumerate Domain Users with Local Admin Privileges',
}

forward_combinations = {
    ('Domain_User_Name', 'Domain_User_Password', 'Domain_Name'): [
        "Tasks Scheduling on Remote Host",
        'Connect to Remote Server Using RDP',
        'Remote Credential Dumping with Psexec and Mimikatz',
        'Psexec',
        'WMIExec: Run Arbitrary Command on Remote Machine'
    ],
    ('Domain_User_Name', 'Domain_Name', 'NTLM_Hash'): [
        'WmiExec: Lateral Movement Using Pass-the-Hash',
        'Mimikatz: Pass the Hash (PTH) (64-bit)',
        'Execute Mimikatz Pass The Hash (PTH) with Expired NVIDIA Digital Certificate (64-bit)',
        'Pass the Hash (PTH) with Invoke-TheHash'
    ],
    ('Domain_User_Name', 'Domain_User_Password'): [
        'Scanning to Find Where a Specified User Has Local Admin Rights'
    ],
}

forward_input_providers = {
    'input_file_data': [
        'String Exfiltration via HTTP', 'HTTPS Data Exfiltration from String (Windows, Linux, and Mac)',
        'Telnet Data Exfiltration from XOR-Encrypted Strings on Windows, Linux, and Mac',
        'Telnet Data Exfiltration from String',
        'Telnet Data Exfiltration from Bse85-Encoded Strings on Windows, Linux, and Mac'
    ],
    'Hash_File_To_Crack': [
        'Password Cracking Using Hashcat (GPU-Based)',
        'Password Guessing with John the Ripper'
    ],
    'Upload_File_Path': ['Upload a File to Google Drive'],
    'Exfil_File_Path': [
        'Encoding and Exfiltration over DNS',
        'Scheduled Exfiltration of Data over HTTP: Base64 Encoded File (Windows)',
        'Exfiltration Over Alternative Protocol: ICMP',
        'Telnet Data Exfiltration from File (XOR-Encrypted) on Windows'
    ],
    'File_To_Encipher': ['CymRansom: Custom Ransomware']
}
