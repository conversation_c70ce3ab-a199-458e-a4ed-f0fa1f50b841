import asyncio
import os
import random
import time
from typing import Any, Dict, List, Tuple

from corellm.providers import ModelProvider
from json_repair import repair_json
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import HumanMessage, SystemMessage
from logger import logger
from partial_json_parser import loads


class Prompts:
    def __init__(self):
        self.client: BaseChatModel = ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME",'azure_model_2'))

    async def run_query_on_openai_chatgpt(self, query: str, verbose=False, user_input: str=None):
        res = ""
        max_retries = 5
        base_delay = 1

        for attempt in range(max_retries):
            try:
                messages = [HumanMessage(content=query, id=time.time())]
                if user_input:
                    messages = [
                        SystemMessage(content=query, id=time.time()),
                        HumanMessage(content=str(user_input), id=time.time() + 1)
                    ]

                response = await self.client.ainvoke(messages)

                res = response.content

                if verbose:
                    logger.info(f"Response from OpenAI API: {res}")

                if "<start>" not in res or "<end>" not in res:
                    return res

                return res.split("<start>")[1].split("<end>")[0]

            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed: {str(e)}")

                if attempt == max_retries - 1:
                    logger.error(f"Failed after {max_retries} attempts: {str(e)}")
                    return res

                delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                logger.warning(f"Retrying in {delay:.2f} seconds...")
                await asyncio.sleep(delay)

        return res

    async def run_query(self, prompt: str, retry: int = 10, verbose: bool = False, replace_newlines: bool = True, with_eval=True, user_input: str=None) -> Any:
        while retry > 0:
            try:
                answer = await self.run_query_on_openai_chatgpt(query=prompt, verbose=verbose, user_input=user_input)

                if replace_newlines:
                    answer = answer.replace("\n", "")

                logger.info(f"OpenAI response: {answer}")
                if with_eval:
                    return loads(repair_json(answer))

                return answer.strip("<start>").strip("<end>")
            except Exception as e:
                logger.warning(f"Error: {e}. Retries left: {retry}")
                retry -= 1
        logger.opt(exception=True).error(f"Failed to get response after {retry} retries.")
        return {}

    async def disassemble_story_to_use_cases(self, story: str, retry: int = 10) -> List[str]:
        logger.info(f"Disassembling story to use cases: {story}")
        prompt = """
    You are a cybersecurity expert. Analyze the given input text to extract specific use cases associated with attacker activities, similar to what MITRE ATT&CK does. Each use case should represent a discrete activity or tactic used by the attacker. If a use case involves multiple software or techniques, split them into separate use cases. 
    - Ignore Urls and try to get some description from url path.
    Output the results in a JSON list format, starting with <start> and ending with <end>. 
    Example output format: 
    <start>["Extracting credentials using Mimikatz", "Bypassing authentication with Pass-the-Hash attack", "Dumping SAM database using built-in tools"]<end>
    """
        return await self.run_query(prompt, user_input=story, retry=retry)

    async def write_template_name(self, article: str, retry: int = 10) -> str:
        logger.info(f"Generating template name for: {article}")
        prompt = """
    You are tasked with creating a concise and descriptive template name for an attack scenario. The name should:
    - Be short, with normal spaces.
    - Capitalize the first letter of each word.
    - Be relevant to the breach or attack described.
    - End with "[AI Attack Planner]".

    Your response must start with <start> and end with <end>.
    """
        return await self.run_query(prompt, user_input=article, retry=retry, with_eval=False)

    async def write_template_description(self, attack_plan: str, retry: int = 10) -> str:
        logger.info(f"Writing template description for attack plan ({attack_plan}).")
        prompt = f"""i need you to write a breach an attack scenario description, write it short and readable add new line when needed !!! ,base your answer on the simulations below to represent the spirit of the common behavior of apt group campaign describe the scenario based on mitre tactics logical order use examples from the attack plan tools and techniques "single executions" and "Attack chained scenarios" , Start with the words Cymulate AI Attack Planner has curated the following attack scenarios .  your response must start with <start> and end with <end>:"""
        return await self.run_query(prompt, retry=retry, with_eval=False)

    async def write_screen_output(self, data: Dict[str, Any], retry: int = 10) -> str:
        logger.info(f"Writing screen output.")
        prompt = f"""**write the following text in a readable compact format, add title Cymulate AI Attack Planner Use Cases and Chained Executions, the context is cymulate AI Attack planner, on the "single" part the key is the "Attack Use Case" and the values are the "cymulate execution" and a score that represent its resemblance to the use case described (write down percent), on the "chained_" part write down a short story of few lines which represent the chained executions available: {data}. Your response must start with `<start>` and end with `<end>`."""
        return await self.run_query(prompt, retry=retry, replace_newlines=False, with_eval=False)

    async def rank_options_based_on_use_case(self, use_case: str, execution_options: List[str], retry: int = 10) -> Dict[str, int]:
        logger.info(f"Ranking options for use case: {use_case}")
        _format = """<start>{"EXECUTION NAME": SCORE, "EXECUTION NAME": SCORE}<end>"""
        prompt = (
            f"""the context is breach and attack simulation , here is a use case {use_case}, and here is list of execution that may simulate this use case {execution_options} , Please evaluate how closely the following executions matching the test case on a scale from 0 to 100, if there is a specific usage of a CVE you need to give low ranking if there is no exact match, if a specific software was mentioned you can choose a replace software that does the same action but rank it lower than 75. your response must start with <start> and end with <end>, this is output example: {_format} 
        The Text: {use_case} 
        """)
        return await self.run_query(prompt, retry=retry)

    async def rank_test_case(self, test_case: str, current_graph: List[Tuple], retry: int = 10) -> Dict[str, Any]:
        logger.info(f"Ranking test case: {test_case}")
        _format = """{"rank": <RANK>, "Reasoning" :line of explanation why, "Executions missing to fill the request": <Suggest an exectution that will fill the gap of what is missing if needed if not leve empty string>},"Executions that are irrelvant and need to be removed": <Executions that are irrelvant and need to be removed>"""

        prompt = (
            f"""""Please evaluate how closely the following breach and attack scenario matches the test case on a scale from 0 to 100%. your answer should start with <start> and end with <end>. from this, Consider the specific details and objectives of the test case and compare them to the actual simulation steps. Focus on the core elements of the test case, the scenario must have a significant overlap with the test case. A ranking of 60% or higher indicates a high and precise match , respond with a json with the following format (fill the questions under <>  , the context is a breach and attack simulation ,  your response must start with <start> and end with <end>: <start>{_format}<end> 
        here is the test case details: {test_case} 
        here is the attack flow graph, the format of the flow is [from, to, connector argument]: {current_graph}
        """)
        return await self.run_query(prompt, retry=retry)

    async def choose_executions_based_on_test_case(self, test_case: str, options_list: List[str], retry: int = 10) -> List[List[str]]:
        logger.info(f"Choosing executions based on test case: {test_case}")
        _format = """<start>[["execution1", "execution2"], ["execution3", "execution4"]]<end>"""
        prompt = (
            f"""this is a breach and attack scenario test case, take into account what the test case describe and what the simulation options are and choose two executions that will fulfill the test case, you can choose up to 5 different options , don't repeat the same scenarios, keep logical order for the execution based on common attack flow, important you must choose executions from the given list only and they should have a different behavior !!, your response must start with <start> and end with <end>, do not add any other comments, response _format: {_format}
        here is the test case details: {test_case} 
        options_list: {options_list}
        """)
        return await self.run_query(prompt, retry=retry)

    async def disassemble_story(self, text: str, retry: int = 10) -> Dict:
        logger.info(f"Disassembling story: {text}")
        data_structure = """{"article_summery": "XXXXX", "stories": {"story1": ["example", "example2"], "story2":  ["example", "example2" ], ...}}"""
        prompt = (f"""
                Down below there's a text that relates to breach and attack simulation. 

                Firstly, decide if the text is relevant If it's irrelevant, output an empty stories dict. Content should be considered relevant if it contains detailed descriptions of cyber attacks, techniques, tactics, procedures, vulnerabilities, or tools that can be recreated and simulated on a breach and attack simulation platform. 

                Then, I want you to provide me a summary of an article and put it in "article_summery" key. emphasize on the tools, techniques, and flow of the attack. use simple language, write short actionable items. do not separate into categories

                Then, separate the text into ONE or more different stories, based on the article_summery each story must have two parts exactly.  break down this text into SHORT, precise action items. Keep the context of breach and attack simulation. If a story contains only one item, try to add a logical second one. Don't include learning, don't suggest mitigations and actual commands, ignore download and navigation to the folder. Focus ONLY on precise action items! Keep them short and precise. Rearrange the order of the actions by chronological order.

                Your response should look like the example:
                <start>{data_structure}<end>,  EACH STORY need to be placed under "stories" key
                IMPORTANT: your response must start with <start> and end with <end>, do not add any other comments.""")
        return await self.run_query(prompt, user_input=text, retry=retry)

    async def run_description_enrichment_query_on_openai(self, description, retry=10):
        logger.info(f"Running description enrichment query.")
        prompt = (f"""Create 4 different variations of the following text, explaining in one sentence what action is performed in the scenario in simple human terms. Explain the action only. The context is a breach and attack simulation. Your response should be in the format: <start>["example1", "example2", ...]<end>. Here is the text: {description}""")
        return await self.run_query(prompt, retry=retry)
