import asyncio
import time

from .execution_store import ExecutionStore
from .graph_handler import <PERSON><PERSON>h<PERSON><PERSON><PERSON>
from .prompts import Prompts, logger
from .utils import Utils


class StoryBuilder:
    def __init__(self, prompts: Prompts, execution_store: ExecutionStore, graph_handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, utils: Utils):
        self.prompts: Prompts = prompts
        self.execution_store: ExecutionStore = execution_store
        self.graph_handler: GraphHandler = graph_handler
        self.utils: Utils = utils

    async def get_relevant_executions(self, question, top_n=5, min_confidence=0.70, max_story_len=10):
        raw_stories_data = await self.prompts.disassemble_story(question)
        raw_stories = raw_stories_data.get('stories', {})
        article_summary = raw_stories_data.get('article_summery', '')

        semaphore = asyncio.Semaphore(30)
        relevant_stories = await self._process_main(raw_stories, top_n, min_confidence, max_story_len, semaphore)
        return raw_stories, relevant_stories

    async def _process_main(self, raw_stories, top_n, min_confidence, max_story_len, semaphore):
        tasks = [
            self._process_story(story, disassemble_question, top_n, min_confidence, max_story_len, semaphore)
            for story, disassemble_question in raw_stories.items()
        ]
        results = await asyncio.gather(*tasks)
        relevant_stories = {story: relevant_executions for story, relevant_executions in results}
        return relevant_stories

    async def _process_story(self, story, disassemble_question, top_n, min_confidence, max_story_len, semaphore):
        relevant_executions = {}
        disassemble_question = disassemble_question[-max_story_len:]
        results = await self._process_questions(disassemble_question, top_n, min_confidence, semaphore)

        for q, result in results:
            relevant_executions.setdefault(q, []).extend(result)

        return story, relevant_executions

    async def _process_questions(self, disassemble_question, top_n, min_confidence, semaphore):
        async with semaphore:
            tasks = [self.process_single_question(q, top_n, min_confidence) for q in disassemble_question]
            return await asyncio.gather(*tasks)

    async def process_single_question(self, q, top_n, min_confidence):
        res = await self.execution_store.query_embeddings(q, top_n=top_n, min_confidence=min_confidence)
        filtered_results = [
            doc.metadata.get("name") for doc in res
            if isinstance(doc.metadata, dict) and doc.metadata.get("name")
        ]
        return q, filtered_results

    async def run_question(self, question, print_images=True):
        start_time = time.time()

        for e in self.execution_store.executions_list:
            e['suitable_output_args'] = e['suitable_input_args']
            e['input_arg'] = e.get('input_args')

        raw_stories, relevant_stories = await self.get_relevant_executions(
            question=question, top_n=10, min_confidence=0.70, max_story_len=6
        )

        ai_chosen_stories = {}
        await self._process_all_stories(relevant_stories, ai_chosen_stories)

        final_results = {}
        await self._process_all_single_stories(ai_chosen_stories, final_results)

        end_time = time.time()
        elapsed_time = end_time - start_time

        if print_images:
            await self._print_images(final_results)

        return final_results

    async def _process_all_stories(self, relevant_stories, ai_chosen_stories):
        semaphore = asyncio.Semaphore(30)
        tasks = [
            self._process_executions_story(name, semaphore, relevant_stories)
            for name in relevant_stories.keys()
        ]
        results = await asyncio.gather(*tasks)
        for name, chosen_executions, verbal_story in results:
            ai_chosen_stories[name] = [chosen_executions, verbal_story]

    async def _process_executions_story(self, name, semaphore, relevant_stories):
        async with semaphore:
            verbal_story = f"{list(relevant_stories[name].keys())[0]} and then {list(relevant_stories[name].keys())[-1]}"
            all_possible_executions = self.utils.extract_unique_values(relevant_stories[name])
            chosen_executions = await self.prompts.choose_executions_based_on_test_case(
                test_case=verbal_story, options_list=all_possible_executions
            )
            return name, chosen_executions, verbal_story

    async def _process_all_single_stories(self, ai_chosen_stories, final_results):
        semaphore = asyncio.Semaphore(30)
        tasks = []
        task_metadata = []

        for verbal_story, (stories, _) in ai_chosen_stories.items():
            final_results[verbal_story] = []
            for story in stories:
                task = self._process_single_story_with_semaphore(verbal_story, story, semaphore)
                tasks.append(task)
                task_metadata.append((verbal_story, story))

        results = await asyncio.gather(*tasks)
        for result, (verbal_story, _) in zip(results, task_metadata):
            if result:
                final_results[verbal_story].append(result)

    async def _process_single_story_with_semaphore(self, verbal_story, story, semaphore):
        async with semaphore:
            return await self._process_single_story(verbal_story, story)

    async def _process_single_story(self, verbal_story, story):
        try:
            res = self.graph_handler.process_story(story, visualize=False, verbal_story=verbal_story)
        except Exception as e:
            logger.opt(exception=True).error(f"Error processing story: {story} {e}")
            return None

        chains = res['chains']
        completed_forward = res.get('completed_forward', [])
        completed_backwards = res.get('completed_backwards', [])

        actions_to_remove = []
        for item in completed_backwards:
            if isinstance(item, list):
                actions_to_remove.extend(item)
            else:
                actions_to_remove.append(item)

        actions_to_remove.append((completed_forward,))
        actions_to_remove = [x[0] for x in actions_to_remove if isinstance(x, tuple) and len(x) > 0]

        clean_chains = self.utils.remove_actions(chains, actions_to_remove)
        story_rank = await self.prompts.rank_test_case(test_case=verbal_story, current_graph=clean_chains)

        return {"chains": chains, "story_rank": (story_rank or {}).get("rank", 0)}

    async def _print_images(self, final_results):
        for verbal_story, possible_chains in final_results.items():
            for idx, option in enumerate(possible_chains):
                rank = option['story_rank']
                if rank > 49:
                    img_name = f'{idx}.{verbal_story}'
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.graph_handler.visualize_with_graphviz, option["chains"], img_name, rank
                    )
                    await asyncio.sleep(1)

    def parse_attack_plan(self, attack_plan):
        story = ""
        for key, value in attack_plan.items():
            if isinstance(value, str):
                logger.debug(f"bug in attack plan {key} {value}")
                continue

            attack_chains = value.get("attack_chains", {})
            for main_story, sub_stories in attack_chains.items():
                for sub_story in sub_stories:
                    story_rank = sub_story.get("story_rank", 0)
                    if story_rank < 30:
                        continue
                    story += "  Sub Story:\n"
                    for chain in sub_story.get("chains", []):
                        flattened_chain = self.utils.flatten_chain(chain)
                        chain_story = " -> ".join(flattened_chain)
                        story += f"    Chain: {chain_story}\n"
        return story

    @staticmethod
    def rearrange_based_on_appearances(dicts_list, target_strings):
        def count_appearances(single_dict):
            count = 0
            for value in single_dict.values():
                for item in value:
                    if item.get('connected_to') in target_strings:
                        count += 1
            return count

        counts_and_dicts = [(count_appearances(d), d) for d in dicts_list]
        counts_and_dicts.sort(key=lambda x: x[0])
        sorted_dicts = [item[1] for item in counts_and_dicts]
        return sorted_dicts

    @staticmethod
    def assemble_new_relevant_stories(relevant_stories, choice=0):
        new_relevant_stories = {}
        for story, relevant_executions in relevant_stories.items():
            new_relevant_stories.setdefault(story, set())
            for execu in relevant_executions.values():
                try:
                    new_relevant_stories[story].add(execu[choice])
                except IndexError:
                    pass
        return new_relevant_stories

    @staticmethod
    def describe_stories(data):
        stories = {}
        for idx, story in enumerate(data, start=1):
            story_key = f"story{idx}"
            story_path = " -> ".join([step[0] for step in story])
            stories[story_key] = story_path
        return stories
