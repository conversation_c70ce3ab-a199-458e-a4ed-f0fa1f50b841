import asyncio
from typing import Callable, Coroutine, Dict, List, Optional, Union

from corellm.providers import ModelProvider
from langchain_core.embeddings import Embeddings
from langchain_elasticsearch import ElasticsearchStore
from logger import logger

from cyber_security.infra.init import infra

EXECUTIONS_MAPPING_INDEX = infra.get_secret("ES_EXECUTIONS_MAPPING_INDEX","cy-ai-executions-mapping")
EXECUTIONS_LIST_INDEX = infra.get_secret("ES_EXECUTIONS_LIST_INDEX","cy-ai-executions")
EXECUTION_RELOAD_INTERVAL = infra.get_secret("EXECUTION_RELOAD_INTERVAL", 86400)
CLIENT_TYPE_STR = infra.get_secret("CLIENT_TYPE_STR", "")


embeddings: Embeddings = ModelProvider().get_embeddings(model_name="azure_text_embedding_3_small")


class ExecutionStore:
    _instance: Optional["ExecutionStore"] = None
    _lock = asyncio.Lock()
    initBackend: bool = False

    def __init__(self, init_list: bool = False):
        """Singleton Initialization."""
        if hasattr(self, "_initialized") and self._initialized:
            return

        self.store_execution_mapping: ElasticsearchStore = None
        self.store_execution_list: ElasticsearchStore = None
        self.executions_list: List[Dict] = []
        self.executions_mapping: Dict[str, Dict] = {}
        self.on_update_callbacks: List[Callable[[List[Dict]], Coroutine]] = []
        self.init_list = init_list
        self._initialized = True  # Mark as initialized

    @classmethod
    async def create(cls, init_list: bool = False) -> "ExecutionStore":
        """Asynchronous factory method to create a singleton instance."""
        async with cls._lock:
            if cls._instance is None:
                cls._instance = cls(init_list)

                cls._instance.store_execution_mapping = ElasticsearchStore(
                    es_connection=infra.elasticsearch_client,
                    index_name=EXECUTIONS_MAPPING_INDEX,
                    embedding=embeddings,
                )

                cls._instance.store_execution_list = ElasticsearchStore(
                    es_connection=infra.elasticsearch_client,
                    index_name=EXECUTIONS_LIST_INDEX,
                    embedding=embeddings,
                )

                if cls._instance.init_list and CLIENT_TYPE_STR == "websocket":
                    await cls._instance.reload_metadata()
                    asyncio.create_task(cls._instance.start_background_reload())

        return cls._instance

    def register_callback(self, callback: Callable[[List[Dict]], Coroutine]):
        """Register a callback to notify on metadata reload."""
        self.on_update_callbacks.append(callback)

    async def reload_metadata(self):
        """Reload execution metadata from Elasticsearch."""
        logger.info("Reloading metadata from Elasticsearch...")

        executions_list = await self.get_all_metadata_from_index(index_name=EXECUTIONS_LIST_INDEX)
        executions_mapping = await self.get_all_metadata_from_index(
            index_name=EXECUTIONS_MAPPING_INDEX, dict_param="name"
        )

        self.executions_list = executions_list
        self.executions_mapping = executions_mapping

        logger.info("Metadata reloaded successfully.")

        for callback in self.on_update_callbacks:
            if asyncio.iscoroutinefunction(callback):
                await callback(self.executions_list)
            else:
                callback(self.executions_list)

    async def start_background_reload(self):
        """Continuously reload metadata at defined intervals."""
        if self.initBackend:
            logger.warning("Background reload already started.")
            return

        self.initBackend = True

        while True:
            await asyncio.sleep(EXECUTION_RELOAD_INTERVAL)
            await self.reload_metadata()

    @staticmethod
    async def get_all_metadata_from_index(
            index_name: str,
            batch_size: int = 1000,
            dict_param: Optional[str] = None
    ) -> Union[List[Dict], Dict[str, Dict]]:
        """Fetch all metadata from a given Elasticsearch index using async generators."""
        query: Dict = {
            "query": {"match_all": {}},
            "_source": ["metadata"]
        }

        logger.info(f"Retrieving metadata from index: {index_name}")

        response = await asyncio.to_thread(
            infra.elasticsearch_client.search,
            index=index_name,
            body=query,
            scroll="2m",
            size=batch_size
        )

        scroll_id: str = response["_scroll_id"]
        total_docs: int = response["hits"]["total"]["value"]

        metadata_dict: Optional[Dict[str, Dict]] = {} if dict_param else None
        metadata_list: Optional[List[Dict]] = [] if not dict_param else None

        async def process_hits(hits):
            for hit in hits:
                metadata: Dict = hit["_source"]["metadata"]
                if dict_param:
                    key: Optional[str] = metadata.get(dict_param)
                    if key:
                        metadata_dict[key] = metadata
                else:
                    metadata_list.append(metadata)

        await process_hits(response["hits"]["hits"])

        logger.info(f"[{index_name}] Total documents to retrieve: {total_docs}")

        # Fetch subsequent results using async generator
        while True:
            response = await asyncio.to_thread(infra.elasticsearch_client.scroll, scroll_id=scroll_id, scroll="2m")
            hits: List[Dict] = response["hits"]["hits"]

            if not hits:
                break

            await process_hits(hits)

            scroll_id = response["_scroll_id"]

        await asyncio.to_thread(infra.elasticsearch_client.clear_scroll, scroll_id=scroll_id)
        logger.info(f"[Elasticsearch:{index_name}] Retrieved {total_docs} documents")

        return metadata_dict if dict_param else metadata_list

    def find_execution_input_args_by_name(self, execution_name: str) -> List[Dict]:
        """Find execution input arguments by name."""
        return next(
            (execution['input_args'] for execution in self.executions_list if execution['name'] == execution_name),
            []
        )

    async def query_embeddings(self, query, top_n=5, min_confidence=0.8):
        """Query stored embeddings and retrieve similar results."""
        logger.info(f"Querying Embedding for {query}...")

        docs = await self.store_execution_list.asimilarity_search(
            query=query,
            vector_field="vector",
            k=top_n,
            score_threshold=min_confidence,
            fetch_k=10000,
        )

        logger.info(f"Found {len(docs)} results {docs}")
        return docs
