import asyncio
import json
import random

import networkx as nx
from graphviz import Di<PERSON>
from logger import logger
from networkx.classes import Di<PERSON>raph

from .input_providers import (
    backward_combinations,
    backward_input_providers,
    forward_combinations,
    forward_input_providers,
)
from .utils import Utils


class GraphHandler:
    def __init__(self, execution_store, utils: Utils):
        self.utils = utils
        self.execution_store = execution_store
        self.executions_list = []
        self.graph = None

        # Initialize graph using the current executions_list
        self.update_graph(self.execution_store.executions_list)

        # Register callback to update the graph on executions_list change
        self.execution_store.register_callback(self.update_graph)

    def update_graph(self, executions_list):
        """Rebuild the graph using the updated executions_list."""
        logger.info("Updating graph with the latest executions_list...")
        self.executions_list = executions_list
        self.graph = self.build_graph_with_networkx_and_params(executions_list)
        logger.info("Graph updated successfully.")

    async def parse_attack_plan_depth_level_is_last(self, attack_plan):
        edges = await asyncio.to_thread(self.utils.translate_to_tuple, attack_plan)

        G = nx.DiGraph()
        for source, target, attributes in edges:
            G.add_edge(source, target, attributes=attributes)

        def get_execution_order_and_depth(graph):
            levels = {}
            execution_order = {}

            for node in nx.topological_sort(graph):
                if node in levels:
                    continue
                level = 0
                for predecessor in graph.predecessors(node):
                    if predecessor in levels:
                        level = max(level, levels[predecessor] + 1)
                levels[node] = level

            sorted_levels = sorted(levels.items(), key=lambda x: (x[1], x[0]))
            max_depth = max(levels.values())

            for idx, (node, depth) in enumerate(sorted_levels):
                execution_order[node] = {
                    "depth": -depth + max_depth,
                    "level": idx,
                    "is_last": (depth == max_depth and idx == len(sorted_levels) - 1),
                }
            return execution_order

        execution_order = await asyncio.to_thread(get_execution_order_and_depth, G)
        return execution_order

    @staticmethod
    def separate_disconnected_graphs(edges):
        G: DiGraph = nx.DiGraph()

        for source, target, attributes in edges:
            G.add_edge(source, target, attributes=attributes)

        sub_graphs = list(nx.weakly_connected_components(G))
        separated_graphs = []

        for subgraph in sub_graphs:
            sub_G = G.subgraph(subgraph).copy()
            sub_edges = []
            for source, target in sub_G.edges():
                sub_edges.append([source, target, sub_G[source][target]['attributes']])
            separated_graphs.append(sub_edges)

        return separated_graphs

    @staticmethod
    def visualize_with_graphviz(updated_path_info, verbal_story, rank = ""):
        dot = Digraph(comment='Execution Path for ' + verbal_story, format='png')
        dot.node('title', '\n\n\n\nTest Case: \n' + verbal_story + "\n" + "rank" + str(rank), shape='plaintext')

        for from_node, to_node, params in updated_path_info:
            dot.node(from_node, from_node)
            dot.node(to_node, to_node)
            dot.edge(from_node, to_node, label=", ".join(params))

        dot.render(f'pictures\\{verbal_story}', view=True)

    @staticmethod
    def find_shortest_path_with_intermediates_and_params(graph, start_node, end_node, intermediate_nodes):
        full_path_info = []
        nodes_sequence = [start_node] + intermediate_nodes + [end_node]

        for i in range(len(nodes_sequence) - 1):
            segment_start, segment_end = nodes_sequence[i], nodes_sequence[i + 1]
            try:
                segment_path = nx.shortest_path(graph, segment_start, segment_end)

                for j in range(len(segment_path) - 1):
                    from_node, to_node = segment_path[j], segment_path[j + 1]
                    params = graph.edges[from_node, to_node]['params']
                    full_path_info.append((from_node, to_node, params))
            except nx.NetworkXNoPath:
                logger.debug(f"No path found from {segment_start} to {segment_end}")
                return []

        return full_path_info

    @staticmethod
    def parse_story(story, random_order=False):
        if len(story) < 2:
            raise ValueError("Story must have at least two nodes (start and end nodes)")

        if random_order:
            shuffled_story = story[:]
            random.shuffle(shuffled_story)
            start_node = shuffled_story[0]
            end_node = shuffled_story[-1]

            if len(shuffled_story) > 2:
                intermediate_nodes = shuffled_story[1:-1]
            else:
                intermediate_nodes = []

        else:
            start_node = story[0]
            end_node = story[-1]

            if len(story) > 2:
                intermediate_nodes = story[1:-1]
            else:
                intermediate_nodes = []

        return start_node, end_node, intermediate_nodes

    def create_full_path(self, story, max_attempts=3):
        attempt_count = 0
        full_path_info = None

        while attempt_count < max_attempts and not full_path_info:
            start_node, end_node, intermediate_nodes = self.parse_story(story, random_order=True if attempt_count > 0 else False)
            full_path_info = self.find_shortest_path_with_intermediates_and_params(self.graph, start_node, end_node,intermediate_nodes)
            attempt_count += 1

            if full_path_info:
                logger.debug("Path and parameters used for connections:")
                for from_node, to_node, params in full_path_info:
                    logger.debug(f"{from_node} -> {to_node} using parameters: {', '.join(params)}")
                break
            else:
                logger.debug(f"No path found in attempt {attempt_count}. Retrying...")

        if not full_path_info:
            logger.debug("No path found that meets all criteria after 3 attempts.")
            return [(start_node, end_node, [])]


        return full_path_info

    @staticmethod
    def build_graph_with_networkx_and_params(executions_list) -> DiGraph:
        G: DiGraph = nx.DiGraph()
        for e in executions_list:
            e_outputs = set(e['suitable_output_args'])
            for other in executions_list:
                other_inputs = set(other['input_arg'])
                common_params = e_outputs.intersection(other_inputs)
                if common_params and e['name'] != other['name']:
                    G.add_edge(e['name'], other['name'], params=list(common_params))
        return G


    def process_story(self, story, visualize = False, verbal_story=''):
        story = list(story)
        if len(story) >1 :
            full_path_info = self.create_full_path(story, max_attempts = 1)

            if full_path_info:
                target_execution_name = full_path_info[-1][1]
            else:
                target_execution_name = story[0]
                full_path_info = []
        else:
            full_path_info = []
            target_execution_name = story[0]
            logger.debug(story)

        updated_path_info, completed_forward = self.find_forward_producers_for_missing_inputs(full_path_info, target_execution_name)
        updated_path_info, completed_backwards = self.find_producers_for_missing_inputs(updated_path_info)
        updated_path_info = self.remove_duplicates(updated_path_info)
        path_json = self.generate_json_representation(updated_path_info)

        if visualize and updated_path_info:
            logger.debug(json.dumps(path_json, indent=4))
            self.visualize_with_graphviz(updated_path_info, verbal_story)

        return {
            'chains': updated_path_info,
            'single_executions': self.find_missing_items(updated_path_info, story),
            'completed_forward': completed_forward,
            'completed_backwards': completed_backwards
        }


    def find_forward_producers_for_missing_inputs(self, path_info, target_execution_name):
        execution_lookup = {e['name']: e for e in self.executions_list}
        updated_path_info = path_info
        execution_name = []
        missing_outputs = execution_lookup[target_execution_name]['suitable_output_args']
        if missing_outputs:
            for target_set, execution_options in forward_combinations.items():
                if execution_options:
                    execution_name = random.choice(execution_options)
                    found_inputs_set = self.check_set_in_list(set(target_set), set(missing_outputs))
                    if found_inputs_set:
                        updated_path_info = updated_path_info + [(target_execution_name, execution_name, list(found_inputs_set))]
                        return updated_path_info, execution_name
            for target_input in missing_outputs:
                for input_name, execution_options in forward_input_providers.items():
                    if target_input == input_name:
                        execution_name = random.choice(execution_options)
                        updated_path_info = updated_path_info + [(target_execution_name, execution_name, [target_input])]
                        return updated_path_info, execution_name
        return updated_path_info, [execution_name]

    def find_producers_for_missing_inputs(self, path_info):
        execution_lookup = {e['name']: e for e in self.executions_list}

        updated_path_info = []
        added_executions = []
        iteration = 0
        for from_node, to_node, params in path_info:

            if iteration == 0:
                required_inputs = execution_lookup[from_node]['input_arg']
                missing_inputs = required_inputs
                for target_set, execution_name in backward_combinations.items():
                    found_inputs_set = self.check_set_in_list(set(target_set), missing_inputs)
                    if found_inputs_set:
                        updated_path_info = [(execution_name, from_node, list(found_inputs_set))] + updated_path_info
                        added_executions.append([(execution_name, from_node, list(found_inputs_set))])
                        break

                missing_inputs = list(set(missing_inputs) - found_inputs_set)

                for missing_input in missing_inputs:
                    if missing_input in backward_input_providers:
                        updated_path_info = [(backward_input_providers[missing_input], from_node, [missing_input])] + updated_path_info
                        added_executions.append([(backward_input_providers[missing_input], from_node, [missing_input])])

                updated_path_info.append((from_node, to_node, params))

            required_inputs = execution_lookup[to_node]['input_arg']
            missing_inputs = [input for input in required_inputs if input not in params]

            for target_set, execution_name in backward_combinations.items():
                found_inputs_set = self.check_set_in_list(set(target_set), missing_inputs)
                if found_inputs_set:
                    updated_path_info.append((execution_name, to_node, list(found_inputs_set)))
                    added_executions.append((execution_name, to_node, list(found_inputs_set)))
                    break

            missing_inputs = list(set(missing_inputs) - found_inputs_set)

            for missing_input in missing_inputs:
                if missing_input in backward_input_providers:
                    updated_path_info.append((backward_input_providers[missing_input], to_node, [missing_input]))
                    added_executions.append((backward_input_providers[missing_input], to_node, [missing_input]))

            updated_path_info.append((from_node, to_node, params))
            iteration += 1
        return updated_path_info, added_executions

    @staticmethod
    def check_set_in_list(target_set, missing_inputs):
        if target_set.issubset(missing_inputs):
            return target_set
        else:
            return set()

    @staticmethod
    def find_missing_items(data, target_list):
        all_strings = set()
        for item in data:
            all_strings.add(item[0])
            all_strings.add(item[1])
            all_strings.update(item[2])

        missing_items = [item for item in target_list if item not in all_strings]

        return missing_items

    @staticmethod
    def generate_json_representation(path_info):
        path_json = [{
            'from': from_node,
            'to': to_node,
            'using_parameter': params
        } for from_node, to_node, params in path_info]

        return path_json

    @staticmethod
    def remove_duplicates(tuples_list):
        unique_tuples = set()
        for item in tuples_list:

            if isinstance(item[-1], list):
                unique_tuples.add(item[:-1] + (tuple(item[-1]),))
            else:
                unique_tuples.add(item)

        result = [item[:-1] + (list(item[-1]),) if isinstance(item[-1], tuple) else item for item in unique_tuples]

        return result

    @staticmethod
    def filter_nodes(nodes_list):
        unique_nodes = {}

        for node in nodes_list:
            key = (node['depth'], node['i'])

            if key in unique_nodes:
                if not unique_nodes[key]['chainedNodes'] and node['chainedNodes']:
                    unique_nodes[key] = node
            else:
                unique_nodes[key] = node

        return list(unique_nodes.values())

