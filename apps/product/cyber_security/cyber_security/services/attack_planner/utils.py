
class Utils:
    @staticmethod
    def format_response(response_dict):
        rank = response_dict['rank']
        reasoning = response_dict['Reasoning']
        words = reasoning.split()
        reasoning_lines = []

        for i in range(0, len(words), 5):
            reasoning_lines.append(' '.join(words[i:i + 5]))

        formatted_reasoning = '\n'.join(reasoning_lines)
        output = f"Rank: {rank}\nReasoning:\n{formatted_reasoning}"

        return output

    @staticmethod
    def remove_actions(data_structure, actions_to_remove):
        modified_data_structure = [
            (action, next_action, attributes)
            for action, next_action, attributes in data_structure
            if action not in actions_to_remove and next_action not in actions_to_remove
        ]

        return modified_data_structure

    @staticmethod
    def extract_unique_values(data_dict):
        unique_values = set()
        for values in data_dict.values():
            unique_values.update(values)
        return list(unique_values)

    @staticmethod
    def translate_to_tuple(input_list):
        result = []
        for item in input_list:
            task1, task2, fields = item
            result.append((task1, task2, fields))
        return result

    @staticmethod
    def separate_tuples(tuples_list):
        with_value = []
        without_value = []

        for item in tuples_list:
            if item[2]:
                with_value.append(item)
            else:
                without_value.append(item)

        return with_value, without_value

    @staticmethod
    def combine_values_to_set(nested_list):
        combined_set = set()

        for sublist in nested_list:
            for item in sublist:
                combined_set.add(item[0])
                combined_set.add(item[1])

        return combined_set

    @staticmethod
    def find_input_args_for_single_execution(data_list, execution_name):
        for item in data_list:
            if item.get('name') == execution_name:
                return item.get('input_args', [])
        return []

    @staticmethod
    def extract_execution_names_from_chains(data_structure):
        actions = set()
        for action, next_action, _ in data_structure:
            actions.add(action)
            actions.add(next_action)

        return list(actions)

    @staticmethod
    def flatten_chain(chain):
        flattened = []
        for item in chain:
            if isinstance(item, list):
                flattened.extend(item)
            else:
                flattened.append(item)
        return flattened


    @staticmethod
    def find_index_by_name_and_group(dict_list, name, group):
        for index, item in enumerate(dict_list):
            if isinstance(item, dict) and item.get('name') == name and item.get('group') == group:
                return index
        return None

    @staticmethod
    def filter_above_threshold(data, threshold, max_results_per_key):
        result = {}
        used_items = set()

        for category, items in data.items():
            filtered_items = {k: v for k, v in items.items() if v > threshold}
            unique_filtered_items = {k: v for k, v in filtered_items.items() if k not in used_items}
            sorted_items = dict(sorted(unique_filtered_items.items(), key=lambda item: item[1], reverse=True))
            limited_items = dict(list(sorted_items.items())[:max_results_per_key])
            if limited_items:
                result[category] = limited_items
                used_items.update(limited_items.keys())

        return result