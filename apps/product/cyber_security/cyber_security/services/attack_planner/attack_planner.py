import asyncio
import time
import uuid
from typing import final

from logger import logger

from cyber_security.models.event_data import EventDataType

from .execution_store import ExecutionStore
from .graph_handler import <PERSON><PERSON>h<PERSON>and<PERSON>
from .prompts import Prompts
from .story_builder import StoryBuilder
from .utils import Utils

execution_store: ExecutionStore = asyncio.run(ExecutionStore.create(init_list=True))
utils: Utils = Utils()
graph_handler: GraphHandler = GraphHandler(execution_store, utils)

class AttackPlanner:
    def __init__(self ):
        self.prompts: Prompts = Prompts()
        self.utils: Utils = utils
        self.graph_handler = graph_handler
        self.story_builder = StoryBuilder(self.prompts, execution_store, self.graph_handler, self.utils)

    @final
    async def start(self, event: EventDataType, question: str):
        start_time = time.time()
        template_name = await self.prompts.write_template_name(question)

        # if self.event_handler:
        #     await self.event_handler.send_update(
        #         event_data=event,
        #         message=f":loading-2: Disassembling the story to use cases..",
        #     )

        list_of_use_cases = await self.prompts.disassemble_story_to_use_cases(question)
        results = {}


        # if self.event_handler:
        #     await self.event_handler.send_update(
        #         event_data=event,
        #         message=f":loading-2: Ranking Executions based on use cases..",
        #     )

        async def process_use_case(q):
            use_case, execution_options = await self.story_builder.process_single_question(q, top_n=10, min_confidence=0.7)
            return q, await self.prompts.rank_options_based_on_use_case(use_case, execution_options)

        async def process_all_use_cases():
            semaphore = asyncio.Semaphore(30)
            tasks = []

            for q in list_of_use_cases:
                task = asyncio.create_task(process_use_case_with_semaphore(q, semaphore))
                tasks.append(task)

            results_list = await asyncio.gather(*tasks)

            for q, ranked_options in results_list:
                results[q] = ranked_options

        async def process_use_case_with_semaphore(q, semaphore):
            async with semaphore:
                return await process_use_case(q)

        await process_all_use_cases()

        max_results_per_key = 1
        score_threshold = 60
        filtered_results = self.utils.filter_above_threshold(results, score_threshold, max_results_per_key)

        # if self.event_handler:
        #     await self.event_handler.send_update(
        #         event_data=event,
        #         message=f":loading-2: Assembling Chained Attack Scenarios..",
        #     )

        final_results = {}
        apt_flow = {
            'attack_plan_1': list_of_use_cases
        }

        for attack_plan, attack_steps in apt_flow.items():
            logger.info(f"Attack Plan: {attack_plan}, Attack Steps: {attack_steps}")
            final_results[attack_plan] = {}
            final_results[attack_plan]['attack_chains'] = await self.story_builder.run_question(attack_steps, print_images=False)

        text_plan = self.story_builder.parse_attack_plan(final_results)

        final = {"single": [], "chained_": []}
        for k, v in final_results.items():
            if "attack_plan" not in k:
                continue
            chains = list(v['attack_chains'].values())
            for mini_chain in chains:
                for mini_mini_chain in mini_chain:
                    data = mini_mini_chain['chains']
                    rank = mini_mini_chain['story_rank']
                    if rank < 30:
                        continue
                    chained, single_executions = self.utils.separate_tuples(data)
                    if single_executions:
                        final["single"].append(single_executions)
                    if chained:
                        final["chained_"].append(chained)
        final['chained'] = sorted(final['chained_'], key=len, reverse=True)[:15]
        final['single'] = filtered_results

        stories_desc = self.story_builder.describe_stories(final['chained'])
        singles = final['single'].keys()
        str_to_send = "single executions: " + str(singles) + ", Attack chained scenarios: " + str(stories_desc)
        template_description = await self.prompts.write_template_description(str_to_send)
        final["template_description"] = template_description
        final["template_name"] = template_name

        base_template = {
            "template": {
                "cleanup_at_end": True,
                "description": final['template_description'],
                "author": "Cymulate Demo",
                "public": False,
                "name": final['template_name'],
                "commands": [],
                "scenariosDelay": 0,
                "addCleanup": True,
                "group": "User Templates"
            },
            "action": ""
        }
        scenario_idx = -1


        # if self.event_handler:
        #     await self.event_handler.send_update(
        #         event_data=event,
        #         message=f":loading-2: Assembling the final template..",
        #     )

        for _, single_executions in final['single'].items():
            for execution_name, score in single_executions.items():
                try:
                    logger.info(f"Execution Name: {execution_name}, Score: {score}")
                    scenario_idx += 1
                    scenario_group_id = str(uuid.uuid4())
                    new_single_node = {
                        "name": execution_name,
                       "_id": execution_store.executions_mapping[execution_name]["_id"],
                       "supportedPlatforms": execution_store.executions_mapping[execution_name]["supportedPlatforms"],
                       "os": execution_store.executions_mapping[execution_name]["os"],
                       "inputArguments": execution_store.executions_mapping[execution_name]["inputArguments"],
                       "executor": execution_store.executions_mapping[execution_name]["executor"],
                       "cleanupCommands": execution_store.executions_mapping[execution_name]["cleanupCommands"],
                       "successIndicators": execution_store.executions_mapping[execution_name]["successIndicators"],
                       "dependencies": execution_store.executions_mapping[execution_name]["dependencies"],
                       "id": str(uuid.uuid4()),
                       "group": scenario_group_id,
                       "groupIndex": scenario_idx,
                       "dir": "up",
                       "isMain": False,
                       "depth": 0,
                       "i": 1,
                       "chainedNodes": [],
                    }

                    logger.info(f"created New Single Node: {new_single_node}\n*****************")
                    base_template['template']["commands"].append(new_single_node)
                    break
                except:
                    logger.info(f"!!!!!!!!Failed to parse single execution {execution_name}")
                    continue

        for chaining_option in final['chained']:
            separated_graphs = self.graph_handler.separate_disconnected_graphs(chaining_option)
            for graph in separated_graphs:
                try:
                    logger.info(f"Graph {scenario_idx}:")
                    try:
                        depth_level_is_last = await self.graph_handler.parse_attack_plan_depth_level_is_last(graph)
                    except:
                        logger.info(f"!!!!!Failed to parse attack plan depth level is last Graph{scenario_idx}")
                        continue

                    logger.info(f"Depth Level Is Last: {depth_level_is_last}")
                    scenario_idx += 1
                    scenario_group_id = str(uuid.uuid4())
                    logger.info(f"Scenario Group ID: {scenario_group_id}")
                    new_graph = []
                    for part in graph:
                        start_node = part[0]
                        end_node = part[1]
                        connecting_input_args = part[2]
                        logger.info(
                            f"Start Node: {start_node}, End Node: {end_node}, Connecting Input Args: {connecting_input_args}")

                        start_node_exist_in_template = self.utils.find_index_by_name_and_group(new_graph,
                                                                                    start_node, scenario_group_id)
                        if start_node_exist_in_template is None:
                            logger.info(f"Start Node DOES NOT!! Exist In Template: {start_node}")
                            new_start_node = {
                                "name": start_node,
                                "_id": execution_store.executions_mapping[start_node]["_id"],
                                "supportedPlatforms": execution_store.executions_mapping[start_node]["supportedPlatforms"],
                                "os": execution_store.executions_mapping[start_node]["os"],
                                "inputArguments": execution_store.executions_mapping[start_node]["inputArguments"],
                                "executor": execution_store.executions_mapping[start_node]["executor"],
                                "cleanupCommands": execution_store.executions_mapping[start_node]["cleanupCommands"],
                                "successIndicators": execution_store.executions_mapping[start_node]["successIndicators"],
                                "dependencies": execution_store.executions_mapping[start_node]["dependencies"],
                                "id": str(uuid.uuid4()),
                                "group": scenario_group_id,
                                "groupIndex": scenario_idx,
                                "dir": "up",
                                "isMain": depth_level_is_last[start_node]["is_last"],
                                "depth": depth_level_is_last[start_node]["depth"],
                                "i": depth_level_is_last[start_node]["level"],
                                "chainedNodes": [],
                            }

                            logger.info(f"created New Start Node: {new_start_node}\n*****************")
                            new_graph.append(new_start_node)
                        else:
                            logger.info(f"Start Node Exist In Template, skipping : {start_node}")
                            new_start_node = new_graph[start_node_exist_in_template]
                        ######part 2

                        end_node_exist_in_template = self.utils.find_index_by_name_and_group(new_graph, end_node, scenario_group_id)

                        if end_node_exist_in_template is None:
                            logger.info(f"End Node DOES NOT Exist In Template: {end_node_exist_in_template}")
                            chained_nodes = []
                            chained_nodes.append(
                                {"id": new_start_node["id"], "_id": new_start_node["_id"], "isConnected": True})

                            input_args = []
                            for in_arg in execution_store.executions_mapping[end_node]["inputArguments"]:
                                if in_arg['name'] in connecting_input_args:
                                    tmp = in_arg.copy()
                                    tmp["chainedCommand"] = new_start_node["id"]
                                    tmp["inputType"] = "chain"
                                    tmp["outputParser"] = execution_store.executions_mapping[start_node]['outputParsers'][in_arg['name']]['uuid']
                                    input_args.append(tmp)
                                else:
                                    input_args.append(in_arg)

                            new_end_node = {
                                "name": end_node,
                                "_id": execution_store.executions_mapping[end_node]["_id"],
                                "supportedPlatforms": execution_store.executions_mapping[end_node]["supportedPlatforms"],
                                "os": execution_store.executions_mapping[end_node]["os"],
                                "inputArguments": input_args,
                                "executor": execution_store.executions_mapping[end_node]["executor"],
                                "cleanupCommands": execution_store.executions_mapping[end_node]["cleanupCommands"],
                                "successIndicators": execution_store.executions_mapping[end_node]["successIndicators"],
                                "dependencies": execution_store.executions_mapping[end_node]["dependencies"],
                                "id": str(uuid.uuid4()),
                                "group": scenario_group_id,
                                "groupIndex": scenario_idx,
                                "dir": "up",
                                "isMain": depth_level_is_last[end_node]["is_last"],
                                "depth": depth_level_is_last[end_node]["depth"],
                                "i": depth_level_is_last[end_node]["level"],
                                "chainedNodes": chained_nodes,
                            }
                            logger.info(f"CREATED New End Node: {new_end_node}\n*******************")
                            new_graph.append(new_end_node)

                        else:  # if end_node_exist_in_template exist True
                            base_template_content = new_graph[end_node_exist_in_template]
                            logger.info(f"End Node Exist In Template, before update: {base_template_content}")
                            new_graph[end_node_exist_in_template]['chainedNodes'].append(
                                {"id": new_start_node["id"], "_id": new_start_node["_id"], "isConnected": True})

                            for index_of_dict, in_arg in enumerate(
                                    new_graph[end_node_exist_in_template].get('inputArguments', [])):
                                if isinstance(in_arg, dict) and 'name' in in_arg:
                                    if in_arg['name'] in connecting_input_args:
                                        try:
                                            new_graph[end_node_exist_in_template]['inputArguments'][index_of_dict][
                                                "chainedCommand"] = new_start_node["id"]
                                            new_graph[end_node_exist_in_template]['inputArguments'][index_of_dict][
                                                "inputType"] = "chain"
                                            new_graph[end_node_exist_in_template]['inputArguments'][index_of_dict][
                                                "outputParser"] = \
                                                execution_store.executions_mapping[start_node]['outputParsers'][
                                                    in_arg['name']]['uuid']
                                        except KeyError as e:
                                            logger.error(
                                                f"Missing key in execution mapping for start_node '{start_node}': {e}")
                                else:
                                    logger.warning(
                                        f"Skipping input argument at index {index_of_dict} due to unexpected type or missing 'name'.")

                            logger.info(f"End Node Exist In Template, after update: {base_template_content}")
                except:
                    logger.critical(f"Failed to parse attack plan depth level is last Graph{scenario_idx}")
                    continue

                new_graph = self.graph_handler.filter_nodes(new_graph)
                base_template['template']["commands"] = list(
                    filter(None, base_template['template']["commands"] + new_graph)
                )

        final['cymulate_template'] = base_template

        return {
            "cymulate_template": final['cymulate_template'],
            "template_name": final['template_name'],
            "ui_response": (await self.prompts.write_screen_output({"single": final["single"], "chained": final["chained"]})) + "\n\n **Attack Planner AI has generated an advanced scenario template. Please follow the link below to view it.**"
        }
