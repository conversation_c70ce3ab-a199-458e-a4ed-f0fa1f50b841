import re
from typing import Any, Dict

import aiohttp
from logger import logger

from cyber_security.models.enums import ClientType<PERSON>num
from cyber_security.models.event_data import EventDataType
from cyber_security.models.user_data import Endpoints, UserData
from cyber_security.services.attack_planner import AttackPlanner
from cyber_security.utils.glbl import fetch_and_clean_html, is_valid_url


class AttackPlannerManager:
    def __init__(self, client_type: ClientTypeEnum, ):
        self.client_type: ClientTypeEnum = client_type
        self.attack_planner = AttackPlanner()

    async def start(self, message: str, cookie: str, user_data: UserData, event: EventDataType = None) -> list[str | list[Any] | Any]:
        docs = []
        question = re.sub(r'(?i)attack planner:', '', message).strip()
        try:
            message = re.sub(r'[\n\r]', '', (await self._process_str_or_url_from_message(question)).strip())
        except Exception as e:
            logger.opt(exception=True).error(f"Error processing message: {e}")
            error = "Unable to fetch the provided URL as our server IP address is being blocked. You can alternatively copy the relevant article text and paste it into this prompt."
            return [error, docs]

        logger.info(f"Fetching attack planner for message: {message}")

        try:
            response = await self.attack_planner.start(event, message)
            logger.info(f"Attack planner response: {response["template_name"]}")
            await self.send_request(cookie, user_data.endpoints, response["cymulate_template"])
            logger.info(f"Attack planner ({response["template_name"]}) saved successfully")
            docs = [{"template": response["template_name"], "type": "attack_planner"}]
            attack_planner_answer = response["ui_response"]
        except Exception as e:
            logger.opt(exception=True).error(f"Error in attack planner: {e}")
            attack_planner_answer = "An error occurred while processing the attack planner. Please try again later."

        return [attack_planner_answer, docs]

    @staticmethod
    async def send_request(cookie: str, endpoints: Endpoints, payload: Dict[str, Any]) -> Any:
        url = endpoints.app_server + '/api/red-team/template?env=default'

        headers = {
            'Content-Type': 'application/json',
            'Cookie': cookie
        }

        timeout = aiohttp.ClientTimeout(total=120)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.post(url, json=payload, headers=headers) as response:
                    response.raise_for_status()
                    logger.info(f"Request successful ({url}): {response.status}")
                    return await response.json()
            except aiohttp.ClientError as e:
                logger.opt(exception=True).error(f"Request failed ({url}): {e}")
                raise


    @staticmethod
    async def _process_str_or_url_from_message(message: str):
        trimmed_message = message.strip()
        if is_valid_url(trimmed_message):
            return await fetch_and_clean_html(trimmed_message)
        else:
            return trimmed_message
