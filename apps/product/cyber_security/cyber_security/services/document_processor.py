import asyncio
import os
from typing import Any, List, Literal, Optional

from corellm.providers.provider import Model<PERSON>rovider
from ddtrace.llmobs import LLMObs
from ddtrace.llmobs.decorators import retrieval
from elasticsearch import ApiError
from langchain_elasticsearch import ElasticsearchStore
from logger import logger

from cyber_security.infra.init import infra
from cyber_security.models.enums import ClientTypeEnum

embeddings = ModelProvider().get_embeddings(model_name=os.getenv("BOT_EMBEDDING_MODEL",'azure_text_embedding_3_small'))


docs360_store: ElasticsearchStore = ElasticsearchStore(
    es_connection=infra.elasticsearch_client,
    index_name=infra.secret.chatbot_ai.ES_INDEX,
    embedding=embeddings,
)

swagger_api_store: ElasticsearchStore = ElasticsearchStore(
    es_connection=infra.elasticsearch_client,
    index_name=infra.get_secret(key="SWAGGER_API_INDEX", default="cy-ai-swagger-api"),
    embedding=embeddings,
)

type documentType = Literal["docs360", "swagger_api"]

class DocumentProcessor:
    def __init__(self, type: documentType, client_type: Optional[ClientTypeEnum] = None):
        self.client_type: ClientTypeEnum = client_type
        self.max_concurrent_tasks: int = 8
        self.type: documentType = type

    def get_retrieval(self):
        if self.type == "docs360":
            return self.get_docs360_store()
        elif self.type == "swagger_api":
            return self.get_swagger_api_store()

    @retrieval(name="document360")
    def get_docs360_store(self):
        return docs360_store

    @retrieval(name="swagger_api")
    def get_swagger_api_store(self):
        return swagger_api_store

    async def get_docs(self, question: str, assessments: Optional[List[dict]] = None) -> List[Any]:
        if not assessments:
            assessments = []

        additional_info = " ".join(
            f"{assessment.get('fullModuleName', '')} {assessment.get('failedText', '') or ''}"
            for assessment in assessments
        )

        enhanced_question = f"{question} {additional_info}"

        logger.info(f"Retrieving documents for question: '{question}'")
        docs = await self.retry_query(
            query=enhanced_question,
            retries=3,
            delay=5,
            timeout=30
        )
        logger.info(f"Retrieved {len(docs)} documents for question: '{enhanced_question}'")

        if not docs:
            return []

        metadata_only = [
            doc.get('metadata', {}).get('name') if isinstance(doc, dict) else doc.metadata
            for doc in docs
        ]

        LLMObs.annotate(
            span=None,
            input_data=question,
            metadata={ "docs": metadata_only },
            tags={"additional_info": additional_info},
        )

        return docs

    async def retry_query(self, query: str, retries: int, delay: int, timeout: int) -> List[Any]:
        for attempt in range(retries):
            try:
                return await self.get_retrieval().asimilarity_search(
                    query=query,
                    vector_field="vector",
                    k=2 if self.type == "docs360" else 10,
                    score_threshold= 0.85,
                    filter={
                        "bool": {
                            "must_not": [{
                                "match": {"metadata.resource": "dynamic-dashboards"}
                            }]
                        }
                    },
                    fetch_k=10000,
                )
            except asyncio.TimeoutError:
                if attempt < retries - 1:
                    logger.warning(f"Timeout occurred, retrying... ({attempt + 1}/{retries})")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Retriever query timed out after {retries} attempts")
                    raise
            except ApiError as e:
                logger.error(f"Retriever query failed: {e}")
                raise
