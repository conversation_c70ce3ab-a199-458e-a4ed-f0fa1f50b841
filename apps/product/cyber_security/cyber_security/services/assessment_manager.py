import asyncio
from typing import Any, Dict, List, Optional

from elasticsearch import NotFoundError
from logger import logger

from cyber_security.infra.init import infra
from cyber_security.models.user_data import UserData


class AssessmentManager:
    def __init__(self, validate_user: bool = False):
        self.validate_user: bool = validate_user
        pass

    async def get_assessments_logs(
            self,
            assessment_ids: Optional[List[str]] = None,
            apm_errors: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        if not assessment_ids:
            return []

        body = {
            "size": 15 if apm_errors and len(apm_errors) else 60,
            "_source": ["module", "message", "level", "@timestamp"],
            "query": {
                "bool": {
                    "should": [
                        {"terms": {"attackId": assessment_ids}},
                        {"terms": {"agentId": assessment_ids}}
                    ],
                    "minimum_should_match": 1,
                    "filter": [
                        {"terms": {"level": ["Fatal", "Error"]}}
                    ]
                }
            },
            "sort": [
                {"level": {"order": "desc"}},
                {"@timestamp": {"order": "desc"}}
            ]
        }
        return await self.get_documents_by_query("cy-agent-logs", body)

    async def get_apm_errors(self, mongo_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        if mongo_ids is None or self.validate_user:
            return []

        body = {
            "size": 15,
            "_source": ["error.log.message", "error.exception.message", "labels", "url.original", "service.name",
                        "service.environment"],
            "query": {
                "bool": {
                    "should": [],
                    "minimum_should_match": 1
                }
            },
            "aggs": {
                "error_messages": {
                    "terms": {
                        "field": "error.grouping_key",
                        "size": 10000,
                        "order": {"_count": "desc"}
                    },
                    "aggs": {
                        "top_hits": {
                            "top_hits": {
                                "_source": {"includes": ["error.log.message", "error.exception.message", "labels",
                                                         "url.original", "service.name", "service.environment"]},
                                "size": 1
                            }
                        }
                    }
                }
            }
        }
        for mongo_id in mongo_ids:
            body["query"]["bool"]["should"].extend([
                {"wildcard": {"labels.attackID": {"boost": 1, "case_insensitive": True, "value": f"*{mongo_id}*",
                                                  "rewrite": "constant_score"}}},
                {"wildcard": {"labels.shortBody": {"boost": 1, "case_insensitive": True, "value": f"*{mongo_id}*",
                                                   "rewrite": "constant_score"}}},
                {"wildcard": {"http.request.body": {"boost": 1, "case_insensitive": True, "value": f"*{mongo_id}*",
                                                    "rewrite": "constant_score"}}},
                {"match": {"http.request.headers.X-Attackid": mongo_id}}
            ])
        return await self.get_documents_by_query("logs-apm.error*", body)

    async def get_assessments_data(
            self,
            mongo_ids: Optional[List[str]] = None,
            user_data: UserData = None,
            ignore_validation=False
    ) -> List[Dict[str, Any]]:

        software_env: str = user_data.env if user_data else None
        client_ids: List[str] = user_data.client_ids if user_data else None
        user_env: Optional[List[str]] = (
            user_data.permissions.env
            if user_data and hasattr(user_data, "permissions") and hasattr(user_data.permissions, "env")
            else None
        )
        user_modules: List[str] = (
            user_data.permissions.modules
            if user_data and hasattr(user_data, "permissions") and hasattr(user_data.permissions, "modules")
            else []
        )

        if not mongo_ids:
            return []

        body = {
            "size": 5,
            "_source": [
                "fullModuleName", "attackID", "failedText", "name", "agentVersion", "clientID", "progress",
                "clientInfo.paying", "scheduleLoop", "csManager", "environment", "env", "integrations",
                "dateStarted", "dateFinished", "stoppedTime", "lastUpdated", "status", "score", "agentName",
                "sentWithAPI", "messages", "totalPayloads", "totalSent", "totalArrived"
            ],
            "query": {
                "bool": {
                    "must": [
                        {
                            "terms": {
                                "attackID": mongo_ids
                            }
                        },
                        {
                            "range": {
                                "date": {
                                    "lt": "now"
                                }
                            }
                        }
                    ]
                }
            }
        }

        if self.validate_user and not ignore_validation:
            body["query"]["bool"]["must"].append({
                "terms": {
                    "clientID": client_ids
                }
            })

            body["query"]["bool"]["must"].append({
                "terms": {
                    "moduleName": user_modules or []
                }
            })

            body["query"]["bool"]["must"].append({
                "bool": {
                    "should": [
                        {
                            "terms": {
                                "envID": user_env
                            }
                        },
                        {
                            "term": {
                                "env": {
                                    "value": "default"
                                }
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            })

            if software_env:
                body["query"]["bool"]["must"].append({
                    "terms": {
                        "softwareEnv": [software_env]
                    }
                })

        return await self.get_documents_by_query("cymulate.*.*", body)

    async def get_agent_metrics_data(self, assessments: List[Dict[str, Any]], usage_threshold: int = 80) -> List[
        Dict[str, Any]]:
        if not assessments:
            return []

        try:
            date_started_values = [a.get('dateStarted') for a in assessments if 'dateStarted' in a]
            date_finished_values = [a.get('dateFinished') for a in assessments if 'dateFinished' in a]

            if not date_started_values or not date_finished_values:
                logger.warning("No valid dateStarted or dateFinished values found in assessments")
                return []

            start_date = min(date_started_values)
            end_date = max(date_finished_values)
            agent_names = list({a['agentName'] for a in assessments if 'agentName' in a})
            client_ids = list({a['clientID'] for a in assessments if 'clientID' in a})

            body = {
                "_source": ["agentVersion", "clientID", "cpu", "memory", "disk", "@timestamp"],
                "query": {
                    "bool": {
                        "must": [
                            {"terms": {"agentName": agent_names}},
                            {"terms": {"clientID": client_ids}},
                            {"range": {"@timestamp": {"gte": start_date, "lte": end_date,
                                                      "format": "strict_date_optional_time"}}},
                        ],
                        "filter": [
                            {
                                "bool": {
                                    "should": [
                                        {"range": {"cpu.usage": {"gte": usage_threshold}}},
                                        {
                                            "script": {
                                                "script": {
                                                    "source": f"""
                                                    doc['memory.usage'].size() > 0 && doc['memory.total'].size() > 0 
                                                    ? (doc['memory.usage'].value / doc['memory.total'].value) * 100 >= {usage_threshold}
                                                    : false
                                                    """,
                                                    "lang": "painless"
                                                }
                                            }
                                        },
                                        {
                                            "script": {
                                                "script": {
                                                    "source": f"""
                                                    doc['disk.usage'].size() > 0 && doc['disk.total'].size() > 0 
                                                    ? (doc['disk.usage'].value / doc['disk.total'].value) * 100 >= {usage_threshold}
                                                    : false
                                                    """,
                                                    "lang": "painless"
                                                }
                                            }
                                        }
                                    ],
                                    "minimum_should_match": 1
                                }
                            }
                        ]
                    }
                }
            }

            return await self.get_documents_by_query("agent-metrics", body)
        except Exception as e:
            logger.error(e)
            return []

    @staticmethod
    async def get_documents_by_query(index_name: str, body: Dict[str, Any]) -> List[Dict[str, Any]]:
        try:
            response = await asyncio.to_thread(infra.elasticsearch_client.search, index=index_name, body=body)
        except Exception as e:
            logger.error(e)
            return []
        try:
            documents = [doc["_source"] for doc in response["hits"]["hits"]]
        except Exception as e:
            logger.error(e)
            documents = []
        return documents

    @staticmethod
    async def create_data_stream_if_not_exists(data_stream_name):
        try:
            exists = await asyncio.to_thread(
                infra.elasticsearch_client.indices.exists,
                index=data_stream_name
            )

            if not exists:
                await asyncio.to_thread(
                    infra.elasticsearch_client.indices.create_data_stream,
                    name=data_stream_name
                )
                logger.info(f"Created data stream '{data_stream_name}'")
        except Exception as e:
            logger.error(f"Failed to create data stream '{data_stream_name}': {e}")

    async def upsert_document(self, attack_id, doc_data):
        data_stream_name = "assessment-logs-summary"
        await self.create_data_stream_if_not_exists(data_stream_name)

        try:
            search_response = await asyncio.to_thread(
                infra.elasticsearch_client.search,
                index=data_stream_name,
                body={
                    "query": {
                        "term": {
                            "attackID.keyword": {
                                "value": attack_id
                            }
                        }
                    },
                    "seq_no_primary_term": True
                }
            )

            hits = search_response['hits']['hits']
            if hits:
                doc_id = hits[0]['_id']
                seq_no = hits[0]['_seq_no']
                primary_term = hits[0]['_primary_term']
                backing_index = hits[0]['_index']
                await asyncio.to_thread(
                    infra.elasticsearch_client.index,
                    index=backing_index,
                    id=doc_id,
                    document=doc_data,
                    if_seq_no=seq_no,
                    if_primary_term=primary_term
                )
                logger.info(f"Document with attackID '{attack_id}' updated successfully.")
            else:
                await asyncio.to_thread(
                    infra.elasticsearch_client.index,
                    index=data_stream_name,
                    document=doc_data
                )
                logger.info(f"Document with attackID '{attack_id}' created successfully.")
        except NotFoundError:
            logger.error(f"Index '{data_stream_name}' not found")
        except Exception as e:
            logger.error(f"Failed to upsert document with attackID '{attack_id}': {e}")
