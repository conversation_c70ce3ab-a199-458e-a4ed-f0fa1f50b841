from typing import Dict

import aiohttp
from logger import logger

from cyber_security.models.user_data import Endpoints


class ProfileManager:
    def __init__(self):
        pass
    

    @staticmethod
    async def get_profile_data(cookie: str, endpoints: 'Endpoints') -> Dict[str, str]:
        url = endpoints.app_server + '/api/me'

        headers = {
            'Content-Type': 'application/json',
            'Cookie': cookie
        }

        timeout = aiohttp.ClientTimeout(total=120)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.get(url, headers=headers) as response:
                    if response.status == 401:
                        logger.error(f"Unauthorized access ({url}): {response.status}")
                        raise Exception("Unauthorized access: Invalid or expired cookie.")

                    response.raise_for_status()
                    logger.info(f"Request successful ({url}): {response.status}")
                    return await response.json()
            except aiohttp.ClientError as e:
                logger.error(f"Request failed ({url}): {e}")
                raise
