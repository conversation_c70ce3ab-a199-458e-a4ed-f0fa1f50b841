import asyncio
import copy
import json
import os
from collections import Counter, defaultdict
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import aiohttp
from corellm.providers.provider import ModelProvider
from ddtrace.llmobs import LLMObs
from ddtrace.llmobs.decorators import retrieval
from elasticsearch import ApiError
from langchain_core.documents import Document
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_elasticsearch import ElasticsearchStore
from logger import logger

from cyber_security.infra.init import infra
from cyber_security.models.enums import ClientTypeEnum
from cyber_security.models.user_data import Endpoints

embeddings = ModelProvider().get_embeddings(model_name=os.getenv("BOT_EMBEDDING_MODEL",'azure_text_embedding_3_small'))


dynamic_dashboard_store: ElasticsearchStore = ElasticsearchStore(
    es_connection=infra.elasticsearch_client,
    index_name=infra.get_secret(key="DYNAMIC_DASHBOARD_WIDGETS_INDEX", default="cy-ai-widgets-docs"),
    embedding=embeddings,
)


class WidgetProcessor:
    def __init__(
        self,
        client_type: ClientTypeEnum,
     ):
        self.max_concurrent_tasks: int = 8
        self.client_type: ClientTypeEnum = client_type

    @retrieval(name="get_relevant_docs_widgets")
    async def get_docs(self, question: str, _env: str, client_ids: List[str], question_type: str) -> List[Dict[str, Any]]:
        logger.info(f"Retrieving documents for question: '{question}' with Client IDs: '{client_ids}'")

        docs = await self.retry_query(
            query=question,
            _env=_env,
            client_ids=client_ids,
            retries=3,
            delay=5,
            timeout=30
        )

        logger.info(f"Retrieved {len(docs)} documents for question: '{question}'")
        docs = await self.filter_widgets(docs, question, question_type)

        return docs

    @staticmethod
    async def retry_query(query: str, _env: str, client_ids: Optional[List[str]],
                          retries: int, delay: int, timeout: int) -> list[Document] | None:
        for attempt in range(retries):
            try:
                must_filters = [
                    {
                        "match": {
                            "metadata.dashboard_name": query.removesuffix(" Dashboard").removesuffix(" dashboard")
                        }
                    },
                    {
                        "term": {"metadata.resource.keyword": "dynamic-dashboards"}
                    },
                    {
                        "term": {"metadata.software_env.keyword": _env}
                    },
                    {
                        "term": {"metadata.public": True}
                    }

                ]

                # should_filters = [{"term": { "metadata.public": {"value": True }}}]
                #
                # if client_ids:
                #     should_filters.insert(0, {
                #         "bool": {
                #             "must": [
                #                 {"match": {"metadata.public": False}},
                #                 {"terms": {"metadata.client_id": client_ids}}
                #             ]
                #         }
                #     })

                filter_query = {
                    "bool": {
                        "must": must_filters,
                        # "should": should_filters,
                        #"minimum_should_match": 1
                    }
                }

                return await dynamic_dashboard_store.asimilarity_search(
                    query=query.removesuffix(" Dashboard").removesuffix(" dashboard"),
                    vector_field="vector",
                    k=20,
                    score_threshold=0.85,
                    filter=filter_query,
                    fetch_k=10000,
                    sort=[{
                        "metadata.dashboard_name.keyword": {
                            "order": "asc"
                        }
                    }]
                )
            except asyncio.TimeoutError:
                if attempt < retries - 1:
                    logger.warning(f"Timeout occurred, retrying... ({attempt + 1}/{retries})")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"Elasticsearch query timed out after {retries} attempts")
                    raise
            except ApiError as e:
                logger.error(f"Elasticsearch query failed: {e}")
                raise

    async def filter_widgets(
            self,
            docs: List[Any],
            question: str,
            question_type: str
    ) -> List[Dict[str, Any]]:
        if not docs:
            return []

        docs_serializable = [
            {
                'page_content': doc.page_content,
                'metadata': doc.metadata
            }
            for doc in docs
        ]

        if question_type == 'dashboards':
            dashboard_names = [doc['metadata']['dashboard_name'] for doc in docs_serializable]
            dashboard_name_counts = Counter(dashboard_names)
            most_common_dashboard_name = dashboard_name_counts.most_common(1)[0][0]

            filtered_docs = [doc for doc in docs_serializable if
                             doc['metadata']['dashboard_name'] == most_common_dashboard_name]

            widget_ids = {doc['metadata']['widget_id'] for doc in filtered_docs}

        else:
            template = infra.langfuse.get_prompt('widgets_filter_template.jinja')
            rendered_template = template.render(question_type=question_type)

            llm_response = await ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME",'azure_model_2')).ainvoke([
                HumanMessage(content=f"User Question:\n{question}", id=datetime.now().timestamp()),
                SystemMessage(content=f"Widget Desired:\n{json.dumps(docs_serializable)}", id=datetime.now().timestamp()),
                HumanMessage(content=rendered_template, id=datetime.now().timestamp()),
            ])

            widget_ids = {wid.strip() for wid in llm_response.content.split(',')}

        widgets = [doc for doc in docs_serializable if doc.get('metadata', {}).get('widget_id', '') in widget_ids]

        return widgets

    @retrieval(name="get_relevant_docs_widgets_responses")
    async def get_widgets_responses(self, widget_docs: List[Dict[str, Any]], question: str, cookie: str, endpoints: Endpoints) -> List[Dict[str, Any]]:
        if not widget_docs:
            return []

        tasks = []
        semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        total_widget_docs = len(widget_docs)

        for i, widget in enumerate(widget_docs):
            doc_name = widget.get('metadata', {}).get('name', 'Unnamed document')
            logger.info(f"Processing widget {i + 1}/{total_widget_docs}: {doc_name}")

            task = asyncio.create_task(self.fetch(semaphore, widget, cookie, endpoints))
            tasks.append(task)

        _responses = await asyncio.gather(*tasks)
        responses = [result for result in _responses if result is not None]
        widget_ids = [{"widgetID": result['widget_id'], "dashboardID": result['dashboard_id']} for result in responses]

        logger.info(f"Finished processing all widgets for question: '{question}'")

        LLMObs.annotate(
            span=None,
            input_data=question,
            metadata={ "responses": responses },
            tags={ "widget_ids": widget_ids },
        )

        return responses

    async def get_widgets_answers(self, widget_docs: List[Dict[str, Any]], widget_responses: List[Dict[str, Any]],
                                  question: str, question_type: str) -> str:
        if not widget_docs or not widget_responses:
            return ""

        all_metadata = [widget.get('mitigations', {}) for widget in widget_responses]
        metadata_merged = self.merge_metadata_responses(all_metadata)

        widget_responses_cleaned = [{key: value for key, value in widget.items() if key != 'mitigations'} for widget in
                                    widget_responses][:20]

        template_name = 'widgets_summary_template.jinja' if question_type == 'widgets' else 'dashboard_summary_template.jinja'
        template = infra.langfuse.get_prompt(template_name).render()

        results = (await ModelProvider().get_llm(model_name=os.getenv("MODEL_NAME",'azure_model_2')).ainvoke([
            HumanMessage(content=f"User Question:\n{question}", id=datetime.now().timestamp()),
            SystemMessage(content=f"Data:\n{json.dumps(widget_responses_cleaned)}", id=datetime.now().timestamp()),
            SystemMessage(content=f"Here is the summary of the findings based on the context of test cases and risk: {json.dumps(metadata_merged)}. This summary provides an overview of the findings categorized by risk, along with the test cases and suggested mitigations for each risk level."),
            SystemMessage(content=template, id=datetime.now().timestamp()),
        ])).content

        logger.info(f"Summary of the findings based on the context of test cases and risk: {json.dumps(metadata_merged)}")
        return results

    async def fetch(self, semaphore: asyncio.Semaphore, doc: Dict[str, Any], cookie: str, endpoints: Endpoints) -> \
    dict[str, dict[str, Any] | None | Any] | None:
        async with semaphore:
            metadata = doc.get('metadata', {})
            base_url = metadata.get("url", "")

            if not base_url:
                logger.error("Missing URL in document metadata")
                return None

            base_url = (
                base_url.replace(":findingsServerUrl", endpoints.finding_server)
                .replace(":dashboardId", metadata.get("dashboard_id", ""))
                .replace(":widgetId", metadata.get("widget_id", ""))
            )

            logger.info(f"Fetching widget data from {base_url}")

            async with aiohttp.ClientSession() as session:
                try:
                    data = await self.fetch_data(session, base_url, cookie)
                    metadata = await self.fetch_metadata(session, base_url, cookie)

                    if data and 'data' in data and 'result' in data['data']:
                        if data.get('data').get("type") == "table":
                            return None

                        widget_data = {
                            "widget_id": doc.get('metadata', {}).get("widget_id"),
                            "dashboard_id": doc.get('metadata', {}).get("dashboard_id"),
                            "data": WidgetProcessor.trim_data(data.get('data')),
                            "name": doc.get('metadata', {}).get('name'),
                            "mitigations": metadata
                        }

                        return widget_data
                    else:
                        logger.error(f"Data format incorrect in response from {base_url}")
                except Exception as e:
                    logger.error(f"Failed to fetch widget data from {base_url}: {e}")
            return None

    @staticmethod
    async def fetch_data(session: aiohttp.ClientSession, url: str, cookie: str) -> Any | None:
        try:
            async with session.get(url, headers={"Cookie": cookie, "Cache-Control": "no-cache"},
                                   timeout=60) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logger.error(f"Failed to fetch data from {url}. Status code: {response.status}")
        except aiohttp.ClientError as e:
            logger.error(f"Client error while fetching data from {url}: {e}")
        except asyncio.TimeoutError:
            logger.error(f"Request timed out while fetching data from {url}")
        return None

    @staticmethod
    async def fetch_metadata(session: aiohttp.ClientSession, base_url: str, cookie: str) -> dict[str, list[dict[
        str, Any]] | list[dict[str, Any]] | list[dict[str, Any]]] | None:
        metadata_url = f"{base_url}?subType=distinct-values"
        logger.info(f"Fetching widget metadata from {metadata_url}")

        try:
            async with session.get(metadata_url, headers={"Cookie": cookie, "Cache-Control": "no-cache"},
                                   timeout=60) as response:
                if response.status == 200:
                    metadata = await response.json()
                    risks = [
                        {
                            "severity": item["severity"],
                            "total_findings": item["count"]
                        }
                        for item in metadata.get("data", {}).get("result", {}).get("riskSeverity", [])
                    ]
                    findings_names = [
                        {
                            "name": item["details"]["name"],
                            "mitigation": item["details"].get("mitigation", ""),
                            "total_findings": item["count"].get("count", 0)
                        }
                        for item in metadata.get("data", {}).get("result", {}).get("findingNameIDDetails", [])
                    ]
                    findings_test_cases = [
                        {
                            "name": item["details"]["name"],
                            "mitigation": item["details"].get("mitigation", ""),
                            "total_findings": item["count"].get("count", 0)
                        }
                        for item in metadata.get("data", {}).get("result", {}).get("testCaseIDDetails", [])
                    ]

                    return {
                        "findingsNames": findings_names[:50],
                        "findingsTestCases": findings_test_cases[:50],
                        "risks": risks[:50]
                    }
                else:
                    logger.error(f"Failed to fetch metadata from {metadata_url}. Status code: {response.status}")
        except aiohttp.ClientError as e:
            logger.error(f"Client error while fetching metadata from {metadata_url}: {e}")
        except asyncio.TimeoutError:
            logger.error(f"Request timed out while fetching metadata from {metadata_url}")
        return None

    @staticmethod
    def merge_metadata_responses(metadata_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        merged_findings_names = defaultdict(lambda: {"total_findings": 0, "mitigation": "", "mitigation_count": 0})
        merged_findings_test_cases = defaultdict(lambda: {"total_findings": 0, "mitigation": "", "mitigation_count": 0})
        merged_risks = defaultdict(int)

        for metadata in metadata_list:
            for finding in metadata.get("findingsNames", []):
                name = finding["name"]
                merged_findings_names[name]["total_findings"] += finding["total_findings"]
                if finding["mitigation"] and finding["total_findings"] > merged_findings_names[name]["mitigation_count"]:
                    merged_findings_names[name]["mitigation"] = finding["mitigation"]
                    merged_findings_names[name]["mitigation_count"] = finding["total_findings"]

            for test_case in metadata.get("findingsTestCases", []):
                name = test_case["name"]
                merged_findings_test_cases[name]["total_findings"] += test_case["total_findings"]
                if test_case["mitigation"] and test_case["total_findings"] > merged_findings_test_cases[name][
                    "mitigation_count"]:
                    merged_findings_test_cases[name]["mitigation"] = test_case["mitigation"]
                    merged_findings_test_cases[name]["mitigation_count"] = test_case["total_findings"]

            for risk in metadata.get("risks", []):
                merged_risks[risk["severity"]] += risk["total_findings"]

        merged_findings_names_cleaned = [
            {"name": name, "total_findings": data["total_findings"], "mitigation": data["mitigation"]}
            for name, data in merged_findings_names.items()
        ]
        merged_findings_test_cases_cleaned = [
            {"name": name, "total_findings": data["total_findings"], "mitigation": data["mitigation"]}
            for name, data in merged_findings_test_cases.items()
        ]
        merged_risks_cleaned = [
            {"severity": severity, "total_findings": count}
            for severity, count in merged_risks.items()
        ]

        return {
            "findingsNames": merged_findings_names_cleaned[:50],
            "findingsTestCases": merged_findings_test_cases_cleaned[:50],
            "risks": merged_risks_cleaned[:50]
        }

    @staticmethod
    def trim_list(obj: Union[Dict, List], limit: int) -> Union[Dict, List]:
        """Recursively trims lists in a dictionary or list."""
        if isinstance(obj, list):
            return obj[:limit]
        elif isinstance(obj, dict):
            return {key: WidgetProcessor.trim_list(value, limit) for key, value in obj.items()}
        return obj

    @staticmethod
    def trim_data(data: Dict[str, Any], trim: int = 50) -> Dict[str, Any]:
        """Recursively trims lists inside 'dimensions' and 'result' fields to a maximum of `trim` entries."""
        trimmed_data = copy.deepcopy(data)

        if "dimensions" in trimmed_data and isinstance(trimmed_data["dimensions"], dict):
            trimmed_data["dimensions"] = WidgetProcessor.trim_list(trimmed_data["dimensions"], trim)

        if "result" in trimmed_data:
            if isinstance(trimmed_data["result"], list):
                trimmed_data["result"] = trimmed_data["result"][:trim]

                for entry in trimmed_data["result"]:
                    if isinstance(entry, dict):
                        for key, value in entry.items():
                            if isinstance(value, list):
                                entry[key] = WidgetProcessor.trim_list(value, trim)

            elif isinstance(trimmed_data["result"], dict):
                trimmed_data["result"] = dict(list(trimmed_data["result"].items())[:trim])

        return trimmed_data
