import tempfile
from typing import Optional

from pydantic import BaseModel, Field, field_validator

from cyber_security.models import config_vars
from cyber_security.models.enums import ALLOWED_EXTENSIONS, FileCategory


class EventFile(BaseModel):
    name: str
    type: str
    size: int
    mimetype: str
    category: Optional[FileCategory] = Field(None)
    content: bytes
    file_path: Optional[str] = None

    def __init__(self, **data):
        super().__init__(**data)
        if not self.file_path and self.content:
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{self.type}") as tmp_file:
                tmp_file.write(self.content)
                self.file_path = tmp_file.name

    @field_validator("category", mode="before")
    def validate_file_category(cls, v, values) -> Optional[FileCategory]:
        file_type = values.data.get('type').lower()
        category = cls.get_category(file_type)
        if category is None:
            raise ValueError(f"Unsupported file type '{file_type}' for file '{values.data.get('name')}'")
        return category

    @field_validator("size")
    def validate_file_size(cls, v, values) -> int:
        if v > config_vars.MAX_FILE_SIZE_BYTES:
            raise ValueError(f"File '{values.data.get('name')}' exceeds the size limit of {config_vars.MAX_FILE_SIZE_BYTES} bytes.")
        return v

    @classmethod
    def get_category(cls, file_extension: str) -> Optional[FileCategory]:
        for category, extensions in ALLOWED_EXTENSIONS.items():
            if file_extension in extensions:
                return category
        return None

