
# from cyber_security.infra.init import config_manager

# CHATBOT_VERSION = config_manager(key="CHATBOT_VERSION", default="v2")
# ENV_VAR = config_manager(key="NODE_ENV", default="development")
# REDIS_URI = config_manager(key='websocket', default='redis://localhost:6379/0', secret_type=SecretTypeEnum.Redis)
# CLIENT_TYPE_STR: str = config_manager(key='CLIENT_TYPE', default=ClientTypeEnum.Slack.value).lower()

# MAX_FILE_SIZE_BYTES: int = int(config_manager(key="MAX_FILE_SIZE_BYTES", default=100 * 1024 * 1024))
# MAX_FILE_LINES: int = int(config_manager(key="MAX_FILE_LINES", default=300))
# MAX_FILE_CHARS_PER_LINE: int = int(config_manager(key="MAX_FILE_CHARS_PER_LINE", default=1000))
# ARCHIVE_PASSWORD: str = config_manager(key="ARCHIVE_PASSWORD", default="CyM2022!@")

# TAVILY_APY_KEY: SecretStr = config_manager(key="TAVILY_APY_KEY", default="")

# CYMULATE_API_TOKEN = config_manager(key="SASHA_CY_API_TOKEN")
# CYMULATE_API_URL = config_manager(key="SASHA_CY_API_URL")

# EXECUTION_RELOAD_INTERVAL = int(config_manager(key="EXECUTION_RELOAD_INTERVAL", default=86400)) # 24 hours
# EXECUTIONS_MAPPING_INDEX = config_manager(key="EXECUTIONS_MAPPING_INDEX", default="cy-ai-executions-mapping")
# EXECUTIONS_LIST_INDEX = config_manager(key="EXECUTIONS_LIST_INDEX", default="cy-ai-executions")

# THREAT_ACTORS_INDEX = config_manager(key="THREAT_ACTORS_INDEX", default="cy-ai-threat-actors")
# BAS2_SCENARIOS_INDEX = config_manager(key="BAS2_SCENARIOS_INDEX", default="cy-ai-bas2-scenarios")

# SLACK_APP_TOKEN = config_manager(key="SLACK_APP_TOKEN")
# SLACK_BOT_TOKEN = config_manager(key="SLACK_BOT_TOKEN")


# ELASTIC_API_KEY_DICT = config_manager(key="apiKey", secret_type=SecretTypeEnum.Elasticsearch)
# ELASTIC_CLOUD_ID = config_manager(key="cloudId", secret_type=SecretTypeEnum.Elasticsearch)
# ELSER_MODEL = config_manager(key="ELSER_MODEL", default=".elser_model_2_linux-x86_64")

# CHATBOT_CACHE_INDEX = config_manager(key="CHATBOT_CACHE_INDEX", default="cy-ai-chatbot-cache")
# SWAGGER_API_INDEX = config_manager(key="SWAGGER_API_INDEX", default="cy-ai-swagger-api")
# DYNAMIC_DASHBOARD_WIDGETS_INDEX = config_manager(key="DYNAMIC_DASHBOARD_WIDGETS_INDEX", default="cy-ai-widgets-docs")
# DOCS_360_INDEX = config_manager(key="ES_INDEX", default="cy-ai-docs")
# DOCUMENT360_PROJECT_ID = config_manager(key="DOCUMENT360_PROJECT_ID")
# DOCUMENT360_API_TOKEN = config_manager(key="DOCUMENT360_API_TOKEN")


# QUESTION_LENGTH_THRESHOLD = int(config_manager("QUESTION_LENGTH_THRESHOLD", default=5000))
# INDEX_CHAT_HISTORY = config_manager(key="ES_INDEX_CHAT_HISTORY", default="cy-ai-docs-chat-history")
# INDEX_CHAT_HISTORY_METRICS = config_manager(key="INDEX_CHAT_HISTORY_METRICS", default="cy-ai-docs-chat-history-metrics")


# ZENDESK_EMAIL = config_manager(key="ZENDESK_EMAIL")
# ZENDESK_TOKEN = config_manager(key="ZENDESK_TOKEN")
# ZENDESK_SUBDOMAIN = config_manager("ZENDESK_SUBDOMAIN", default="cymulate")
# ZENDESK_INTERVAL = int(config_manager(key="ZENDESK_INTERVAL", default=600)) # in seconds

# WEBSOCKET_TOKEN_REFRESH_PERIOD = int(config_manager(key='WS_TOKEN_REFRESH_PERIOD', default=60 * 60))
# WEBSOCKET_MAX_QUEUE_SIZE = int(config_manager(key='MAX_QUEUE_SIZE', default=500))
# WEBSOCKET_SERVER_URL = config_manager(key='WEBSOCKET_URL')


# JWT_SECRET = config_manager(key="JWT_SECRET")
# USER_ENDPOINTS = config_manager(key="endpoints", default={})

# SERVER_PORT = config_manager(key="SERVER_PORT", default=8080)