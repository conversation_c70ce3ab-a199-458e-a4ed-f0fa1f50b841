import uuid
from datetime import datetime
from typing import List, Literal, Optional, Union

from pydantic import BaseModel, ConfigDict, Field, field_validator

# from cyber_security.config.metadata_generator import Metadata
from cyber_security.models.enums import ClientTypeEnum


class BaseEvent(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True, use_enum_values=True)
    validate_user: bool = Field( default=False )
    listener: ClientTypeEnum
    question_id: str = Field(default_factory=lambda: str(uuid.uuid4()), alias="questionID")
    text: str
    channel: str
    thread_ts: Optional[str] = Field(default=None, alias="thread_ts")

class SlackBlockElement(BaseModel):
    """Represents an individual element within a block."""
    type: str
    elements: Optional[List[dict]] = None  # Nested elements

class SlackBlock(BaseModel):
    """Represents a block in Slack event data."""
    block_id: Optional[str] = None
    type: str
    elements: List[SlackBlockElement]


class SlackEvent(BaseEvent):
    """Slack-specific event with dynamic ts and session_id logic."""
    listener: Literal[ClientTypeEnum.Slack] = Field(default=ClientTypeEnum.Slack)
    ts: str = Field(alias="ts")
    user: str
    username: Optional[str] = None
    channel_name: Optional[str] = None
    team: Optional[str] = None
    channel_type: Optional[str] = None
    event_ts: str
    blocks: Optional[List[SlackBlock]] = None
    session_id: Optional[str] = Field(default=None)

    model_config = ConfigDict(
        validate_assignment=True,
        populate_by_name=True,
        arbitrary_types_allowed=True,
        use_enum_values=True
    )

    @field_validator('session_id', mode='before')
    def compute_session_id(cls, v, values:dict):
        return values.get("ts") or values.get("thread_ts")

class WebsocketEvent(BaseEvent):
    listener: Literal[ClientTypeEnum.Websocket]
    session_id: str = Field(alias="sessionID")
    validate_user: bool = Field( default=True )
    client_ids: list[str] = Field(alias="clientIDs")
    software_env: str = Field(alias="softwareEnv")
    # metadata: Metadata = Field(default=Metadata(), alias="metadata")



class CustomField(BaseModel):
    id: int
    value: Optional[Union[str, bool, int, None]] = None

class ZendeskEvent(BaseEvent):
    listener: Literal[ClientTypeEnum.Zendesk]
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()), alias="sessionID")
    ticket_id: int
    assignee_id: Optional[int] = None
    brand_id: int
    collaborator_ids: List[int]
    created_at: datetime
    custom_fields: List[CustomField]
    custom_status_id: int
    description: str
    due_at: Optional[datetime] = None
    email_cc_ids: List[int]
    external_id: Optional[str] = None
    fields: List[CustomField]
    follower_ids: List[int]
    followup_ids: List[int]
    forum_topic_id: Optional[int] = None
    from_messaging_channel: bool
    generated_timestamp: int
    group_id: int
    has_incidents: bool
    id: int
    is_public: bool
    organization_id: int
    priority: str
    problem_id: Optional[int] = None
    raw_subject: str
    recipient: Optional[str] = None
    requester_id: int
    sharing_agreement_ids: List[int]
    status: str
    subject: str
    submitter_id: int
    tags: List[str]
    ticket_form_id: int
    type: str
    updated_at: datetime
    url: str

    model_config = ConfigDict(
        validate_assignment=True,
        populate_by_name=True,
        arbitrary_types_allowed=True,
        use_enum_values=True
    )



class AdminEvent(BaseEvent):
    listener: Literal[ClientTypeEnum.Admin]
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()), alias="sessionID")
    attack_id: str = Field(alias="attackID")
    software_env: str = Field(alias="softwareEnv")

type EventDataType = Union[SlackEvent, WebsocketEvent, ZendeskEvent, AdminEvent]

class EventData(BaseModel):
    data: EventDataType = Field(..., discriminator='listener')

