import uuid
from dataclasses import field
from typing import TYPE_CHECKING, Dict, List, Optional, Union

from pydantic import BaseModel

from cyber_security.models.enums import ClientTypeEnum
from cyber_security.models.user_data import AIDetails, UserData, UserProfile

if TYPE_CHECKING:
    from cyber_security.models.event_data import EventDataType


class Metadata(BaseModel):
    question_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_id: Optional[str] = None
    client_id: Optional[str] = None
    client_ids: Optional[List[str]] = None
    client: Optional[str] = None
    account_type: Optional[str] = None
    tenant_id: Optional[str] = None
    email: Optional[str] = None
    username: Optional[str] = None
    channel_id: Optional[str] = None
    ai_details: Optional[AIDetails] = None

    def to_dict(self) -> Dict[str, Union[str, List[str], None]]:
        return self.__dict__

class MetadataGenerator:
    @staticmethod
    def generate_metadata(
        event: "EventDataType",
        event_type: ClientTypeEnum,
        user_data: Optional[UserData] = None,
        user_profile: Optional[UserProfile] = None,
    ) -> Metadata:
        """Generates metadata based on event type and optional user data."""

        if user_data and event_type != ClientTypeEnum.Websocket:
            raise ValueError("UserData can only be used with WebSocket events.")

        metadata = Metadata(
            question_id=event.question_id,
            session_id=event.session_id,
            user_id=user_data.id,
            client_id=getattr(getattr(user_profile, "client_id", None), "id", getattr(user_data, "id", None)),
            client_ids=user_data.client_ids,
            client=user_data.client,
            ai_details=getattr(getattr(user_profile, "client_id", None), "aiDetails", None),
            account_type=user_data.account_type,
            tenant_id=user_data.tenant_id,
            email=user_data.email,
            username=MetadataGenerator.generate_username(user_data) if user_profile else None,
            #channel_id=MetadataGenerator.get_channel_id(event, event_type),
        )
        return metadata

    @staticmethod
    def generate_username(user_data: Union[UserProfile, UserData]) -> Optional[str]:
        """Generates a username from first and last name."""
        firstname = user_data.firstname
        lastname = user_data.lastname
        if firstname or lastname:
            return f"{firstname} {lastname}".strip()
        return None
