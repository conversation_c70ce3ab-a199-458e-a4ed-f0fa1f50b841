from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import AliasChoices, BaseModel, Field, model_validator


class ValidEmail(BaseModel):
    address: Optional[str] = None
    version: Optional[str] = None
    support: Optional[List[int]] = None
    rule: Optional[bool] = None
    isOn: Optional[bool] = None
    agentType: Optional[int] = None
    subVersion: Optional[str] = None
    elevated: Optional[bool] = None
    mac: Optional[bool] = None
    dll: Optional[bool] = None
    arch: Optional[str] = None
    os: Optional[str] = None
    os_full_name: Optional[str] = None
    smtpConnectionType: Optional[int] = None
    isService: Optional[bool] = None
    lastUpdated: Optional[datetime] = None


class City(BaseModel):
    name: Optional[str] = None
    lat: Optional[float] = None
    lon: Optional[float] = None
    timezone: Optional[str] = None


class IpAccessControl(BaseModel):
    active: Optional[bool] = None
    activeAPI: Optional[bool] = None
    ips: Optional[List[str]] = None


class WsfedSaml2Adfs(BaseModel):
    idType: Optional[str] = None


class WsfedSaml2Saml(BaseModel):
    forceAuthn: Optional[bool] = None
    disableRequestedAuthnContext: Optional[bool] = None
    wantAuthnResponseSigned: Optional[bool] = None
    wantAssertionsSigned: Optional[bool] = None
    autoProvisioning: Optional[bool] = None
    relayState: Optional[bool] = None
    callbackUrl: Optional[str] = None
    idType: Optional[str] = None
    validateInResponseTo: Optional[bool] = None
    signMetadata: Optional[bool] = None


class WsfedSaml2AzureAd(BaseModel):
    emailParam: Optional[str] = None
    callbackUrl: Optional[str] = None
    idType: Optional[str] = None


class WsfedSaml2(BaseModel):
    isLogin: Optional[bool] = None
    active: Optional[bool] = None
    ssoType: Optional[str] = None
    adfs: Optional[WsfedSaml2Adfs] = None
    saml: Optional[WsfedSaml2Saml] = None
    azure_ad: Optional[WsfedSaml2AzureAd] = None


class ModuleConfig(BaseModel):
    license: Optional[str] = None
    amount: Optional[int] = None
    overall: Optional[int] = None
    lastRunID: Optional[str] = None
    lastRunDate: Optional[datetime] = None
    siteLimit: Optional[int] = None


class WafConfig(BaseModel):
    wafModuleSentRate: Optional[int] = None
    wafModuleSentRateEnterprise: Optional[int] = None
    wafCrawlingRate: Optional[int] = None


class AIDetails(BaseModel):
    countries: Optional[List[str]] = None
    industries: Optional[List[str]] = None


class ClientID(BaseModel):
    id: Optional[str] = Field(default=None, alias="_id")
    name: Optional[str] = None
    created: Optional[datetime] = None
    deleted: Optional[bool] = None
    partnerAddresses: Optional[List[str]] = None
    updated: Optional[datetime] = None
    expirationTime: Optional[datetime] = None
    temporaryToken: Optional[str] = None
    cymulateWafHeader: Optional[bool] = None
    edrHideOutput: Optional[bool] = None
    activateResourcesButton: Optional[bool] = None
    numOfEmployees: Optional[int] = None
    showEdrHideOutput: Optional[bool] = None
    canCreateClients: Optional[bool] = None
    awsCustomerIdentifier: Optional[str] = None
    reconLicenseAccepted: Optional[bool] = None
    edrPacker: Optional[bool] = None
    phishingHidePlugins: Optional[bool] = None
    reconCommercialName: Optional[str] = None
    redTeamLicenseAccepted: Optional[bool] = None
    ipAccessControl: Optional[IpAccessControl] = None
    displayName: Optional[str] = None
    paying: Optional[bool] = None
    salesForceID: Optional[str] = None
    wsfedsaml2: Optional[WsfedSaml2] = None
    accountType: Optional[str] = None
    licenseEnd: Optional[datetime] = None
    modules: Optional[Dict[str, Union["ModuleConfig", dict]]] = None
    totalAssessments: Optional[int] = None
    addEmailToAddress: Optional[bool] = None
    apiKeyRotationEnabled: Optional[bool] = None
    mailTransporter: Optional[str] = None
    showEnv: Optional[bool] = None
    canChangePurpleTeamExecutionStatus: Optional[bool] = None
    canDownloadAVFromReport: Optional[bool] = None
    cookieJar: Optional[bool] = None
    mailGatewayTimeToWait: Optional[int] = None
    logo: Optional[str] = None
    apiBasicAuth: Optional[bool] = None
    reportLogo: Optional[str] = None
    featureFlags: Optional[List[str]] = None
    automationUser: Optional[bool] = None
    alertsFromLeft: Optional[bool] = None
    isEncryptReportPassword: Optional[bool] = None
    browsingSiteConfig: Optional[dict[str, dict[str, Union[bool, List[str]]]]] = None
    isAllowedClientAPI: Optional[bool] = None
    isASMScalingEnabled: Optional[bool] = None
    lastUpdated: Optional[datetime] = None
    maxAlerts: Optional[int] = None
    maxEvents: Optional[int] = None
    csManager: Optional[str] = None
    relayState: Optional[bool] = None
    globalSupervisor: Optional[str] = None
    organizations: Optional[List[str]] = None
    upgradeLicensePressed: Optional[bool] = None
    useEnforceTabSessionPolicy: Optional[bool] = None
    emailBodyType: Optional[str] = None
    isCymulateRelatedClient: Optional[bool] = None
    minimalCores: Optional[int] = None
    minimalDiskSpaceGB: Optional[int] = None
    minimalMemoryGB: Optional[int] = None
    isExcludeRedFlagsDashboard: Optional[bool] = None
    attackHttpDisconnectionTimeout: Optional[int] = None
    licenseEndLastUpdated: Optional[datetime] = None
    licenseEndLastUpdatedBy: Optional[str] = None
    validDomains: Optional[List[str]] = None
    licenseStart: Optional[datetime] = None
    isASMBetaClient: Optional[bool] = None
    wafConfig: Optional[WafConfig] = None
    country: Optional[str] = None
    industry: Optional[str] = None
    updatedAt: Optional[datetime] = None
    isChatBotAiEnabled: Optional[bool] = None
    aiDetails: Optional[AIDetails] = Field(
        default=None, validation_alias=AliasChoices("ai_details", "aiDetails")
    )

    @model_validator(mode="before")
    def validate_modules(cls, values):
        """
        Validates and corrects the 'modules' field.
        """
        modules = values.get("modules")
        if modules is not None:
            if not isinstance(modules, dict):
                raise TypeError("Modules should be a dictionary.")
            corrected_modules = {}
            for key, value in modules.items():
                if isinstance(value, (dict, ModuleConfig)):
                    corrected_modules[key] = value
                else:
                    # Log a warning or provide a default structure for invalid input
                    print(
                        f"⚠️ Warning: Module '{key}' has invalid type '{type(value).__name__}'. "
                        f"Replacing it with a default value."
                    )
                    corrected_modules[key] = {"license": None, "amount": 0}
            values["modules"] = corrected_modules
        return values

    class Config:
        arbitrary_types_allowed = True


class AlertData(BaseModel):
    title: Optional[str] = None
    vectors: Optional[List[str]] = None
    date: Optional[str] = None
    integrationType: Optional[str] = None
    integrationError: Optional[str] = None
    agent: Optional[str] = None


class Alert(BaseModel):
    id: Optional[str] = Field(default=None, alias="_id")
    client: Optional[str] = None
    date: Optional[str] = None
    data: Optional[AlertData] = None
    severity: Optional[str] = None
    type: Optional[str] = None
    lastUpdated: Optional[datetime] = None
    updated: Optional[datetime] = None
    createdAt: Optional[datetime] = None
    created: Optional[datetime] = None
    updatedAt: Optional[datetime] = None


class Environment(BaseModel):
    id: Optional[str] = Field(default=None, alias="_id")
    clientID: Optional[str] = None
    name: Optional[str] = None
    agents: Optional[List[str]] = None


class UserProfile(BaseModel):
    id: str = Field(validation_alias=AliasChoices("id", "user_id"))
    client: str = Field(validation_alias=AliasChoices("client", "client_name"))
    email: str = Field(validation_alias=AliasChoices("email", "email_address"))
    client_id: "ClientID" = Field(
        validation_alias=AliasChoices("clientID", "client_id")
    )
    client_ids: Optional[List[str]] = Field(
        default=None, validation_alias=AliasChoices("clientIDs", "client_ids")
    )
    is_first: Optional[bool] = Field(
        default=None, validation_alias=AliasChoices("isFirst", "first_login")
    )
    used_attacks_categories: Optional[List[str]] = Field(
        default=None,
        validation_alias=AliasChoices("usedAttacksCategories", "attack_categories"),
    )
    valid_emails: Optional[List[dict]] = Field(
        default=None,
        validation_alias=AliasChoices("validEmails", "valid_email_addresses"),
    )
    firstname: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("firstname", "first_name")
    )
    lastname: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("lastname", "last_name")
    )
    sso_id: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("ssoID", "sso_id")
    )
    city: Optional["City"] = Field(
        default=None, validation_alias=AliasChoices("city", "location_city")
    )
    country: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("country", "location_country")
    )
    industry: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("industry", "business_industry")
    )
    language: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("language", "preferred_language")
    )
    format: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("format", "display_format")
    )
    phone: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("phone", "contact_number")
    )
    created_at: Optional[datetime] = Field(
        default=None, validation_alias=AliasChoices("createdAt", "creation_date")
    )
    role: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("role", "user_role")
    )
    widgets: Optional[List[str]] = Field(
        default=None, validation_alias=AliasChoices("widgets", "dashboard_widgets")
    )
    organizations: Optional[List[str]] = Field(
        default=None, validation_alias=AliasChoices("organizations", "orgs")
    )
    global_supervisor: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("globalSupervisor", "supervisor")
    )
    hot_files: Optional[int] = Field(
        default=None, validation_alias=AliasChoices("hotFiles", "priority_files")
    )
    tfa_password_authenticated: Optional[datetime] = Field(
        default=None,
        validation_alias=AliasChoices(
            "tfaPasswordAuthenticated", "two_factor_auth_date"
        ),
    )
    tfa: Optional[bool] = Field(
        default=None, validation_alias=AliasChoices("tfa", "two_factor_auth")
    )
    active_sso: Optional[bool] = Field(
        default=None, validation_alias=AliasChoices("activeSso", "sso_active")
    )
    mail_module_sent_rate: Optional[int] = Field(
        default=None,
        validation_alias=AliasChoices("mailModuleSentRate", "mail_sent_rate"),
    )
    upgrade_license_pressed: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices(
            "upgradeLicensePressed", "license_upgrade_pressed"
        ),
    )
    mssp_branch: Optional[List[str]] = Field(
        default=None,
        validation_alias=AliasChoices("msspBranch", "managed_service_branch"),
    )
    onpremise: Optional[bool] = Field(
        default=None, validation_alias=AliasChoices("onpremise", "on_premise")
    )
    db_connected: Optional[bool] = Field(
        default=None, validation_alias=AliasChoices("dbConnected", "database_connected")
    )
    download_files: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices("downloadFiles", "file_download_enabled"),
    )
    browsing_in_browser: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices("browsingInBrowser", "browser_browsing"),
    )
    word_report: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices("wordReport", "word_document_report"),
    )
    resell: Optional[bool] = Field(
        default=None, validation_alias=AliasChoices("resell", "is_reseller")
    )
    idle: Optional[int] = Field(
        default=None, validation_alias=AliasChoices("idle", "idle_time")
    )
    hopper_upload: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices("hopperUpload", "hopper_file_upload"),
    )
    is_new_dashboard: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices("isNewDashboard", "dashboard_version"),
    )
    client_display: Optional[str] = Field(
        default=None,
        validation_alias=AliasChoices("clientDisplay", "client_display_name"),
    )
    hopper_local_settings: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices("hopperLocalSettings", "local_hopper_settings"),
    )
    is_receiving_poc_email: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices("isReceivingPocEmail", "poc_email_opt_in"),
    )
    allow_switching_users: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices("allowSwitchingUsers", "user_switching_enabled"),
    )
    hidden_modules: Optional[List[str]] = Field(
        default=None, validation_alias=AliasChoices("hiddenModules", "disabled_modules")
    )
    env_permissions: Optional[List[str]] = Field(
        default=None,
        validation_alias=AliasChoices("envPermissions", "environment_permissions"),
    )
    can_edit_templates: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices("canEditTemplates", "template_editing"),
    )
    keep_notifications: Optional[bool] = Field(
        default=None,
        validation_alias=AliasChoices("keepNotifications", "persistent_notifications"),
    )
    whitelabel: Optional[bool] = Field(
        default=None, validation_alias=AliasChoices("whitelabel", "is_whitelabeled")
    )
    country_name: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("countryName", "country_full_name")
    )
    alerts: Optional[List[Any]] = Field(
        default=None, validation_alias=AliasChoices("alerts", "user_alerts")
    )
    total_score: Optional[int] = Field(
        default=None, validation_alias=AliasChoices("totalScore", "aggregate_score")
    )
    environments: Optional[List["Environment"]] = Field(
        default=None, validation_alias=AliasChoices("environments", "user_environments")
    )
    default_env_id: Optional[str] = Field(
        default=None,
        validation_alias=AliasChoices("defaultEnvID", "default_environment_id"),
    )
    children_ids: Optional[List[str]] = Field(
        default=None, validation_alias=AliasChoices("childrenIDs", "child_ids")
    )

    class Config:
        populate_by_name = True
        validate_assignment = True
        validate_default = True
        arbitrary_types_allowed = True


class Endpoints(BaseModel):
    # api_server: str = Field(validation_alias=AliasChoices('apiserver', 'api_server', 'apiServer'))
    app_server: str = Field(
        validation_alias=AliasChoices("appserver", "app_server", "appServer")
    )
    bas_server: Optional[str] = Field(
        default=None,
        validation_alias=AliasChoices("basserver", "bas_server", "basServer"),
    )
    finding_server: str = Field(
        validation_alias=AliasChoices(
            "findingsserver", "finding_server", "findingServer"
        )
    )


class Permissions(BaseModel):
    modules: Optional[List[str]] = Field(default=None, alias="modules")
    env: Optional[List[str]] = Field(default=None, alias="env")


class UserData(BaseModel):
    id: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("id", "user_id", "_id")
    )
    account_type: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("accountType", "account_type")
    )
    client: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("client", "client_name")
    )
    client_id: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("clientID", "client_id")
    )
    client_ids: Optional[List[str]] = Field(
        default=None, validation_alias=AliasChoices("clientIDs", "client_ids")
    )
    email: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("email", "email_address")
    )
    endpoints: Optional["Endpoints"] = Field(
        default=None, validation_alias=AliasChoices("endpoints", "endpoint_data")
    )
    env: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("env", "environment")
    )
    exp: Optional[int] = Field(
        default=None, validation_alias=AliasChoices("exp", "expiration")
    )
    firstname: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("firstname", "first_name")
    )
    iat: Optional[int] = Field(
        default=None, validation_alias=AliasChoices("iat", "issued_at")
    )
    lastname: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("lastname", "last_name")
    )
    permissions: Optional["Permissions"] = Field(
        default=None, validation_alias=AliasChoices("permissions", "user_permissions")
    )
    session_id: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("sessionID", "session_id")
    )
    tenant_id: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("tenantId", "tenant_id")
    )
    timezone: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("timezone", "time_zone")
    )
    type: Optional[str] = Field(
        default=None, validation_alias=AliasChoices("type", "user_type")
    )
