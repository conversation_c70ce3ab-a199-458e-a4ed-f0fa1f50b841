from enum import Enum
from typing import Dict, Set


class ClassificationType(Enum):
    ATTACK_PLANNER = 'attack_planner'
    TEMPLATE_GENERATOR = 'template_generator'
    TEMPLATE_GENERATOR_FREE_TEXT = 'template_generator_free_text'
    ZENDESK = 'zendesk'
    DASHBOARDS = 'dashboards'
    GENERAL = 'general'

CLASSIFICATION_PATTERNS = {
    r"^Attack Planner: ": ClassificationType.ATTACK_PLANNER,
    r"^Template Generator: ": ClassificationType.TEMPLATE_GENERATOR,
    r"^Template Generator Free Text: ": ClassificationType.TEMPLATE_GENERATOR_FREE_TEXT,
    r"^!!zendesk!!: ": ClassificationType.ZENDESK,
    r"^Summarize .* Dashboard\.?$": ClassificationType.DASHBOARDS,
}

class FileCategory(str, Enum):
    IMAGE = "IMAGE"
    DOCUMENT = "DOCUMENT"
    LOG = "LOG"
    ARCHIVE = "ARCHIVE"

ALLOWED_EXTENSIONS: Dict[FileCategory, Set[str]] = {
    FileCategory.IMAGE: {
        "png", "jpeg", "jpg", "gif"
    },
    FileCategory.DOCUMENT: {
        "pdf", "doc", "docx"
    },
    FileCategory.LOG: {
        "log", "txt", "text", "json"
    },
    FileCategory.ARCHIVE: {
        "zip", "rar"
    },
}

class ClientTypeEnum(str, Enum):
    Slack = 'slack'
    Websocket = 'websocket'
    Zendesk = 'zendesk'
    Admin = 'admin'

class SecretTypeEnum(str):
    Chatbot = 'chatbot-ai'
    Redis = 'redis'
    Elasticsearch = 'elastic'
    GENERAL = 'general'
