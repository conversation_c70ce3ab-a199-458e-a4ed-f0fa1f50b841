import asyncio
import re
import time
import uuid
from functools import cmp_to_key, wraps
from typing import Any, Dict, List, Optional

import aiohttp
import validators
from aiohttp import ClientError, ClientSession
from aiohttp_retry import ExponentialRetry, RetryClient
from fake_useragent import UserAgent
from langchain_core.documents import Document
from logger import logger
from lxml import html

from cyber_security.infra.init import infra

LEVEL_PRIORITY = {
    "Fatal": 1,
    "Error": 2,
    "Warning": 3,
    "Info": 4,
    "Debug": 5
}

def extract_level_priority(document: Document) -> int:
    """
    Extracts the log level from the document's content and returns its priority.
    Default priority is set for unknown levels.
    """
    match = re.match(r'\[(\w+)\]', document.page_content)
    level = match.group(1) if match else "Unknown"
    return LEVEL_PRIORITY.get(level, 6)  # Default priority for unknown levels


def compare_documents(doc1: Document, doc2: Document) -> int:
    """
    Comparator function to compare two documents by their log level priority.
    """
    priority1 = extract_level_priority(doc1)
    priority2 = extract_level_priority(doc2)
    return priority1 - priority2  # Lower priority values appear first

def sort_by_level(documents: List[Document]) -> List[Document]:
    """
    Sorts a list of Document objects by log level, prioritizing Fatal and Error logs first.
    """
    return sorted(documents, key=cmp_to_key(compare_documents))


def truncate_documents(documents: List[Document]) -> List[Document]:
    """
    Truncates documents to ensure total lines do not exceed MAX_LINES
    and each line does not exceed MAX_CHARS_PER_LINE.
    """
    truncated_documents = []
    total_lines = 0

    for doc in documents:
        content_lines = doc.page_content.splitlines()
        truncated_content_lines = []

        for line in content_lines:
            truncated_line = line[:infra.get_secret(key="MAX_FILE_CHARS_PER_LINE", default=1000)]
            truncated_content_lines.append(truncated_line)

            total_lines += 1
            if total_lines >= infra.get_secret(key="MAX_FILE_LINES", default=300):
                break

        truncated_content = "\n".join(truncated_content_lines)
        truncated_documents.append(Document(page_content=truncated_content, metadata=doc.metadata))

        if total_lines >= infra.get_secret(key="MAX_FILE_LINES", default=300):
            break

    return truncated_documents


def is_valid_uuid(val):
    try:
        uuid.UUID(str(val))
        return True
    except ValueError:
        return False


async def execute_tool_request(
        url: str,
        user_cookies: str,
        body: Optional[Dict[str, Any]] = None,
        method: Optional[str] = "POST",
        endpoints: Optional[Any] = None,
) -> Any:
    """
    Executes a generic tool request.

    Args:
        url (str): The API endpoint URL.
        user_cookies (str): User's authentication cookies. Required for access.
        body (Dict[str, Any]): The request payload to be sent as JSON.
        method (str): HTTP method to use (e.g., "GET", "POST"). Defaults to "POST".
        endpoints (Optional[Any]): Endpoint configuration object. Used to validate server presence.

    Returns:
        Any: The JSON response from the API.

    Raises:
        ValueError: If `user_cookies` or `endpoints` are not provided.
        Exception: For any HTTP-related issues or non-200 responses.
    """
    if not user_cookies:
        raise ValueError("Access denied. User cookies are required.")

    if not endpoints or not hasattr(endpoints, "bas_server"):
        raise ValueError("Valid endpoints configuration must be provided.")

    headers = {
        'Content-Type': 'application/json',
        'Cookie': user_cookies
    }

    if method.upper() == 'GET':
        headers.pop('Content-Type', None)
        body = None

    retry_options = ExponentialRetry(attempts=3)

    try:
        async with ClientSession() as session:
            retry_client = RetryClient(client_session=session, retry_options=retry_options)
            async with retry_client.request(method=method, url=url, headers=headers, json=body) as response:
                response.raise_for_status()
                content_type = response.headers.get('Content-Type', '').lower()
                if 'application/json' in content_type:
                    return await response.json()
                else:
                    logger.warning(f"Unexpected Content-Type: {content_type}. Response: {await response.text()}")
                    return {
                        "success": False,
                        "data": "Unexpected response format. Check the endpoint."
                    }
    except Exception as e:
        logger.error(f"Request failed ({url}): {e}")
        return {"success": False, "data": "Unable to fetch data"}

def execution_time(func):
    """Decorator to log execution time and handle async functions."""
    
    # Get the function name with class if it's a method
    func_name = func.__qualname__ if '.' in func.__qualname__ else func.__name__

    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = await func(*args, **kwargs)
        _end_time = time.perf_counter()
        _execution_time = _end_time - start_time
        logger.info(f"EXECUTION [{func_name}] Execution time: {_execution_time:.4f} seconds")
        return result

    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        _end_time = time.perf_counter()
        _execution_time = _end_time - start_time
        logger.info(f"EXECUTION [{func_name}] Execution time: {_execution_time:.4f} seconds")
        return result

    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper



class UnauthorizedError(Exception):
    """Custom exception for handling 401 Unauthorized errors."""
    pass



def is_valid_url(message):
    return validators.url(message)


async def fetch_and_clean_html(url, retries=3, delay=2):
    ua = UserAgent()
    headers = {
        'User-Agent': ua.random
    }

    for attempt in range(1, retries + 1):
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, ssl=False) as response:
                    response.raise_for_status()
                    content = await response.text()
                    tree = html.fromstring(content)
                    return tree.text_content()

        except (ClientError, aiohttp.ClientResponseError, aiohttp.http_exceptions.HttpProcessingError) as e:
            print(f"Attempt {attempt} failed with error: {e}")
            if attempt == retries:
                raise
            await asyncio.sleep(delay)

        except Exception as e:
            print(f"Unexpected error: {e}")
            raise