
from typing import List

from cyber_security.models.user_data import UserData


def merge_client_ids(user_data: UserData, user_profile: dict) -> List[str]:
    client_ids_set = set()

    client_id = user_data.client_id
    if client_id:
        client_ids_set.add(client_id)

    children_ids = user_data.client_ids
    if children_ids:
        if isinstance(children_ids, list):
            client_ids_set.update(filter(None, children_ids))
        elif isinstance(children_ids, str):
            client_ids_set.add(children_ids)

    profile_children_ids = user_profile.get('childrenIDs')
    if profile_children_ids and isinstance(profile_children_ids, list):
        client_ids_set.update(filter(None, profile_children_ids))

    return list(client_ids_set)
