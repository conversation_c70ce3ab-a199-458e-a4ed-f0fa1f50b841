"""
History API endpoints for retrieving conversation history by user ID.

This module provides FastAPI endpoints for accessing conversation history
stored in MongoDB checkpointer.
"""

from typing import List, Optional, Dict, Any, Union
from fastapi import APIRouter, HTTPException, Query
from langgraph.checkpoint.mongodb import AsyncMongoDBSaver
from pymongo import AsyncMongoClient
from pydantic import BaseModel
from logger import logger
import json


# Utility functions
def serialize_checkpoint_data(data: Any, force_dict: bool = False) -> Any:
    """
    Safely serialize checkpoint data by converting non-serializable objects to dictionaries
    """
    if data is None:
        return None

    try:
        # Try to convert to JSON and back to ensure it's serializable
        json.dumps(data)
        return data
    except (TypeError, ValueError):
        # If it's not JSON serializable, convert to safe dictionary representation
        if hasattr(data, "__dict__"):
            try:
                # Try to extract basic attributes for objects
                return {
                    "type": str(type(data).__name__),
                    "repr": str(data)[:200],  # Limit string length
                    "serializable": False,
                }
            except:
                return {"type": str(type(data).__name__), "serializable": False}
        else:
            # For primitive types that can't be serialized, wrap in dict if needed
            if force_dict:
                return {
                    "type": str(type(data).__name__),
                    "value": str(data)[:200],
                    "serializable": False,
                }
            return str(data)[:200]  # Limit string length


# Response models
class Message(BaseModel):
    """Individual message in conversation"""

    id: Optional[str] = None
    type: str  # 'human', 'ai', 'system', etc.
    content: str
    timestamp: Optional[str] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    response_metadata: Optional[Dict[str, Any]] = None


class ConversationThread(BaseModel):
    """A conversation thread with its messages"""

    thread_id: str
    user_id: str
    first_message_time: Optional[str] = None
    last_message_time: Optional[str] = None
    message_count: int
    messages: List[Message]


class ConversationHistoryResponse(BaseModel):
    """Response model for conversation history endpoint"""

    user_id: str
    total_threads: int
    threads: List[ConversationThread]


# Legacy models for backward compatibility
class CheckpointMetadata(BaseModel):
    """Metadata for a checkpoint"""

    source: Optional[str] = None
    step: Optional[int] = None
    writes: Optional[Dict[str, Any]] = None
    parents: Optional[Dict[str, Any]] = None


class CheckpointData(BaseModel):
    """Checkpoint data structure"""

    checkpoint_id: Optional[str] = None
    thread_id: Optional[str] = None
    timestamp: Optional[str] = None
    metadata: Optional[CheckpointMetadata] = None
    config: Optional[Union[Dict[str, Any], str]] = None
    state: Optional[Union[Dict[str, Any], str]] = None


class HistoryResponse(BaseModel):
    """Response model for history endpoint"""

    user_id: str
    total_checkpoints: int
    checkpoints: List[CheckpointData]


class HistoryRouter:
    """Router class for history-related endpoints"""

    def __init__(self):
        self.router = APIRouter(prefix="/history", tags=["history"])
        self._setup_routes()

    def _extract_messages_from_state(self, state_data: Any) -> List[Message]:
        """Extract messages from checkpoint state data"""
        messages = []

        try:
            if isinstance(state_data, dict):
                # Look for messages in various possible locations
                message_sources = [
                    state_data.get("messages", []),
                    state_data.get("channel_values", {}).get("messages", []),
                ]

                for msg_list in message_sources:
                    if msg_list:
                        for msg in msg_list:
                            if hasattr(msg, "type") and hasattr(msg, "content"):
                                # LangChain message object
                                message = Message(
                                    id=getattr(msg, "id", None),
                                    type=msg.type,
                                    content=str(msg.content),
                                    tool_calls=serialize_checkpoint_data(
                                        getattr(msg, "tool_calls", None)
                                    ),
                                    response_metadata=serialize_checkpoint_data(
                                        getattr(msg, "response_metadata", None)
                                    ),
                                )
                                messages.append(message)
                            elif isinstance(msg, dict):
                                # Dictionary representation
                                message = Message(
                                    id=msg.get("id"),
                                    type=msg.get("type", "unknown"),
                                    content=str(msg.get("content", "")),
                                    tool_calls=serialize_checkpoint_data(
                                        msg.get("tool_calls")
                                    ),
                                    response_metadata=serialize_checkpoint_data(
                                        msg.get("response_metadata")
                                    ),
                                )
                                messages.append(message)
                        break  # Stop after finding first non-empty message list

        except Exception as e:
            logger.warning(f"⚠️ Error extracting messages: {str(e)}")

        return messages

    def _setup_routes(self):
        """Setup all history-related routes"""

        @self.router.get(
            "/conversations/{user_id}", response_model=ConversationHistoryResponse
        )
        async def get_user_conversations(
            user_id: str,
            limit: Optional[int] = Query(
                10, description="Maximum number of threads to retrieve", le=50
            ),
            include_messages: bool = Query(
                True, description="Include message content in response"
            ),
        ) -> ConversationHistoryResponse:
            """
            Retrieve conversation history grouped by threads for a specific user ID.

            Args:
                user_id: The user ID to retrieve conversations for
                limit: Maximum number of threads to return (default: 10, max: 50)
                include_messages: Whether to include message content (default: True)

            Returns:
                ConversationHistoryResponse containing the user's conversations grouped by thread
            """
            try:
                logger.info(f"🔍 Retrieving conversations for user_id: {user_id}")

                # Initialize MongoDB connection and checkpointer
                client = AsyncMongoClient("mongodb://localhost:27017/")
                checkpointer = AsyncMongoDBSaver(client, db_name="uli_db")

                config_filter = {"configurable": {"user_id": user_id}}

                # Group checkpoints by thread
                threads_data = {}

                async for checkpoint in checkpointer.alist(config_filter):
                    try:
                        thread_id = checkpoint.config.get("configurable", {}).get(
                            "thread_id"
                        )
                        if not thread_id:
                            continue

                        if thread_id not in threads_data:
                            threads_data[thread_id] = {
                                "thread_id": thread_id,
                                "checkpoints": [],
                                "messages": [],
                                "timestamps": [],
                            }

                        # Extract messages from this checkpoint
                        if include_messages and checkpoint.checkpoint:
                            channel_values = checkpoint.checkpoint.get(
                                "channel_values", {}
                            )
                            messages = self._extract_messages_from_state(channel_values)
                            threads_data[thread_id]["messages"].extend(messages)

                        # Track timestamps
                        if checkpoint.checkpoint and checkpoint.checkpoint.get("ts"):
                            threads_data[thread_id]["timestamps"].append(
                                checkpoint.checkpoint.get("ts")
                            )

                        threads_data[thread_id]["checkpoints"].append(checkpoint)

                        # Limit number of threads processed
                        if len(threads_data) >= limit:
                            break

                    except Exception as e:
                        logger.warning(f"⚠️ Error processing checkpoint: {str(e)}")
                        continue

                await client.close()

                # Build response
                conversation_threads = []
                for thread_id, data in threads_data.items():
                    # Remove duplicate messages (based on content and type)
                    unique_messages = []
                    seen_messages = set()

                    for msg in data["messages"]:
                        msg_key = (
                            msg.type,
                            msg.content[:100],
                        )  # Use first 100 chars for dedup
                        if msg_key not in seen_messages:
                            seen_messages.add(msg_key)
                            unique_messages.append(msg)

                    # Sort messages by timestamp if available, otherwise by order added
                    unique_messages.sort(key=lambda x: x.timestamp or "")

                    timestamps = (
                        sorted(data["timestamps"]) if data["timestamps"] else []
                    )

                    thread = ConversationThread(
                        thread_id=thread_id,
                        user_id=user_id,
                        first_message_time=timestamps[0] if timestamps else None,
                        last_message_time=timestamps[-1] if timestamps else None,
                        message_count=len(unique_messages),
                        messages=unique_messages,
                    )
                    conversation_threads.append(thread)

                # Sort threads by last message time (most recent first)
                conversation_threads.sort(
                    key=lambda x: x.last_message_time or "", reverse=True
                )

                logger.info(
                    f"✅ Retrieved {len(conversation_threads)} conversation threads for user {user_id}"
                )

                return ConversationHistoryResponse(
                    user_id=user_id,
                    total_threads=len(conversation_threads),
                    threads=conversation_threads,
                )

            except Exception as e:
                logger.error(
                    f"❌ Error retrieving conversations for user {user_id}: {str(e)}"
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to retrieve conversations: {str(e)}",
                )

        @self.router.get("/user/{user_id}", response_model=HistoryResponse)
        async def get_user_history(
            user_id: str,
            limit: Optional[int] = Query(
                50, description="Maximum number of checkpoints to retrieve", le=200
            ),
            thread_id: Optional[str] = Query(
                None, description="Optional thread ID to filter by"
            ),
        ) -> HistoryResponse:
            """
            Retrieve conversation history for a specific user ID.

            Args:
                user_id: The user ID to retrieve history for
                limit: Maximum number of checkpoints to return (default: 50, max: 200)
                thread_id: Optional thread ID to filter results

            Returns:
                HistoryResponse containing the user's conversation history

            Raises:
                HTTPException: If there's an error accessing the database
            """
            try:
                logger.info(f"🔍 Retrieving history for user_id: {user_id}")

                # Initialize MongoDB connection and checkpointer
                client = AsyncMongoClient("mongodb://localhost:27017/")
                checkpointer = AsyncMongoDBSaver(client, db_name="uli_db")

                # Build configuration for checkpointer query
                config_filter = {"configurable": {"user_id": user_id}}
                if thread_id:
                    config_filter["configurable"]["thread_id"] = thread_id

                checkpoints = []
                checkpoint_count = 0

                # Retrieve checkpoints from MongoDB
                async for checkpoint in checkpointer.alist(config_filter):
                    if checkpoint_count >= limit:
                        break

                    try:
                        # Safely extract and serialize checkpoint data
                        safe_metadata = CheckpointMetadata(
                            source=(
                                checkpoint.metadata.get("source")
                                if checkpoint.metadata
                                else None
                            ),
                            step=(
                                checkpoint.metadata.get("step")
                                if checkpoint.metadata
                                else None
                            ),
                            writes=(
                                serialize_checkpoint_data(
                                    checkpoint.metadata.get("writes")
                                )
                                if checkpoint.metadata
                                else None
                            ),
                            parents=(
                                serialize_checkpoint_data(
                                    checkpoint.metadata.get("parents")
                                )
                                if checkpoint.metadata
                                else None
                            ),
                        )

                        safe_state = None
                        if checkpoint.checkpoint:
                            channel_values = checkpoint.checkpoint.get(
                                "channel_values", {}
                            )
                            # Try to keep as dict if possible, fallback to string if needed
                            try:
                                json.dumps(channel_values)
                                safe_state = channel_values
                            except (TypeError, ValueError):
                                safe_state = str(channel_values)[
                                    :500
                                ]  # Truncate for safety

                        # Try to keep config as dict if possible, fallback to string if needed
                        try:
                            json.dumps(checkpoint.config)
                            safe_config = checkpoint.config
                        except (TypeError, ValueError):
                            safe_config = str(checkpoint.config)[:500]

                        checkpoint_data = CheckpointData(
                            checkpoint_id=(
                                checkpoint.config.get("configurable", {}).get(
                                    "checkpoint_id"
                                )
                                if checkpoint.config
                                else None
                            ),
                            thread_id=(
                                checkpoint.config.get("configurable", {}).get(
                                    "thread_id"
                                )
                                if checkpoint.config
                                else None
                            ),
                            timestamp=(
                                checkpoint.checkpoint.get("ts")
                                if checkpoint.checkpoint
                                else None
                            ),
                            metadata=safe_metadata,
                            config=safe_config,
                            state=safe_state,
                        )
                        checkpoints.append(checkpoint_data)
                        checkpoint_count += 1

                    except Exception as e:
                        logger.warning(f"⚠️ Error processing checkpoint: {str(e)}")
                        continue

                await client.close()

                logger.info(
                    f"✅ Retrieved {checkpoint_count} checkpoints for user {user_id}"
                )

                return HistoryResponse(
                    user_id=user_id,
                    total_checkpoints=checkpoint_count,
                    checkpoints=checkpoints,
                )

            except Exception as e:
                logger.error(
                    f"❌ Error retrieving history for user {user_id}: {str(e)}"
                )
                raise HTTPException(
                    status_code=500, detail=f"Failed to retrieve history: {str(e)}"
                )

        @self.router.get("/threads", response_model=List[Dict[str, Any]])
        async def get_user_threads(
            user_id: str = Query(..., description="User ID to get threads for"),
            limit: Optional[int] = Query(
                20, description="Maximum number of threads to retrieve", le=100
            ),
        ) -> List[Dict[str, Any]]:
            """
            Retrieve all thread IDs for a specific user.

            Args:
                user_id: The user ID to retrieve threads for
                limit: Maximum number of threads to return

            Returns:
                List of thread information with metadata
            """
            try:
                logger.info(f"🔍 Retrieving threads for user_id: {user_id}")

                client = AsyncMongoClient("mongodb://localhost:27017/")
                checkpointer = AsyncMongoDBSaver(client, db_name="uli_db")

                config_filter = {"configurable": {"user_id": user_id}}

                threads = {}
                thread_count = 0

                async for checkpoint in checkpointer.alist(config_filter):
                    if thread_count >= limit:
                        break

                    thread_id = checkpoint.config.get("configurable", {}).get(
                        "thread_id"
                    )
                    if thread_id and thread_id not in threads:
                        threads[thread_id] = {
                            "thread_id": thread_id,
                            "last_checkpoint": checkpoint.config.get(
                                "configurable", {}
                            ).get("checkpoint_id", ""),
                            "timestamp": (
                                checkpoint.checkpoint.get("ts", "")
                                if checkpoint.checkpoint
                                else ""
                            ),
                            "step_count": checkpoint.metadata.get("step", 0),
                        }
                        thread_count += 1

                await client.close()

                logger.info(f"✅ Retrieved {len(threads)} threads for user {user_id}")

                return list(threads.values())

            except Exception as e:
                logger.error(
                    f"❌ Error retrieving threads for user {user_id}: {str(e)}"
                )
                raise HTTPException(
                    status_code=500, detail=f"Failed to retrieve threads: {str(e)}"
                )


# Create router instance
history_router = HistoryRouter()
router = history_router.router
