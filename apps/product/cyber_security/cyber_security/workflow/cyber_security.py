import os
from typing import Union

from corelanggraph import EnhancedStateGraph
from langgraph.checkpoint.base import BaseCheckpointSaver
from langgraph.graph import END, START
from langgraph.graph.state import CompiledStateGraph

from cyber_security.agents.assessment.workflow.assessment import (
    compiled_workflow as assessment_agent,
)
from cyber_security.agents.cyber_expert.workflow.cyber_expert import (
    compiled_workflow as cyber_expert_agent,
)
from cyber_security.agents.cymulate_knowledge_base.workflow.cymulate_knowledge_base import (
    compiled_workflow as cymulate_knowledge_base_agent,
)
from cyber_security.agents.supervisor import (
    CyberSecuritySupervisorAgent,
)
from cyber_security.agents.utilities import GuardrailsAgent
from cyber_security.agents.template_generator.graph.template_generator_workflow import (
    template_generator_graph as template_generator_agent,
)
from cyber_security.workflow.configuration import Configuration
from cyber_security.workflow.state import (
    CyberSecuritySupervisorState,
    CyberSecuritySupervisorResponse,
)


def add_nodes(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_node("guardrails_agent", GuardrailsAgent())

    workflow_builder.add_node(
        "supervisor_agent",
        CyberSecuritySupervisorAgent(
            available_agents=[
                assessment_agent,
                cymulate_knowledge_base_agent,
                template_generator_agent,
                cyber_expert_agent,
            ],
        ).get_supervisor_agent(),
    )


def should_continue_to_supervisor(state):
    """Check if content was blocked by guardrails"""
    return "supervisor_agent" if not getattr(state, 'blocked_by_guardrails', False) else END

def add_edges(workflow_builder: EnhancedStateGraph):
    workflow_builder.add_edge(START, "guardrails_agent")
    workflow_builder.add_conditional_edges(
        "guardrails_agent",
        should_continue_to_supervisor
    )
    workflow_builder.add_edge("supervisor_agent", END)


def base_workflow(checkpointer: Union[BaseCheckpointSaver, None]) -> CompiledStateGraph:
    workflow_builder = EnhancedStateGraph(
        CyberSecuritySupervisorState, config_schema=Configuration
    )
    add_nodes(workflow_builder)
    add_edges(workflow_builder)
    return workflow_builder.compile(checkpointer=checkpointer)


if os.getenv("LANGGRAPH_UI_BUNDLER") == "true":
    from cyber_security.infra import init

compiled_workflow = base_workflow(checkpointer=None)
