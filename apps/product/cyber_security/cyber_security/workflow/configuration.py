"""Define the configurable parameters for the agent."""

from __future__ import annotations

import os
import uuid
from dataclasses import dataclass, field, fields
from typing import Annotated, Any, Dict, Optional

from corelanggraph.app_builder.core.cookie_processor import CookieProcessor
from langchain_core.runnables import ensure_config
from langgraph.config import get_config


@dataclass(kw_only=True)
class Configuration:
    """The configuration for the agent."""

    messagesId: str = field(default=uuid.uuid4(), init=True)

    user_token: str = field(default="", init=True)
    """The user token."""

    cookie_string: Optional[str] = field(default=None, init=True)
    """The cookie string."""

    model: Annotated[str, {"__template_metadata__": {"kind": "llm"}}] = field(
        default=os.getenv("MODEL_NAME"),
        metadata={
            "description": "The name of the language model to use for the agent. "
            "Should be in the form: provider/model-name."
        },
    )
    user_data: Optional[Dict[str, Any]] = field(default=None, init=True)
    """Processed user data extracted from cookies."""

    def _process_cookies(self):
        """Process the cookie string to extract user data and token."""
        try:
            cookie_processor = CookieProcessor(self.cookie_string, self.user_token)
            self.user_data = cookie_processor.decoded_token
            self.user_token = cookie_processor.auth_token or ""
        except Exception as e:
            # Log the error but don't raise to avoid breaking initialization
            print(f"Error processing cookies: {e}")
            self.user_data = None
            self._user_token = ""

    def _validate_user_token(self):
        """Validate the user token."""
        # Skip validation for development
        if not self.user_token and not self.cookie_string:
            return
        if self.user_token and self.cookie_string:
            raise ValueError("user_token and cookie_string cannot be provided together")
        if self.user_token:
            self._process_cookies()
        if self.cookie_string:
            self._process_cookies()

    @classmethod
    def from_context(cls) -> Configuration:
        """Create a Configuration instance from a RunnableConfig object."""
        try:
            config = get_config()
        except RuntimeError:
            config = None
        config = ensure_config(config)
        configurable = config.get("configurable") or {}
        _fields = {f.name for f in fields(cls) if f.init}
        pg = cls(**{k: v for k, v in configurable.items() if k in _fields})
        pg._validate_user_token()
        pg._process_cookies()
        return pg
