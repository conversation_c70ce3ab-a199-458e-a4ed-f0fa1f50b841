from typing import Any, List, Optional

from langchain_core.messages import BaseMessage
from pydantic import BaseModel, ConfigDict, Field, model_validator

from cyber_security.models.enums import ClientTypeEnum
from cyber_security.models.event_data import (
    AdminEvent,
    EventDataType,
    SlackEvent,
    WebsocketEvent,
    ZendeskEvent,
)
from cyber_security.models.files import EventFile
from cyber_security.models.metadata_generator import Metadata
from cyber_security.models.user_data import UserData, UserProfile

"""Define the shared values."""


from dataclasses import dataclass

from langchain_core.messages import AnyMessage
from langgraph.graph import add_messages
from pydantic import Field
from typing_extensions import Annotated


class AgentStateBase(BaseModel):
    """Base class for the agent state model."""

    classification: str = Field(
        default="general", description="Classification of the user's question"
    )

    docs: List[Any] = Field(
        default_factory=list,
        description="List of documents retrieved based on the user's question",
    )

    mongo_ids: List[str] = Field(
        default_factory=list, description="MongoDB IDs extracted from the conversation"
    )

    assessments: List[dict[str, Any]] = Field(
        default_factory=list,
        description="Assessment data retrieved based on MongoDB IDs",
    )

    assessment_logs: list[dict[str, Any]] = Field(
        default_factory=list,
        description="Assessment logs retrieved based on MongoDB IDs",
    )

    agent_metrics: list[dict[str, Any]] = Field(
        default_factory=list, description="Agent Metrics retrieved based on Assessments"
    )

    @model_validator(mode="after")
    def validate_assessment_dependencies(self):
        if (self.assessment_logs or self.agent_metrics) and not self.assessments:
            raise ValueError(
                "Both 'assessment_logs' and 'agent_metrics' require 'assessments' to be present."
            )
        return self

    class Config:
        model_config = ConfigDict
        arbitrary_types_allowed = True
        validate_assignment = True
        validate_default = True


class CyberSecurityState(AgentStateBase):
    """
    State for the CyberSecurity agent.
    """

    messages: List[BaseMessage] = Field(
        default_factory=list,
        description="List of messages exchanged in the conversation (history)",
    )

    files: List[EventFile] = Field(
        default_factory=list, description="List of files exchanged in the conversation"
    )

    error_message: Optional[str] = Field(
        default=None, description="Error message to be displayed to the user"
    )

    user_cookies: str = Field(default="", description="User cookies")

    user_data: Optional[UserData] = Field(
        default=None, description="Optional user data"
    )

    user_profile: Optional[UserProfile] = Field(
        default=None, description="Optional user profile"
    )

    event_data: "EventDataType" = Field(
        ..., description="Event data based on client type"
    )

    initial_response: Optional[dict] = Field(
        default=None, description="Initial response for the slack message"
    )

    metadata: Metadata = Field(
        default_factory=Metadata, description="Metadata for the agent"
    )

    client_type: ClientTypeEnum = Field(description="Client type of the agent")

    user_input: str = Field(
        default_factory=str, description="User input message for the agent"
    )

    input_metadata: List[BaseMessage] = Field(
        default_factory=list, description="Metadata for the user input"
    )

    @model_validator(mode="before")
    def validate_user(cls, values):
        """Ensure user data is provided for events that require it."""
        event_data: EventDataType = values.get("event_data")
        user_data = values.get("user_data")

        if event_data.validate_user and not user_data:
            raise ValueError("User data is required for this event.")

        return values

    @model_validator(mode="before")
    def validate_user_data(cls, values):
        """Ensure user_data is provided for WebSocket clients."""
        user_data: UserData = values.get("user_data")
        event_data: EventDataType = values.get("event_data")

        if event_data.validate_user and not user_data:
            raise ValueError("UserData is required for WebSocket clients.")
        elif not event_data.validate_user and user_data:
            raise ValueError("UserData can only be used with WebSocket clients.")

        return values

    @model_validator(mode="before")
    def validate_event_data(cls, values):
        """Ensure event_data corresponds to the correct client_type."""
        client_type: ClientTypeEnum = values.get("client_type")
        event_data: EventDataType = values.get("event_data")

        if client_type == ClientTypeEnum.Slack and not isinstance(
            event_data, SlackEvent
        ):
            raise ValueError("SlackEvent data is required for Slack clients.")
        elif client_type == ClientTypeEnum.Websocket and not isinstance(
            event_data, WebsocketEvent
        ):
            raise ValueError("WebsocketEvent data is required for WebSocket clients.")
        elif client_type == ClientTypeEnum.Zendesk and not isinstance(
            event_data, ZendeskEvent
        ):
            raise ValueError("ZendeskEvent data is required for Zendesk clients.")
        elif client_type == ClientTypeEnum.Admin and not isinstance(
            event_data, AdminEvent
        ):
            raise ValueError("AdminEvent data is required for Admin clients.")

        return values

    def should_retrieve_history(self) -> bool:
        """Determines if the agent should retrieve the conversation history."""
        return self.classification not in ["dashboards", "attack_planner"]

    def should_validate_user(self) -> bool:
        """Determines if the user data should be validated."""
        return self.event_data.validate_user


class DocumentationLink(BaseModel):
    title: str = Field(description="The title of the documentation link.")
    url: str = Field(description="The URL of the documentation in Document360.")


class Button(BaseModel):
    placeholder: str = Field(description="The placeholder of the button.")
    title: str = Field(description="The title of the button.")
    action: str = Field(description="The action of the button.")
    type: str = Field(description="The type of the button.")


class CyberSecuritySupervisorResponse(BaseModel):
    """Response for the CyberSecurity supervisor."""

    documentation_links: list[DocumentationLink] = Field(default_factory=list)
    buttons: list[Button] = Field(
        default_factory=list, description="The buttons to be displayed to the user."
    )
    response: str = Field(description="The response to the user's question.")


@dataclass(kw_only=True)
class CyberSecuritySupervisorState:
    """Main graph state."""

    messages: Annotated[list[AnyMessage], add_messages]
    """The messages in the conversation."""
    
    active_agent: Optional[str] = None
    """Currently active agent for conversation continuity."""
    
    conversation_context: Optional[dict] = None
    """Context for ongoing conversations."""

    blocked_by_guardrails: bool = False
    """Flag to indicate if content was blocked by input guardrails."""

    output_blocked_by_guardrails: bool = False
    """Flag to indicate if AI response was blocked by output guardrails."""

    output_validation: Optional[dict] = None
    """Output validation result from guardrails agent."""


class TemplateGeneratorState(AgentStateBase):
    """State for the Template Generator agent."""
    
    user_input: str = Field(default="", description="User input message")
    template_name: Optional[str] = Field(default=None, description="Template name")
    next_step: Any = Field(default="pre_requirements_agent", description="Next workflow step")
    is_final: bool = Field(default=False, description="Final state flag")
    success: bool = Field(default=True, description="Success flag")
    error_message: Optional[str] = Field(default=None, description="Error message")
    user_profile: Optional[dict] = Field(default=None, description="User profile")
    event_data: Optional[dict] = Field(default=None, description="Event data")
    metadata: Optional[dict] = Field(default=None, description="Metadata")
    messages: Annotated[List[BaseMessage], add_messages] = Field(default_factory=list, description="Messages")
    pre_requirements: Optional[Any] = Field(default=None, description="Pre-requirements")
    scenarios: List[Any] = Field(default_factory=list, description="Scenarios")


__all__ = [
    "CyberSecuritySupervisorState",
    "CyberSecuritySupervisorResponse",
    "TemplateGeneratorState",
]
