# Supervisor Template Generator Integration Guide

## Agent Description for Langfuse Prompt

Add this to your "CyberSecurity/supervisor_agent" prompt in Langfuse:

```xml
<agent id="template_generator">
  <purpose>Interactive assessment template creation through guided conversation and intelligent scenario selection</purpose>
  <triggers>
    <trigger>template creation, assessment template, or template generation</trigger>
    <trigger>security assessment planning or simulation design</trigger>
    <trigger>BAS scenario selection or attack simulation setup</trigger>
    <trigger>compliance framework mapping or regulatory requirements</trigger>
    <trigger>APT group targeting or threat actor analysis</trigger>
    <trigger>platform-specific security testing (cloud, on-premise, hybrid)</trigger>
  </triggers>
  <capabilities>
    <capability>Interactive requirement gathering through conversational flow</capability>
    <capability>Intelligent scenario selection from 1000+ BAS2 scenarios</capability>
    <capability>Multi-platform support (AWS, Azure, GCP, Kubernetes, Windows, Linux)</capability>
    <capability>APT group and threat actor mapping with 80+ groups</capability>
    <capability>Security controls integration (EDR, SIEM, WAF, IPS, etc.)</capability>
    <capability>Compliance framework alignment (SOC2, ISO27001, NIST, PCI-DSS)</capability>
    <capability>Team size optimization and time allocation</capability>
    <capability>Template naming and description generation</capability>
  </capabilities>
</agent>
```

## Critical Supervisor Routing Rules

Add these rules to your supervisor prompt:

### Conversation Flow Handling
```
IMPORTANT ROUTING RULES FOR TEMPLATE GENERATOR:

1. **Conversational Flow**: When template_generator is asking questions to gather requirements, 
   ALL subsequent user responses should be routed directly to template_generator without analysis.
   
2. **Context Preservation**: If the conversation is already with template_generator and the user 
   provides answers, information, or clarifications, route to template_generator immediately.

3. **Question Detection**: If template_generator asks questions like:
   - "What industry are you in?"
   - "What platforms do you use?"
   - "What security controls do you have?"
   - "What's your team size?"
   Then route ALL follow-up responses to template_generator.

4. **Conversation Indicators**: Look for these patterns that indicate ongoing template generation:
   - User providing lists (platforms, controls, etc.)
   - User answering specific questions
   - User providing organizational details
   - User clarifying requirements

5. **Direct Routing**: When in doubt during template generation conversation, 
   route to template_generator to maintain conversation flow.
```

## Example Routing Scenarios

### ✅ Correct Routing
```
User: "I need to create an assessment template"
→ Route to: template_generator

Template Generator: "What industry are you in? (Healthcare, Finance, Energy, etc.)"
User: "Healthcare"
→ Route to: template_generator (conversational flow)

Template Generator: "What platforms do you use?"
User: "AWS and Windows servers"
→ Route to: template_generator (answering question)
```

### ❌ Incorrect Routing
```
Template Generator: "What security controls do you have?"
User: "We have EDR and SIEM"
→ Route to: cyber_expert (WRONG - breaks conversation)
```

## Implementation Notes

1. **State Awareness**: The supervisor should recognize when a template generation conversation is active
2. **Context Preservation**: Don't interrupt the conversational flow with analysis or routing to other agents
3. **Question-Answer Pattern**: Recognize question-answer patterns and maintain routing consistency
4. **Completion Detection**: Only route away from template_generator when the template is complete or user explicitly changes topic

## Testing the Integration

Test these scenarios:
1. Initial template creation request
2. Multi-turn conversation with requirement gathering
3. User providing answers to template generator questions
4. Completion of template generation process