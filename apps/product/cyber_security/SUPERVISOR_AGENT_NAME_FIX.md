# Supervisor Agent Name Fix

## The Problem
Your supervisor prompt refers to `template_generator` but the actual agent name is `template_generator_agent`.

## The Fix
In your Langfuse prompt "CyberSecurity/supervisor_agent", change:

```xml
<agent id="template_generator">
```

To:

```xml
<agent id="template_generator_agent">
```

And update all references in the conversational flow handling:

```xml
<conversational_flow_handling>
  <template_generator_rules>
    <rule priority="CRITICAL">
      <name>Maintain Template Generation Conversation</name>
      <condition>When template_generator_agent is asking questions to gather requirements</condition>
      <action>Route ALL subsequent user responses directly to template_generator_agent without analysis</action>
      <rationale>Preserve interactive conversation flow for requirement gathering</rationale>
    </rule>
    
    <routing_override>
      <condition>Active template generation conversation detected</condition>
      <action>Route to template_generator_agent regardless of query content</action>
      <exception>Only route away if user explicitly changes topic or says "stop"</exception>
    </routing_override>
  </template_generator_rules>
</conversational_flow_handling>
```

## Why This Matters
The supervisor uses the agent names to route requests. If the name in the prompt doesn't match the actual compiled agent name, routing fails and the supervisor generates its own response instead of delegating to the correct agent.