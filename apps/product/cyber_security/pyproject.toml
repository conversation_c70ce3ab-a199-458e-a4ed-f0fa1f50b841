[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mas.cyber_security"
version = "0.1.0"
description = "stam"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
license = "LicenseRef-Proprietary"
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
]
requires-python = ">=3.12"
dependencies = [
    # Core dependencies
    "fastapi>=0.109.0",
    "uvicorn>=0.25.0",
    "validators==0.35.0",
    "fake-useragent==2.2.0",
    "lxml==5.4.0",
    "pydantic>=2.5.0",
    "httpx>=0.26.0",
    "corelanggraph",
    "corellm",
    "logger",
    "kafka",
    "graphviz==0.20.3",
    "ddtrace>=3.9.4",
    "partial_json_parser==*******.post5",
    "json_repair==0.44.1",
    "elastic-transport==8.17.1",
    "elasticsearch==8.18.1",
    "langchain-elasticsearch==0.3.2",
    "langchain-tavily==0.2.11",
    "aiohttp-retry>=2.9.1",
    "rarfile>=4.2",
    # ML dependencies for cybersecurity intelligence validation
    "scikit-learn>=1.5.0",
    "numpy>=1.24.0",
    "python-dateutil>=2.8.0",
    # Evaluation dependencies - Latest 2025 versions
    "ragas>=0.3.1",
    "datasets>=2.14.0",
    "Pillow>=10.0.0",
    "beautifulsoup4>=4.12.0",
    "PyJWT>=2.8.0",
    "nemoguardrails>=0.9.0",
]

[project.optional-dependencies]
dev = ["pytest>=7.4.0", "black>=23.12.0", "isort>=5.12.0", "mypy>=1.7.1"]

[project.scripts]
cyber_security = "server:main"

[project.urls]
"Homepage" = "https://bitbucket.org/cymulate/pythonlibs"
"Bug Tracker" = "https://bitbucket.org/cymulate/pythonlibs/jira"

[tool.setuptools.packages.find]
where = ["."]
include = ["*"]

# Use the same formatting and linting configuration as the root project
[tool.black]
line-length = 88
target-version = ["py312"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.uv.sources]
corelanggraph = { workspace = true }
corellm = { workspace = true }
logger = { workspace = true }
kafka = { workspace = true }
cymredis = { workspace = true }


[tool.ruff]
select = ["I", "F401"]


[tool.pytest.ini_options]
addopts = "--cov=cyber_security --cov-report=term-missing"


[[tool.mypy.overrides]]
module = ["corelanggraph.agents"]
follow_untyped_imports = true
