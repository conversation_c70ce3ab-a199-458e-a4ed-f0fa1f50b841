# Supervisor Conversation Flow Fix

## The Problem
The supervisor workflow is too simple: `START → supervisor_agent → END`

When template generator asks a question:
1. Template generator asks question → ends workflow
2. Control returns to supervisor 
3. Supervisor generates its own response instead of waiting for user

## The Solution
The supervisor needs to track conversation state and understand when it's in the middle of a template generation conversation.

## Required Changes

### 1. Add Conversation State Tracking
The supervisor needs to track:
- Which agent is currently in conversation
- Whether we're waiting for user response to continue with that agent

### 2. Modify Supervisor Prompt
Add this logic to your supervisor prompt:

```
CRITICAL CONVERSATION FLOW RULES:

1. **Template Generator Conversation Detection**:
   - If the last message is from template_generator asking a question
   - AND user provides an answer/response
   - THEN route directly to template_generator (don't analyze)

2. **Conversation State Indicators**:
   - Template generator asking: "Does this look correct?"
   - Template generator asking: "What industry are you in?"
   - Template generator asking: "What platforms do you use?"
   - User providing answers to these questions

3. **Routing Override**:
   - When template_generator is asking questions, ALL subsequent user responses go to template_generator
   - Only route away if user explicitly changes topic
```

### 3. Alternative: Use Interrupt Pattern
Instead of ending the template generator workflow, use <PERSON><PERSON><PERSON><PERSON>'s interrupt pattern:
- Template generator asks question
- Workflow interrupts and waits for user input
- User responds
- Workflow resumes with template generator

This requires changing the template generator to use interrupts instead of ending the workflow.