"""
Main entry point for the Codebase RAG application.
"""

from dotenv import load_dotenv

load_dotenv(override=True)

import asyncio
import os
import signal
import traceback

import uvicorn
from corelanggraph.app_builder.models.event import ClientTypeEnum
from logger import logger

from cyber_security.events.slack_event import SlackCyberEvent
from cyber_security.events.websocket_event import WebsocketCyberEvent
from cyber_security.events.zendesk_event import ZendeskCyberEvent

CLIENT_TYPE_STR = os.getenv("CLIENT_TYPE", f"{ClientTypeEnum.Websocket.value}")


# The app is now correctly exported from app.main
def handle_sigterm(signum, frame):
    """Handle SIGTERM signal by performing cleanup and exiting gracefully."""
    logger.info("Received SIGTERM signal. Performing cleanup...")
    # Add any cleanup code here
    os._exit(0)


# Register SIGTERM handler


async def start_websocket_consumer():
    # Appeler directement la coroutine au lieu d'utiliser run_in_executor
    await WebsocketCyberEvent().run()


async def start_slack_consumer():
    # Appeler directement la coroutine au lieu d'utiliser run_in_executor
    await SlackCyberEvent().run()


async def start_zendesk_consumer():
    # Appeler directement la coroutine au lieu d'utiliser run_in_executor
    await ZendeskCyberEvent().run()


async def start_http_consumer():
    config = uvicorn.Config(
        "cyber_security.api.app:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 19000)),
        reload=True,
    )
    server = uvicorn.Server(config)
    await server.serve()


async def main():
    clients = CLIENT_TYPE_STR.split(",")
    to_start = [start_http_consumer()]
    if ClientTypeEnum.Slack.value in clients:
        to_start.append(start_slack_consumer())
    if "zendesk" in clients:
        to_start.append(start_zendesk_consumer())
    if "websocket" in clients:
        to_start.append(start_websocket_consumer())

    await asyncio.gather(*to_start)


if __name__ == "__main__":
    try:
        asyncio.run(main())

        signal.signal(signal.SIGTERM, handle_sigterm)
        signal.signal(signal.SIGINT, handle_sigterm)

    except Exception as e:
        print(f"Error: {e}")
        print(f"Error type: {type(e)}")
        print(f"Error traceback: {traceback.format_exc()}")
        print(f"Error message: {str(e)}")
        print(f"Error args: {e.args}")
