"""
Supervisor Models

This module defines the Pydantic models used by the SupervisorAgent for structured output.
"""

from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class Task(BaseModel):
    """A task to be executed by an agent"""
    task_id: str = Field(..., description="Unique identifier for the task")
    description: str = Field(..., description="Description of the task")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Parameters for the task")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the model to a dictionary"""
        return {
            "task_id": self.task_id,
            "description": self.description,
            "parameters": self.parameters
        }


class SupervisorOutput(BaseModel):
    """Output from the supervisor agent"""
    analysis: str = Field(..., description="Supervisor's understanding and analysis of the request")
    task: Optional[Task] = Field(None, description="Task to be executed for the next step")
    next_step: str = Field(..., description="Which agent to call next, or 'end' to complete. Need to be strictly one of the available agents") 