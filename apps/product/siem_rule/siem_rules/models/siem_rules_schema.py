from typing import Any, Dict, List, Optional

from bson import ObjectId
from pydantic import BaseModel, Field


class SiemRule(BaseModel):
    """Pydantic model for SIEM rules database schema"""
    name: str = Field(..., description="Name of the SIEM rule")
    description: Optional[str] = Field(None, description="Description of the SIEM rule")
    query: str = Field(..., description="Query for the SIEM rule")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata for the rule")
    ruleID: str = Field(..., description="ID of the rule")
    clientID: ObjectId = Field(..., description="ID of the client")
    product: str = Field(..., description="SIEM product type")
    enabled: bool = Field(default=True, description="Whether the rule is enabled")
    relevantScenarios: Optional[List[ObjectId]] = Field(default=[], description="List of relevant scenario IDs")
    desicion: Optional[str] = Field(default=None, description="Desicion of the LLM")

    class Config:
        collection = "integrationSiemRules"
        arbitrary_types_allowed = True
        json_schema_extra = {
            "example": {
                "name": "Example Rule",
                "description": "An example SIEM rule",
                "query": "source=example",
                "metadata": {"severity": "high"},
                "client_id": "client123",
                "product": "splunk",
                "enabled": True,
                "relevant_scenarios": ["scenario1", "scenario2"],
                "desicion": "The desicion of the LLM"
            }
        }
