

from typing import List

from pydantic import BaseModel, Field

from siem_rules.workflow.siem_rules.state import RelevantScenario


class SiemRules(BaseModel):
    """
    This is the schema for the SiemRules.
    """
    rules: List[str] = Field(..., description="List of SiemRules")
    found_suitable_rule: bool = Field(..., description="Found suitable rule")

class MatchingScenarios(BaseModel):
    scenarios: List[RelevantScenario] = Field(..., description="List of scenarios")
    desicion: str = Field(..., description="Explain the desicion of the LLM")
