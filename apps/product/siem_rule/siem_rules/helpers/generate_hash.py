import hashlib


def generate_hash_from_id(document_id):
    """
    Generate a hash from a document ID
    
    Args:
        document_id: The document ID to hash
        
    Returns:
        str: A hash of the document ID
    """
    if document_id is None:
        return None
    
    # Convert ID to string if it's not already
    id_str = str(document_id)
    
    # Generate SHA-256 hash
    hash_obj = hashlib.sha256(id_str.encode())
    return hash_obj.hexdigest()