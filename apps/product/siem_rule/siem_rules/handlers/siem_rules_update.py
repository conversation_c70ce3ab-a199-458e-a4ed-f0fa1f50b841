from time import time
from typing import Dict

from langchain_core.runnables import RunnableConfig
from langfuse import get_client
from pydantic import BaseModel, Field

# These imports will be replaced with the actual project name during template rendering
from siem_rules.handlers.handler import HandlerWithCheckpointer
from siem_rules.metrics import RULES_RUN, RULES_RUN_FAILURES, WORKFLOW_COMPLETE_DURATION
from siem_rules.workflow.siem_rules_update.siem_rules_update import base_workflow
from siem_rules.workflow.siem_rules_update.state import SiemRulesUpdateState


class SiemRulesUpdateInput(BaseModel):
    rules_ids:list[str] = Field(description="The IDs of the rules to update")
    product:str = Field(description="The product of the rules")
    client_id:str = Field(description="The client ID of the rules")
    tenant_id:str = Field(description="The tenant ID of the rules")

class SiemRulesUpdateHandler(HandlerWithCheckpointer[SiemRulesUpdateState,SiemRulesUpdateInput]):

    def __init__(self):
        super().__init__()
    
    
    def parse_state(self,state:Dict)->SiemRulesUpdateState:
        return SiemRulesUpdateState(**state)
    
    def get_workflow(self,checkpointer):
        return base_workflow(
            checkpointer=checkpointer
        )

    def init_state(self,input:SiemRulesUpdateInput) -> SiemRulesUpdateState:   
        return SiemRulesUpdateState(
            rules_ids=input.rules_ids,
            product=input.product,
            client_id=input.client_id,
            tenant_id=input.tenant_id,
        )

    def get_config(self,input:SiemRulesUpdateInput) -> RunnableConfig:
        session_id = f"update_rules_{input.product}-{input.client_id}-{input.tenant_id}".replace("-", "").replace("_", "")
        return {
            "run_id": get_client().get_current_trace_id(),
            "callbacks": [self.get_langfuse_handler()],
            "configurable": {
                "thread_id": session_id,
            }
        }

    async def run(self, input: SiemRulesUpdateInput):
        start_time = time()
        try:
            # Increment counter before processing
            RULES_RUN.inc()
            
            # Call parent's run method
            result = await super().run(input)
            
            # Record total workflow duration
            WORKFLOW_COMPLETE_DURATION.observe(time() - start_time)
            
            return result
            
        except Exception as e:
            RULES_RUN_FAILURES.inc()
            raise
    
    