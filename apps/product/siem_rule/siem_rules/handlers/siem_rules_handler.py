from time import time
from typing import Dict, Optional, override

from corelanggraph.handler import <PERSON><PERSON><PERSON><PERSON>
from langchain_core.runnables import RunnableConfig
from langfuse import get_client
from pydantic import BaseModel, Field

from siem_rules.metrics import RULES_RUN, RULES_RUN_FAILURES, WORKFLOW_COMPLETE_DURATION

# These imports will be replaced with the actual project name during template rendering
from siem_rules.workflow.siem_rules.siem_rules import base_workflow
from siem_rules.workflow.siem_rules.state import SiemRulesState


class SiemRulesInput(BaseModel):
    name: str = Field(description="The name of the rule")
    query: str = Field(description="The query of the rule")
    description: str = Field(description="The description of the rule")
    rule_id: str = Field(description="The id of the rule")
    product: str = Field(description="The SIEM product name")
    client_id: str = Field(description="The client id")
    tenant_id: str = Field(description="The tenant id", default="default")
    metadata: Optional[dict] = Field(
        description="Other metadata about the rule", default_factory=dict
    )


class SiemRulesHandler(BaseHandler[SiemRulesState, SiemRulesInput]):

    def __init__(self):
        super().__init__()

    def parse_state(self, state: Dict) -> SiemRulesState:
        retState = SiemRulesState(**state)
        retState.context = dict(state)
        return retState

    def get_workflow(self, checkpointer):
        return base_workflow(checkpointer=None)

    @override
    def concat_state(
        self, input: SiemRulesInput, state: SiemRulesState
    ) -> SiemRulesState:
        return SiemRulesState(
            name=input.name,
            query=input.query,
            description=input.description,
            product=input.product,
            client_id=input.client_id,
            tenant_id=input.tenant_id,
            metadata=input.metadata,
            rule_id=input.rule_id,
        )

    def init_state(self, input: SiemRulesInput) -> SiemRulesState:
        return SiemRulesState(
            name=input.name,
            query=input.query,
            description=input.description,
            product=input.product,
            client_id=input.client_id,
            tenant_id=input.tenant_id,
            metadata=input.metadata,
            rule_id=input.rule_id,
        )

    def get_config(self, input: SiemRulesInput) -> RunnableConfig:
        session_id = f"{input.product}-{input.client_id}-{input.tenant_id}-{input.rule_id}".replace(
            "-", ""
        ).replace(
            "_", ""
        )
        return {
            "run_id": get_client().get_current_trace_id(),
            "callbacks": [self.get_langfuse_handler()],
            "configurable": {
                "thread_id": session_id,
                "langfuse_session_id": session_id,
                "langfuse_user_id": input.client_id,
                "langfuse_tags": ["siem-rules"],
            },
        }

    async def run(self, input: SiemRulesInput):
        start_time = time()
        try:
            # Increment counter before processing
            RULES_RUN.inc()

            # Call parent's run method
            result = await super().run(input)

            # Record total workflow duration
            WORKFLOW_COMPLETE_DURATION.observe(time() - start_time)

            return result

        except Exception as e:
            RULES_RUN_FAILURES.inc()
            raise
