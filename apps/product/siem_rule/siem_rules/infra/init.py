import threading


# Décorateur debounce pour méthodes de classe/instance
def debounce(wait):
    def decorator(fn):
        timer = None
        lock = threading.Lock()
        result = [None]

        def debounced(*args, **kwargs):
            nonlocal timer

            def call_it():
                with lock:
                    timer = None
                    result[0] = fn(*args, **kwargs)

            with lock:
                if timer is not None:
                    timer.cancel()
                timer = threading.Timer(wait, call_it)
                timer.start()
            # Optionnel: retourne la dernière valeur connue
            return result[0]

        return debounced

    return decorator


class InfraSingleton:
    _instance = None

    def __init__(self):
        if InfraSingleton._instance is not None:
            raise Exception("This class is a singleton!")
        import os

        from corelanggraph.langfuse import LangfuseClient
        from corellm import ModelProvider
        from elasticapm.contrib.starlette import make_apm_client
        from mongo import CymulateMongoClient
        from secretmanager import SecretManagerFactory

        os.environ["LANGFUSE_TRACING_ENVIRONMENT"] = os.getenv("ENV", "local")
        os.environ["LANGFUSE_PROMPT_FOLDER"] = "SiemRules"

        self.langfuse = LangfuseClient(
            secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
            public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
            host=os.getenv("LANGFUSE_HOST"),
        ).client

        self.secret = SecretManagerFactory.create().get_secret()
        CymulateMongoClient._load_all_dbs()
        self.model_provider = ModelProvider(
            list(map(lambda x: x.model_dump(), self.secret.chatbot_ai.models))
        )

        os.environ["CUST_AWS_ACCESS_KEY_ID"] = self.secret.s3.accessKeyId
        os.environ["CUST_AWS_SECRET_ACCESS_KEY"] = self.secret.s3.secretAccessKey
        os.environ["CUST_AWS_SECRET_ACCESS_KEY_ENCRYPTED"] = "True"

        InfraSingleton._instance = self

        self.apm = make_apm_client(
            {
                "SERVICE_NAME": "siem-rule",
                "SECRET_TOKEN": self.secret.elastic.apmToken,
                "SERVER_URL": self.secret.elastic.apmUrl,
                "ENVIRONMENT": os.getenv("ENV", "local"),
            }
        )

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls()
        return cls._instance


infra = InfraSingleton.get_instance()
