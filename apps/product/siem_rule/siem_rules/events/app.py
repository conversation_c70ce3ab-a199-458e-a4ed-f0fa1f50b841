import os
import uuid
from typing import Any, Dict

from confluent_kafka.cimpl import Message
from kafka.manager import Kaf<PERSON><PERSON>ana<PERSON>

from siem_rules.handlers.siem_rules_handler import SiemRulesHandler, SiemRulesInput
from siem_rules.handlers.siem_rules_update import (
    SiemRulesUpdate<PERSON><PERSON><PERSON>,
    SiemRulesUpdateInput,
)

from ..infra import init

env = os.environ.get("ENV", "local")
group_id = uuid.uuid4() if env == "local" else f"siem_rules"

kafka_manager = KafkaManager(config=init.infra.secret.kafka, group_id=group_id)


@kafka_manager.kafka_event(topics="siem_rules.process_rule")
async def siem_rules_handler(data: Dict[str, Any], message: Message):
    # Add UUID to the message data
    try:
        handler = SiemRulesHandler()
        await handler.run(SiemRulesInput(**data))
    except Exception as e:
        init.infra.apm.capture_exception()


@kafka_manager.kafka_event(topics="siem_rules.process_batch")
async def siem_rules_list_handler(data: Dict[str, Any], message: Message):
    """
    This function is used to process a list of rules.
    Disable all rules that are not in the list.
    """
    try:
        handler = SiemRulesUpdateHandler()
        await handler.run(SiemRulesUpdateInput(**data))
    except Exception as e:
        init.infra.apm.capture_exception(e)


if __name__ == "__main__":
    kafka_manager.start_consuming()
