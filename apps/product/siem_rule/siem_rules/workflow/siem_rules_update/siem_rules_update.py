from corelanggraph import EnhancedStateGraph
from langgraph.checkpoint.base import Base<PERSON><PERSON>ckpointSaver
from langgraph.graph import START
from langgraph.graph.state import CompiledStateGraph

from siem_rules.agents.update.update_rules_mongo import UpdateRulesMongoAgent
from siem_rules.workflow.siem_rules_update.state import SiemRulesUpdateState


def base_workflow(checkpointer: BaseCheckpointSaver) -> CompiledStateGraph:
    workflow_builder = EnhancedStateGraph(SiemRulesUpdateState)
    
    workflow_builder.add_node("update_rules_mongo",UpdateRulesMongoAgent)

    workflow_builder.add_edge(START , "update_rules_mongo")

    return workflow_builder.compile(checkpointer=checkpointer)
