from pydantic import BaseModel, Field


class SiemRulesUpdateState(BaseModel):
    """
    State for the Siem Rules agent.
    """
    rules_ids:list[str] = Field(description="The IDs of the rules to update")
    product:str = Field(description="The product of the rules")
    client_id:str = Field(description="The client ID of the rules")
    tenant_id:str = Field(description="The tenant ID of the rules")