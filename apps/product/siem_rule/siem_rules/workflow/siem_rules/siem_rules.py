import os

from corelanggraph import EnhancedStateGraph
from langgraph.checkpoint.base import Base<PERSON>heckpointSaver
from langgraph.graph import START
from langgraph.graph.state import CompiledStateGraph

from siem_rules.agents.chosing_using_llm import ChosingUsingLLMAgent
from siem_rules.agents.prepare_rules_agent import PrepareRulesAgent
from siem_rules.agents.rag_proccessing_agent import RAGProcessingAgent
from siem_rules.agents.save_result_to_mongo_agent import SaveResultToMongoAgent
from siem_rules.workflow.siem_rules.state import SiemRulesState


def base_workflow(checkpointer: BaseCheckpointSaver) -> CompiledStateGraph:
    if os.getenv("LANGGRAPH_UI_BUNDLER") == "true":
        pass
    workflow_builder = EnhancedStateGraph(SiemRulesState)
    
    workflow_builder.add_node("prepare_rules",PrepareRulesAgent)
    workflow_builder.add_node("rag_processing",RAGProcessingAgent)
    workflow_builder.add_node("chosing_using_llm",ChosingUsingLLMAgent)
    workflow_builder.add_node("save_result_to_mongo",SaveResultToMongoAgent)

    workflow_builder.add_edge(START , "prepare_rules")

    return workflow_builder.compile(checkpointer=checkpointer)
