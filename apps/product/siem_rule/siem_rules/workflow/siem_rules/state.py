from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class RelevantScenario(BaseModel):
    id: str = Field(description="The id of the scenario")
    text: str = Field(description="The text of the scenario")


class SiemRulesState(BaseModel):
    """
    State for the Siem Rules agent.
    """

    name: str = Field(description="The name of the rule")
    query: str = Field(description="The query of the rule")
    rule_id: str = Field(description="The id of the rule")
    description: str = Field(description="The description of the rule")
    product: str = Field(description="The SIEM product name")
    client_id: str = Field(description="The client id")
    tenant_id: str = Field(description="The tenant id", default="default")
    metadata: Optional[dict] = Field(
        description="Other metadata about the rule", default_factory=dict
    )
    relevant_scenarios: Optional[List[RelevantScenario]] = Field(
        description="The relevant scenarios for the rule", default_factory=list
    )
    document_id: Optional[str] = Field(
        description="The document id of the rule", default=None
    )
    desicion: Optional[str] = Field(description="The desicion of the LLM", default=None)
    context: Optional[Dict] = Field(
        description="Additional context for the state", default=None
    )
