import concurrent.futures
import logging
import multiprocessing
import os
from typing import Any, Dict, List

import numpy as np
from corellm import ModelProvider
from fastapi import APIRouter, HTTPException
from mongo import CymulateMongoClient
from pydantic import BaseModel
from pymilvus import MilvusClient
from pymongo.database import Database
from tqdm import tqdm

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configuration
class IndexerConfig:
    MILVUS_URI = os.getenv("MILVUS_URI", "http://localhost:19530")
    BATCH_SIZE = 100
    COLLECTION_NAME = 'bas2_scenarios'
    DEFAULT_TENANT = 'default'
    MAX_WORKERS = min(multiprocessing.cpu_count() * 2, 16)  # Limit max workers

# Response models
class IndexingResponse(BaseModel):
    status: str
    total_processed: int
    newly_indexed: int

router = APIRouter(prefix="/indexer", tags=["Indexer"])


def get_db(tenant_id: str) -> Database:
    """Get MongoDB database instance for the given tenant."""
    return CymulateMongoClient.get_instance(tenant_id).get_db()

def get_milvus_client() -> MilvusClient:
    """Get Milvus client instance."""
    return MilvusClient(uri=IndexerConfig.MILVUS_URI)

def get_embedding_model():
    """Get embedding model instance."""
    return ModelProvider().get_embeddings('azure_text_embedding_3_small')

def count_scenarios(db: Database) -> int:
    """Count the total number of scenarios to process."""
    return db.get_collection(IndexerConfig.COLLECTION_NAME).count_documents(
        {"public": True, "visible": True}
    )

def get_scenarios_to_process(db: Database):
    """Get a cursor for scenarios that need to be processed."""
    return db.get_collection(IndexerConfig.COLLECTION_NAME).find(
        {"public": True, "visible": True},
        {"_id": 1, "name": 1, "description": 1}
    ).batch_size(IndexerConfig.BATCH_SIZE)

def check_existing_scenarios(client: MilvusClient, scenario_ids: List[str]) -> set:
    """Check which scenarios already exist in Milvus."""
    existing_docs = client.query(
        collection_name=IndexerConfig.COLLECTION_NAME,
        filter=f"id in {scenario_ids}",
        output_fields=["id"]
    )
    return set(doc["id"] for doc in existing_docs)

def process_single_scenario(scenario: Dict[str, Any], embedding_model) -> Dict[str, Any]:
    """Process a single scenario and return its embedding data."""
    scenario_id = str(scenario["_id"])
    text_to_embed = f"{scenario.get('name', '')}\n{scenario.get('description', '')}"
    embeddings = embedding_model.embed_documents(text_to_embed)
    
    # Convert embeddings to the correct format for Milvus
    # Milvus expects a float_vector, which is a numpy array of float32
    if isinstance(embeddings, list):
        # If embeddings is a list of lists, take the first embedding
        if embeddings and isinstance(embeddings[0], list):
            embeddings = embeddings[0]
        # Convert to numpy array of float32
        embeddings = np.array(embeddings, dtype=np.float32)
    
    # Ensure embeddings is a 1D array
    if len(embeddings.shape) > 1:
        embeddings = embeddings.flatten()
    
    # For Milvus, we need to return the vector as a numpy array, not a list
    return {
        "id": scenario_id,
        "text": text_to_embed,
        "vector": embeddings  # Keep as numpy array for Milvus
    }

def prepare_batch_data(scenarios: List[Dict[str, Any]], embedding_model) -> List[Dict[str, Any]]:
    """Prepare batch data for insertion into Milvus using parallel processing."""
    batch_data = []
    
    # Process scenarios in parallel
    with concurrent.futures.ThreadPoolExecutor(max_workers=IndexerConfig.MAX_WORKERS) as executor:
        # Submit all tasks
        future_to_scenario = {
            executor.submit(process_single_scenario, scenario, embedding_model): scenario 
            for scenario in scenarios
        }
        
        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_scenario):
            try:
                result = future.result()
                batch_data.append(result)
            except Exception as e:
                scenario = future_to_scenario[future]
                logger.error(f"Error processing scenario {scenario.get('_id')}: {str(e)}")
    
    return batch_data

@router.get("/scenarios", response_model=IndexingResponse)
async def get_scenarios():
    """Index scenarios in Milvus for vector search."""
    logger.info("Starting scenario indexing process")
    
    try:
        # Initialize clients and models
        embedding_model = get_embedding_model()
        client = get_milvus_client()
        db = get_db(IndexerConfig.DEFAULT_TENANT)
        
        # Get total count and cursor for scenarios
        total_scenarios = count_scenarios(db)
        scenarios_cursor = get_scenarios_to_process(db)
        logger.info(f"Found {total_scenarios} public scenarios to process")
        
        # Load collection in Milvus
        client.load_collection(IndexerConfig.COLLECTION_NAME)
        
        processed = 0
        indexed_count = 0
        current_batch = []
        
        # Process scenarios in batches
        with tqdm(total=total_scenarios, desc="Processing scenarios") as pbar:
            for scenario in scenarios_cursor:
                current_batch.append(scenario)
                processed += 1
                
                # Process batch when it reaches batch_size or for last remaining items
                if len(current_batch) == IndexerConfig.BATCH_SIZE or processed == total_scenarios:
                    # Check which scenarios already exist
                    scenario_ids = [str(s["_id"]) for s in current_batch]
                    existing_ids = check_existing_scenarios(client, scenario_ids)
                    
                    # Filter out existing scenarios
                    new_scenarios = [s for s in current_batch if str(s["_id"]) not in existing_ids]
                    
                    if new_scenarios:
                        try:
                            # Prepare and insert batch data using parallel processing
                            batch_data = prepare_batch_data(new_scenarios, embedding_model)
                            
                            # Insert the batch data into Milvus
                            client.insert(
                                collection_name=IndexerConfig.COLLECTION_NAME,
                                data=batch_data
                            )
                            indexed_count += len(batch_data)
                            logger.debug(f"Indexed {len(batch_data)} scenarios in batch")
                        except Exception as e:
                            logger.error(f"Error in batch processing: {str(e)}")
                            raise HTTPException(status_code=500, detail=f"Error processing batch: {str(e)}")
                    
                    pbar.update(len(current_batch))
                    current_batch = []
        
        logger.info(f"Indexing complete. Processed {processed} scenarios, newly indexed {indexed_count} scenarios")
        return IndexingResponse(
            status="success",
            total_processed=processed,
            newly_indexed=indexed_count
        )
    
    except Exception as e:
        logger.error(f"Error during indexing process: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Indexing process failed: {str(e)}")
