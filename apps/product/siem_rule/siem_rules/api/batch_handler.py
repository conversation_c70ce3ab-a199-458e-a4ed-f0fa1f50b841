import json
import logging
import traceback

from fastapi import APIRouter, HTTPException
from kafka import KafkaProducer
from pydantic import BaseModel, Field
from storage.factory import StorageFactory

from ..infra.init import infra

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

router = APIRouter(tags=["Batch"])

S3_BUCKET = "cy-siem-rules-data-result"


class BatchRequest(BaseModel):
    key: str = Field(..., description="The S3 key (path) to the rules file")
    product: str = Field(default="splunk", description="The product identifier")
    client_id: str = Field(..., description="The client identifier")
    tenant_id: str = Field(..., description="The tenant identifier")

    class Config:
        json_schema_extra = {
            "example": {
                "key": "rules/client123/rules.json",
                "client_id": "client123",
                "tenant_id": "tenant456",
            }
        }


class BatchResponse(BaseModel):
    status: str
    message: str


@router.post("/batch", response_model=BatchResponse)
async def batch_handler(request: BatchRequest):
    """
    Handle batch requests.
    1. Gets the S3 key from the request body
    2. Downloads and processes the rules from S3
    3. Sends individual rule messages to siem_rules.process_rule topic
    4. Sends a batch message to siem_rules.process_batch topic
    """
    try:
        # Initialize S3 client
        s3_storage = StorageFactory.create_s3_storage()

        # Download rules from S3
        response = s3_storage.get_file({"storage": S3_BUCKET, "key": request.key})

        rules_json = json.loads(response.decode("utf-8"))

        producer = KafkaProducer(infra.secret.kafka)

        # Process rules and send to Kafka

        headers = [("tenant_id", request.tenant_id.encode("utf-8"))]
        rule_ids = []
        for rule in rules_json.get("entry", []):
            try:
                rule_data = {
                    "name": rule.get("name"),
                    "query": rule.get("content", {}).get("search"),
                    "description": rule.get("content", {}).get("description"),
                    "product": request.product,
                    "client_id": request.client_id,
                    "tenant_id": request.tenant_id,
                    "metadata": {},
                    "rule_id": rule.get("id"),
                }
                producer.send(
                    "siem_rules.process_rule",
                    json.dumps(rule_data).encode("utf-8"),
                    headers=headers,
                )
                rule_ids.append(rule.get("id"))
            except Exception as e:
                logger.error(f"Error sending rule to Kafka: {str(e)}")

        # Send batch message
        batch_data = {
            "rules_ids": rule_ids,
            "product": request.product,
            "client_id": request.client_id,
            "tenant_id": request.tenant_id,
        }

        producer.send(
            "siem_rules.process_batch",
            json.dumps(batch_data).encode("utf-8"),
            headers=headers,
        )

        # Ensure all messages are delivered
        # producer.flush()

        return BatchResponse(
            status="success",
            message=f"Processed {len(rule_ids)} rules and sent to Kafka",
        )

    except Exception as e:
        logger.error(f"Error processing batch request: {str(e)}")
        logger.error(f"Stack trace:\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
