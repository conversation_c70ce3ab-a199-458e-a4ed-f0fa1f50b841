import logging
import os

from pymilvus import DataType, MilvusClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_bas2_scenarios_collection():
    """Create the bas2_scenarios collection with proper schema for scenario storage"""
    try:
        # Initialize Milvus client
        client = MilvusClient(
            uri=os.getenv("MILVUS_URI", "http://localhost:19530")
        )
        
        # Check if collection exists
        if client.has_collection('bas2_scenarios'):
            logger.info("Collection 'bas2_scenarios' already exists. Dropping it to recreate...")
            client.drop_collection('bas2_scenarios')
        
        # Create schema
        schema = MilvusClient.create_schema()
        
        # Add fields to schema
        schema.add_field(field_name="id", datatype=DataType.VARCHAR, max_length=512, is_primary=True)
        schema.add_field(field_name="text", datatype=DataType.VARCHAR, max_length=65535)
        schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=1536)

        # Prepare index parameters
        index_params = client.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            index_type="HNSW",
            metric_type="COSINE",
            params={
                'M': 8,  # Number of bi-directional links for each node
                'efConstruction': 200  # Factor that influences index building speed and index quality
            }
        )
        
        # Create collection with schema and index
        client.create_collection(
            collection_name='bas2_scenarios',
            schema=schema,
            consistency_level="Strong",
            index_params=index_params
        )
        
        logger.info("Successfully created collection 'bas2_scenarios' with schema and index")
        
    except Exception as e:
        logger.error(f"Error creating collection: {str(e)}")
        raise

if __name__ == "__main__":
    create_bas2_scenarios_collection() 