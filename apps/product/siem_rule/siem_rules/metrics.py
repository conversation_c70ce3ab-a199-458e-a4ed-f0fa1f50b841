from prometheus_client import Counter, Histogram

# RAG Processing Metrics
RAG_PROCESSING_DURATION = Histogram(
    'cymulate_rag_processing_duration_seconds',
    'Time taken for RAG processing',
    buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, float("inf"))
)

RAG_RETRIEVED_DOCUMENTS = Histogram(
    'cymulate_rag_retrieved_documents_count',
    'Number of documents retrieved per query',
    buckets=(1, 5, 10, 20, 50, 100, float("inf"))
)

RAG_PROCESSING_TOTAL = Counter(
    'cymulate_rag_processing_total',
    'Total number of RAG processing operations'
)

RAG_PROCESSING_FAILURES = Counter(
    'cymulate_rag_processing_failures_total',
    'Total number of RAG processing failures'
)

LLM_RETRIEVED_SCENARIOS = Histogram(
    'cymulate_llm_retrieved_scenarios_count',
    'Number of scenarios retrieved per query',
    buckets=(1, 5, 10, 20, 50, 100, float("inf"))
)

# Database Operation Metrics
DB_SAVE_DURATION = Histogram(
    'cymulate_db_save_duration_seconds',
    'Time taken to save rules to MongoDB',
    buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, float("inf"))
)

DB_QUERY_DURATION = Histogram(
    'cymulate_db_query_duration_seconds',
    'Time taken to query MongoDB',
    buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, float("inf"))
)

DB_SAVE_FAILURES = Counter(
    'cymulate_db_save_failures_total',
    'Failed database save operations'
)

# Workflow Metrics
WORKFLOW_STEP_DURATION = Histogram(
    'cymulate_workflow_step_duration_seconds',
    'Duration of each workflow step',
    ['step_name'],
    buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, float("inf"))
)

WORKFLOW_STEP_FAILURES = Counter(
    'cymulate_workflow_step_failures_total',
    'Failures at each workflow step',
    ['step_name']
)

WORKFLOW_COMPLETE_DURATION = Histogram(
    'cymulate_workflow_complete_duration_seconds',
    'End-to-end processing time for workflow',
    buckets=(1.0, 5.0, 10.0, 30.0, 60.0, 120.0, float("inf"))
)

# Vector DB Metrics
VECTOR_DB_QUERY_DURATION = Histogram(
    'cymulate_vector_db_query_duration_seconds',
    'Time taken for vector database queries',
    buckets=(0.1, 0.5, 1.0, 2.0, 5.0, 10.0, float("inf"))
) 

RULES_RUN = Counter(
    'cymulate_rules_run_total',
    'Total number of rules run'
)

RULES_RUN_FAILURES = Counter(
    'cymulate_rules_run_failures_total',
    'Total number of rules run failures'
)
