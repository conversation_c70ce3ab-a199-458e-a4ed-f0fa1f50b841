from time import time

from corelanggraph.agents import BaseAgent
from langchain_core.documents import Document
from langgraph.types import Command
from logger import logger

from siem_rules.integrations.rag_processing import RAGProcessing
from siem_rules.metrics import (
    RAG_PROCESSING_DURATION,
    RAG_PROCESSING_FAILURES,
    RAG_PROCESSING_TOTAL,
    RAG_RETRIEVED_DOCUMENTS,
    WORKFLOW_STEP_DURATION,
    WORKFLOW_STEP_FAILURES,
)
from siem_rules.workflow.siem_rules.state import RelevantScenario, SiemRulesState


class RAGProcessingAgent(BaseAgent):
    def __init__(self):
        super().__init__(name="rag_processing", description="Process the RAG results")

        self.rag_processing = RAGProcessing()

    async def process_rules(self, state: SiemRulesState) -> SiemRulesState:
        start_time = time()
        try:
            RAG_PROCESSING_TOTAL.inc()

            doc = Document(
                page_content=f"{state.name}\n{state.query}\n{state.description}",
            )

            # Get related files with reranking
            results = await self.rag_processing.get_related_files(
                query=doc.page_content,
                collection_name="scenarios",
                top_k=50,
                hybrid_weights=None,
            )

            # Record metrics
            RAG_RETRIEVED_DOCUMENTS.observe(len(results))
            RAG_PROCESSING_DURATION.observe(time() - start_time)

            # Store the results in the state
            state.relevant_scenarios = [RelevantScenario(**doc) for doc in results]

            return state

        except Exception as e:
            RAG_PROCESSING_FAILURES.inc()
            WORKFLOW_STEP_FAILURES.labels(step_name="rag_processing").inc()
            logger.error(f"Error in RAG processing: {str(e)}")
            raise

    async def execute(self, state: SiemRulesState) -> SiemRulesState:
        start_time = time()
        try:
            # Process rules and get RAG results
            state = await self.process_rules(state)

            # Record workflow step duration
            WORKFLOW_STEP_DURATION.labels(step_name="rag_processing").observe(
                time() - start_time
            )

            # Return command to move to the next step
            return Command(goto="chosing_using_llm", update=state)
        except Exception as e:
            WORKFLOW_STEP_FAILURES.labels(step_name="rag_processing").inc()
            raise
