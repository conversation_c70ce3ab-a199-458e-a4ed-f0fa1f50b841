from time import time

from bson.objectid import ObjectId
from corelanggraph.agents import BaseAgent
from langgraph.graph import END
from langgraph.types import Command
from logger import logger
from mongo import CymulateMongoClient
from pymongo.collection import Collection
from pymongo.database import Database

from siem_rules.helpers.generate_hash import generate_hash_from_id
from siem_rules.metrics import (
    DB_QUERY_DURATION,
    WORKFLOW_STEP_DURATION,
    WORKFLOW_STEP_FAILURES,
)
from siem_rules.workflow.siem_rules.state import SiemRulesState


class PrepareRulesAgent(BaseAgent):
    def __init__(self):
        super().__init__(
            name="prepare_rules", description="Prepare rules for the SIEM product"
        )
        self.integration_rules_collection_name = "integrationSiemRules"

    def get_db(self, state: SiemRulesState) -> Database:
        return CymulateMongoClient.get_instance(state.tenant_id).get_db()

    def get_integration_rules_collection(self, state: SiemRulesState) -> Collection:
        return self.get_db(state).get_collection(self.integration_rules_collection_name)

    def check_exact_rule_exists(self, state: SiemRulesState) -> str:
        """
        Check if a rule with the exact same fields (name, query, product, clientID) already exists.

        Args:
            state: The current state of the SIEM rules workflow

        Returns:
            Hash of document ID if an exact match exists, None otherwise
        """
        try:
            logger.info(f"Checking if exact rule exists: {state.name}")

            # Get the integration rules collection
            integration_rules_collection = self.get_integration_rules_collection(state)

            logger.info(f"Integration rules collection: {integration_rules_collection}")

            # Create a query for OR match on specific field combinations
            query = {
                "$or": [
                    {
                        "clientID": ObjectId(state.client_id),
                        "product": state.product,
                        "ruleID": state.rule_id,
                    },
                    {
                        "clientID": ObjectId(state.client_id),
                        "product": state.product,
                        "name": state.name,
                        "query": state.query,
                    },
                ]
            }

            # Time the database query
            query_start_time = time()
            try:
                # Check if any document matches the query
                document = integration_rules_collection.find_one(query)
                logger.info(f"Found rule: {document}")
                DB_QUERY_DURATION.observe(time() - query_start_time)
            except Exception as e:
                logger.error(f"Database query failed: {str(e)}")
                raise

            if document is not None:
                return document.get("_id")
            else:
                return None

        except Exception as e:
            logger.error(f"Error checking if rule exists: {str(e)}")
            return None

    def execute(self, state: SiemRulesState) -> SiemRulesState:
        start_time = time()
        try:
            state.metadata["id"] = state.rule_id
            state.rule_id = generate_hash_from_id(state.rule_id)
            # Check if an exact rule already exists
            document_id = self.check_exact_rule_exists(state)

            # Record workflow step duration
            WORKFLOW_STEP_DURATION.labels(step_name="prepare_rules").observe(
                time() - start_time
            )

            if document_id is not None:
                return Command(goto=END, update=state)

            return Command(goto="rag_processing", update=state)
        except Exception as e:
            WORKFLOW_STEP_FAILURES.labels(step_name="prepare_rules").inc()
            raise
