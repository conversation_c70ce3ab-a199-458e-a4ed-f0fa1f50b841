from time import time

from corelanggraph.agents import BaseAgent
from langgraph.graph import END
from langgraph.types import Command
from logger import logger
from mongo import CymulateMongoClient
from pymongo.collection import Collection
from pymongo.database import Database

from siem_rules.helpers.generate_hash import generate_hash_from_id
from siem_rules.metrics import (
    DB_SAVE_DURATION,
    DB_SAVE_FAILURES,
    WORKFLOW_STEP_DURATION,
    WORKFLOW_STEP_FAILURES,
)
from siem_rules.models.siem_rules_schema import SiemRule
from siem_rules.workflow.siem_rules_update.state import SiemRulesUpdateState


class UpdateRulesMongoAgent(BaseAgent):
    collection_name: str = SiemRule.Config.collection  # Updated to match schema

    def __init__(self):
        super().__init__(
            name="save_result_to_mongo",
            description="Save the SIEM rule to MongoDB"
        )

    def get_db(self, state: SiemRulesUpdateState) -> Database:
        return CymulateMongoClient.get_instance(state.tenant_id).get_db()
    
    def get_collection(self, state: SiemRulesUpdateState) -> Collection:
        return self.get_db(state).get_collection(self.collection_name)

    def execute(self, state: SiemRulesUpdateState) -> SiemRulesUpdateState:
        start_time = time()
        try:
            # Convert state data to SiemRule model
           
            
            # Time the database operation
            db_start_time = time()
            try:
                # Validate and save to MongoDB
                #update all ids that not in the list set enabled to false
                logger.info(f"Updating {len(state.rules_ids)} rules in MongoDB")
                rules = list(map(lambda rule_id: generate_hash_from_id(rule_id), state.rules_ids))
                self.get_collection(state).update_many({
                    "ruleID": {"$nin":  rules},
                    "clientID": state.client_id,
                    "product": state.product
                }, {"$set": {"enabled": False}})
                DB_SAVE_DURATION.observe(time() - db_start_time)
            except Exception as e:
                DB_SAVE_FAILURES.inc()
                logger.error(f"Failed to save rule to MongoDB: {str(e)}")
                raise
            
            logger.info(f"Updated SIEM rules in MongoDB")
            
            # Record workflow step duration
            WORKFLOW_STEP_DURATION.labels(step_name="save_result_to_mongo").observe(time() - start_time)
            
            return Command(
                goto=END,
                update=state
            )
        except Exception as e:
            WORKFLOW_STEP_FAILURES.labels(step_name="save_result_to_mongo").inc()
            raise