from time import time

from bson import ObjectId
from corelanggraph.agents import BaseAgent
from langgraph.graph import END
from langgraph.types import Command
from logger import logger
from mongo import CymulateMongoClient
from pymongo.collection import Collection
from pymongo.database import Database

from siem_rules.metrics import (
    DB_SAVE_DURATION,
    DB_SAVE_FAILURES,
    WORKFLOW_STEP_DURATION,
    WORKFLOW_STEP_FAILURES,
)
from siem_rules.models.siem_rules_schema import SiemRule
from siem_rules.workflow.siem_rules.state import SiemRulesState


class SaveResultToMongoAgent(BaseAgent):
    collection_name: str = SiemRule.Config.collection  # Updated to match schema

    def __init__(self):
        super().__init__(
            name="save_result_to_mongo", description="Save the SIEM rule to MongoDB"
        )

    def get_db(self, state: SiemRulesState) -> Database:
        return CymulateMongoClient.get_instance(state.tenant_id).get_db()

    def get_collection(self, state: SiemRulesState) -> Collection:
        return self.get_db(state).get_collection(self.collection_name)

    def execute(self, state: SiemRulesState) -> SiemRulesState:
        start_time = time()
        try:
            # Convert state data to SiemRule model
            siem_rule = SiemRule(
                name=state.name,
                description=state.description,
                query=state.query,
                metadata=state.metadata,
                clientID=ObjectId(state.client_id),
                ruleID=state.rule_id,
                product=state.product,
                enabled=True,
                relevantScenarios=list(
                    map(lambda x: ObjectId(x.id), state.relevant_scenarios)
                ),
                desicion=state.desicion,
            )

            # Time the database operation
            db_start_time = time()
            try:
                # Upsert: update if exists, insert if not exists
                filter_criteria = {
                    "clientID": ObjectId(state.client_id),
                    "product": state.product,
                    "name": state.name,
                    "query": state.query,
                }
                self.get_collection(state).replace_one(
                    filter_criteria, siem_rule.model_dump(), upsert=True
                )
                DB_SAVE_DURATION.observe(time() - db_start_time)
            except Exception as e:
                DB_SAVE_FAILURES.inc()
                logger.error(f"Failed to save rule to MongoDB: {str(e)}")
                raise

            logger.info(f"Saved SIEM rule to MongoDB: {siem_rule.name}")

            # Record workflow step duration
            WORKFLOW_STEP_DURATION.labels(step_name="save_result_to_mongo").observe(
                time() - start_time
            )

            return Command(goto=END, update=state)
        except Exception as e:
            WORKFLOW_STEP_FAILURES.labels(step_name="save_result_to_mongo").inc()
            raise
