from typing import Any, Dict, List, Optional

from logger import logger


class CrossEncoderRerank:
    """Client for reranking using sentence-transformers CrossEncoder"""
    
    def __init__(
        self,
        model_name: str = "cross-encoder/ms-marco-MiniLM-L-6-v2"
    ):
        """
        Initialize the CrossEncoder Rerank client
        
        Args:
            model_name: Name of the cross-encoder model to use
        """
        try:
            from sentence_transformers import CrossEncoder
            self.CrossEncoder = CrossEncoder
            self.model_name = model_name
            logger.info(f"Initializing CrossEncoder with model: {model_name}")
            self.cross_encoder = CrossEncoder(model_name)
            logger.info("CrossEncoder initialized successfully")
        except ImportError:
            logger.error("sentence-transformers package not found. Please install it with 'pip install sentence-transformers'")
            raise ImportError("sentence-transformers package is required for cross-encoder reranking")
        except Exception as e:
            logger.error(f"Error initializing CrossEncoder: {str(e)}")
            raise
    
    def rerank_documents(
        self, 
        query: str, 
        documents: List[Dict[str, Any]], 
        top_n: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Rerank documents based on relevance to the query using cross-encoder
        
        Args:
            query: The search query
            documents: List of documents to rerank, each with at least a 'text' field
            top_n: Number of top results to return (defaults to all)
            
        Returns:
            List of reranked documents with relevance scores
        """
        logger.info(f"Reranking {len(documents)} documents for query: '{query}'")
        
        try:
            # Prepare document texts
            document_texts = [doc.get("text", "") for doc in documents]
            
            # Create pairs for cross-encoder
            pairs = [(query, doc_text) for doc_text in document_texts]
            
            # Get rerank scores
            rerank_scores = self.cross_encoder.predict(pairs)
            
            # Combine scores with original documents
            reranked_docs = []
            for i, score in enumerate(rerank_scores):
                original_doc = documents[i]
                reranked_doc = original_doc.copy()
                reranked_doc["relevance_score"] = float(score)
                reranked_doc["index"] = i
                reranked_docs.append(reranked_doc)
            
            # Sort by relevance score
            reranked_docs.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            # Apply top_n if specified
            if top_n is not None:
                reranked_docs = reranked_docs[:top_n]
            
            logger.info(f"Reranking complete. Returning {len(reranked_docs)} documents")
            return reranked_docs
            
        except Exception as e:
            logger.error(f"Error during cross-encoder reranking: {str(e)}")
            raise 