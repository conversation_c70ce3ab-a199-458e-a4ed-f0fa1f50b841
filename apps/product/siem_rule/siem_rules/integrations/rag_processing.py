import os
from typing import Any, Dict, Optional

from corellm import ModelProvider
from langchain_community.embeddings import HuggingFaceEmbeddings
from logger import logger
from rag.database.milvus import MilvusClientArgs
from rag.stores import MilvusVectorStore, VectorStoreFactory

from siem_rules.integrations.cross_encoder_rerank import CrossEncoderRerank
from siem_rules.metrics import VECTOR_DB_QUERY_DURATION


class RAGProcessing:
    """Client for interacting with CodeAnalyser's RAG"""
    
    def __init__(
        self,
        api_base_url: str = "http://localhost:8080"
    ):
        """
        Initialize the Bitbucket client
        
        Args:
            api_base_url: Code analyser API base URL
        """
        logger.info(f"Initializing CodeAnalyserRag with API base URL: {api_base_url}")
        
        milvus_uri = os.getenv("MILVUS_URI", "http://localhost:19530")
        logger.info(f"Using Milvus URI: {milvus_uri}")
        
        self.client = MilvusClientArgs(
            uri=milvus_uri,
            # token=MILVUS_TOKEN,
            # db_name=kwargs.get("db_name"),
            # user=kwargs.get("user"),
            # password=kwargs.get("password"),
            # timeout=kwargs.get("timeout")
        )
        logger.debug("MilvusClientArgs initialized")
        
        logger.info("Loading embedding model")
        self.embeddings = self.embedding_model()
        
        self.collection_name:str = os.getenv("COLLECTION_NAME", 'siem_rules_embeddings')
        logger.info(f"Using collection name: {self.collection_name}")
        
        self.db_name:str = os.getenv("DB_NAME", 'cymulate_siem_rules')
        logger.info(f"Using database name: {self.db_name}")
        
        # Initialize CrossEncoder Rerank
        self.use_rerank = os.getenv("USE_CROSS_ENCODER_RERANK", "true").lower() == "true"
        if self.use_rerank:
            logger.info("Initializing CrossEncoder Rerank")
            self.reranker = CrossEncoderRerank()
            logger.info("CrossEncoder Rerank initialized")
        else:
            logger.info("CrossEncoder Rerank is disabled")

    async def get_related_files(self, query: str, collection_name: str, top_k: int = 10, hybrid_weights: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        logger.info(f"Getting related files for query: '{query}', collection_name: '{collection_name}', top_k: {top_k}")
        if not collection_name:
            collection_name = self.collection_name

        logger.debug(f"Initializing vector store with client and db_name: {self.db_name}")
        vector_store = VectorStoreFactory.get_vector_store('milvus', client=self.client, db_name=self.db_name, embedding_model=self.embeddings)
        logger.debug("Vector store initialized successfully")
        
        # Use hybrid search for Milvus if requested
        vector_store_type = os.getenv("VECTOR_STORE_TYPE", "hybrid2")
        logger.info(f"Using vector store type: {vector_store_type}")
        
        # Retrieve more documents than needed if we're using reranking
        retrieval_top_k = top_k * 2 if self.use_rerank else top_k
        
        if vector_store_type == "hybrid":
            logger.info("Performing hybrid search")
            if hybrid_weights:
                logger.debug(f"Using custom hybrid weights: {hybrid_weights}")
            else:
                logger.debug("Using default hybrid weights")
            
            vector_store: MilvusVectorStore = vector_store
            with VECTOR_DB_QUERY_DURATION.time():
                results = vector_store.query_hybrid(
                    query=query, 
                    collection_name=collection_name, 
                    top_k=retrieval_top_k, 
                    weights=hybrid_weights
                )
            logger.info(f"Hybrid search completed, found {len(results)} results")
        else:
            logger.info("Performing standard vector search")
            with VECTOR_DB_QUERY_DURATION.time():
                results = vector_store.query(
                    query=query, 
                    collection_name=collection_name, 
                    top_k=retrieval_top_k,
                    search_params={"params": {"ef": 100}}
                )
            logger.info(f"Standard search completed, found {len(results)} results")
        
        # Apply CrossEncoder Rerank if enabled
        if self.use_rerank and results:
            logger.info("Applying CrossEncoder Rerank to improve search results")
            try:
                # Convert results to format expected by reranker
                documents = []
                for doc, score in results:
                    doc = {
                        "text": doc.page_content,
                        "metadata": doc.metadata,
                        "id": doc.metadata.get('id'),
                        "score": score
                    }
                    documents.append(doc)
                
                # Rerank documents
                # reranked_docs = self.reranker.rerank_documents(query, documents, top_n=top_k)
                
                # Convert back to original format
                results = []
                for doc in documents:
                    result = {
                        "text": doc.get("text", ""),
                        "metadata": doc.get("metadata", {}),
                        "id": doc.get("id", ""),
                        "score": doc.get("relevance_score", 0)
                    }
                    results.append(result)
                
                logger.info(f"Reranking complete. Returning {len(results)} documents")
            except Exception as e:
                logger.error(f"Error during reranking: {str(e)}")
                # If reranking fails, just use the original results
                results = results[:top_k]
        
        # Ensure we return exactly top_k results
        if len(results) > top_k:
            results = results[:top_k]
        
        return results
    
    def embedding_model(self):
        embedding_model_name = os.getenv("EMBEDDING_MODEL")
        logger.info(f"Getting embedding model, EMBEDDING_MODEL env var: {embedding_model_name}")
        
        if embedding_model_name == "all-MiniLM-L6-v2":
            logger.info("Using HuggingFaceEmbeddings: all-MiniLM-L6-v2")
            return HuggingFaceEmbeddings(model_name="all-MiniLM-L6-v2")
        else:
            logger.info("Using default Azure Text Embedding model: azure_text_embedding_3_small")
            return ModelProvider().get_embeddings('azure_text_embedding_3_small')
    
    

    
   