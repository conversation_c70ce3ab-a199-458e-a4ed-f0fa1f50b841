import os
from typing import Any, Dict, List, Optional

import httpx
from logger import logger


class OpenAIRerank:
    """Client for interacting with OpenAI Rerank API"""
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        model: str = "text-embedding-3-large"
    ):
        """
        Initialize the OpenAI Rerank client
        
        Args:
            api_key: OpenAI API key (defaults to OPENAI_API_KEY env var)
            model: OpenAI model to use for reranking
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable or pass api_key parameter.")
        
        self.model = model
        self.api_url = "https://api.openai.com/v1/embeddings"
        logger.info(f"Initialized OpenAI Rerank with model: {model}")
    
    async def rerank_documents(
        self, 
        query: str, 
        documents: List[Dict[str, Any]], 
        top_n: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Rerank documents based on relevance to the query using OpenAI embeddings
        
        Args:
            query: The search query
            documents: List of documents to rerank, each with at least a 'text' field
            top_n: Number of top results to return (defaults to all)
            
        Returns:
            List of reranked documents with relevance scores
        """
        logger.info(f"Reranking {len(documents)} documents for query: '{query}'")
        
        try:
            # Get query embedding
            query_embedding = await self._get_embedding(query)
            
            # Get document embeddings
            document_embeddings = []
            for doc in documents:
                doc_text = doc.get("text", "")
                doc_embedding = await self._get_embedding(doc_text)
                document_embeddings.append(doc_embedding)
            
            # Calculate cosine similarity between query and documents
            reranked_docs = []
            for i, doc_embedding in enumerate(document_embeddings):
                similarity = self._cosine_similarity(query_embedding, doc_embedding)
                original_doc = documents[i]
                reranked_doc = original_doc.copy()
                reranked_doc["relevance_score"] = similarity
                reranked_doc["index"] = i
                reranked_docs.append(reranked_doc)
            
            # Sort by relevance score
            reranked_docs.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            # Apply top_n if specified
            if top_n is not None:
                reranked_docs = reranked_docs[:top_n]
            
            logger.info(f"Reranking complete. Returning {len(reranked_docs)} documents")
            return reranked_docs
            
        except Exception as e:
            logger.error(f"Error during OpenAI reranking: {str(e)}")
            raise
    
    async def _get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for a text using OpenAI API
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        async with httpx.AsyncClient() as client:
            response = await client.post(
                self.api_url,
                json={
                    "input": text,
                    "model": self.model
                },
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code != 200:
                logger.error(f"OpenAI API error: {response.text}")
                raise Exception(f"OpenAI API error: {response.status_code} - {response.text}")
            
            result = response.json()
            return result["data"][0]["embedding"]
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        Calculate cosine similarity between two vectors
        
        Args:
            vec1: First vector
            vec2: Second vector
            
        Returns:
            Cosine similarity score
        """
        import numpy as np
        
        # Convert to numpy arrays
        vec1 = np.array(vec1)
        vec2 = np.array(vec2)
        
        # Calculate cosine similarity
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0
        
        return dot_product / (norm1 * norm2) 