# SIEM Rules Application

This is the SIEM Rules Application for the MAS (Modular Multi-Agent System).

## Description

The SIEM Rules Application provides automated security information and event management capabilities using AI agents and rule-based processing.

## Features

- AI-powered security analysis
- Event processing and correlation
- Rule-based threat detection
- Integration with security tools
- Real-time monitoring

## Structure

- `agents/` - AI agents for security analysis
- `api/` - REST API endpoints
- `events/` - Event processing
- `handlers/` - Event handlers
- `helpers/` - Helper utilities
- `infra/` - Infrastructure components
- `integrations/` - External service integrations
- `models/` - Data models
- `triggers/` - Event triggers
- `workflow/` - Workflow definitions 