"""
Main entry point for the Codebase RAG application.
"""

import asyncio
import os
import signal
import time
import traceback
import uvicorn
from siem_rules.events.app import kafka_manager
from logger import logger

os.environ["LANGFUSE_TRACING_ENVIRONMENT"] = os.getenv("ENV","local")

# The app is now correctly exported from app.main
def handle_sigterm(signum, frame):
    """Handle SIGTERM signal by performing cleanup and exiting gracefully."""
    logger.info("Received SIGTERM signal. Performing cleanup...")
    # Add any cleanup code here
    kafka_manager.stop_consuming()
    kafka_manager.close()
    os._exit(0)

# Register SIGTERM handler


async def start_kafka_consumer():
    # Run the blocking kafka consumer in a thread pool
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, kafka_manager.start_consuming)

async def start_http_consumer():
    config = uvicorn.Config("siem_rules.api.app:app", host="0.0.0.0", 
                           port=int(os.getenv("PORT", 19000)), reload=True,workers=10)
    server = uvicorn.Server(config)
    await server.serve()

async def main():
     await asyncio.gather(
                start_kafka_consumer(),
                start_http_consumer()
            )

if __name__ == "__main__":
    try:
        asyncio.run(main())
    
        signal.signal(signal.SIGTERM, handle_sigterm)
        signal.signal(signal.SIGINT, handle_sigterm)

    except Exception as e:
        print(f"Error: {e}")
        print(f"Error type: {type(e)}")
        print(f"Error traceback: {traceback.format_exc()}")
        print(f"Error message: {str(e)}")
        print(f"Error args: {e.args}")





