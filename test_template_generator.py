#!/usr/bin/env python3

import asyncio
import os
import sys

# Add the project root to Python path
sys.path.insert(0, '/Users/<USER>/PycharmProjects/mas')

from apps.product.cyber_security.cyber_security.agents.template_generator.workflow.template_generator import compiled_workflow
from apps.product.cyber_security.cyber_security.agents.template_generator.graph.agent_state import AssessmentGeneratorAgentState
from langchain_core.messages import HumanMessage

async def test_template_generator():
    """Test the template generator workflow with minimal input."""
    
    # Create initial state
    initial_state = AssessmentGeneratorAgentState(
        messages=[HumanMessage(content="Create a security assessment template for AWS infrastructure")],
        user_input="Create a security assessment template for AWS infrastructure",
        classification="template_generator"
    )
    
    # Test configuration
    config = {
        "configurable": {
            "session_id": "test_session_123"
        }
    }
    
    print("🚀 Starting template generator test...")
    
    try:
        # Run the workflow
        result = await compiled_workflow.ainvoke(initial_state, config=config)
        print("✅ Workflow completed successfully!")
        print(f"Final state: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Workflow failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_template_generator())
    sys.exit(0 if success else 1)