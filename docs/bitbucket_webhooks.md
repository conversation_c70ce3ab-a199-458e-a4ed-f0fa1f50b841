# Bitbucket Webhooks Integration

This document provides instructions for setting up and using Bitbucket webhooks with our Multi-Agent System.

## Overview

The Multi-Agent System can respond to events from Bitbucket repositories by setting up webhooks. This integration allows the system to automatically process:

- Repository events (push, fork, update, transfer)
- Commit events (comments, build status)
- Issue events (creation, updates, comments)
- Pull request events (creation, updates, approvals, comments, merges)

## Setting Up Webhooks in Bitbucket

1. Navigate to your Bitbucket repository
2. Go to Repository Settings > Webhooks
3. Click "Add webhook"
4. Enter the following information:
   - Title: A descriptive name (e.g., "MAS Integration")
   - URL: `https://your-server-url/webhooks/bitbucket`
   - Status: Active
   - Triggers: Select the events you want to trigger the webhook
     - You can choose specific events or select "Everything" to receive all events

## Webhook Security

For security, we recommend setting up a webhook secret:

1. Generate a random secret string
2. Set it as an environment variable on your server:
   ```
   BITBUCKET_WEBHOOK_SECRET=your_secret_here
   ```
3. In Bitbucket, when configuring the webhook, add this same secret to the "Secret" field

This ensures that only Bitbucket can trigger your webhook endpoint.

## Webhook Event Payloads

The system processes webhook payloads according to the [Bitbucket event payloads documentation](https://support.atlassian.com/bitbucket-cloud/docs/event-payloads/).

### Supported Events

#### Repository Events
- `repo:push`: When code is pushed to a repository
- `repo:fork`: When a repository is forked
- `repo:updated`: When repository details are updated
- `repo:transfer`: When a repository is transferred

#### Commit Events
- `repo:commit_comment_created`: When a comment is added to a commit
- `repo:commit_status_created`: When a build status is created
- `repo:commit_status_updated`: When a build status is updated

#### Issue Events
- `issue:created`: When an issue is created
- `issue:updated`: When an issue is updated
- `issue:comment_created`: When a comment is added to an issue

#### Pull Request Events
- `pullrequest:created`: When a pull request is created
- `pullrequest:updated`: When a pull request is updated
- `pullrequest:approved`: When a pull request is approved
- `pullrequest:unapproved`: When an approval is removed
- `pullrequest:fulfilled`: When a pull request is merged
- `pullrequest:rejected`: When a pull request is declined
- `pullrequest:comment_created`: When a comment is added to a pull request
- `pullrequest:comment_updated`: When a comment is updated
- `pullrequest:comment_deleted`: When a comment is deleted
- `pullrequest:changes_request_created`: When changes are requested
- `pullrequest:changes_request_removed`: When a changes request is removed

## Testing Webhooks

You can test your webhook setup using Bitbucket's test feature or by manually triggering events in your repository.

### Response Format

When the system processes a webhook event, it returns a response with the following format:

```json
{
  "status": "processed", 
  "event": "event:type",
  "repository": "workspace/repository",
  "additional_fields": "..."
}
```

The response will include different fields depending on the type of event.

## Automatic Pull Request Reviews

When a pull request is created, the system can automatically:

1. Analyze the code changes
2. Provide feedback on code quality and potential issues
3. Add comments to specific lines in the code
4. Approve or request changes based on the analysis

This behavior can be customized in the `handle_pull_request_created` function in the webhook handler. 