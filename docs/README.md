# MAS Architecture Diagrams

This directory contains Mermaid diagrams that visualize the architecture and workflows of the Modular Agent System (MAS) framework.

## Available Diagrams

### 1. System Architecture Overview

**File:** `architecture_diagram.md`

This diagram provides a comprehensive overview of the MAS architecture, including:
- Layer organization (API, Security, Orchestration, State Management, Core, Agent, Integration)
- Component relationships
- Data flow
- Agent interactions
- Component relationships

### 2. Workflow Execution Process

**File:** `workflow_execution.md`

This diagram illustrates the workflow execution process in detail, including:
- Workflow initialization and execution steps
- Error handling process
- Agent communication flow
- Custom agent integration

### 3. API Endpoints and Data Models

**File:** `api_endpoints.md`

This diagram shows the API endpoints and data models, including:
- API endpoints overview
- Data model relationships
- Authentication flow
- API request-response flow

## Viewing the Diagrams

These diagrams are written in Mermaid, a markdown-based diagramming language. To view them:

1. **GitHub**: If you're viewing these files on GitHub, the diagrams will render automatically.

2. **VS Code**: Install the "Markdown Preview Mermaid Support" extension to view the diagrams in the markdown preview.

3. **Mermaid Live Editor**: Copy the Mermaid code and paste it into the [Mermaid Live Editor](https://mermaid.live/) to view and edit the diagrams.

4. **Documentation Site**: If you're viewing the generated documentation site, the diagrams should render automatically.

## Customizing the Diagrams

You can customize these diagrams to reflect changes in the architecture or to focus on specific aspects of the system:

1. Edit the Mermaid code in the respective markdown files
2. Use the Mermaid Live Editor to preview your changes
3. Update the markdown files with your modified diagrams

## Diagram Types

The diagrams use various Mermaid diagram types:

- **Flowchart**: Used for system architecture and workflow processes
- **Sequence Diagram**: Used for interaction sequences and data flow
- **State Diagram**: Used for state transitions and error handling
- **Class Diagram**: Used for data models and class relationships
- **Entity Relationship Diagram**: Used for component relationships

## Adding New Diagrams

To add new diagrams:

1. Create a new markdown file in this directory
2. Add a Mermaid code block with your diagram
3. Update this README to include your new diagram

## Troubleshooting

If you have issues viewing the diagrams:

1. Make sure you're using a Mermaid-compatible viewer
2. Check the Mermaid syntax for errors
3. Try simplifying complex diagrams if they don't render properly 