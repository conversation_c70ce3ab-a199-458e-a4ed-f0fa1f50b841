# MAS n8n-Style Workflow Architecture

This document provides a visual representation of how the MAS framework can be structured in an n8n-style workflow architecture, incorporating both AI and non-AI nodes.

## n8n-Style Workflow Overview

```mermaid
graph LR
    subgraph "Workflow Canvas"
        Start([Start Trigger]) --> TriggerNode
        
        subgraph "Trigger Nodes"
            TriggerNode[HTTP Webhook]
            ScheduleTrigger[Schedule]
            EventTrigger[Event Listener]
        end
        
        subgraph "Standard Nodes"
            DataFetch[Data Fetcher]
            Filter[Filter]
            Transform[Transform]
            Merge[Merge]
            Split[Split]
            Switch[Switch]
            Loop[Loop]
            HTTP[HTTP Request]
            Email[Email]
            Database[Database]
            FileOps[File Operations]
        end
        
        subgraph "AI Agent Nodes"
            JiraNode[Jira Agent]
            SlackNode[Slack Agent]
            BitbucketNode[Bitbucket Agent]
            K8sNode[Kubernetes Agent]
            ProcessingNode[Processing Agent]
            ErrorHandlerNode[Error Handler Agent]
        end
        
        subgraph "Custom AI Nodes"
            WeatherNode[Weather Agent]
            TranslationNode[Translation Agent]
            SentimentNode[Sentiment Analysis]
            SummarizerNode[Text Summarizer]
        end
        
        %% Workflow connections
        TriggerNode --> DataFetch
        DataFetch --> Filter
        Filter --> |Data OK| Transform
        Filter --> |Data Error| ErrorHandlerNode
        
        Transform --> Split
        Split --> JiraNode
        Split --> SlackNode
        Split --> ProcessingNode
        
        JiraNode --> Merge
        SlackNode --> Merge
        ProcessingNode --> SummarizerNode
        SummarizerNode --> Merge
        
        Merge --> Switch
        Switch --> |Case 1| BitbucketNode
        Switch --> |Case 2| K8sNode
        Switch --> |Case 3| WeatherNode
        
        BitbucketNode --> Loop
        K8sNode --> Loop
        WeatherNode --> TranslationNode
        TranslationNode --> Loop
        
        Loop --> HTTP
        HTTP --> |Success| Email
        HTTP --> |Error| ErrorHandlerNode
        
        Email --> Database
        Database --> FileOps
        FileOps --> End([End])
        
        ErrorHandlerNode --> Email
    end
    
    %% Style
    classDef trigger fill:#f96,stroke:#333,stroke-width:2px;
    classDef standard fill:#9af,stroke:#333,stroke-width:2px;
    classDef aiAgent fill:#f9f,stroke:#333,stroke-width:2px;
    classDef customAI fill:#bfb,stroke:#333,stroke-width:2px;
    classDef endpoint fill:#7f7,stroke:#333,stroke-width:2px;
    
    class TriggerNode,ScheduleTrigger,EventTrigger trigger;
    class DataFetch,Filter,Transform,Merge,Split,Switch,Loop,HTTP,Email,Database,FileOps standard;
    class JiraNode,SlackNode,BitbucketNode,K8sNode,ProcessingNode,ErrorHandlerNode aiAgent;
    class WeatherNode,TranslationNode,SentimentNode,SummarizerNode customAI;
    class Start,End endpoint;
```

## Node Configuration Interface

```mermaid
classDiagram
    class BaseNode {
        +string id
        +string name
        +string type
        +Dict inputs
        +Dict outputs
        +Dict credentials
        +Dict parameters
        +execute()
    }
    
    class TriggerNode {
        +string triggerType
        +Dict webhookConfig
        +Dict scheduleConfig
        +Dict eventConfig
        +onTrigger()
    }
    
    class StandardNode {
        +string operationType
        +Dict operationConfig
        +transform()
        +filter()
        +merge()
        +split()
    }
    
    class AIAgentNode {
        +string agentType
        +string modelName
        +float temperature
        +Dict agentConfig
        +executeAgent()
        +handleError()
    }
    
    class WorkflowExecution {
        +string workflowId
        +Dict workflowData
        +Dict nodeResults
        +Dict executionState
        +executeNode()
        +handleNodeError()
        +saveState()
        +loadState()
    }
    
    BaseNode <|-- TriggerNode
    BaseNode <|-- StandardNode
    BaseNode <|-- AIAgentNode
    
    WorkflowExecution --> BaseNode
```

## Workflow Execution Flow

```mermaid
sequenceDiagram
    participant User
    participant Canvas as Workflow Canvas
    participant Executor as Workflow Executor
    participant TN as Trigger Node
    participant SN as Standard Nodes
    participant AIN as AI Agent Nodes
    participant EH as Error Handler
    participant DB as State Database
    
    User->>Canvas: Design Workflow
    Canvas->>Executor: Save Workflow
    
    Note over Executor,DB: Workflow Execution
    
    Executor->>DB: Initialize Workflow State
    
    Executor->>TN: Activate Trigger
    TN-->>Executor: Trigger Fired
    
    loop Node Execution
        Executor->>DB: Load Current State
        
        alt Standard Node
            Executor->>SN: Execute Node
            SN-->>Executor: Node Result
        else AI Agent Node
            Executor->>AIN: Execute Agent
            AIN-->>Executor: Agent Result
        end
        
        alt Error Occurs
            Executor->>EH: Handle Error
            EH->>EH: Determine Strategy
            EH-->>Executor: Recovery Action
        end
        
        Executor->>DB: Save Updated State
    end
    
    Executor-->>User: Workflow Result
```

## n8n-Style Node Editor

```mermaid
flowchart TD
    subgraph "Node Editor Interface"
        NodeSettings[Node Settings]
        
        subgraph "Input Configuration"
            InputMapping[Input Mapping]
            InputTransform[Input Transformation]
        end
        
        subgraph "Node Configuration"
            GeneralSettings[General Settings]
            OperationSettings[Operation Settings]
            AISettings[AI Model Settings]
            CredentialsSettings[Credentials]
        end
        
        subgraph "Output Configuration"
            OutputMapping[Output Mapping]
            OutputTransform[Output Transformation]
        end
        
        subgraph "Error Handling"
            ErrorStrategy[Error Strategy]
            Fallbacks[Fallbacks]
            Retries[Retries]
        end
        
        NodeSettings --> InputMapping
        NodeSettings --> GeneralSettings
        NodeSettings --> OutputMapping
        NodeSettings --> ErrorStrategy
        
        InputMapping --> InputTransform
        GeneralSettings --> OperationSettings
        GeneralSettings --> AISettings
        GeneralSettings --> CredentialsSettings
        OutputMapping --> OutputTransform
        ErrorStrategy --> Fallbacks
        ErrorStrategy --> Retries
    end
    
    %% Style
    classDef section fill:#f9f,stroke:#333,stroke-width:2px;
    classDef subsection fill:#9af,stroke:#333,stroke-width:2px;
    
    class NodeSettings,InputConfiguration,NodeConfiguration,OutputConfiguration,ErrorHandling section;
    class InputMapping,InputTransform,GeneralSettings,OperationSettings,AISettings,CredentialsSettings,OutputMapping,OutputTransform,ErrorStrategy,Fallbacks,Retries subsection;
```

## Integration with MAS Architecture

```mermaid
graph TD
    subgraph "n8n-Style Workflow Layer"
        WC[Workflow Canvas]
        NE[Node Editor]
        WE[Workflow Executor]
        NR[Node Registry]
    end
    
    subgraph "MAS Framework"
        WB[WorkflowBuilder]
        Agents[Agent Layer]
        SM[State Manager]
        GM[Graph Manager]
    end
    
    subgraph "Storage Layer"
        WFS[Workflow Storage]
        SS[State Storage]
        CS[Credentials Storage]
    end
    
    %% Connections
    WC --> NE
    WC --> WE
    NE --> NR
    WE --> NR
    
    NR --> WB
    WB --> Agents
    WB --> SM
    WB --> GM
    
    SM --> SS
    WE --> WFS
    NR --> CS
    
    %% Style
    classDef n8nLayer fill:#f9f,stroke:#333,stroke-width:2px;
    classDef masLayer fill:#9af,stroke:#333,stroke-width:2px;
    classDef storageLayer fill:#bfb,stroke:#333,stroke-width:2px;
    
    class WC,NE,WE,NR n8nLayer;
    class WB,Agents,SM,GM masLayer;
    class WFS,SS,CS storageLayer;
``` 