# BitbucketAgent Usage Guide

The BitbucketAgent is a powerful component of the MAS (Multi-Agent System) that enables interaction with Bitbucket repositories, including managing pull requests, commits, and performing automated code reviews.

## Features

- Create, update, and manage pull requests
- List and filter pull requests
- Merge or decline pull requests
- Add comments to pull requests
- Add inline comments to specific lines in pull requests
- Perform automated code reviews using AI

## Setup

### Prerequisites

- Bitbucket account with appropriate permissions
- Bitbucket App Password with repository read/write access
- Python 3.8+

### Installation

The BitbucketAgent is included in the MAS package. Make sure you have installed all dependencies:

```bash
pip install -r requirements.txt
```

### Configuration

To use the BitbucketAgent, you need to initialize it with your Bitbucket credentials:

```python
from mas.agents.bitbucket_agent import BitbucketAgent

# Initialize the agent
bitbucket_agent = BitbucketAgent(
    bitbucket_username="your_username",
    bitbucket_app_password="your_app_password",
    default_workspace="your_workspace",
    default_repository="your_repository",
    model_name="gpt-4o",  # Model for general operations
    code_review_model="gpt-4o",  # Model for code reviews (more powerful)
    code_review_temperature=0.1  # Lower temperature for more focused reviews
)
```

## Basic Usage

### Creating a Pull Request

```python
from mas.core.graph_manager import AgentState

# Create an initial state
state = AgentState(
    messages=[
        {"role": "user", "content": "Create a pull request from feature/new-feature to main with title 'Add new feature'"}
    ],
    context={},
    next_steps=["bitbucket_agent"]
)

# Execute the agent
result_state = await bitbucket_agent.execute(state)

# Check the results
print(result_state.context.get("bitbucket_results"))
```

### Commenting on Pull Requests

You can add comments to pull requests in two ways: general comments and inline comments on specific lines.

#### Adding a General Comment

```python
from mas.agents.bitbucket_agent import BitbucketAgent
from mas.core.agent import AgentState

# Initialize the agent
agent = BitbucketAgent(
    bitbucket_username="your_username",
    bitbucket_app_password="your_app_password"
)

# Create the initial state with the task
state = AgentState(
    task="Comment on a pull request",
    context={
        "workspace": "your_workspace",
        "repository": "your_repository",
        "pr_id": 123,
        "comment": "This looks good! Ready to merge."
    }
)

# Execute the agent
result_state = await agent.execute(state)
```

#### Adding Inline Comments

Inline comments are attached to specific lines in the code, making it easier to provide feedback on specific parts of the code.

```python
from mas.agents.bitbucket_agent import BitbucketAgent, add_inline_comment
from mas.core.agent import AgentState
from mas.models.bitbucket_models import CodeReviewComment

# Initialize the agent
agent = BitbucketAgent(
    bitbucket_username="your_username",
    bitbucket_app_password="your_app_password"
)

# Method 1: Using the agent's execute method with context
state = AgentState(
    task="Add an inline comment to a specific line",
    context={
        "workspace": "your_workspace",
        "repository": "your_repository",
        "pr_id": 123
    }
)

# Create the comment model
comment = CodeReviewComment(
    file="path/to/file.py",
    line=42,
    content="Consider using a more descriptive variable name here."
)

# Execute the agent with the comment
result_state = await agent.execute(state)

# Method 2: Directly calling the add_inline_comment tool
result = await add_inline_comment({
    "workspace": "your_workspace",
    "repository": "your_repository",
    "pr_id": 123,
    "file_path": "path/to/file.py",
    "line_number": 42,
    "content": "Consider using a more descriptive variable name here."
})
```

For a complete example, see the `examples/bitbucket_inline_comment_example.py` file.

### Listing Pull Requests

```python
state = AgentState(
    messages=[
        {"role": "user", "content": "List all open pull requests in the repository"}
    ],
    context={},
    next_steps=["bitbucket_agent"]
)

result_state = await bitbucket_agent.execute(state)
```

## Automated Code Reviews

One of the most powerful features of the BitbucketAgent is its ability to perform automated code reviews using AI.

### Performing a Code Review

```python
state = AgentState(
    messages=[
        {"role": "user", "content": "Review the code in pull request #123"}
    ],
    context={},
    next_steps=["bitbucket_agent"]
)

result_state = await bitbucket_agent.execute(state)
```

### Customizing Code Reviews

You can customize the code review by specifying which files to review and providing additional context:

```python
state = AgentState(
    messages=[
        {"role": "user", "content": "Review the Python files in PR #123 focusing on security best practices"}
    ],
    context={},
    next_steps=["bitbucket_agent"]
)

result_state = await bitbucket_agent.execute(state)
```

### Code Review Options

The code review functionality supports several options:

- **files_to_review**: List of specific files or patterns to review
- **review_message**: Custom message to include in the review
- **inline_comments**: Whether to add comments inline (True) or as a single comment (False)

Example with explicit task configuration:

```python
state = AgentState(
    messages=[
        {"role": "user", "content": "Perform a detailed code review"}
    ],
    context={
        "tasks": [
            {
                "agent": "bitbucket_agent",
                "description": "Review the code in PR #123, focusing on src/main.py and utils/*.js files. Check for security issues and performance optimizations."
            }
        ]
    },
    next_steps=["bitbucket_agent"]
)

result_state = await bitbucket_agent.execute(state)
```

## Advanced Usage

### Chaining with Other Agents

The BitbucketAgent can be chained with other agents in your workflow:

```python
from mas.agents.slack_agent import SlackAgent

# Initialize agents
bitbucket_agent = BitbucketAgent(...)
slack_agent = SlackAgent(...)

# Create a workflow
state = AgentState(
    messages=[
        {"role": "user", "content": "Review PR #123 and post a summary to the #dev-team Slack channel"}
    ],
    context={},
    next_steps=["bitbucket_agent", "slack_agent"]
)

# Execute the workflow
state = await bitbucket_agent.execute(state)
state = await slack_agent.execute(state)
```

### Handling Errors

The BitbucketAgent includes error handling that will update the state with error information:

```python
state = await bitbucket_agent.execute(state)

if "error" in state:
    print(f"Error occurred: {state.error}")
    # Handle the error
```

## Best Practices

1. **Use Specific Models**: For code reviews, use more powerful models like GPT-4 for better analysis.

2. **Provide Context**: When requesting a code review, provide specific context about what to focus on.

3. **Filter Files**: For large PRs, specify which files to review to get more focused feedback.

4. **Review the Reviews**: Always have a human review the AI-generated code reviews before acting on them.

5. **Secure Credentials**: Never hardcode your Bitbucket credentials. Use environment variables or a secure secret manager.

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure your Bitbucket App Password has the correct permissions.

2. **Rate Limiting**: Bitbucket API has rate limits. If you're making many requests, implement rate limiting.

3. **Large PRs**: For very large PRs, the diff might be too large for the AI model. Consider reviewing specific files.

4. **JSON Parsing Errors**: If you see JSON parsing errors, check that the model's output is valid JSON.

### Getting Help

If you encounter issues with the BitbucketAgent, check the logs for detailed error messages:

```python
import logging
```

## Example Workflow: Automated PR Review Pipeline

Here's an example of setting up an automated PR review pipeline:

```python
import os
from mas.agents.bitbucket_agent import BitbucketAgent
from mas.core.graph_manager import AgentState

# Initialize the agent with credentials from environment variables
bitbucket_agent = BitbucketAgent(
    bitbucket_username=os.environ.get("BITBUCKET_USERNAME"),
    bitbucket_app_password=os.environ.get("BITBUCKET_PASSWORD"),
    default_workspace="my-workspace",
    default_repository="my-repo",
    code_review_model="gpt-4o"
)

async def review_pull_request(pr_id):
    """Function to review a specific pull request"""
    state = AgentState(
        messages=[],
        context={
            "tasks": [
                {
                    "agent": "bitbucket_agent",
                    "description": f"Perform a comprehensive code review on PR #{pr_id}. Focus on code quality, security, and performance. Add inline comments with specific suggestions for improvement."
                }
            ]
        },
        next_steps=["bitbucket_agent"]
    )
    
    result = await bitbucket_agent.execute(state)
    
    if "error" in result:
        print(f"Error reviewing PR #{pr_id}: {result.error}")
        return False
    
    print(f"Successfully reviewed PR #{pr_id}")
    print(f"Added {result.context['bitbucket_results'][0]['result']['comments_added']} comments")
    
    return True

# Usage
import asyncio
asyncio.run(review_pull_request(123))
```

This documentation should help you get started with the enhanced BitbucketAgent and its code review capabilities. 