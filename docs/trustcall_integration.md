# Trustcall Integration

## Overview

We've integrated the [trustcall](https://github.com/hinthornw/trustcall) library to improve the reliability of structured output generation in our agents. Trustcall solves common issues with LLMs generating complex JSON structures by using JSON patch operations, which is a simpler task that can be done iteratively.

## Benefits

- **Faster & cheaper generation** of structured output
- **Resilient retrying** of validation errors, even for complex, nested schemas
- **Accurate updates** to existing schemas, avoiding undesired deletions

## Implementation

### Supervisor Agent

The Supervisor Agent now uses trustcall to generate structured output according to our Pydantic models. This ensures that the output is always valid and follows our schema.

```python
from trustcall import create_extractor
from mas.models.supervisor_models import SupervisorOutput

# Create the trustcall extractor
self.extractor = create_extractor(
    self.model,
    tools=[SupervisorOutput],
    tool_choice="SupervisorOutput"
)

# Use the extractor
result = await self.extractor.ainvoke({
    "messages": [
        {
            "role": "system",
            "content": system_prompt
        },
        {
            "role": "user",
            "content": user_input
        }
    ]
})

# Extract the validated SupervisorOutput
supervisor_output = result["responses"][0]
```

### Pydantic Models

We've created Pydantic models to define the structure of the output:

- `SupervisorOutput`: The main output from the supervisor agent
- `Task`: A task to be executed by an agent
- `TaskParameters`: Parameters for a task

## How It Works

1. The LLM is asked to generate parameters for the schemas of zero or more tools.
2. If any of these schemas raise validation errors, trustcall re-prompts the LLM to fix by generating a JSON Patch.
3. This process continues until a valid output is generated.

## Testing

We've added tests to verify that the trustcall integration works correctly:

- `test_supervisor_agent_execution`: Tests that the supervisor agent executes correctly with trustcall
- `test_supervisor_agent_error_handling`: Tests that the supervisor agent handles errors correctly

## Future Improvements

- Extend trustcall integration to other agents
- Add support for updating existing schemas
- Implement more complex validation rules 