# MAS Workflow Execution

This document provides a visual representation of the workflow execution process in the MAS (Modular Agent System) framework.

## Workflow Execution Process

```mermaid
flowchart TD
    Start([Start]) --> InitWF[Initialize Workflow]
    InitWF --> LoadState[Load Existing State]
    LoadState --> InitAgents[Initialize Agents]
    
    InitAgents --> SupervisorAnalysis[Supervisor Analyzes Request]
    SupervisorAnalysis --> TaskDelegation[Delegate Tasks to Agents]
    
    TaskDelegation --> ParallelExec{Parallel Execution}
    
    ParallelExec --> JiraExec[Jira Agent Execution]
    ParallelExec --> SlackExec[Slack Agent Execution]
    ParallelExec --> BitbucketExec[Bitbucket Agent Execution]
    ParallelExec --> K8sExec[Kubernetes Agent Execution]
    ParallelExec --> ProcessExec[Processing Agent Execution]
    
    JiraExec --> |Error?| ErrorHandler{Error Handler}
    SlackExec --> |Error?| ErrorHandler
    BitbucketExec --> |Error?| ErrorHandler
    K8sExec --> |Error?| ErrorHandler
    ProcessExec --> |Error?| ErrorHandler
    
    ErrorHandler --> |Retry| RetryExec[Retry Execution]
    ErrorHandler --> |Fallback| FallbackExec[Execute Fallback]
    ErrorHandler --> |Abort| AbortExec[Abort Workflow]
    ErrorHandler --> |Notify| NotifyExec[Generate Notification]
    
    RetryExec --> ResultCollection
    FallbackExec --> ResultCollection
    AbortExec --> ResultCollection
    NotifyExec --> ResultCollection
    
    JiraExec --> |Success| ResultCollection[Collect Results]
    SlackExec --> |Success| ResultCollection
    BitbucketExec --> |Success| ResultCollection
    K8sExec --> |Success| ResultCollection
    ProcessExec --> |Success| ResultCollection
    
    ResultCollection --> SupervisorReview[Supervisor Reviews Results]
    SupervisorReview --> SaveState[Save Workflow State]
    SaveState --> GenerateResponse[Generate Response]
    GenerateResponse --> End([End])
    
    %% Style
    classDef start fill:#7f7,stroke:#333,stroke-width:2px;
    classDef end fill:#f77,stroke:#333,stroke-width:2px;
    classDef process fill:#7cf,stroke:#333,stroke-width:2px;
    classDef decision fill:#fcf,stroke:#333,stroke-width:2px;
    classDef error fill:#f9c,stroke:#333,stroke-width:2px;
    
    class Start,End start;
    class InitWF,LoadState,InitAgents,SupervisorAnalysis,TaskDelegation,ResultCollection,SupervisorReview,SaveState,GenerateResponse process;
    class ParallelExec,ErrorHandler decision;
    class JiraExec,SlackExec,BitbucketExec,K8sExec,ProcessExec process;
    class RetryExec,FallbackExec,AbortExec,NotifyExec error;
```

## Error Handling Process

```mermaid
stateDiagram-v2
    [*] --> Normal: Start Execution
    
    state "Normal Execution" as Normal {
        [*] --> AgentExecution
        AgentExecution --> ResultCollection
        ResultCollection --> [*]
    }
    
    Normal --> ErrorDetected: Error Occurs
    
    state "Error Handling" as ErrorHandling {
        [*] --> ErrorAnalysis
        
        ErrorAnalysis --> Strategy
        
        state "Recovery Strategy" as Strategy {
            [*] --> StrategySelection
            
            StrategySelection --> Retry: If retryable
            StrategySelection --> Fallback: If fallback available
            StrategySelection --> Abort: If critical
            StrategySelection --> Notify: Always
            
            Retry --> StrategyOutcome
            Fallback --> StrategyOutcome
            Abort --> StrategyOutcome
            Notify --> StrategyOutcome
            
            StrategyOutcome --> [*]
        }
        
        Strategy --> ErrorReport
        ErrorReport --> [*]
    }
    
    ErrorDetected --> ErrorHandling
    
    ErrorHandling --> Normal: If recovered
    ErrorHandling --> WorkflowTermination: If not recovered
    
    WorkflowTermination --> [*]: End Execution
```

## Agent Communication Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant WB as WorkflowBuilder
    participant SA as SupervisorAgent
    participant JA as JiraAgent
    participant SLA as SlackAgent
    participant BBA as BitbucketAgent
    participant KA as KubernetesAgent
    participant PA as ProcessingAgent
    participant EHA as ErrorHandlerAgent
    
    Client->>API: POST /agent
    API->>WB: Create workflow
    WB->>SA: Initialize
    
    SA->>SA: Analyze request
    
    par Task Delegation
        SA->>JA: Jira task
        SA->>SLA: Slack task
        SA->>BBA: Bitbucket task
        SA->>KA: Kubernetes task
        SA->>PA: Processing task
    end
    
    par Agent Execution
        JA->>JA: Execute Jira task
        SLA->>SLA: Execute Slack task
        BBA->>BBA: Execute Bitbucket task
        KA->>KA: Execute Kubernetes task
        PA->>PA: Execute Processing task
    end
    
    alt Error in Jira Agent
        JA->>EHA: Report error
        EHA->>EHA: Analyze error
        EHA->>JA: Retry strategy
        JA->>JA: Retry execution
        JA->>SA: Report result
    else Error in Slack Agent
        SLA->>EHA: Report error
        EHA->>EHA: Analyze error
        EHA->>SA: Abort strategy
        SA->>SA: Handle abort
    end
    
    JA->>SA: Jira result
    SLA->>SA: Slack result
    BBA->>SA: Bitbucket result
    KA->>SA: Kubernetes result
    PA->>SA: Processing result
    
    SA->>SA: Consolidate results
    SA->>WB: Return final result
    WB->>API: Return response
    API->>Client: Response
```

## Custom Agent Integration

```mermaid
flowchart LR
    subgraph "MAS Framework"
        BA[BaseAgent]
        WB[WorkflowBuilder]
        SA[SupervisorAgent]
    end
    
    subgraph "Custom Agents"
        CA1[WeatherAgent]
        CA2[TranslationAgent]
        CA3[CustomAgent3]
    end
    
    BA --> CA1
    BA --> CA2
    BA --> CA3
    
    CA1 --> WB
    CA2 --> WB
    CA3 --> WB
    
    WB --> SA
    
    SA --> CA1
    SA --> CA2
    SA --> CA3
    
    %% Style
    classDef framework fill:#bbf,stroke:#333,stroke-width:2px;
    classDef custom fill:#bfb,stroke:#333,stroke-width:2px;
    
    class BA,WB,SA framework;
    class CA1,CA2,CA3 custom;
``` 