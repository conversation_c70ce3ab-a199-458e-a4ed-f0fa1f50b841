# MAS Architecture Diagram

This document provides a visual representation of the MAS (Modular Agent System) architecture using Mermaid diagrams.

## System Architecture Overview

```mermaid
graph TD
    subgraph "API Layer"
        API[FastAPI App]
        API_Endpoints[API Endpoints]
    end

    subgraph "Security Layer"
        Auth[AuthManager]
        User[User]
        Role[Role]
        Permission[Permission]
    end

    subgraph "Orchestration Layer"
        WB[WorkflowBuilder]
    end

    subgraph "State Management Layer"
        SM[StateManager]
        SB[StateBackend]
        MSB[MemoryStateBackend]
        RSB[RedisStateBackend]
    end

    subgraph "Core Layer"
        GM[GraphManager]
        AS[AgentState]
    end

    subgraph "Agent Layer"
        BA[BaseAgent]
        SA[SupervisorAgent]
        JA[JiraAgent]
        SLA[SlackAgent]
        BBA[BitbucketAgent]
        KA[KubernetesAgent]
        EHA[ErrorHandlerAgent]
        PA[ProcessingAgent]
    end

    subgraph "Integration Layer"
        JC[JiraClient]
        SLC[SlackClient]
        BBC[BitbucketClient]
        KC[KubernetesClient]
    end

    subgraph "External Systems"
        Jira[Jira]
        Slack[Slack]
        Bitbucket[Bitbucket]
        K8s[Kubernetes]
    end

    %% Connections between layers
    API --> Auth
    API --> WB
    WB --> GM
    WB --> SM
    GM --> AS
    GM --> BA
    SM --> SB
    SB --> MSB
    SB --> RSB
    
    %% Agent connections
    BA --> SA
    BA --> JA
    BA --> SLA
    BA --> BBA
    BA --> KA
    BA --> EHA
    BA --> PA
    
    %% Integration connections
    JA --> JC
    SLA --> SLC
    BBA --> BBC
    KA --> KC
    
    %% External system connections
    JC --> Jira
    SLC --> Slack
    BBC --> Bitbucket
    KC --> K8s
    
    %% API endpoints
    API_Endpoints --> API
    
    %% Security connections
    Auth --> User
    User --> Role
    Role --> Permission

    %% Style
    classDef apiLayer fill:#f9f,stroke:#333,stroke-width:2px;
    classDef securityLayer fill:#bbf,stroke:#333,stroke-width:2px;
    classDef orchestrationLayer fill:#bfb,stroke:#333,stroke-width:2px;
    classDef stateLayer fill:#fbf,stroke:#333,stroke-width:2px;
    classDef coreLayer fill:#fbb,stroke:#333,stroke-width:2px;
    classDef agentLayer fill:#bff,stroke:#333,stroke-width:2px;
    classDef integrationLayer fill:#ffb,stroke:#333,stroke-width:2px;
    classDef externalLayer fill:#ddd,stroke:#333,stroke-width:2px;
    
    class API,API_Endpoints apiLayer;
    class Auth,User,Role,Permission securityLayer;
    class WB orchestrationLayer;
    class SM,SB,MSB,RSB stateLayer;
    class GM,AS coreLayer;
    class BA,SA,JA,SLA,BBA,KA,EHA,PA agentLayer;
    class JC,SLC,BBC,KC integrationLayer;
    class Jira,Slack,Bitbucket,K8s externalLayer;
```

## Data Flow Diagram

```mermaid
sequenceDiagram
    participant Client
    participant API as API Layer
    participant Auth as Security Layer
    participant WB as WorkflowBuilder
    participant SA as SupervisorAgent
    participant Agents as Specialized Agents
    participant EHA as ErrorHandlerAgent
    participant SM as StateManager
    
    Client->>API: POST /agent (message, context)
    API->>Auth: Authenticate (API key)
    Auth-->>API: Authentication result
    
    API->>WB: Create/load workflow
    WB->>SM: Load state (if existing)
    SM-->>WB: Workflow state
    
    WB->>SA: Analyze request
    SA->>SA: Determine required agents
    
    loop For each required agent
        SA->>Agents: Delegate task
        
        alt Successful execution
            Agents-->>SA: Task result
        else Error occurs
            Agents-->>EHA: Error details
            EHA->>EHA: Analyze error
            EHA->>EHA: Determine recovery strategy
            
            alt Retry strategy
                EHA->>Agents: Retry task
                Agents-->>EHA: Retry result
                EHA-->>SA: Final result
            else Fallback strategy
                EHA->>Agents: Execute fallback
                Agents-->>EHA: Fallback result
                EHA-->>SA: Final result
            else Abort strategy
                EHA-->>SA: Abort with error
            else Notify strategy
                EHA->>EHA: Generate notification
                EHA-->>SA: Error with notification
            end
        end
    end
    
    SA-->>WB: Workflow result
    WB->>SM: Save state
    SM-->>WB: State saved
    WB-->>API: Workflow result
    API-->>Client: Response (result, status)
```

## Agent Interaction Diagram

```mermaid
flowchart TD
    subgraph "Workflow"
        SA[SupervisorAgent]
        
        subgraph "Task Agents"
            JA[JiraAgent]
            SLA[SlackAgent]
            BBA[BitbucketAgent]
            KA[KubernetesAgent]
        end
        
        subgraph "Support Agents"
            PA[ProcessingAgent]
            EHA[ErrorHandlerAgent]
        end
    end
    
    Client[Client] --> |Request| SA
    
    SA --> |Jira tasks| JA
    SA --> |Slack tasks| SLA
    SA --> |Bitbucket tasks| BBA
    SA --> |Kubernetes tasks| KA
    SA --> |Processing tasks| PA
    
    JA --> |Error| EHA
    SLA --> |Error| EHA
    BBA --> |Error| EHA
    KA --> |Error| EHA
    PA --> |Error| EHA
    
    JA --> |Result| SA
    SLA --> |Result| SA
    BBA --> |Result| SA
    KA --> |Result| SA
    PA --> |Result| SA
    EHA --> |Recovery strategy| SA
    
    SA --> |Response| Client
    
    %% Style
    classDef supervisor fill:#f96,stroke:#333,stroke-width:2px;
    classDef taskAgent fill:#9af,stroke:#333,stroke-width:2px;
    classDef supportAgent fill:#9f9,stroke:#333,stroke-width:2px;
    classDef client fill:#ddd,stroke:#333,stroke-width:2px;
    
    class SA supervisor;
    class JA,SLA,BBA,KA taskAgent;
    class PA,EHA supportAgent;
    class Client client;
```

## Component Relationships

```mermaid
erDiagram
    WorkflowBuilder ||--o{ Agent : contains
    WorkflowBuilder ||--|| SupervisorAgent : has
    WorkflowBuilder ||--|| GraphManager : uses
    WorkflowBuilder ||--|| StateManager : uses
    
    Agent ||--|| BaseAgent : inherits
    SupervisorAgent ||--|| BaseAgent : inherits
    JiraAgent ||--|| BaseAgent : inherits
    SlackAgent ||--|| BaseAgent : inherits
    BitbucketAgent ||--|| BaseAgent : inherits
    KubernetesAgent ||--|| BaseAgent : inherits
    ErrorHandlerAgent ||--|| BaseAgent : inherits
    ProcessingAgent ||--|| BaseAgent : inherits
    
    JiraAgent ||--|| JiraClient : uses
    SlackAgent ||--|| SlackClient : uses
    BitbucketAgent ||--|| BitbucketClient : uses
    KubernetesAgent ||--|| KubernetesClient : uses
    
    StateManager ||--|| StateBackend : uses
    MemoryStateBackend ||--|| StateBackend : implements
    RedisStateBackend ||--|| StateBackend : implements
    
    AuthManager ||--o{ User : manages
    User ||--o{ Role : has
    Role ||--o{ Permission : contains
``` 