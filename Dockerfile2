# Base stage
FROM python:3.12-slim as base

LABEL maintainer="Reuven M. <<EMAIL>>"
LABEL description="Modular Multi-Agent System with LangGraph"

ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH="/app:$PYTHONPATH"

WORKDIR /app

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install stage
FROM base as install

ADD https://astral.sh/uv/install.sh /uv-installer.sh
RUN sh /uv-installer.sh && rm /uv-installer.sh

ENV PATH="/root/.local/bin/:$PATH"

COPY pyproject.toml setup.py ./
RUN uv sync

# Final stage
FROM base as final

COPY --from=install /app/.venv /app/.venv

COPY . .

EXPOSE 8080

CMD ["/app/.venv/bin/python", "server.py"] 